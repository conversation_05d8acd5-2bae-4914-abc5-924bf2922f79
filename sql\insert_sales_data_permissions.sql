-- 销售数据功能权限配置脚本

-- 插入销售数据菜单
INSERT INTO system_menu (
    pid, name, path, perms, component, is_show, is_delte, 
    menu_type, sort, icon, create_time, update_time
) VALUES 
-- 销售数据主菜单
(0, '销售数据', '/sales', 'admin:sales:data', 'Layout', 1, 0, 'M', 8, 'chart', NOW(), NOW()),

-- 销售数据子菜单
((SELECT id FROM system_menu WHERE name = '销售数据' AND pid = 0 LIMIT 1), 
 '销售数据管理', '/sales/data', 'admin:sales:data:list', 'sales/data/index', 1, 0, 'C', 1, 'chart', NOW(), NOW()),

-- 销售数据权限按钮
((SELECT id FROM system_menu WHERE name = '销售数据管理' LIMIT 1), 
 '查看销售数据', '', 'admin:sales:data:list', '', 0, 0, 'A', 1, '', NOW(), NOW()),

((SELECT id FROM system_menu WHERE name = '销售数据管理' LIMIT 1), 
 '销售数据统计', '', 'admin:sales:data:statistics', '', 0, 0, 'A', 2, '', NOW(), NOW()),

((SELECT id FROM system_menu WHERE name = '销售数据管理' LIMIT 1), 
 '查看销售明细', '', 'admin:sales:data:detail', '', 0, 0, 'A', 3, '', NOW(), NOW()),

((SELECT id FROM system_menu WHERE name = '销售数据管理' LIMIT 1), 
 '导出销售数据', '', 'admin:sales:data:export', '', 0, 0, 'A', 4, '', NOW(), NOW())

ON DUPLICATE KEY UPDATE 
    name = VALUES(name),
    path = VALUES(path),
    perms = VALUES(perms),
    update_time = NOW();

-- 获取菜单ID
SET @sales_menu_id = (SELECT id FROM system_menu WHERE name = '销售数据' AND pid = 0 LIMIT 1);
SET @sales_data_menu_id = (SELECT id FROM system_menu WHERE name = '销售数据管理' LIMIT 1);
SET @sales_list_perm_id = (SELECT id FROM system_menu WHERE perms = 'admin:sales:data:list' AND name = '查看销售数据' LIMIT 1);
SET @sales_statistics_perm_id = (SELECT id FROM system_menu WHERE perms = 'admin:sales:data:statistics' LIMIT 1);
SET @sales_detail_perm_id = (SELECT id FROM system_menu WHERE perms = 'admin:sales:data:detail' LIMIT 1);
SET @sales_export_perm_id = (SELECT id FROM system_menu WHERE perms = 'admin:sales:data:export' LIMIT 1);

-- 为超级管理员角色添加销售数据权限
INSERT INTO system_role_menu (rid, menu_id, create_time, update_time) VALUES 
(1, @sales_menu_id, NOW(), NOW()),
(1, @sales_data_menu_id, NOW(), NOW()),
(1, @sales_list_perm_id, NOW(), NOW()),
(1, @sales_statistics_perm_id, NOW(), NOW()),
(1, @sales_detail_perm_id, NOW(), NOW()),
(1, @sales_export_perm_id, NOW(), NOW())
ON DUPLICATE KEY UPDATE update_time = NOW();

-- 创建销售数据管理员角色（可选）
INSERT INTO system_role (
    role_name, rules, level, status, create_time, update_time
) VALUES 
('销售数据管理员', CONCAT(@sales_menu_id, ',', @sales_data_menu_id, ',', @sales_list_perm_id, ',', @sales_statistics_perm_id, ',', @sales_detail_perm_id), 1, 1, NOW(), NOW())
ON DUPLICATE KEY UPDATE 
    rules = VALUES(rules),
    update_time = NOW();

-- 获取销售数据管理员角色ID
SET @sales_admin_role_id = (SELECT id FROM system_role WHERE role_name = '销售数据管理员' LIMIT 1);

-- 为销售数据管理员角色添加菜单权限
INSERT INTO system_role_menu (rid, menu_id, create_time, update_time) VALUES 
(@sales_admin_role_id, @sales_menu_id, NOW(), NOW()),
(@sales_admin_role_id, @sales_data_menu_id, NOW(), NOW()),
(@sales_admin_role_id, @sales_list_perm_id, NOW(), NOW()),
(@sales_admin_role_id, @sales_statistics_perm_id, NOW(), NOW()),
(@sales_admin_role_id, @sales_detail_perm_id, NOW(), NOW())
ON DUPLICATE KEY UPDATE update_time = NOW();

-- 插入系统权限表（如果使用）
INSERT INTO system_permissions (
    pid, name, path, is_delte, create_time, update_time
) VALUES 
(0, '销售数据管理', 'admin:sales:data', 0, NOW(), NOW()),
((SELECT id FROM system_permissions WHERE name = '销售数据管理' AND pid = 0 LIMIT 1), '查看销售数据列表', 'admin:sales:data:list', 0, NOW(), NOW()),
((SELECT id FROM system_permissions WHERE name = '销售数据管理' AND pid = 0 LIMIT 1), '查看销售数据统计', 'admin:sales:data:statistics', 0, NOW(), NOW()),
((SELECT id FROM system_permissions WHERE name = '销售数据管理' AND pid = 0 LIMIT 1), '查看销售明细', 'admin:sales:data:detail', 0, NOW(), NOW()),
((SELECT id FROM system_permissions WHERE name = '销售数据管理' AND pid = 0 LIMIT 1), '导出销售数据', 'admin:sales:data:export', 0, NOW(), NOW())
ON DUPLICATE KEY UPDATE 
    name = VALUES(name),
    path = VALUES(path),
    update_time = NOW();

-- 获取权限ID
SET @sales_perm_id = (SELECT id FROM system_permissions WHERE name = '销售数据管理' AND pid = 0 LIMIT 1);
SET @sales_list_perm_id2 = (SELECT id FROM system_permissions WHERE path = 'admin:sales:data:list' LIMIT 1);
SET @sales_statistics_perm_id2 = (SELECT id FROM system_permissions WHERE path = 'admin:sales:data:statistics' LIMIT 1);
SET @sales_detail_perm_id2 = (SELECT id FROM system_permissions WHERE path = 'admin:sales:data:detail' LIMIT 1);
SET @sales_export_perm_id2 = (SELECT id FROM system_permissions WHERE path = 'admin:sales:data:export' LIMIT 1);

-- 为超级管理员角色添加权限
INSERT INTO system_role_permissions (rid, pid, create_time, update_time) VALUES 
(1, @sales_perm_id, NOW(), NOW()),
(1, @sales_list_perm_id2, NOW(), NOW()),
(1, @sales_statistics_perm_id2, NOW(), NOW()),
(1, @sales_detail_perm_id2, NOW(), NOW()),
(1, @sales_export_perm_id2, NOW(), NOW())
ON DUPLICATE KEY UPDATE update_time = NOW();

-- 为销售数据管理员角色添加权限
INSERT INTO system_role_permissions (rid, pid, create_time, update_time) VALUES 
(@sales_admin_role_id, @sales_perm_id, NOW(), NOW()),
(@sales_admin_role_id, @sales_list_perm_id2, NOW(), NOW()),
(@sales_admin_role_id, @sales_statistics_perm_id2, NOW(), NOW()),
(@sales_admin_role_id, @sales_detail_perm_id2, NOW(), NOW())
ON DUPLICATE KEY UPDATE update_time = NOW();

-- 添加系统配置
INSERT INTO system_config (menu_name, menu_key, value, info, sort, status, create_time, update_time) VALUES 
('销售数据功能开关', 'sales_data_enabled', '1', '是否启用销售数据功能：1-启用，0-禁用', 100, 1, NOW(), NOW()),
('销售数据缓存时间', 'sales_data_cache_time', '300', '销售数据缓存时间（秒），默认5分钟', 101, 1, NOW(), NOW()),
('销售数据导出限制', 'sales_data_export_limit', '10000', '单次导出销售数据的最大条数', 102, 1, NOW(), NOW())
ON DUPLICATE KEY UPDATE 
    value = VALUES(value),
    info = VALUES(info),
    update_time = NOW();

COMMIT;

-- 验证权限配置
SELECT '=== 销售数据菜单 ===' AS info;
SELECT id, pid, name, path, perms, menu_type, is_show 
FROM system_menu 
WHERE name LIKE '%销售数据%' OR perms LIKE '%sales:data%'
ORDER BY pid, sort;

SELECT '=== 销售数据权限 ===' AS info;
SELECT id, pid, name, path 
FROM system_permissions 
WHERE name LIKE '%销售数据%' OR path LIKE '%sales:data%'
ORDER BY pid;

SELECT '=== 角色权限关联 ===' AS info;
SELECT r.role_name, m.name AS menu_name, m.perms 
FROM system_role r
JOIN system_role_menu rm ON r.id = rm.rid
JOIN system_menu m ON rm.menu_id = m.id
WHERE m.perms LIKE '%sales:data%'
ORDER BY r.role_name, m.sort;
