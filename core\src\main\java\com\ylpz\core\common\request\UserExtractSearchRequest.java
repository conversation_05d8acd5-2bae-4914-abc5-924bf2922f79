package com.ylpz.core.common.request;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 用户提现表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_extract")
@ApiModel(value = "UserExtract对象", description = "用户提现表")
public class UserExtractSearchRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "搜索关键字")
    private String keywords;

    @ApiModelProperty(value = "bank = 银行卡 alipay = 支付宝 weixin = 微信")
    private String extractType;

    @ApiModelProperty(value = "-1 已拒绝 0 待审核 1 审核通过 2 提现成功 3 打款失败")
    private Integer status;

    @ApiModelProperty(value = "today,yesterday,lately7,lately15,lately30,month,year,/yyyy-MM-dd hh:mm:ss,yyyy-MM-dd hh:mm:ss/")
    private String dateLimit;

    @ApiModelProperty(value = "开始时间 yyyy-MM-dd HH:mm:ss")
    private String startTime;

    @ApiModelProperty(value = "结束时间 yyyy-MM-dd HH:mm:ss")
    private String endTime;

    @ApiModelProperty(value = "提现编号")
    private String extractNumber;
}
