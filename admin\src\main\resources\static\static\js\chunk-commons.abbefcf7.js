(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-commons"],{"0d66":function(e,t,a){"use strict";a("65d4")},"0f56":function(e,t,a){"use strict";var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-row",{staticClass:"ivu-mt",attrs:{align:"middle",gutter:20}},e._l(e.cardLists,(function(t,o){return a("el-col",{key:o,staticClass:"ivu-mb mb20",attrs:{xl:6,lg:6,md:12,sm:12,xs:24}},[a("div",{staticClass:"card_box"},[a("div",{staticClass:"card_box_cir",class:t.class},[t.icon?a("span",{staticClass:"iconfont",class:t.icon,style:{color:t.color}}):a("i",{staticClass:"el-icon-edit",staticStyle:{color:"#fff"}})]),e._v(" "),a("div",{staticClass:"card_box_txt"},[a("span",{staticClass:"sp2",domProps:{textContent:e._s(t.name)}}),e._v(" "),a("span",{staticClass:"sp1",domProps:{textContent:e._s(t.count||0)}})])])])})),1)},n=[],i={name:"index",props:{cardLists:Array}},l=i,c=(a("ecc7"),a("2877")),r=Object(c["a"])(l,o,n,!1,null,"0dc87649",null);t["a"]=r.exports},"15e7":function(e,t,a){"use strict";function o(e){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}function n(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,o)}return a}function i(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?n(Object(a),!0).forEach((function(t){l(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):n(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function l(e,t,a){return(t=c(t))in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function c(e){var t=r(e,"string");return"symbol"==o(t)?t:t+""}function r(e,t){if("object"!=o(e)||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var n=a.call(e,t||"default");if("object"!=o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function s(e,t,a){t.props.value=a,t.on.input=function(t){e.$emit("input",t)}}var u={},d=a("fb9d"),p=d.keys()||[];p.forEach((function(e){var t=e.replace(/^\.\/(.*)\.\w+$/,"$1"),a=d(e).default;u[t]=a})),t["a"]={render:function(e){var t=this,a={attrs:{},props:{},on:{},style:{}},o=JSON.parse(JSON.stringify(this.conf)),n=[],l=u[o.__config__.tag];return l&&Object.keys(l).forEach((function(t){var a=l[t];o.__slot__&&o.__slot__[t]&&n.push(a(e,o,t))})),Object.keys(o).forEach((function(e){var n=o[e];"__vModel__"===e?s(t,a,o.__config__.defaultValue):a[e]?a[e]=i(i({},a[e]),n):a.attrs[e]=n})),delete a.attrs.__config__,delete a.attrs.__slot__,e(this.conf.__config__.tag,a,n)},props:["conf"]}},"1b31":function(e,t,a){},"373c":function(e,t,a){"use strict";a.r(t),t["default"]={prepend:function(e,t,a){return e("template",{slot:"prepend"},[t.__slot__[a]])},append:function(e,t,a){return e("template",{slot:"append"},[t.__slot__[a]])}}},"3fbe":function(e,t,a){"use strict";var o,n,i=a("ec7f"),l=i["a"],c=a("2877"),r=Object(c["a"])(l,o,n,!1,null,null,null);t["a"]=r.exports},"4b3f":function(e){e.exports=["platform-eleme","eleme","delete-solid","delete","s-tools","setting","user-solid","user","phone","phone-outline","more","more-outline","star-on","star-off","s-goods","goods","warning","warning-outline","question","info","remove","circle-plus","success","error","zoom-in","zoom-out","remove-outline","circle-plus-outline","circle-check","circle-close","s-help","help","minus","plus","check","close","picture","picture-outline","picture-outline-round","upload","upload2","download","camera-solid","camera","video-camera-solid","video-camera","message-solid","bell","s-cooperation","s-order","s-platform","s-fold","s-unfold","s-operation","s-promotion","s-home","s-release","s-ticket","s-management","s-open","s-shop","s-marketing","s-flag","s-comment","s-finance","s-claim","s-custom","s-opportunity","s-data","s-check","s-grid","menu","share","d-caret","caret-left","caret-right","caret-bottom","caret-top","bottom-left","bottom-right","back","right","bottom","top","top-left","top-right","arrow-left","arrow-right","arrow-down","arrow-up","d-arrow-left","d-arrow-right","video-pause","video-play","refresh","refresh-right","refresh-left","finished","sort","sort-up","sort-down","rank","loading","view","c-scale-to-original","date","edit","edit-outline","folder","folder-opened","folder-add","folder-remove","folder-delete","folder-checked","tickets","document-remove","document-delete","document-copy","document-checked","document","document-add","printer","paperclip","takeaway-box","search","monitor","attract","mobile","scissors","umbrella","headset","brush","mouse","coordinate","magic-stick","reading","data-line","data-board","pie-chart","data-analysis","collection-tag","film","suitcase","suitcase-1","receiving","collection","files","notebook-1","notebook-2","toilet-paper","office-building","school","table-lamp","house","no-smoking","smoking","shopping-cart-full","shopping-cart-1","shopping-cart-2","shopping-bag-1","shopping-bag-2","sold-out","sell","present","box","bank-card","money","coin","wallet","discount","price-tag","news","guide","male","female","thumb","cpu","link","connection","open","turn-off","set-up","chat-round","chat-line-round","chat-square","chat-dot-round","chat-dot-square","chat-line-square","message","postcard","position","turn-off-microphone","microphone","close-notification","bangzhu","time","odometer","crop","aim","switch-button","full-screen","copy-document","mic","stopwatch","medal-1","medal","trophy","trophy-1","first-aid-kit","discover","place","location","location-outline","location-information","add-location","delete-location","map-location","alarm-clock","timer","watch-1","watch","lock","unlock","key","service","mobile-phone","bicycle","truck","ship","basketball","football","soccer","baseball","wind-power","light-rain","lightning","heavy-rain","sunrise","sunrise-1","sunset","sunny","cloudy","partly-cloudy","cloudy-and-sunny","moon","moon-night","dish","dish-1","food","chicken","fork-spoon","knife-fork","burger","tableware","sugar","dessert","ice-cream","hot-water","water-cup","coffee-cup","cold-drink","goblet","goblet-full","goblet-square","goblet-square-full","refrigerator","grape","watermelon","cherry","apple","pear","orange","coffee","ice-tea","ice-drink","milk-tea","potato-strips","lollipop","ice-cream-square","ice-cream-round"]},"5abd":function(e,t,a){"use strict";var o,n,i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"container-FromGen"},[a("div",{staticClass:"left-board"},[e._m(0),e._v(" "),a("el-scrollbar",{staticClass:"left-scrollbar"},[a("div",{staticClass:"components-list"},e._l(e.leftComponents,(function(t,o){return a("div",{key:o},[a("div",{staticClass:"components-title"},[a("svg-icon",{attrs:{"icon-class":"component"}}),e._v("\n              "+e._s(t.title)+"\n            ")],1),e._v(" "),a("draggable",{staticClass:"components-draggable",attrs:{list:t.list,group:{name:"componentsGroup",pull:"clone",put:!1},clone:e.cloneComponent,draggable:".components-item",sort:!1},on:{end:e.onEnd}},e._l(t.list,(function(t,o){return a("div",{key:o,staticClass:"components-item",on:{click:function(a){return e.addComponent(t)}}},[a("div",{staticClass:"components-body"},[a("svg-icon",{attrs:{"icon-class":t.__config__.tagIcon}}),e._v("\n                  "+e._s(t.__config__.label)+"\n                ")],1)])})),0)],1)})),0)])],1),e._v(" "),a("div",{staticClass:"center-board"},[a("div",{staticClass:"action-bar"},[a("el-form",{ref:"selfForm",attrs:{inline:"",model:e.selfForm}},[a("el-form-item",{attrs:{label:"名称",prop:"name",rules:[{required:!0,message:"请填写名称",trigger:["blur","change"]}]}},[a("el-input",{attrs:{placeholder:"名称"},model:{value:e.selfForm.name,callback:function(t){e.$set(e.selfForm,"name",t)},expression:"selfForm.name"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"描述",prop:"info",rules:[{required:!0,message:"请填写描述",trigger:["blur","change"]}]}},[a("el-input",{attrs:{placeholder:"描述"},model:{value:e.selfForm.info,callback:function(t){e.$set(e.selfForm,"info",t)},expression:"selfForm.info"}})],1),e._v(" "),a("el-form-item",[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:system:form:update"],expression:"['admin:system:form:update']"}],attrs:{type:"primary"},on:{click:function(t){return e.handlerSaveJSON("selfForm")}}},[e._v("保存")])],1)],1)],1),e._v(" "),a("el-scrollbar",{staticClass:"center-scrollbar"},[a("el-row",{staticClass:"center-board-row",attrs:{gutter:e.formConf.gutter}},[a("el-form",{attrs:{size:e.formConf.size,"label-position":e.formConf.labelPosition,disabled:e.formConf.disabled,"label-width":e.formConf.labelWidth+"px"}},[a("draggable",{staticClass:"drawing-board",attrs:{list:e.drawingList,animation:340,group:"componentsGroup"}},e._l(e.drawingList,(function(t,o){return a("draggable-item",{key:t.renderKey,attrs:{"drawing-list":e.drawingList,element:t,index:o,"active-id":e.activeId,"form-conf":e.formConf},on:{activeItem:e.activeFormItem,copyItem:e.drawingItemCopy,deleteItem:e.drawingItemDelete}})})),1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:!e.drawingList.length,expression:"!drawingList.length"}],staticClass:"empty-info"},[e._v("\n              从左侧拖入或点选组件进行表单设计\n            ")])],1)],1)],1)],1),e._v(" "),a("right-panel",{attrs:{"active-data":e.activeData,"form-conf":e.formConf,"show-field":!!e.drawingList.length},on:{"tag-change":e.tagChange}}),e._v(" "),a("form-drawer",{attrs:{visible:e.drawerVisible,"form-data":e.formData,size:"100%","generate-conf":e.generateConf},on:{"update:visible":function(t){e.drawerVisible=t}}}),e._v(" "),a("json-drawer",{attrs:{size:"60%",visible:e.jsonDrawerVisible,"json-str":JSON.stringify(e.formData)},on:{"update:visible":function(t){e.jsonDrawerVisible=t},refresh:e.refreshJson}}),e._v(" "),a("code-type-dialog",{attrs:{visible:e.dialogVisible,title:"选择生成类型","show-file-name":e.showFileName},on:{"update:visible":function(t){e.dialogVisible=t},confirm:e.generate}}),e._v(" "),a("input",{attrs:{id:"copyNode",type:"hidden"}})],1)},l=[function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"logo-wrapper"},[a("div",{staticClass:"logo"},[a("span",[e._v("CRMEB")])])])}],c=a("b76a"),r=a.n(c),s=a("7a1a"),u=a("21a6"),d=a("b311"),p=a.n(d),_=a("15e7"),f=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-drawer",e._g(e._b({on:{opened:e.onOpen,close:e.onClose}},"el-drawer",e.$attrs,!1),e.$listeners),[a("div",{staticStyle:{height:"100%"}},[a("el-row",{staticStyle:{height:"100%",overflow:"auto"}},[a("el-col",{staticClass:"left-editor",attrs:{md:24,lg:12}},[a("div",{staticClass:"setting",attrs:{title:"资源引用"},on:{click:e.showResource}},[a("el-badge",{staticClass:"item",attrs:{"is-dot":!!e.resources.length}},[a("i",{staticClass:"el-icon-setting"})])],1),e._v(" "),a("el-tabs",{staticClass:"editor-tabs",attrs:{type:"card"},model:{value:e.activeTab,callback:function(t){e.activeTab=t},expression:"activeTab"}},[a("el-tab-pane",{attrs:{name:"html"}},[a("span",{attrs:{slot:"label"},slot:"label"},["html"===e.activeTab?a("i",{staticClass:"el-icon-edit"}):a("i",{staticClass:"el-icon-document"}),e._v("\n                template\n              ")])]),e._v(" "),a("el-tab-pane",{attrs:{name:"js"}},[a("span",{attrs:{slot:"label"},slot:"label"},["js"===e.activeTab?a("i",{staticClass:"el-icon-edit"}):a("i",{staticClass:"el-icon-document"}),e._v("\n                script\n              ")])]),e._v(" "),a("el-tab-pane",{attrs:{name:"css"}},[a("span",{attrs:{slot:"label"},slot:"label"},["css"===e.activeTab?a("i",{staticClass:"el-icon-edit"}):a("i",{staticClass:"el-icon-document"}),e._v("\n                css\n              ")])])],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:"html"===e.activeTab,expression:"activeTab==='html'"}],staticClass:"tab-editor",attrs:{id:"editorHtml"}}),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:"js"===e.activeTab,expression:"activeTab==='js'"}],staticClass:"tab-editor",attrs:{id:"editorJs"}}),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:"css"===e.activeTab,expression:"activeTab==='css'"}],staticClass:"tab-editor",attrs:{id:"editorCss"}})],1),e._v(" "),a("el-col",{staticClass:"right-preview",attrs:{md:24,lg:12}},[a("div",{staticClass:"action-bar",style:{"text-align":"left"}},[a("span",{staticClass:"bar-btn",on:{click:e.runCode}},[a("i",{staticClass:"el-icon-refresh"}),e._v("\n              刷新\n            ")]),e._v(" "),a("span",{staticClass:"bar-btn",on:{click:e.exportFile}},[a("i",{staticClass:"el-icon-download"}),e._v("\n              导出vue文件\n            ")]),e._v(" "),a("span",{ref:"copyBtn",staticClass:"bar-btn copy-btn"},[a("i",{staticClass:"el-icon-document-copy"}),e._v("\n              复制代码\n            ")]),e._v(" "),a("span",{staticClass:"bar-btn delete-btn",on:{click:function(t){return e.$emit("update:visible",!1)}}},[a("i",{staticClass:"el-icon-circle-close"}),e._v("\n              关闭\n            ")])]),e._v(" "),a("iframe",{directives:[{name:"show",rawName:"v-show",value:e.isIframeLoaded,expression:"isIframeLoaded"}],ref:"previewPage",staticClass:"result-wrapper",attrs:{frameborder:"0",src:"preview.html"},on:{load:e.iframeLoad}}),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:!e.isIframeLoaded,expression:"!isIframeLoaded"},{name:"loading",rawName:"v-loading",value:!0,expression:"true"}],staticClass:"result-wrapper"})])],1)],1)]),e._v(" "),a("resource-dialog",{attrs:{visible:e.resourceVisible,"origin-resource":e.resources},on:{"update:visible":function(t){e.resourceVisible=t},save:e.setResource}})],1)},m=[],v=a("1861"),h=a("8446"),b=a("5f87");function g(e){return'<el-dialog v-bind="$attrs" v-on="$listeners" @open="onOpen"  @close="onClose" title="Dialog Titile">\n    '.concat(e,'\n    <div slot="footer">\n      <el-button @click="close">取消</el-button>\n      <el-button type="primary" @click="handelConfirm">确定</el-button>\n    </div>\n  </el-dialog>')}function y(e){return"<template>\n    <div>\n      ".concat(e,"\n    </div>\n  </template>")}function D(e){return"<script>\n    ".concat(e,"\n  <\/script>")}function w(e){return"<style>\n    ".concat(e,"\n  </style>")}function k(e,t,a){var o="";"right"!==e.labelPosition&&(o='label-position="'.concat(e.labelPosition,'"'));var i=e.disabled?':disabled="'.concat(e.disabled,'"'):"",l='<el-form ref="'.concat(e.formRef,'" :model="').concat(e.formModel,'" :rules="').concat(e.formRules,'" size="').concat(e.size,'" ').concat(i,' label-width="').concat(e.labelWidth,'px" ').concat(o,">\n      ").concat(t,"\n      ").concat(x(e,a),"\n    </el-form>");return n&&(l='<el-row :gutter="'.concat(e.gutter,'">\n        ').concat(l,"\n      </el-row>")),l}function x(e,t){var a="";return e.formBtns&&"file"===t&&(a='<el-form-item size="large">\n          <el-button type="primary" @click="submitForm">提交</el-button>\n          <el-button @click="resetForm">取消</el-button>\n        </el-form-item>',n&&(a='<el-col :span="24">\n          '.concat(a,"\n        </el-col>"))),a}function C(e,t){return n||24!==e.__config__.span?'<el-col :span="'.concat(e.__config__.span,'">\n      ').concat(t,"\n    </el-col>"):t}var O={colFormItem:function(e){var t=e.__config__,a="",n='label="'.concat(t.label,'"');t.labelWidth&&t.labelWidth!==o.labelWidth&&(a='label-width="'.concat(t.labelWidth,'px"')),!1===t.showLabel&&(a='label-width="0"',n="");var i=!h["a"][t.tag]&&t.required?"required":"",l=I[t.tag]?I[t.tag](e):null,c="<el-form-item ".concat(a," ").concat(n,' prop="').concat(e.__vModel__,'" ').concat(i,">\n        ").concat(l,"\n      </el-form-item>");return c=C(e,c),c},rowFormItem:function(e){var t=e.__config__,a="default"===e.type?"":'type="'.concat(e.type,'"'),o="default"===e.type?"":'justify="'.concat(e.justify,'"'),n="default"===e.type?"":'align="'.concat(e.align,'"'),i=e.gutter?':gutter="'.concat(e.gutter,'"'):"",l=t.children.map((function(e){return O[e.__config__.layout](e)})),c="<el-row ".concat(a," ").concat(o," ").concat(n," ").concat(i,">\n      ").concat(l.join("\n"),"\n    </el-row>");return c=C(e,c),c}},I={"el-button":function(e){var t=j(e),a=t.tag,o=t.disabled,n=e.type?'type="'.concat(e.type,'"'):"",i=e.icon?'icon="'.concat(e.icon,'"'):"",l=e.round?"round":"",c=e.size?'size="'.concat(e.size,'"'):"",r=e.plain?"plain":"",s=e.circle?"circle":"",u=$(e);return u&&(u="\n".concat(u,"\n")),"<".concat(a," ").concat(n," ").concat(i," ").concat(l," ").concat(c," ").concat(r," ").concat(o," ").concat(s,">").concat(u,"</").concat(a,">")},"el-input":function(e){var t=j(e),a=t.tag,o=t.disabled,n=t.vModel,i=t.clearable,l=t.placeholder,c=t.width,r=e.maxlength?':maxlength="'.concat(e.maxlength,'"'):"",s=e["show-word-limit"]?"show-word-limit":"",u=e.readonly?"readonly":"",d=e["prefix-icon"]?"prefix-icon='".concat(e["prefix-icon"],"'"):"",p=e["suffix-icon"]?"suffix-icon='".concat(e["suffix-icon"],"'"):"",_=e["show-password"]?"show-password":"",f=e.type?'type="'.concat(e.type,'"'):"",m=e.autosize&&e.autosize.minRows?':autosize="{minRows: '.concat(e.autosize.minRows,", maxRows: ").concat(e.autosize.maxRows,'}"'):"",v=L(e);return v&&(v="\n".concat(v,"\n")),"<".concat(a," ").concat(n," ").concat(f," ").concat(l," ").concat(r," ").concat(s," ").concat(u," ").concat(o," ").concat(i," ").concat(d," ").concat(p," ").concat(_," ").concat(m," ").concat(c,">").concat(v,"</").concat(a,">")},"el-input-number":function(e){var t=j(e),a=t.tag,o=t.disabled,n=t.vModel,i=t.placeholder,l=e["controls-position"]?"controls-position=".concat(e["controls-position"]):"",c=e.min?":min='".concat(e.min,"'"):"",r=e.max?":max='".concat(e.max,"'"):"",s=e.step?":step='".concat(e.step,"'"):"",u=e["step-strictly"]?"step-strictly":"",d=e.precision?":precision='".concat(e.precision,"'"):"";return"<".concat(a," ").concat(n," ").concat(i," ").concat(s," ").concat(u," ").concat(d," ").concat(l," ").concat(c," ").concat(r," ").concat(o,"></").concat(a,">")},"el-select":function(e){var t=j(e),a=t.tag,o=t.disabled,n=t.vModel,i=t.clearable,l=t.placeholder,c=t.width,r=e.filterable?"filterable":"",s=e.multiple?"multiple":"",u=T(e);return u&&(u="\n".concat(u,"\n")),"<".concat(a," ").concat(n," ").concat(l," ").concat(o," ").concat(s," ").concat(r," ").concat(i," ").concat(c,">").concat(u,"</").concat(a,">")},"el-radio-group":function(e){var t=j(e),a=t.tag,o=t.disabled,n=t.vModel,i='size="'.concat(e.size,'"'),l=S(e);return l&&(l="\n".concat(l,"\n")),"<".concat(a," ").concat(n," ").concat(i," ").concat(o,">").concat(l,"</").concat(a,">")},"el-checkbox-group":function(e){var t=j(e),a=t.tag,o=t.disabled,n=t.vModel,i='size="'.concat(e.size,'"'),l=e.min?':min="'.concat(e.min,'"'):"",c=e.max?':max="'.concat(e.max,'"'):"",r=M(e);return r&&(r="\n".concat(r,"\n")),"<".concat(a," ").concat(n," ").concat(l," ").concat(c," ").concat(i," ").concat(o,">").concat(r,"</").concat(a,">")},"el-switch":function(e){var t=j(e),a=t.tag,o=t.disabled,n=t.vModel,i=e["active-text"]?'active-text="'.concat(e["active-text"],'"'):"",l=e["inactive-text"]?'inactive-text="'.concat(e["inactive-text"],'"'):"",c=e["active-color"]?'active-color="'.concat(e["active-color"],'"'):"",r=e["inactive-color"]?'inactive-color="'.concat(e["inactive-color"],'"'):"",s=!0!==e["active-value"]?":active-value='".concat(JSON.stringify(e["active-value"]),"'"):"",u=!1!==e["inactive-value"]?":inactive-value='".concat(JSON.stringify(e["inactive-value"]),"'"):"";return"<".concat(a," ").concat(n," ").concat(i," ").concat(l," ").concat(c," ").concat(r," ").concat(s," ").concat(u," ").concat(o,"></").concat(a,">")},"el-cascader":function(e){var t=j(e),a=t.tag,o=t.disabled,n=t.vModel,i=t.clearable,l=t.placeholder,c=t.width,r=e.options?':options="'.concat(e.__vModel__,'Options"'):"",s=e.props?':props="'.concat(e.__vModel__,'Props"'):"",u=e["show-all-levels"]?"":':show-all-levels="false"',d=e.filterable?"filterable":"",p="/"===e.separator?"":'separator="'.concat(e.separator,'"');return"<".concat(a," ").concat(n," ").concat(r," ").concat(s," ").concat(c," ").concat(u," ").concat(l," ").concat(p," ").concat(d," ").concat(i," ").concat(o,"></").concat(a,">")},"el-slider":function(e){var t=j(e),a=t.tag,o=t.disabled,n=t.vModel,i=e.min?":min='".concat(e.min,"'"):"",l=e.max?":max='".concat(e.max,"'"):"",c=e.step?":step='".concat(e.step,"'"):"",r=e.range?"range":"",s=e["show-stops"]?':show-stops="'.concat(e["show-stops"],'"'):"";return"<".concat(a," ").concat(i," ").concat(l," ").concat(c," ").concat(n," ").concat(r," ").concat(s," ").concat(o,"></").concat(a,">")},"el-time-picker":function(e){var t=j(e),a=t.tag,o=t.disabled,n=t.vModel,i=t.clearable,l=t.placeholder,c=t.width,r=e["start-placeholder"]?'start-placeholder="'.concat(e["start-placeholder"],'"'):"",s=e["end-placeholder"]?'end-placeholder="'.concat(e["end-placeholder"],'"'):"",u=e["range-separator"]?'range-separator="'.concat(e["range-separator"],'"'):"",d=e["is-range"]?"is-range":"",p=e.format?'format="'.concat(e.format,'"'):"",_=e["value-format"]?'value-format="'.concat(e["value-format"],'"'):"",f=e["picker-options"]?":picker-options='".concat(JSON.stringify(e["picker-options"]),"'"):"";return"<".concat(a," ").concat(n," ").concat(d," ").concat(p," ").concat(_," ").concat(f," ").concat(c," ").concat(l," ").concat(r," ").concat(s," ").concat(u," ").concat(i," ").concat(o,"></").concat(a,">")},"el-date-picker":function(e){var t=j(e),a=t.tag,o=t.disabled,n=t.vModel,i=t.clearable,l=t.placeholder,c=t.width,r=e["start-placeholder"]?'start-placeholder="'.concat(e["start-placeholder"],'"'):"",s=e["end-placeholder"]?'end-placeholder="'.concat(e["end-placeholder"],'"'):"",u=e["range-separator"]?'range-separator="'.concat(e["range-separator"],'"'):"",d=e.format?'format="'.concat(e.format,'"'):"",p=e["value-format"]?'value-format="'.concat(e["value-format"],'"'):"",_="date"===e.type?"":'type="'.concat(e.type,'"'),f=e.readonly?"readonly":"";return"<".concat(a," ").concat(_," ").concat(n," ").concat(d," ").concat(p," ").concat(c," ").concat(l," ").concat(r," ").concat(s," ").concat(u," ").concat(i," ").concat(f," ").concat(o,"></").concat(a,">")},"el-rate":function(e){var t=j(e),a=t.tag,o=t.disabled,n=t.vModel,i=e.max?":max='".concat(e.max,"'"):"",l=e["allow-half"]?"allow-half":"",c=e["show-text"]?"show-text":"",r=e["show-score"]?"show-score":"";return"<".concat(a," ").concat(n," ").concat(i," ").concat(l," ").concat(c," ").concat(r," ").concat(o,"></").concat(a,">")},"el-color-picker":function(e){var t=j(e),a=t.tag,o=t.disabled,n=t.vModel,i='size="'.concat(e.size,'"'),l=e["show-alpha"]?"show-alpha":"",c=e["color-format"]?'color-format="'.concat(e["color-format"],'"'):"";return"<".concat(a," ").concat(n," ").concat(i," ").concat(l," ").concat(c," ").concat(o,"></").concat(a,">")},"el-upload":function(e){var t=e.__config__.tag,a=e.disabled?":disabled='true'":"",o=e.action?':action="'.concat(e.__vModel__,'Action"'):"",n=e.multiple?"multiple":"",i="text"!==e["list-type"]?'list-type="'.concat(e["list-type"],'"'):"",l=e.accept?'accept="'.concat(e.accept,'"'):"",c="file"!==e.name?'name="'.concat(e.name,'"'):"",r=!1===e["auto-upload"]?':auto-upload="false"':"",s=':before-upload="'.concat(e.__vModel__,'BeforeUpload"'),u=':file-list="'.concat(e.__vModel__,'fileList"'),d='ref="'.concat(e.__vModel__,'"'),p={"Authori-zation":Object(b["a"])()},_=e.data?"data":"",f=E(e);return f&&(f="\n".concat(f,"\n")),"<".concat(t," ").concat(p," ").concat(_," ").concat(d," ").concat(u," ").concat(o," ").concat(r," ").concat(n," ").concat(s," ").concat(i," ").concat(l," ").concat(c," ").concat(a,">").concat(f,"</").concat(t,">")},"self-upload":function(e){var t=j(e),a=t.tag,o=t.vModel,n=e.height?':height="'.concat(e.height,'"'):"",i=e.multiple?"multiple":"",l=e.branding?':branding="'.concat(e.branding,'"'):"";return"<".concat(a," ").concat(o," ").concat(n," ").concat(l," ").concat(i,"></").concat(a,">")},"ueditor-from":function(e){var t=j(e),a=t.tag,o=t.vModel,n=e.height?':height="'.concat(e.height,'"'):"";return"<".concat(a," ").concat(o).concat(n," >")},"upload-file":function(e){var t=j(e),a=t.tag,o=t.vModel,n=e.height?':height="'.concat(e.height,'"'):"";return"<".concat(a," ").concat(o).concat(n," >")},"time-select":function(e){var t=j(e),a=t.tag,o=t.vModel,n=e.height?':height="'.concat(e.height,'"'):"";return"<".concat(a," ").concat(o).concat(n," >")},tinymce:function(e){var t=j(e),a=t.tag,o=t.vModel,n=e.branding?':branding="'.concat(e.branding,'"'):"";return"<".concat(a," ").concat(o," ").concat(n,"></").concat(a,">")}};function j(e){return{tag:e.__config__.tag,vModel:'v-model="'.concat(o.formModel,".").concat(e.__vModel__,'"'),clearable:e.clearable?"clearable":"",placeholder:e.placeholder?'placeholder="'.concat(e.placeholder,'"'):"",width:e.style&&e.style.width?":style=\"{width: '100%'}\"":"",disabled:e.disabled?":disabled='true'":""}}function $(e){var t=[],a=e.__slot__||{};return a.default&&t.push(a.default),t.join("\n")}function L(e){var t=[],a=e.__slot__;return a&&a.prepend&&t.push('<template slot="prepend">'.concat(a.prepend,"</template>")),a&&a.append&&t.push('<template slot="append">'.concat(a.append,"</template>")),t.join("\n")}function T(e){var t=[],a=e.__slot__;return a&&a.options&&a.options.length&&t.push('<el-option v-for="(item, index) in '.concat(e.__vModel__,'Options" :key="index" :label="item.label" :value="item.value" :disabled="item.disabled"></el-option>')),t.join("\n")}function S(e){var t=[],a=e.__slot__,o=e.__config__;if(a&&a.options&&a.options.length){var n="button"===o.optionType?"el-radio-button":"el-radio",i=o.border?"border":"";t.push("<".concat(n,' v-for="(item, index) in ').concat(e.__vModel__,'Options" :key="index" :label="item.value" :disabled="item.disabled" ').concat(i,">{{item.label}}</").concat(n,">"))}return t.join("\n")}function M(e){var t=[],a=e.__slot__,o=e.__config__;if(a&&a.options&&a.options.length){var n="button"===o.optionType?"el-checkbox-button":"el-checkbox",i=o.border?"border":"";t.push("<".concat(n,' v-for="(item, index) in ').concat(e.__vModel__,'Options" :key="index" :label="item.value" :disabled="item.disabled" ').concat(i,">{{item.label}}</").concat(n,">"))}return t.join("\n")}function E(e){var t=[],a=e.__config__;return"picture-card"===e["list-type"]?t.push('<i class="el-icon-plus"></i>'):t.push('<el-button size="small" type="primary" icon="el-icon-upload">'.concat(a.buttonText,"</el-button>")),a.showTip&&t.push('<div slot="tip" class="el-upload__tip">只能上传不超过 '.concat(a.fileSize).concat(a.sizeUnit," 的").concat(e.accept,"文件</div>")),t.join("\n")}function F(e,t){var a=[];o=e,n=e.fields.some((function(e){return 24!==e.__config__.span})),e.fields.forEach((function(e){a.push(O[e.__config__.layout](e))}));var i=a.join("\n"),l=k(e,i,t);return"dialog"===t&&(l=g(l)),o=null,l}var N=a("cd5b"),P={"el-rate":".el-rate{display: inline-block; vertical-align: text-top;}","el-upload":".el-upload__tip{line-height: 1.2;}"};function z(e,t){var a=P[t.__config__.tag];a&&-1===e.indexOf(a)&&e.push(a),t.__config__.children&&t.__config__.children.forEach((function(t){return z(e,t)}))}function V(e){var t=[];return e.fields.forEach((function(e){return z(t,e)})),t.join("\n")}var R,q,A=a("9255"),W=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-dialog",e._g(e._b({attrs:{title:"外部资源引用",width:"600px","close-on-click-modal":!1},on:{open:e.onOpen,close:e.onClose}},"el-dialog",e.$attrs,!1),e.$listeners),[e._l(e.resources,(function(t,o){return a("el-input",{key:o,staticClass:"url-item",attrs:{placeholder:"请输入 css 或 js 资源路径","prefix-icon":"el-icon-link",clearable:""},model:{value:e.resources[o],callback:function(t){e.$set(e.resources,o,t)},expression:"resources[index]"}},[a("el-button",{attrs:{slot:"append",icon:"el-icon-delete"},on:{click:function(t){return e.deleteOne(o)}},slot:"append"})],1)})),e._v(" "),a("el-button-group",{staticClass:"add-item"},[a("el-button",{attrs:{plain:""},on:{click:function(t){return e.addOne("https://cdn.bootcss.com/jquery/1.8.3/jquery.min.js")}}},[e._v("\n        jQuery1.8.3\n      ")]),e._v(" "),a("el-button",{attrs:{plain:""},on:{click:function(t){return e.addOne("https://unpkg.com/http-vue-loader")}}},[e._v("\n        http-vue-loader\n      ")]),e._v(" "),a("el-button",{attrs:{icon:"el-icon-circle-plus-outline",plain:""},on:{click:function(t){return e.addOne("")}}},[e._v("\n        添加其他\n      ")])],1),e._v(" "),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.close}},[e._v("\n        取消\n      ")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:e.handelConfirm}},[e._v("\n        确定\n      ")])],1)],2)],1)},B=[],J={components:{},inheritAttrs:!1,props:["originResource"],data:function(){return{resources:null}},computed:{},watch:{},created:function(){},mounted:function(){},methods:{onOpen:function(){this.resources=this.originResource.length?JSON.parse(JSON.stringify(this.originResource)):[""]},onClose:function(){},close:function(){this.$emit("update:visible",!1)},handelConfirm:function(){var e=this.resources.filter((function(e){return!!e}))||[];this.$emit("save",e),this.close(),e.length&&(this.resources=e)},deleteOne:function(e){this.resources.splice(e,1)},addOne:function(e){this.resources.indexOf(e)>-1?this.$message("资源已存在"):this.resources.push(e)}}},K=J,U=(a("bd64"),a("2877")),G=Object(U["a"])(K,W,B,!1,null,"5de361b9",null),H=G.exports,Q=a("7fde"),X=a("5c96"),Y=a.n(X);function Z(e){if(R)e(R);else{var t="https://cdn.bootcss.com/monaco-editor/0.18.0/min/vs",a=Y.a.Loading.service({fullscreen:!0,lock:!0,text:"编辑器资源初始化中...",spinner:"el-icon-loading",background:"rgba(255, 255, 255, 0.5)"});!window.require&&(window.require={}),!window.require.paths&&(window.require.paths={}),window.require.paths.vs=t,Object(Q["b"])(["".concat(t,"/loader.js"),"".concat(t,"/editor/editor.main.nls.js"),"".concat(t,"/editor/editor.main.js")],(function(){a.close(),R=monaco,e(R)}))}}function ee(e){if(q)e(q);else{var t=Y.a.Loading.service({fullscreen:!0,lock:!0,text:"格式化资源加载中...",spinner:"el-icon-loading",background:"rgba(255, 255, 255, 0.5)"});Object(Q["a"])("https://cdn.bootcss.com/js-beautify/1.10.2/beautifier.min.js",(function(){t.close(),q=beautifier,e(q)}))}}var te,ae,oe,ne,ie={html:null,js:null,css:null},le={html:"html",js:"javascript",css:"css"},ce={components:{ResourceDialog:H},props:["formData","generateConf"],data:function(){return{activeTab:"html",htmlCode:"",jsCode:"",cssCode:"",codeFrame:"",isIframeLoaded:!1,isInitcode:!1,isRefreshCode:!1,resourceVisible:!1,scripts:[],links:[],monaco:null}},computed:{resources:function(){return this.scripts.concat(this.links)}},watch:{},created:function(){},mounted:function(){var e=this;window.addEventListener("keydown",this.preventDefaultSave);var t=new p.a(".copy-btn",{text:function(t){var a=e.generateCode();return e.$notify({title:"成功",message:"代码已复制到剪切板，可粘贴。",type:"success"}),a}});t.on("error",(function(t){e.$message.error("代码复制失败")}))},beforeDestroy:function(){window.removeEventListener("keydown",this.preventDefaultSave)},methods:{preventDefaultSave:function(e){"s"===e.key&&(e.metaKey||e.ctrlKey)&&e.preventDefault()},onOpen:function(){var e=this,t=this.generateConf.type;this.htmlCode=F(this.formData,t),this.jsCode=Object(N["a"])(this.formData,t),this.cssCode=V(this.formData),ee((function(t){te=t,e.htmlCode=te.html(e.htmlCode,A["a"].html),e.jsCode=te.js(e.jsCode,A["a"].js),e.cssCode=te.css(e.cssCode,A["a"].html),Z((function(t){ae=t,e.setEditorValue("editorHtml","html",e.htmlCode),e.setEditorValue("editorJs","js",e.jsCode),e.setEditorValue("editorCss","css",e.cssCode),e.isInitcode||(e.isRefreshCode=!0,e.isIframeLoaded&&(e.isInitcode=!0)&&e.runCode())}))}))},onClose:function(){this.isInitcode=!1,this.isRefreshCode=!1,this.isIframeLoaded=!1},iframeLoad:function(){this.isInitcode||(this.isIframeLoaded=!0,this.isRefreshCode&&(this.isInitcode=!0)&&this.runCode())},setEditorValue:function(e,t,a){var o=this;ie[t]?ie[t].setValue(a):ie[t]=ae.editor.create(document.getElementById(e),{value:a,theme:"vs-dark",language:le[t],automaticLayout:!0}),ie[t].onKeyDown((function(e){49===e.keyCode&&(e.metaKey||e.ctrlKey)&&o.runCode()}))},runCode:function(){var e=ie.js.getValue();try{var t=Object(v["parse"])(e,{sourceType:"module"}),a=t.program.body;if(a.length>1)return void this.$confirm("js格式不能识别，仅支持修改export default的对象内容","提示",{type:"warning"});if("ExportDefaultDeclaration"===a[0].type){var o={type:"refreshFrame",data:{generateConf:this.generateConf,html:ie.html.getValue(),js:e.replace(A["b"],""),css:ie.css.getValue(),scripts:this.scripts,links:this.links}};this.$refs.previewPage.contentWindow.postMessage(o,location.origin)}else this.$message.error("请使用export default")}catch(n){this.$message.error("js错误：".concat(n))}},generateCode:function(){var e=y(ie.html.getValue()),t=D(ie.js.getValue()),a=w(ie.css.getValue());return te.html(e+t+a,A["a"].html)},exportFile:function(){var e=this;this.$prompt("文件名:","导出文件",{inputValue:"".concat(+new Date,".vue"),closeOnClickModal:!1,inputPlaceholder:"请输入文件名"}).then((function(t){var a=t.value;a||(a="".concat(+new Date,".vue"));var o=e.generateCode(),n=new Blob([o],{type:"text/plain;charset=utf-8"});Object(u["saveAs"])(n,a)}))},showResource:function(){this.resourceVisible=!0},setResource:function(e){var t=[],a=[];Array.isArray(e)?(e.forEach((function(e){e.endsWith(".css")?a.push(e):t.push(e)})),this.scripts=t,this.links=a):(this.scripts=[],this.links=[])}}},re=ce,se=(a("cb74"),Object(U["a"])(re,f,m,!1,null,"476cf91d",null)),ue=se.exports,de=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-drawer",e._g(e._b({attrs:{"append-to-body":""},on:{opened:e.onOpen,close:e.onClose}},"el-drawer",e.$attrs,!1),e.$listeners),[a("div",{staticClass:"action-bar",style:{"text-align":"left"}},[a("span",{staticClass:"bar-btn",on:{click:e.refresh}},[a("i",{staticClass:"el-icon-refresh"}),e._v("\n        刷新\n      ")]),e._v(" "),a("span",{ref:"copyBtn",staticClass:"bar-btn copy-json-btn"},[a("i",{staticClass:"el-icon-document-copy"}),e._v("\n        复制JSON\n      ")]),e._v(" "),a("span",{staticClass:"bar-btn",on:{click:e.exportJsonFile}},[a("i",{staticClass:"el-icon-download"}),e._v("\n        导出JSON文件\n      ")]),e._v(" "),a("span",{staticClass:"bar-btn delete-btn",on:{click:function(t){return e.$emit("update:visible",!1)}}},[a("i",{staticClass:"el-icon-circle-close"}),e._v("\n        关闭\n      ")])]),e._v(" "),a("div",{staticClass:"json-editor",attrs:{id:"editorJson"}})])],1)},pe=[],_e={components:{},props:{jsonStr:{type:String,required:!0,beautifier:null,jsonEditor:null}},data:function(){return{}},computed:{},watch:{},created:function(){},mounted:function(){var e=this;window.addEventListener("keydown",this.preventDefaultSave);var t=new p.a(".copy-json-btn",{text:function(t){return e.$notify({title:"成功",message:"代码已复制到剪切板，可粘贴。",type:"success"}),e.beautifierJson}});t.on("error",(function(t){e.$message.error("代码复制失败")}))},beforeDestroy:function(){window.removeEventListener("keydown",this.preventDefaultSave)},methods:{preventDefaultSave:function(e){"s"===e.key&&(e.metaKey||e.ctrlKey)&&e.preventDefault()},onOpen:function(){var e=this;ee((function(t){oe=t,e.beautifierJson=oe.js(e.jsonStr,A["a"].js),Z((function(t){ne=t,e.setEditorValue("editorJson",e.beautifierJson)}))}))},onClose:function(){},setEditorValue:function(e,t){var a=this;this.jsonEditor?this.jsonEditor.setValue(t):(this.jsonEditor=ne.editor.create(document.getElementById(e),{value:t,theme:"vs-dark",language:"json",automaticLayout:!0}),this.jsonEditor.onKeyDown((function(e){49===e.keyCode&&(e.metaKey||e.ctrlKey)&&a.refresh()})))},exportJsonFile:function(){var e=this;this.$prompt("文件名:","导出文件",{inputValue:"".concat(+new Date,".json"),closeOnClickModal:!1,inputPlaceholder:"请输入文件名"}).then((function(t){var a=t.value;a||(a="".concat(+new Date,".json"));var o=e.jsonEditor.getValue(),n=new Blob([o],{type:"text/plain;charset=utf-8"});Object(u["saveAs"])(n,a)}))},refresh:function(){try{this.$emit("refresh",JSON.parse(this.jsonEditor.getValue()))}catch(e){this.$notify({title:"错误",message:"JSON格式错误，请检查",type:"error"})}}}},fe=_e,me=(a("0d66"),Object(U["a"])(fe,de,pe,!1,null,"4c776bef",null)),ve=me.exports,he=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"right-board"},[a("el-tabs",{staticClass:"center-tabs",model:{value:e.currentTab,callback:function(t){e.currentTab=t},expression:"currentTab"}},[a("el-tab-pane",{attrs:{label:"组件属性",name:"field"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"表单属性",name:"form"}})],1),e._v(" "),a("div",{staticClass:"field-box"},[a("el-scrollbar",{staticClass:"right-scrollbar"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:"field"===e.currentTab&&e.showField,expression:"currentTab==='field' && showField"}],attrs:{size:"small","label-width":"90px"}},[e.activeData.__config__.changeTag?a("el-form-item",{attrs:{label:"组件类型"}},[a("el-select",{style:{width:"100%"},attrs:{placeholder:"请选择组件类型"},on:{change:e.tagChange},model:{value:e.activeData.__config__.tagIcon,callback:function(t){e.$set(e.activeData.__config__,"tagIcon",t)},expression:"activeData.__config__.tagIcon"}},e._l(e.tagList,(function(t){return a("el-option-group",{key:t.label,attrs:{label:t.label}},e._l(t.options,(function(t){return a("el-option",{key:t.__config__.label,attrs:{label:t.__config__.label,value:t.__config__.tagIcon}},[a("svg-icon",{staticClass:"node-icon",attrs:{"icon-class":t.__config__.tagIcon}}),e._v(" "),a("span",[e._v(" "+e._s(t.__config__.label))])],1)})),1)})),1)],1):e._e(),e._v(" "),void 0!==e.activeData.__vModel__?a("el-form-item",{attrs:{label:"字段名"}},[a("el-input",{attrs:{placeholder:"请输入字段名（v-model）"},model:{value:e.activeData.__vModel__,callback:function(t){e.$set(e.activeData,"__vModel__",t)},expression:"activeData.__vModel__"}})],1):e._e(),e._v(" "),void 0!==e.activeData.__config__.componentName?a("el-form-item",{attrs:{label:"组件名"}},[e._v("\n          "+e._s(e.activeData.__config__.componentName)+"\n        ")]):e._e(),e._v(" "),void 0!==e.activeData.__config__.label?a("el-form-item",{attrs:{label:"标题"}},[a("el-input",{attrs:{placeholder:"请输入标题"},model:{value:e.activeData.__config__.label,callback:function(t){e.$set(e.activeData.__config__,"label",t)},expression:"activeData.__config__.label"}})],1):e._e(),e._v(" "),void 0!==e.activeData.placeholder?a("el-form-item",{attrs:{label:"占位提示"}},[a("el-input",{attrs:{placeholder:"请输入占位提示"},model:{value:e.activeData.placeholder,callback:function(t){e.$set(e.activeData,"placeholder",t)},expression:"activeData.placeholder"}})],1):e._e(),e._v(" "),void 0!==e.activeData["start-placeholder"]?a("el-form-item",{attrs:{label:"开始占位"}},[a("el-input",{attrs:{placeholder:"请输入占位提示"},model:{value:e.activeData["start-placeholder"],callback:function(t){e.$set(e.activeData,"start-placeholder",t)},expression:"activeData['start-placeholder']"}})],1):e._e(),e._v(" "),void 0!==e.activeData["end-placeholder"]?a("el-form-item",{attrs:{label:"结束占位"}},[a("el-input",{attrs:{placeholder:"请输入占位提示"},model:{value:e.activeData["end-placeholder"],callback:function(t){e.$set(e.activeData,"end-placeholder",t)},expression:"activeData['end-placeholder']"}})],1):e._e(),e._v(" "),void 0!==e.activeData.__config__.span?a("el-form-item",{attrs:{label:"表单栅格"}},[a("el-slider",{attrs:{max:24,min:1,marks:{12:""}},on:{change:e.spanChange},model:{value:e.activeData.__config__.span,callback:function(t){e.$set(e.activeData.__config__,"span",t)},expression:"activeData.__config__.span"}})],1):e._e(),e._v(" "),"rowFormItem"===e.activeData.__config__.layout?a("el-form-item",{attrs:{label:"栅格间隔"}},[a("el-input-number",{attrs:{min:0,placeholder:"栅格间隔"},model:{value:e.activeData.gutter,callback:function(t){e.$set(e.activeData,"gutter",t)},expression:"activeData.gutter"}})],1):e._e(),e._v(" "),"rowFormItem"===e.activeData.__config__.layout?a("el-form-item",{attrs:{label:"布局模式"}},[a("el-radio-group",{model:{value:e.activeData.type,callback:function(t){e.$set(e.activeData,"type",t)},expression:"activeData.type"}},[a("el-radio-button",{attrs:{label:"default"}}),e._v(" "),a("el-radio-button",{attrs:{label:"flex"}})],1)],1):e._e(),e._v(" "),void 0!==e.activeData.justify&&"flex"===e.activeData.type?a("el-form-item",{attrs:{label:"水平排列"}},[a("el-select",{style:{width:"100%"},attrs:{placeholder:"请选择水平排列"},model:{value:e.activeData.justify,callback:function(t){e.$set(e.activeData,"justify",t)},expression:"activeData.justify"}},e._l(e.justifyOptions,(function(e,t){return a("el-option",{key:t,attrs:{label:e.label,value:e.value}})})),1)],1):e._e(),e._v(" "),void 0!==e.activeData.align&&"flex"===e.activeData.type?a("el-form-item",{attrs:{label:"垂直排列"}},[a("el-radio-group",{model:{value:e.activeData.align,callback:function(t){e.$set(e.activeData,"align",t)},expression:"activeData.align"}},[a("el-radio-button",{attrs:{label:"top"}}),e._v(" "),a("el-radio-button",{attrs:{label:"middle"}}),e._v(" "),a("el-radio-button",{attrs:{label:"bottom"}})],1)],1):e._e(),e._v(" "),void 0!==e.activeData.__config__.labelWidth?a("el-form-item",{attrs:{label:"标签宽度"}},[a("el-input",{attrs:{type:"number",placeholder:"请输入标签宽度"},model:{value:e.activeData.__config__.labelWidth,callback:function(t){e.$set(e.activeData.__config__,"labelWidth",e._n(t))},expression:"activeData.__config__.labelWidth"}})],1):e._e(),e._v(" "),e.activeData.style&&void 0!==e.activeData.style.width?a("el-form-item",{attrs:{label:"组件宽度"}},[a("el-input",{attrs:{placeholder:"请输入组件宽度",clearable:""},model:{value:e.activeData.style.width,callback:function(t){e.$set(e.activeData.style,"width",t)},expression:"activeData.style.width"}})],1):e._e(),e._v(" "),void 0!==e.activeData.__vModel__?a("el-form-item",{attrs:{label:"默认值"}},[a("el-input",{attrs:{value:e.setDefaultValue(e.activeData.__config__.defaultValue),placeholder:"请输入默认值"},on:{input:e.onDefaultValueInput}})],1):e._e(),e._v(" "),"el-checkbox-group"===e.activeData.__config__.tag?a("el-form-item",{attrs:{label:"至少应选"}},[a("el-input-number",{attrs:{value:e.activeData.min,min:0,placeholder:"至少应选"},on:{input:function(t){return e.$set(e.activeData,"min",t||void 0)}}})],1):e._e(),e._v(" "),"el-checkbox-group"===e.activeData.__config__.tag?a("el-form-item",{attrs:{label:"最多可选"}},[a("el-input-number",{attrs:{value:e.activeData.max,min:0,placeholder:"最多可选"},on:{input:function(t){return e.$set(e.activeData,"max",t||void 0)}}})],1):e._e(),e._v(" "),e.activeData.__slot__&&void 0!==e.activeData.__slot__.prepend?a("el-form-item",{attrs:{label:"前缀"}},[a("el-input",{attrs:{placeholder:"请输入前缀"},model:{value:e.activeData.__slot__.prepend,callback:function(t){e.$set(e.activeData.__slot__,"prepend",t)},expression:"activeData.__slot__.prepend"}})],1):e._e(),e._v(" "),e.activeData.__slot__&&void 0!==e.activeData.__slot__.append?a("el-form-item",{attrs:{label:"后缀"}},[a("el-input",{attrs:{placeholder:"请输入后缀"},model:{value:e.activeData.__slot__.append,callback:function(t){e.$set(e.activeData.__slot__,"append",t)},expression:"activeData.__slot__.append"}})],1):e._e(),e._v(" "),void 0!==e.activeData["prefix-icon"]?a("el-form-item",{attrs:{label:"前图标"}},[a("el-input",{attrs:{placeholder:"请输入前图标名称"},model:{value:e.activeData["prefix-icon"],callback:function(t){e.$set(e.activeData,"prefix-icon",t)},expression:"activeData['prefix-icon']"}},[a("el-button",{attrs:{slot:"append",icon:"el-icon-thumb"},on:{click:function(t){return e.openIconsDialog("prefix-icon")}},slot:"append"},[e._v("\n              选择\n            ")])],1)],1):e._e(),e._v(" "),void 0!==e.activeData["suffix-icon"]?a("el-form-item",{attrs:{label:"后图标"}},[a("el-input",{attrs:{placeholder:"请输入后图标名称"},model:{value:e.activeData["suffix-icon"],callback:function(t){e.$set(e.activeData,"suffix-icon",t)},expression:"activeData['suffix-icon']"}},[a("el-button",{attrs:{slot:"append",icon:"el-icon-thumb"},on:{click:function(t){return e.openIconsDialog("suffix-icon")}},slot:"append"},[e._v("\n              选择\n            ")])],1)],1):e._e(),e._v(" "),void 0!==e.activeData["icon"]&&"el-button"===e.activeData.__config__.tag?a("el-form-item",{attrs:{label:"按钮图标"}},[a("el-input",{attrs:{placeholder:"请输入按钮图标名称"},model:{value:e.activeData["icon"],callback:function(t){e.$set(e.activeData,"icon",t)},expression:"activeData['icon']"}},[a("el-button",{attrs:{slot:"append",icon:"el-icon-thumb"},on:{click:function(t){return e.openIconsDialog("icon")}},slot:"append"},[e._v("\n              选择\n            ")])],1)],1):e._e(),e._v(" "),"el-cascader"===e.activeData.__config__.tag?a("el-form-item",{attrs:{label:"选项分隔符"}},[a("el-input",{attrs:{placeholder:"请输入选项分隔符"},model:{value:e.activeData.separator,callback:function(t){e.$set(e.activeData,"separator",t)},expression:"activeData.separator"}})],1):e._e(),e._v(" "),void 0!==e.activeData.autosize?a("el-form-item",{attrs:{label:"最小行数"}},[a("el-input-number",{attrs:{min:1,placeholder:"最小行数"},model:{value:e.activeData.autosize.minRows,callback:function(t){e.$set(e.activeData.autosize,"minRows",t)},expression:"activeData.autosize.minRows"}})],1):e._e(),e._v(" "),void 0!==e.activeData.autosize?a("el-form-item",{attrs:{label:"最大行数"}},[a("el-input-number",{attrs:{min:1,placeholder:"最大行数"},model:{value:e.activeData.autosize.maxRows,callback:function(t){e.$set(e.activeData.autosize,"maxRows",t)},expression:"activeData.autosize.maxRows"}})],1):e._e(),e._v(" "),e.isShowMin?a("el-form-item",{attrs:{label:"最小值"}},[a("el-input-number",{attrs:{placeholder:"最小值"},model:{value:e.activeData.min,callback:function(t){e.$set(e.activeData,"min",t)},expression:"activeData.min"}})],1):e._e(),e._v(" "),e.isShowMax?a("el-form-item",{attrs:{label:"最大值"}},[a("el-input-number",{attrs:{placeholder:"最大值"},model:{value:e.activeData.max,callback:function(t){e.$set(e.activeData,"max",t)},expression:"activeData.max"}})],1):e._e(),e._v(" "),void 0!==e.activeData.height?a("el-form-item",{attrs:{label:"组件高度"}},[a("el-input-number",{attrs:{placeholder:"高度"},on:{input:e.changeRenderKey},model:{value:e.activeData.height,callback:function(t){e.$set(e.activeData,"height",t)},expression:"activeData.height"}})],1):e._e(),e._v(" "),e.isShowStep?a("el-form-item",{attrs:{label:"步长"}},[a("el-input-number",{attrs:{placeholder:"步数"},model:{value:e.activeData.step,callback:function(t){e.$set(e.activeData,"step",t)},expression:"activeData.step"}})],1):e._e(),e._v(" "),"el-input-number"===e.activeData.__config__.tag?a("el-form-item",{attrs:{label:"精度"}},[a("el-input-number",{attrs:{min:0,placeholder:"精度"},model:{value:e.activeData.precision,callback:function(t){e.$set(e.activeData,"precision",t)},expression:"activeData.precision"}})],1):e._e(),e._v(" "),"el-input-number"===e.activeData.__config__.tag?a("el-form-item",{attrs:{label:"按钮位置"}},[a("el-radio-group",{model:{value:e.activeData["controls-position"],callback:function(t){e.$set(e.activeData,"controls-position",t)},expression:"activeData['controls-position']"}},[a("el-radio-button",{attrs:{label:""}},[e._v("\n              默认\n            ")]),e._v(" "),a("el-radio-button",{attrs:{label:"right"}},[e._v("\n              右侧\n            ")])],1)],1):e._e(),e._v(" "),void 0!==e.activeData.maxlength?a("el-form-item",{attrs:{label:"最多输入"}},[a("el-input",{attrs:{placeholder:"请输入字符长度"},model:{value:e.activeData.maxlength,callback:function(t){e.$set(e.activeData,"maxlength",t)},expression:"activeData.maxlength"}},[a("template",{slot:"append"},[e._v("\n              个字符\n            ")])],2)],1):e._e(),e._v(" "),void 0!==e.activeData["active-text"]?a("el-form-item",{attrs:{label:"开启提示"}},[a("el-input",{attrs:{placeholder:"请输入开启提示"},model:{value:e.activeData["active-text"],callback:function(t){e.$set(e.activeData,"active-text",t)},expression:"activeData['active-text']"}})],1):e._e(),e._v(" "),void 0!==e.activeData["inactive-text"]?a("el-form-item",{attrs:{label:"关闭提示"}},[a("el-input",{attrs:{placeholder:"请输入关闭提示"},model:{value:e.activeData["inactive-text"],callback:function(t){e.$set(e.activeData,"inactive-text",t)},expression:"activeData['inactive-text']"}})],1):e._e(),e._v(" "),void 0!==e.activeData["active-value"]?a("el-form-item",{attrs:{label:"开启值"}},[a("el-input",{attrs:{value:e.setDefaultValue(e.activeData["active-value"]),placeholder:"请输入开启值"},on:{input:function(t){return e.onSwitchValueInput(t,"active-value")}}})],1):e._e(),e._v(" "),void 0!==e.activeData["inactive-value"]?a("el-form-item",{attrs:{label:"关闭值"}},[a("el-input",{attrs:{value:e.setDefaultValue(e.activeData["inactive-value"]),placeholder:"请输入关闭值"},on:{input:function(t){return e.onSwitchValueInput(t,"inactive-value")}}})],1):e._e(),e._v(" "),void 0!==e.activeData.type&&"el-date-picker"===e.activeData.__config__.tag?a("el-form-item",{attrs:{label:"时间类型"}},[a("el-select",{style:{width:"100%"},attrs:{placeholder:"请选择时间类型"},on:{change:e.dateTypeChange},model:{value:e.activeData.type,callback:function(t){e.$set(e.activeData,"type",t)},expression:"activeData.type"}},e._l(e.dateOptions,(function(e,t){return a("el-option",{key:t,attrs:{label:e.label,value:e.value}})})),1)],1):e._e(),e._v(" "),void 0!==e.activeData.name?a("el-form-item",{attrs:{label:"文件字段名"}},[a("el-input",{attrs:{placeholder:"请输入上传文件字段名"},model:{value:e.activeData.name,callback:function(t){e.$set(e.activeData,"name",t)},expression:"activeData.name"}})],1):e._e(),e._v(" "),"image"===e.activeData.accept?a("el-form-item",{attrs:{label:"文件类型"}},[a("span",[e._v("图片")])]):e._e(),e._v(" "),void 0!==e.activeData.accept&&"image"!==e.activeData.accept?a("el-form-item",{attrs:{label:"文件类型"}},[a("el-select",{style:{width:"100%"},attrs:{placeholder:"请选择文件类型",clearable:""},model:{value:e.activeData.accept,callback:function(t){e.$set(e.activeData,"accept",t)},expression:"activeData.accept"}},[a("el-option",{attrs:{label:"视频",value:"video/*"}}),e._v(" "),a("el-option",{attrs:{label:"音频",value:"audio/*"}}),e._v(" "),a("el-option",{attrs:{label:"excel",value:".xls,.xlsx"}}),e._v(" "),a("el-option",{attrs:{label:"word",value:".doc,.docx"}}),e._v(" "),a("el-option",{attrs:{label:"pdf",value:".pdf"}}),e._v(" "),a("el-option",{attrs:{label:"txt",value:".txt"}})],1)],1):e._e(),e._v(" "),void 0!==e.activeData.__config__.fileSize?a("el-form-item",{attrs:{label:"文件大小"}},[a("el-input",{attrs:{placeholder:"请输入文件大小"},model:{value:e.activeData.__config__.fileSize,callback:function(t){e.$set(e.activeData.__config__,"fileSize",e._n(t))},expression:"activeData.__config__.fileSize"}},[a("el-select",{style:{width:"66px"},attrs:{slot:"append"},slot:"append",model:{value:e.activeData.__config__.sizeUnit,callback:function(t){e.$set(e.activeData.__config__,"sizeUnit",t)},expression:"activeData.__config__.sizeUnit"}},[a("el-option",{attrs:{label:"KB",value:"KB"}}),e._v(" "),a("el-option",{attrs:{label:"MB",value:"MB"}}),e._v(" "),a("el-option",{attrs:{label:"GB",value:"GB"}})],1)],1)],1):e._e(),e._v(" "),void 0!==e.activeData.action?a("el-form-item",{attrs:{label:"上传地址"}},[a("el-input",{attrs:{placeholder:"请输入上传地址",clearable:""},model:{value:e.activeData.action,callback:function(t){e.$set(e.activeData,"action",t)},expression:"activeData.action"}})],1):e._e(),e._v(" "),void 0!==e.activeData["list-type"]?a("el-form-item",{attrs:{label:"列表类型"}},[a("el-radio-group",{attrs:{size:"small"},model:{value:e.activeData["list-type"],callback:function(t){e.$set(e.activeData,"list-type",t)},expression:"activeData['list-type']"}},[a("el-radio-button",{attrs:{label:"text"}},[e._v("\n              text\n            ")]),e._v(" "),a("el-radio-button",{attrs:{label:"picture"}},[e._v("\n              picture\n            ")]),e._v(" "),a("el-radio-button",{attrs:{label:"picture-card"}},[e._v("\n              picture-card\n            ")])],1)],1):e._e(),e._v(" "),void 0!==e.activeData.type&&"el-button"===e.activeData.__config__.tag?a("el-form-item",{attrs:{label:"按钮类型"}},[a("el-select",{style:{width:"100%"},model:{value:e.activeData.type,callback:function(t){e.$set(e.activeData,"type",t)},expression:"activeData.type"}},[a("el-option",{attrs:{label:"primary",value:"primary"}}),e._v(" "),a("el-option",{attrs:{label:"success",value:"success"}}),e._v(" "),a("el-option",{attrs:{label:"warning",value:"warning"}}),e._v(" "),a("el-option",{attrs:{label:"danger",value:"danger"}}),e._v(" "),a("el-option",{attrs:{label:"info",value:"info"}}),e._v(" "),a("el-option",{attrs:{label:"text",value:"text"}})],1)],1):e._e(),e._v(" "),void 0!==e.activeData.__config__.buttonText?a("el-form-item",{directives:[{name:"show",rawName:"v-show",value:"picture-card"!==e.activeData["list-type"],expression:"'picture-card' !== activeData['list-type']"}],attrs:{label:"按钮文字"}},[a("el-input",{attrs:{placeholder:"请输入按钮文字"},model:{value:e.activeData.__config__.buttonText,callback:function(t){e.$set(e.activeData.__config__,"buttonText",t)},expression:"activeData.__config__.buttonText"}})],1):e._e(),e._v(" "),void 0!==e.activeData["range-separator"]?a("el-form-item",{attrs:{label:"分隔符"}},[a("el-input",{attrs:{placeholder:"请输入分隔符"},model:{value:e.activeData["range-separator"],callback:function(t){e.$set(e.activeData,"range-separator",t)},expression:"activeData['range-separator']"}})],1):e._e(),e._v(" "),void 0!==e.activeData["picker-options"]?a("el-form-item",{attrs:{label:"时间段"}},[a("el-input",{attrs:{placeholder:"请输入时间段"},model:{value:e.activeData["picker-options"].selectableRange,callback:function(t){e.$set(e.activeData["picker-options"],"selectableRange",t)},expression:"activeData['picker-options'].selectableRange"}})],1):e._e(),e._v(" "),void 0!==e.activeData.format?a("el-form-item",{attrs:{label:"时间格式"}},[a("el-input",{attrs:{value:e.activeData.format,placeholder:"请输入时间格式"},on:{input:function(t){return e.setTimeValue(t)}}})],1):e._e(),e._v(" "),["el-checkbox-group","el-radio-group","el-select"].indexOf(e.activeData.__config__.tag)>-1?[a("el-divider",[e._v("选项")]),e._v(" "),a("draggable",{attrs:{list:e.activeData.__slot__.options,animation:340,group:"selectItem",handle:".option-drag"}},e._l(e.activeData.__slot__.options,(function(t,o){return a("div",{key:o,staticClass:"select-item"},[a("div",{staticClass:"select-line-icon option-drag"},[a("i",{staticClass:"el-icon-s-operation"})]),e._v(" "),a("el-input",{attrs:{placeholder:"选项名",size:"small"},model:{value:t.label,callback:function(a){e.$set(t,"label",a)},expression:"item.label"}}),e._v(" "),a("el-input",{attrs:{placeholder:"选项值",size:"small",value:t.value},on:{input:function(a){return e.setOptionValue(t,a)}}}),e._v(" "),a("div",{staticClass:"close-btn select-line-icon",on:{click:function(t){return e.activeData.__slot__.options.splice(o,1)}}},[a("i",{staticClass:"el-icon-remove-outline"})])],1)})),0),e._v(" "),a("div",{staticStyle:{"margin-left":"20px"}},[a("el-button",{staticStyle:{"padding-bottom":"0"},attrs:{icon:"el-icon-circle-plus-outline",type:"text"},on:{click:e.addSelectItem}},[e._v("\n              添加选项\n            ")])],1),e._v(" "),a("el-divider")]:e._e(),e._v(" "),["el-cascader"].indexOf(e.activeData.__config__.tag)>-1?[a("el-divider",[e._v("选项")]),e._v(" "),a("el-form-item",{attrs:{label:"数据类型"}},[a("el-radio-group",{attrs:{size:"small"},model:{value:e.activeData.__config__.dataType,callback:function(t){e.$set(e.activeData.__config__,"dataType",t)},expression:"activeData.__config__.dataType"}},[a("el-radio-button",{attrs:{label:"dynamic"}},[e._v("\n                动态数据\n              ")]),e._v(" "),a("el-radio-button",{attrs:{label:"static"}},[e._v("\n                静态数据\n              ")])],1)],1),e._v(" "),"dynamic"===e.activeData.__config__.dataType?[a("el-form-item",{attrs:{label:"标签键名"}},[a("el-input",{attrs:{placeholder:"请输入标签键名"},model:{value:e.activeData.props.props.label,callback:function(t){e.$set(e.activeData.props.props,"label",t)},expression:"activeData.props.props.label"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"值键名"}},[a("el-input",{attrs:{placeholder:"请输入值键名"},model:{value:e.activeData.props.props.value,callback:function(t){e.$set(e.activeData.props.props,"value",t)},expression:"activeData.props.props.value"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"子级键名"}},[a("el-input",{attrs:{placeholder:"请输入子级键名"},model:{value:e.activeData.props.props.children,callback:function(t){e.$set(e.activeData.props.props,"children",t)},expression:"activeData.props.props.children"}})],1)]:e._e(),e._v(" "),"static"===e.activeData.__config__.dataType?a("el-tree",{attrs:{draggable:"",data:e.activeData.options,"node-key":"id","expand-on-click-node":!1,"render-content":e.renderContent}}):e._e(),e._v(" "),"static"===e.activeData.__config__.dataType?a("div",{staticStyle:{"margin-left":"20px"}},[a("el-button",{staticStyle:{"padding-bottom":"0"},attrs:{icon:"el-icon-circle-plus-outline",type:"text"},on:{click:e.addTreeItem}},[e._v("\n              添加父级\n            ")])],1):e._e(),e._v(" "),a("el-divider")]:e._e(),e._v(" "),void 0!==e.activeData.__config__.optionType?a("el-form-item",{attrs:{label:"选项样式"}},[a("el-radio-group",{model:{value:e.activeData.__config__.optionType,callback:function(t){e.$set(e.activeData.__config__,"optionType",t)},expression:"activeData.__config__.optionType"}},[a("el-radio-button",{attrs:{label:"default"}},[e._v("\n              默认\n            ")]),e._v(" "),a("el-radio-button",{attrs:{label:"button"}},[e._v("\n              按钮\n            ")])],1)],1):e._e(),e._v(" "),void 0!==e.activeData["active-color"]?a("el-form-item",{attrs:{label:"开启颜色"}},[a("el-color-picker",{model:{value:e.activeData["active-color"],callback:function(t){e.$set(e.activeData,"active-color",t)},expression:"activeData['active-color']"}})],1):e._e(),e._v(" "),void 0!==e.activeData["inactive-color"]?a("el-form-item",{attrs:{label:"关闭颜色"}},[a("el-color-picker",{model:{value:e.activeData["inactive-color"],callback:function(t){e.$set(e.activeData,"inactive-color",t)},expression:"activeData['inactive-color']"}})],1):e._e(),e._v(" "),void 0!==e.activeData.__config__.showLabel&&void 0!==e.activeData.__config__.labelWidth?a("el-form-item",{attrs:{label:"显示标签"}},[a("el-switch",{model:{value:e.activeData.__config__.showLabel,callback:function(t){e.$set(e.activeData.__config__,"showLabel",t)},expression:"activeData.__config__.showLabel"}})],1):e._e(),e._v(" "),void 0!==e.activeData.branding?a("el-form-item",{attrs:{label:"品牌烙印"}},[a("el-switch",{on:{input:e.changeRenderKey},model:{value:e.activeData.branding,callback:function(t){e.$set(e.activeData,"branding",t)},expression:"activeData.branding"}})],1):e._e(),e._v(" "),void 0!==e.activeData["allow-half"]?a("el-form-item",{attrs:{label:"允许半选"}},[a("el-switch",{model:{value:e.activeData["allow-half"],callback:function(t){e.$set(e.activeData,"allow-half",t)},expression:"activeData['allow-half']"}})],1):e._e(),e._v(" "),void 0!==e.activeData["show-text"]?a("el-form-item",{attrs:{label:"辅助文字"}},[a("el-switch",{on:{change:e.rateTextChange},model:{value:e.activeData["show-text"],callback:function(t){e.$set(e.activeData,"show-text",t)},expression:"activeData['show-text']"}})],1):e._e(),e._v(" "),void 0!==e.activeData["show-score"]?a("el-form-item",{attrs:{label:"显示分数"}},[a("el-switch",{on:{change:e.rateScoreChange},model:{value:e.activeData["show-score"],callback:function(t){e.$set(e.activeData,"show-score",t)},expression:"activeData['show-score']"}})],1):e._e(),e._v(" "),void 0!==e.activeData["show-stops"]?a("el-form-item",{attrs:{label:"显示间断点"}},[a("el-switch",{model:{value:e.activeData["show-stops"],callback:function(t){e.$set(e.activeData,"show-stops",t)},expression:"activeData['show-stops']"}})],1):e._e(),e._v(" "),void 0!==e.activeData.range?a("el-form-item",{attrs:{label:"范围选择"}},[a("el-switch",{on:{change:e.rangeChange},model:{value:e.activeData.range,callback:function(t){e.$set(e.activeData,"range",t)},expression:"activeData.range"}})],1):e._e(),e._v(" "),void 0!==e.activeData.__config__.border&&"default"===e.activeData.__config__.optionType?a("el-form-item",{attrs:{label:"是否带边框"}},[a("el-switch",{model:{value:e.activeData.__config__.border,callback:function(t){e.$set(e.activeData.__config__,"border",t)},expression:"activeData.__config__.border"}})],1):e._e(),e._v(" "),"el-color-picker"===e.activeData.__config__.tag?a("el-form-item",{attrs:{label:"颜色格式"}},[a("el-select",{style:{width:"100%"},attrs:{placeholder:"请选择颜色格式",clearable:""},on:{change:e.colorFormatChange},model:{value:e.activeData["color-format"],callback:function(t){e.$set(e.activeData,"color-format",t)},expression:"activeData['color-format']"}},e._l(e.colorFormatOptions,(function(e,t){return a("el-option",{key:t,attrs:{label:e.label,value:e.value}})})),1)],1):e._e(),e._v(" "),void 0===e.activeData.size||"button"!==e.activeData.__config__.optionType&&!e.activeData.__config__.border&&"el-color-picker"!==e.activeData.__config__.tag&&"el-button"!==e.activeData.__config__.tag?e._e():a("el-form-item",{attrs:{label:"组件尺寸"}},[a("el-radio-group",{model:{value:e.activeData.size,callback:function(t){e.$set(e.activeData,"size",t)},expression:"activeData.size"}},[a("el-radio-button",{attrs:{label:"medium"}},[e._v("\n              中等\n            ")]),e._v(" "),a("el-radio-button",{attrs:{label:"small"}},[e._v("\n              较小\n            ")]),e._v(" "),a("el-radio-button",{attrs:{label:"mini"}},[e._v("\n              迷你\n            ")])],1)],1),e._v(" "),void 0!==e.activeData["show-word-limit"]?a("el-form-item",{attrs:{label:"输入统计"}},[a("el-switch",{model:{value:e.activeData["show-word-limit"],callback:function(t){e.$set(e.activeData,"show-word-limit",t)},expression:"activeData['show-word-limit']"}})],1):e._e(),e._v(" "),"el-input-number"===e.activeData.__config__.tag?a("el-form-item",{attrs:{label:"严格步数"}},[a("el-switch",{model:{value:e.activeData["step-strictly"],callback:function(t){e.$set(e.activeData,"step-strictly",t)},expression:"activeData['step-strictly']"}})],1):e._e(),e._v(" "),"el-cascader"===e.activeData.__config__.tag?a("el-form-item",{attrs:{label:"是否多选"}},[a("el-switch",{model:{value:e.activeData.props.props.multiple,callback:function(t){e.$set(e.activeData.props.props,"multiple",t)},expression:"activeData.props.props.multiple"}})],1):e._e(),e._v(" "),"el-cascader"===e.activeData.__config__.tag?a("el-form-item",{attrs:{label:"展示全路径"}},[a("el-switch",{model:{value:e.activeData["show-all-levels"],callback:function(t){e.$set(e.activeData,"show-all-levels",t)},expression:"activeData['show-all-levels']"}})],1):e._e(),e._v(" "),"el-cascader"===e.activeData.__config__.tag?a("el-form-item",{attrs:{label:"可否筛选"}},[a("el-switch",{model:{value:e.activeData.filterable,callback:function(t){e.$set(e.activeData,"filterable",t)},expression:"activeData.filterable"}})],1):e._e(),e._v(" "),void 0!==e.activeData.clearable?a("el-form-item",{attrs:{label:"能否清空"}},[a("el-switch",{model:{value:e.activeData.clearable,callback:function(t){e.$set(e.activeData,"clearable",t)},expression:"activeData.clearable"}})],1):e._e(),e._v(" "),void 0!==e.activeData.__config__.showTip?a("el-form-item",{attrs:{label:"显示提示"}},[a("el-switch",{model:{value:e.activeData.__config__.showTip,callback:function(t){e.$set(e.activeData.__config__,"showTip",t)},expression:"activeData.__config__.showTip"}})],1):e._e(),e._v(" "),"el-upload"===e.activeData.__config__.tag||"self-upload"===e.activeData.__config__.tag?a("el-form-item",{attrs:{label:"多选文件"}},[a("el-switch",{model:{value:e.activeData.multiple,callback:function(t){e.$set(e.activeData,"multiple",t)},expression:"activeData.multiple"}})],1):e._e(),e._v(" "),void 0!==e.activeData["auto-upload"]?a("el-form-item",{attrs:{label:"自动上传"}},[a("el-switch",{model:{value:e.activeData["auto-upload"],callback:function(t){e.$set(e.activeData,"auto-upload",t)},expression:"activeData['auto-upload']"}})],1):e._e(),e._v(" "),void 0!==e.activeData.readonly?a("el-form-item",{attrs:{label:"是否只读"}},[a("el-switch",{model:{value:e.activeData.readonly,callback:function(t){e.$set(e.activeData,"readonly",t)},expression:"activeData.readonly"}})],1):e._e(),e._v(" "),void 0!==e.activeData.disabled?a("el-form-item",{attrs:{label:"是否禁用"}},[a("el-switch",{model:{value:e.activeData.disabled,callback:function(t){e.$set(e.activeData,"disabled",t)},expression:"activeData.disabled"}})],1):e._e(),e._v(" "),"el-select"===e.activeData.__config__.tag?a("el-form-item",{attrs:{label:"能否搜索"}},[a("el-switch",{model:{value:e.activeData.filterable,callback:function(t){e.$set(e.activeData,"filterable",t)},expression:"activeData.filterable"}})],1):e._e(),e._v(" "),"el-select"===e.activeData.__config__.tag?a("el-form-item",{attrs:{label:"是否多选"}},[a("el-switch",{on:{change:e.multipleChange},model:{value:e.activeData.multiple,callback:function(t){e.$set(e.activeData,"multiple",t)},expression:"activeData.multiple"}})],1):e._e(),e._v(" "),void 0!==e.activeData.__config__.required?a("el-form-item",{attrs:{label:"是否必填"}},[a("el-switch",{model:{value:e.activeData.__config__.required,callback:function(t){e.$set(e.activeData.__config__,"required",t)},expression:"activeData.__config__.required"}})],1):e._e(),e._v(" "),void 0!==e.activeData.__config__.tips?a("el-form-item",{attrs:{label:"开启描述"}},[a("el-switch",{model:{value:e.activeData.__config__.tips,callback:function(t){e.$set(e.activeData.__config__,"tips",t)},expression:"activeData.__config__.tips"}})],1):e._e(),e._v(" "),e.activeData.__config__.tips?a("el-form-item",{attrs:{label:"描述内容"}},[a("el-input",{attrs:{placeholder:"请输入描述"},model:{value:e.activeData.__config__.tipsDesc,callback:function(t){e.$set(e.activeData.__config__,"tipsDesc",t)},expression:"activeData.__config__.tipsDesc"}})],1):e._e(),e._v(" "),e.activeData.__config__.tips?a("el-form-item",{attrs:{label:"描述链接"}},[a("el-switch",{model:{value:e.activeData.__config__.tipsIsLink,callback:function(t){e.$set(e.activeData.__config__,"tipsIsLink",t)},expression:"activeData.__config__.tipsIsLink"}})],1):e._e(),e._v(" "),e.activeData.__config__.tipsIsLink?a("el-form-item",{attrs:{label:"链接地址"}},[a("el-input",{attrs:{placeholder:"请输入链接地址"},model:{value:e.activeData.__config__.tipsLink,callback:function(t){e.$set(e.activeData.__config__,"tipsLink",t)},expression:"activeData.__config__.tipsLink"}})],1):e._e(),e._v(" "),e.activeData.__config__.layoutTree?[a("el-divider",[e._v("布局结构树")]),e._v(" "),a("el-tree",{attrs:{data:[e.activeData.__config__],props:e.layoutTreeProps,"node-key":"renderKey","default-expand-all":"",draggable:""},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.node,n=t.data;return a("span",{},[a("span",{staticClass:"node-label"},[a("svg-icon",{staticClass:"node-icon",attrs:{"icon-class":n.__config__?n.__config__.tagIcon:n.tagIcon}}),e._v("\n                "+e._s(o.label)+"\n              ")],1)])}}],null,!1,3398330875)})]:e._e(),e._v(" "),"colFormItem"===e.activeData.__config__.layout?[a("el-divider",[e._v("正则校验")]),e._v(" "),e._l(e.activeData.__config__.regList,(function(t,o){return a("div",{key:o,staticClass:"reg-item"},[a("span",{staticClass:"close-btn",on:{click:function(t){return e.activeData.__config__.regList.splice(o,1)}}},[a("i",{staticClass:"el-icon-close"})]),e._v(" "),a("el-form-item",{attrs:{label:"表达式"}},[a("el-input",{attrs:{placeholder:"请输入正则"},model:{value:t.pattern,callback:function(a){e.$set(t,"pattern",a)},expression:"item.pattern"}})],1),e._v(" "),a("el-form-item",{staticStyle:{"margin-bottom":"0"},attrs:{label:"错误提示"}},[a("el-input",{attrs:{placeholder:"请输入错误提示"},model:{value:t.message,callback:function(a){e.$set(t,"message",a)},expression:"item.message"}})],1)],1)})),e._v(" "),a("div",{staticStyle:{"margin-left":"20px"}},[a("el-button",{attrs:{icon:"el-icon-circle-plus-outline",type:"text"},on:{click:e.addReg}},[e._v("\n              添加规则\n            ")])],1)]:e._e()],2),e._v(" "),a("el-form",{directives:[{name:"show",rawName:"v-show",value:"form"===e.currentTab,expression:"currentTab === 'form'"}],attrs:{size:"small","label-width":"90px"}},[a("el-form-item",{attrs:{label:"表单名"}},[a("el-input",{attrs:{placeholder:"请输入表单名（ref）"},model:{value:e.formConf.formRef,callback:function(t){e.$set(e.formConf,"formRef",t)},expression:"formConf.formRef"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"表单模型"}},[a("el-input",{attrs:{placeholder:"请输入数据模型"},model:{value:e.formConf.formModel,callback:function(t){e.$set(e.formConf,"formModel",t)},expression:"formConf.formModel"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"校验模型"}},[a("el-input",{attrs:{placeholder:"请输入校验模型"},model:{value:e.formConf.formRules,callback:function(t){e.$set(e.formConf,"formRules",t)},expression:"formConf.formRules"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"表单尺寸"}},[a("el-radio-group",{model:{value:e.formConf.size,callback:function(t){e.$set(e.formConf,"size",t)},expression:"formConf.size"}},[a("el-radio-button",{attrs:{label:"medium"}},[e._v("\n              中等\n            ")]),e._v(" "),a("el-radio-button",{attrs:{label:"small"}},[e._v("\n              较小\n            ")]),e._v(" "),a("el-radio-button",{attrs:{label:"mini"}},[e._v("\n              迷你\n            ")])],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"标签对齐"}},[a("el-radio-group",{model:{value:e.formConf.labelPosition,callback:function(t){e.$set(e.formConf,"labelPosition",t)},expression:"formConf.labelPosition"}},[a("el-radio-button",{attrs:{label:"left"}},[e._v("\n              左对齐\n            ")]),e._v(" "),a("el-radio-button",{attrs:{label:"right"}},[e._v("\n              右对齐\n            ")]),e._v(" "),a("el-radio-button",{attrs:{label:"top"}},[e._v("\n              顶部对齐\n            ")])],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"标签宽度"}},[a("el-input",{attrs:{type:"number",placeholder:"请输入标签宽度"},model:{value:e.formConf.labelWidth,callback:function(t){e.$set(e.formConf,"labelWidth",e._n(t))},expression:"formConf.labelWidth"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"栅格间隔"}},[a("el-input-number",{attrs:{min:0,placeholder:"栅格间隔"},model:{value:e.formConf.gutter,callback:function(t){e.$set(e.formConf,"gutter",t)},expression:"formConf.gutter"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"禁用表单"}},[a("el-switch",{model:{value:e.formConf.disabled,callback:function(t){e.$set(e.formConf,"disabled",t)},expression:"formConf.disabled"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"表单按钮"}},[a("el-switch",{model:{value:e.formConf.formBtns,callback:function(t){e.$set(e.formConf,"formBtns",t)},expression:"formConf.formBtns"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"显示未选中组件边框"}},[a("el-switch",{model:{value:e.formConf.unFocusedComponentBorder,callback:function(t){e.$set(e.formConf,"unFocusedComponentBorder",t)},expression:"formConf.unFocusedComponentBorder"}})],1)],1)],1)],1),e._v(" "),a("treeNode-dialog",{attrs:{visible:e.dialogVisible,title:"添加选项"},on:{"update:visible":function(t){e.dialogVisible=t},commit:e.addNode}}),e._v(" "),a("icons-dialog",{attrs:{visible:e.iconsVisible,current:e.activeData[e.currentIconModel]},on:{"update:visible":function(t){e.iconsVisible=t},select:e.setIcon}})],1)},be=[],ge=a("3022"),ye=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-dialog",e._g(e._b({attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1},on:{open:e.onOpen,close:e.onClose}},"el-dialog",e.$attrs,!1),e.$listeners),[a("el-row",{attrs:{gutter:0}},[a("el-form",{ref:"elForm",attrs:{model:e.formData,rules:e.rules,size:"small","label-width":"100px"}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"选项名",prop:"label"}},[a("el-input",{attrs:{placeholder:"请输入选项名",clearable:""},model:{value:e.formData.label,callback:function(t){e.$set(e.formData,"label",t)},expression:"formData.label"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"选项值",prop:"value"}},[a("el-input",{attrs:{placeholder:"请输入选项值",clearable:""},model:{value:e.formData.value,callback:function(t){e.$set(e.formData,"value",t)},expression:"formData.value"}},[a("el-select",{style:{width:"100px"},attrs:{slot:"append"},slot:"append",model:{value:e.dataType,callback:function(t){e.dataType=t},expression:"dataType"}},e._l(e.dataTypeOptions,(function(e,t){return a("el-option",{key:t,attrs:{label:e.label,value:e.value,disabled:e.disabled}})})),1)],1)],1)],1)],1)],1),e._v(" "),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.handelConfirm}},[e._v("\n        确定\n      ")]),e._v(" "),a("el-button",{on:{click:e.close}},[e._v("\n        取消\n      ")])],1)],1)],1)},De=[],we="drawingItems",ke="1.1",xe="DRAWING_ITEMS_VERSION",Ce="idGlobal",Oe="treeNodeId",Ie="formConf";function je(){var e=localStorage.getItem(xe);if(e!==ke)return localStorage.setItem(xe,ke),$e([]),null;var t=localStorage.getItem(we);return t?JSON.parse(t):null}function $e(e){localStorage.setItem(we,JSON.stringify(e))}function Le(){var e=localStorage.getItem(Ce);return e?parseInt(e,10):100}function Te(e){localStorage.setItem(Ce,"".concat(e))}function Se(){var e=localStorage.getItem(Oe);return e?parseInt(e,10):100}function Me(e){localStorage.setItem(Oe,"".concat(e))}function Ee(){var e=localStorage.getItem(Ie);return e?JSON.parse(e):null}function Fe(e){localStorage.setItem(Ie,JSON.stringify(e))}function Ne(){var e=localStorage.getItem(Ie);e=JSON.parse(e);var t=localStorage.getItem(we);return e||t?(t=JSON.parse(t),e.fields=t,e):"Error"}var Pe=Se(),ze={components:{},inheritAttrs:!1,props:[],data:function(){return{id:Pe,formData:{label:void 0,value:void 0},rules:{label:[{required:!0,message:"请输入选项名",trigger:"blur"}],value:[{required:!0,message:"请输入选项值",trigger:"blur"}]},dataType:"string",dataTypeOptions:[{label:"字符串",value:"string"},{label:"数字",value:"number"}]}},computed:{},watch:{"formData.value":function(e){this.dataType=Object(A["c"])(e)?"number":"string"},id:function(e){Me(e)}},created:function(){},mounted:function(){},methods:{onOpen:function(){this.formData={label:void 0,value:void 0}},onClose:function(){},close:function(){this.$emit("update:visible",!1)},handelConfirm:function(){var e=this;this.$refs.elForm.validate((function(t){t&&("number"===e.dataType&&(e.formData.value=parseFloat(e.formData.value)),e.formData.id=e.id++,e.$emit("commit",e.formData),e.close())}))}}},Ve=ze,Re=Object(U["a"])(Ve,ye,De,!1,null,"0973d882",null),qe=Re.exports,Ae=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"icon-dialog"},[a("el-dialog",e._g(e._b({attrs:{width:"980px","modal-append-to-body":!1},on:{open:e.onOpen,close:e.onClose}},"el-dialog",e.$attrs,!1),e.$listeners),[a("div",{attrs:{slot:"title"},slot:"title"},[e._v("\n      选择图标\n      "),a("el-input",{style:{width:"260px"},attrs:{size:"mini",placeholder:"请输入图标名称","prefix-icon":"el-icon-search",clearable:""},model:{value:e.key,callback:function(t){e.key=t},expression:"key"}})],1),e._v(" "),a("ul",{staticClass:"icon-ul"},e._l(e.iconList,(function(t){return a("li",{key:t,class:e.active===t?"active-item":"",on:{click:function(a){return e.onSelect(t)}}},[a("i",{class:t}),e._v(" "),a("div",[e._v(e._s(t))])])})),0)])],1)},We=[],Be=a("4b3f"),Je=Be.map((function(e){return"el-icon-".concat(e)})),Ke={inheritAttrs:!1,props:["current"],data:function(){return{iconList:Je,active:null,key:""}},watch:{key:function(e){this.iconList=e?Je.filter((function(t){return t.indexOf(e)>-1})):Je}},methods:{onOpen:function(){this.active=this.current,this.key=""},onClose:function(){},onSelect:function(e){this.active=e,this.$emit("select",e),this.$emit("update:visible",!1)}}},Ue=Ke,Ge=(a("dc83"),Object(U["a"])(Ue,Ae,We,!1,null,"37212a44",null)),He=Ge.exports,Qe=(a("27c7"),{formRef:"elForm",formModel:"formData",size:"medium",labelPosition:"right",labelWidth:100,formRules:"rules",gutter:15,disabled:!1,span:24,formBtns:!0}),Xe=[{__config__:{label:"单行文本",labelWidth:null,showLabel:!0,changeTag:!0,tag:"el-input",tagIcon:"input",defaultValue:void 0,required:!0,tips:!1,tipsDesc:"",tipsIsLink:!1,tipsLink:"",layout:"colFormItem",span:24,document:"https://element.eleme.cn/#/zh-CN/component/input",regList:[]},__slot__:{prepend:"",append:""},placeholder:"请输入",style:{width:"95%"},clearable:!0,"prefix-icon":"","suffix-icon":"",maxlength:null,"show-word-limit":!1,readonly:!1,disabled:!1},{__config__:{label:"多行文本",labelWidth:null,showLabel:!0,tag:"el-input",tagIcon:"textarea",defaultValue:void 0,required:!0,tips:!1,tipsDesc:"",tipsIsLink:!1,tipsLink:"",layout:"colFormItem",span:24,regList:[],changeTag:!0,document:"https://element.eleme.cn/#/zh-CN/component/input"},type:"textarea",placeholder:"请输入",autosize:{minRows:4,maxRows:4},style:{width:"95%"},maxlength:null,"show-word-limit":!1,readonly:!1,disabled:!1},{__config__:{label:"密码",showLabel:!0,labelWidth:null,changeTag:!0,tag:"el-input",tagIcon:"password",defaultValue:void 0,layout:"colFormItem",span:24,required:!0,tips:!1,tipsDesc:"",tipsIsLink:!1,tipsLink:"",regList:[],document:"https://element.eleme.cn/#/zh-CN/component/input"},__slot__:{prepend:"",append:""},placeholder:"请输入","show-password":!0,style:{width:"100%"},clearable:!0,"prefix-icon":"","suffix-icon":"",maxlength:null,"show-word-limit":!1,readonly:!1,disabled:!1},{__config__:{label:"计数器",showLabel:!0,changeTag:!0,labelWidth:null,tag:"el-input-number",tagIcon:"number",defaultValue:void 0,span:24,layout:"colFormItem",required:!0,tips:!1,tipsDesc:"",tipsIsLink:!1,tipsLink:"",regList:[],document:"https://element.eleme.cn/#/zh-CN/component/input-number"},placeholder:"",min:void 0,max:void 0,step:1,"step-strictly":!1,precision:void 0,"controls-position":"",disabled:!1}],Ye=[{__config__:{label:"下拉选择",showLabel:!0,labelWidth:null,tag:"el-select",tagIcon:"select",defaultValue:void 0,layout:"colFormItem",span:24,required:!0,tips:!1,tipsDesc:"",tipsIsLink:!1,tipsLink:"",regList:[],changeTag:!0,document:"https://element.eleme.cn/#/zh-CN/component/select"},__slot__:{options:[{label:"选项一",value:1},{label:"选项二",value:2}]},placeholder:"请选择",style:{width:"100%"},clearable:!0,disabled:!1,filterable:!1,multiple:!1},{__config__:{label:"级联选择",showLabel:!0,labelWidth:null,tag:"el-cascader",tagIcon:"cascader",layout:"colFormItem",defaultValue:[],dataType:"dynamic",span:24,required:!0,tips:!1,tipsDesc:"",tipsIsLink:!1,tipsLink:"",regList:[],changeTag:!0,document:"https://element.eleme.cn/#/zh-CN/component/cascader"},options:[{id:1,value:1,label:"选项1",children:[{id:2,value:2,label:"选项1-1"}]}],placeholder:"请选择",style:{width:"100%"},props:{props:{multiple:!1,label:"label",value:"value",children:"children"}},"show-all-levels":!0,disabled:!1,clearable:!0,filterable:!1,separator:"/"},{__config__:{label:"单选框组",labelWidth:null,showLabel:!0,tag:"el-radio-group",tagIcon:"radio",changeTag:!0,defaultValue:void 0,layout:"colFormItem",span:24,optionType:"default",regList:[],required:!0,tips:!1,tipsDesc:"",tipsIsLink:!1,tipsLink:"",border:!1,document:"https://element.eleme.cn/#/zh-CN/component/radio"},__slot__:{options:[{label:"选项一",value:1},{label:"选项二",value:2}]},style:{},size:"medium",disabled:!1},{__config__:{label:"多选框组",tag:"el-checkbox-group",tagIcon:"checkbox",defaultValue:[],span:24,showLabel:!0,labelWidth:null,layout:"colFormItem",optionType:"default",required:!0,tips:!1,tipsDesc:"",tipsIsLink:!1,tipsLink:"",regList:[],changeTag:!0,border:!1,document:"https://element.eleme.cn/#/zh-CN/component/checkbox"},__slot__:{options:[{label:"选项一",value:1},{label:"选项二",value:2}]},style:{},size:"medium",min:null,max:null,disabled:!1},{__config__:{label:"开关",tag:"el-switch",tagIcon:"switch",defaultValue:!1,span:24,showLabel:!0,labelWidth:null,layout:"colFormItem",required:!0,tips:!1,tipsDesc:"",tipsIsLink:!1,tipsLink:"",regList:[],changeTag:!0,document:"https://element.eleme.cn/#/zh-CN/component/switch"},style:{},disabled:!1,"active-text":"","inactive-text":"","active-color":null,"inactive-color":null,"active-value":!0,"inactive-value":!1},{__config__:{label:"滑块",tag:"el-slider",tagIcon:"slider",defaultValue:null,span:24,showLabel:!0,layout:"colFormItem",labelWidth:null,required:!0,tips:!1,tipsDesc:"",tipsIsLink:!1,tipsLink:"",regList:[],changeTag:!0,document:"https://element.eleme.cn/#/zh-CN/component/slider"},disabled:!1,min:0,max:100,step:1,"show-stops":!1,range:!1},{__config__:{label:"时间选择",tag:"el-time-picker",tagIcon:"time",defaultValue:null,span:24,showLabel:!0,layout:"colFormItem",labelWidth:null,required:!0,tips:!1,tipsDesc:"",tipsIsLink:!1,tipsLink:"",regList:[],changeTag:!0,document:"https://element.eleme.cn/#/zh-CN/component/time-picker"},placeholder:"请选择",style:{width:"100%"},disabled:!1,clearable:!0,"picker-options":{selectableRange:"00:00:00-23:59:59"},format:"HH:mm:ss","value-format":"HH:mm:ss"},{__config__:{label:"时间范围",tag:"el-time-picker",tagIcon:"time-range",span:24,showLabel:!0,labelWidth:null,layout:"colFormItem",defaultValue:null,required:!0,tips:!1,tipsDesc:"",tipsIsLink:!1,tipsLink:"",regList:[],changeTag:!0,document:"https://element.eleme.cn/#/zh-CN/component/time-picker"},style:{width:"100%"},disabled:!1,clearable:!0,"is-range":!0,"range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",format:"HH:mm:ss","value-format":"HH:mm:ss"},{__config__:{label:"固定时间范围",tag:"time-select",tagIcon:"time-select",span:24,showLabel:!0,labelWidth:null,layout:"colFormItem",defaultValue:null,required:!0,tips:!1,tipsDesc:"",tipsIsLink:!1,tipsLink:"",regList:[],changeTag:!0,document:"https://element.eleme.cn/#/zh-CN/component/time-picker"},style:{width:"100%"},disabled:!1,clearable:!0,placeholder:"请选择",format:"HH:mm","value-format":"HH:mm"},{__config__:{label:"日期选择",tag:"el-date-picker",tagIcon:"date",defaultValue:null,showLabel:!0,labelWidth:null,span:24,layout:"colFormItem",required:!0,tips:!1,tipsDesc:"",tipsIsLink:!1,tipsLink:"",regList:[],changeTag:!0,document:"https://element.eleme.cn/#/zh-CN/component/date-picker"},placeholder:"请选择",type:"date",style:{width:"100%"},disabled:!1,clearable:!0,format:"yyyy-MM-dd","value-format":"yyyy-MM-dd",readonly:!1},{__config__:{label:"日期范围",tag:"el-date-picker",tagIcon:"date-range",defaultValue:null,span:24,showLabel:!0,labelWidth:null,required:!0,tips:!1,tipsDesc:"",tipsIsLink:!1,tipsLink:"",layout:"colFormItem",regList:[],changeTag:!0,document:"https://element.eleme.cn/#/zh-CN/component/date-picker"},style:{width:"100%"},type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",disabled:!1,clearable:!0,format:"yyyy-MM-dd","value-format":"yyyy-MM-dd",readonly:!1},{__config__:{label:"评分",tag:"el-rate",tagIcon:"rate",defaultValue:0,span:24,showLabel:!0,labelWidth:null,layout:"colFormItem",required:!0,tips:!1,tipsDesc:"",tipsIsLink:!1,tipsLink:"",regList:[],changeTag:!0,document:"https://element.eleme.cn/#/zh-CN/component/rate"},style:{},max:5,"allow-half":!1,"show-text":!1,"show-score":!1,disabled:!1},{__config__:{label:"颜色选择",tag:"el-color-picker",tagIcon:"color",span:24,defaultValue:null,showLabel:!0,labelWidth:null,layout:"colFormItem",required:!0,tips:!1,tipsDesc:"",tipsIsLink:!1,tipsLink:"",regList:[],changeTag:!0,document:"https://element.eleme.cn/#/zh-CN/component/color-picker"},"show-alpha":!1,"color-format":"",disabled:!1,size:"medium"},{__config__:{label:"上传文件",tag:"upload-file",tagIcon:"uploadPicture",layout:"colFormItem",defaultValue:null,showLabel:!0,labelWidth:null,required:!0,tips:!1,tipsDesc:"",tipsIsLink:!1,tipsLink:"",span:24,showTip:!1,buttonText:"点击上传",regList:[],changeTag:!0,document:"https://element.eleme.cn/#/zh-CN/component/upload"},__slot__:{"list-type":!0},accept:"",name:"upfile"},{__config__:{label:"自定义上传",tag:"self-upload",tagIcon:"selfUpload",layout:"colFormItem",defaultValue:null,showLabel:!0,labelWidth:null,required:!0,tips:!1,tipsDesc:"",tipsIsLink:!1,tipsLink:"",span:24,showTip:!1,buttonText:"",regList:[],changeTag:!0,document:"https://element.eleme.cn/#/zh-CN/component/upload"},__slot__:{"list-type":!0},disabled:!0,accept:"image",name:"file",multiple:!1}],Ze=[{__config__:{layout:"rowFormItem",tagIcon:"row",label:"行容器",layoutTree:!0,children:[],document:"https://element.eleme.cn/#/zh-CN/component/layout"},type:"default",justify:"start",align:"top"},{__config__:{label:"按钮",showLabel:!0,changeTag:!0,labelWidth:null,tag:"el-button",tagIcon:"button",defaultValue:void 0,span:24,layout:"colFormItem",document:"https://element.eleme.cn/#/zh-CN/component/button"},__slot__:{default:"主要按钮"},type:"primary",icon:"el-icon-search",round:!1,size:"medium",plain:!1,circle:!1,disabled:!1}],et=a("5317"),tt={date:"yyyy-MM-dd",week:"yyyy 第 WW 周",month:"yyyy-MM",year:"yyyy",datetime:"yyyy-MM-dd HH:mm:ss",daterange:"yyyy-MM-dd",monthrange:"yyyy-MM",datetimerange:"yyyy-MM-dd HH:mm:ss"},at={components:{Templates:et["a"],TreeNodeDialog:qe,IconsDialog:He},props:["showField","activeData","formConf"],data:function(){return{currentTab:"field",currentNode:null,dialogVisible:!1,iconsVisible:!1,currentIconModel:null,dateTypeOptions:[{label:"日(date)",value:"date"},{label:"周(week)",value:"week"},{label:"月(month)",value:"month"},{label:"年(year)",value:"year"},{label:"日期时间(datetime)",value:"datetime"}],dateRangeTypeOptions:[{label:"日期范围(daterange)",value:"daterange"},{label:"月范围(monthrange)",value:"monthrange"},{label:"日期时间范围(datetimerange)",value:"datetimerange"}],colorFormatOptions:[{label:"hex",value:"hex"},{label:"rgb",value:"rgb"},{label:"rgba",value:"rgba"},{label:"hsv",value:"hsv"},{label:"hsl",value:"hsl"}],justifyOptions:[{label:"start",value:"start"},{label:"end",value:"end"},{label:"center",value:"center"},{label:"space-around",value:"space-around"},{label:"space-between",value:"space-between"}],layoutTreeProps:{label:function(e,t){var a=e.__config__;return e.componentName||"".concat(a.label,": ").concat(e.__vModel__)}}}},computed:{dateOptions:function(){return void 0!==this.activeData.type&&"el-date-picker"===this.activeData.__config__.tag?void 0===this.activeData["start-placeholder"]?this.dateTypeOptions:this.dateRangeTypeOptions:[]},tagList:function(){return[{label:"输入型组件",options:Xe},{label:"选择型组件",options:Ye}]},activeTag:function(){return this.activeData.__config__.tag},isShowMin:function(){return["el-input-number","el-slider"].indexOf(this.activeTag)>-1},isShowMax:function(){return["el-input-number","el-slider","el-rate"].indexOf(this.activeTag)>-1},isShowStep:function(){return["el-input-number","el-slider"].indexOf(this.activeTag)>-1}},watch:{formConf:{handler:function(e){Fe(e)},deep:!0}},mounted:function(){Fe(this.formConf)},methods:{addReg:function(){this.activeData.__config__.regList.push({pattern:"",message:""})},addSelectItem:function(){this.activeData.__slot__.options.push({label:"",value:""})},addTreeItem:function(){++this.idGlobal,this.dialogVisible=!0,this.currentNode=this.activeData.options},renderContent:function(e,t){var a=this,o=t.node,n=t.data;t.store;return e("div",{class:"custom-tree-node"},[e("span",[o.label]),e("span",{class:"node-operation"},[e("i",{on:{click:function(){return a.append(n)}},class:"el-icon-plus",attrs:{title:"添加"}}),e("i",{on:{click:function(){return a.remove(o,n)}},class:"el-icon-delete",attrs:{title:"删除"}})])])},append:function(e){e.children||this.$set(e,"children",[]),this.dialogVisible=!0,this.currentNode=e.children},remove:function(e,t){this.activeData.__config__.defaultValue=[];var a=e.parent,o=a.data.children||a.data,n=o.findIndex((function(e){return e.id===t.id}));o.splice(n,1)},addNode:function(e){this.currentNode.push(e)},setOptionValue:function(e,t){e.value=Object(A["c"])(t)?+t:t},setDefaultValue:function(e){return Array.isArray(e)?e.join(","):"boolean"===typeof e?"".concat(e):e},onDefaultValueInput:function(e){Object(ge["isArray"])(this.activeData.__config__.defaultValue)?this.$set(this.activeData.__config__,"defaultValue",e.split(",").map((function(e){return Object(A["c"])(e)?+e:e}))):["true","false"].indexOf(e)>-1?this.$set(this.activeData.__config__,"defaultValue",JSON.parse(e)):this.$set(this.activeData.__config__,"defaultValue",Object(A["c"])(e)?+e:e)},onSwitchValueInput:function(e,t){["true","false"].indexOf(e)>-1?this.$set(this.activeData,t,JSON.parse(e)):this.$set(this.activeData,t,Object(A["c"])(e)?+e:e)},setTimeValue:function(e,t){var a="week"===t?tt.date:e;this.$set(this.activeData.__config__,"defaultValue",null),this.$set(this.activeData,"value-format",a),this.$set(this.activeData,"format",e)},spanChange:function(e){this.formConf.span=e},multipleChange:function(e){this.$set(this.activeData.__config__,"defaultValue",e?[]:"")},dateTypeChange:function(e){this.setTimeValue(tt[e],e)},rangeChange:function(e){this.$set(this.activeData.__config__,"defaultValue",e?[this.activeData.min,this.activeData.max]:this.activeData.min)},rateTextChange:function(e){e&&(this.activeData["show-score"]=!1)},rateScoreChange:function(e){e&&(this.activeData["show-text"]=!1)},colorFormatChange:function(e){this.activeData.__config__.defaultValue=null,this.activeData["show-alpha"]=e.indexOf("a")>-1,this.activeData.__config__.renderKey=+new Date},openIconsDialog:function(e){this.iconsVisible=!0,this.currentIconModel=e},setIcon:function(e){this.activeData[this.currentIconModel]=e},tagChange:function(e){var t=Xe.find((function(t){return t.__config__.tagIcon===e}));t||(t=Ye.find((function(t){return t.__config__.tagIcon===e}))),this.$emit("tag-change",t)},changeRenderKey:function(){this.activeData.__config__.renderKey=+new Date}}},ot=at,nt=(a("a7fd"),Object(U["a"])(ot,he,be,!1,null,"7b445891",null)),it=nt.exports,lt=[{__config__:{label:"单行文本",labelWidth:null,showLabel:!0,changeTag:!0,tag:"el-input",tagIcon:"input",defaultValue:void 0,required:!0,layout:"colFormItem",span:24,document:"https://element.eleme.cn/#/zh-CN/component/input",regList:[{pattern:"/^1(3|4|5|7|8|9)\\d{9}$/",message:"手机号格式错误"}]},__slot__:{prepend:"",append:""},__vModel__:"mobile",placeholder:"请输入手机号",style:{width:"100%"},clearable:!0,"prefix-icon":"el-icon-mobile","suffix-icon":"",maxlength:11,"show-word-limit":!0,readonly:!1,disabled:!1}],ct=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-dialog",e._g(e._b({attrs:{width:"500px","close-on-click-modal":!1,"modal-append-to-body":!1},on:{open:e.onOpen,close:e.onClose}},"el-dialog",e.$attrs,!1),e.$listeners),[a("el-row",{attrs:{gutter:15}},[a("el-form",{ref:"elForm",attrs:{model:e.formData,rules:e.rules,size:"medium","label-width":"100px"}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"生成类型",prop:"type"}},[a("el-radio-group",{model:{value:e.formData.type,callback:function(t){e.$set(e.formData,"type",t)},expression:"formData.type"}},e._l(e.typeOptions,(function(t,o){return a("el-radio-button",{key:o,attrs:{label:t.value,disabled:t.disabled}},[e._v("\n                "+e._s(t.label)+"\n              ")])})),1)],1),e._v(" "),e.showFileName?a("el-form-item",{attrs:{label:"文件名",prop:"fileName"}},[a("el-input",{attrs:{placeholder:"请输入文件名",clearable:""},model:{value:e.formData.fileName,callback:function(t){e.$set(e.formData,"fileName",t)},expression:"formData.fileName"}})],1):e._e()],1)],1)],1),e._v(" "),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.close}},[e._v("\n        取消\n      ")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:e.handelConfirm}},[e._v("\n        确定\n      ")])],1)],1)],1)},rt=[];function st(e){return st="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},st(e)}function ut(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,o)}return a}function dt(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?ut(Object(a),!0).forEach((function(t){pt(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):ut(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function pt(e,t,a){return(t=_t(t))in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function _t(e){var t=ft(e,"string");return"symbol"==st(t)?t:t+""}function ft(e,t){if("object"!=st(e)||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var o=a.call(e,t||"default");if("object"!=st(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var mt={inheritAttrs:!1,props:["showFileName"],data:function(){return{formData:{fileName:void 0,type:"file"},rules:{fileName:[{required:!0,message:"请输入文件名",trigger:"blur"}],type:[{required:!0,message:"生成类型不能为空",trigger:"change"}]},typeOptions:[{label:"页面",value:"file"},{label:"弹窗",value:"dialog"}]}},computed:{},watch:{},mounted:function(){},methods:{onOpen:function(){this.showFileName&&(this.formData.fileName="".concat(+new Date,".vue"))},onClose:function(){},close:function(e){this.$emit("update:visible",!1)},handelConfirm:function(){var e=this;this.$refs.elForm.validate((function(t){t&&(e.$emit("confirm",dt({},e.formData)),e.close())}))}}},vt=mt,ht=Object(U["a"])(vt,ct,rt,!1,null,"f958cf44",null),bt=ht.exports,gt={itemBtns:function(e,t,a,o){var n=this.$listeners,i=n.copyItem,l=n.deleteItem;return[e("span",{class:"drawing-item-copy",attrs:{title:"复制"},on:{click:function(e){i(t,o),e.stopPropagation()}}},[e("i",{class:"el-icon-copy-document"})]),e("span",{class:"drawing-item-delete",attrs:{title:"删除"},on:{click:function(e){l(a,o),e.stopPropagation()}}},[e("i",{class:"el-icon-delete"})])]}},yt={colFormItem:function(e,t,a,o){var n=this,i=this.$listeners.activeItem,l=t.__config__,c=this.activeId===l.formId?"drawing-item active-from-item":"drawing-item";this.formConf.unFocusedComponentBorder&&(c+=" unfocus-bordered");var r=l.labelWidth?"".concat(l.labelWidth,"px"):null;return!1===l.showLabel&&(r="0"),void 0==l.tips&&this.$set(l,"tips",!1),l.tips?e("el-col",{attrs:{span:l.span},class:c,nativeOn:{click:function(e){i(t),e.stopPropagation()}}},[e("el-form-item",{attrs:{"label-width":r,label:l.showLabel?l.label:"",required:l.required}},[e("el-tooltip",{attrs:{effect:"dark",placement:"top-start"},style:"padding:10px 5px 0 0;"},["            ",e("i",{class:"el-icon-warning-outline"}),"            ",e("div",{slot:"content",style:"max-width:400px;"},[l.tipsDesc]),"         "]),e(_["a"],{key:l.renderKey,attrs:{conf:t},on:{input:function(e){n.$set(l,"defaultValue",e)}}})]),gt.itemBtns.apply(this,arguments)]):e("el-col",{attrs:{span:l.span},class:c,nativeOn:{click:function(e){i(t),e.stopPropagation()}}},[e("el-form-item",{attrs:{"label-width":r,label:l.showLabel?l.label:"",required:l.required}},[e(_["a"],{key:l.renderKey,attrs:{conf:t},on:{input:function(e){n.$set(l,"defaultValue",e)}}})]),gt.itemBtns.apply(this,arguments)])},rowFormItem:function(e,t,a,o){var n=this.$listeners.activeItem,i=this.activeId===t.__config__.formId?"drawing-row-item active-from-item":"drawing-row-item",l=Dt.apply(this,arguments);return"flex"===t.type&&(l=e("el-row",{attrs:{type:t.type,justify:t.justify,align:t.align}},[l])),e("el-col",{attrs:{span:t.__config__.span}},[e("el-row",{attrs:{gutter:t.__config__.gutter},class:i,nativeOn:{click:function(e){n(t),e.stopPropagation()}}},[e("span",{class:"component-name"},[t.__config__.componentName]),e(r.a,{attrs:{list:t.__config__.children,animation:340,group:"componentsGroup"},class:"drag-wrapper"},[l]),gt.itemBtns.apply(this,arguments)])])}};function Dt(e,t,a,o){var n=this,i=t.__config__;return Array.isArray(i.children)?i.children.map((function(t,a){var o=yt[t.__config__.layout];return o?o.call(n,e,t,a,i.children):wt.call(n)})):null}function wt(){throw new Error("没有与".concat(this.element.__config__.layout,"匹配的layout"))}var kt,xt,Ct,Ot={components:{render:_["a"],draggable:r.a},props:["element","index","drawingList","activeId","formConf"],render:function(e){var t=yt[this.element.__config__.layout];return t?t.call(this,e,this.element,this.index,this.drawingList):wt.call(this)}},It=Ot,jt=Object(U["a"])(It,kt,xt,!1,null,null,null),$t=jt.exports,Lt=a("61f7");function Tt(e){return Tt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Tt(e)}function St(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,o)}return a}function Mt(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?St(Object(a),!0).forEach((function(t){Et(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):St(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function Et(e,t,a){return(t=Ft(t))in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function Ft(e){var t=Nt(e,"string");return"symbol"==Tt(t)?t:t+""}function Nt(e,t){if("object"!=Tt(e)||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var o=a.call(e,t||"default");if("object"!=Tt(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}je(),Ee();var Pt,zt,Vt=Le(),Rt={components:{draggable:r.a,render:_["a"],FormDrawer:ue,JsonDrawer:ve,RightPanel:it,CodeTypeDialog:bt,DraggableItem:$t},props:{editData:{type:Object,default:{}},isCreate:{type:Number,default:0}},data:function(){return{idGlobal:Vt,formConf:Qe,inputComponents:Xe,selectComponents:Ye,layoutComponents:Ze,labelWidth:100,drawingList:lt,drawingData:{},activeId:lt[0].formId,drawerVisible:!1,formData:{},dialogVisible:!1,jsonDrawerVisible:!1,generateConf:null,showFileName:!1,activeData:lt[0],saveDrawingListDebounce:Object(s["debounce"])(340,$e),saveIdGlobalDebounce:Object(s["debounce"])(340,Te),leftComponents:[{title:"输入型组件",list:Xe},{title:"选择型组件",list:Ye},{title:"布局型组件",list:Ze}],selfForm:{name:null,info:null,id:null}}},computed:{},watch:{"activeData.__config__.label":function(e,t){void 0!==this.activeData.placeholder&&this.activeData.__config__.tag&&Pt===this.activeId&&(this.activeData.placeholder=this.activeData.placeholder.replace(t,"")+e)},activeId:{handler:function(e){Pt=e},immediate:!0},drawingList:{handler:function(e){this.saveDrawingListDebounce(e),0===e.length&&(this.idGlobal=100)},deep:!0},idGlobal:{handler:function(e){this.saveIdGlobalDebounce(e)},immediate:!0}},mounted:function(){var e=this;if(this.editData.content){var t=this.editData,a=t.id,o=t.name,n=t.info,i=t.content;this.selfForm.name=o,this.selfForm.id=a,this.selfForm.info=n,i=JSON.parse(i),this.drawingList=i.fields;var l=JSON.parse(JSON.stringify(i));delete l.fields,this.formConf=l}this.activeFormItem(this.drawingList[0]),ee((function(e){Ct=e}));var c=new p.a("#copyNode",{text:function(t){var a=e.generateCode();return e.$notify({title:"成功",message:"代码已复制到剪切板，可粘贴。",type:"success"}),a}});c.on("error",(function(t){e.$message.error("代码复制失败")}))},methods:{activeFormItem:function(e){this.activeData=e,this.activeId=e.__config__.formId},onEnd:function(e){e.from!==e.to&&(this.activeData=zt,this.activeId=this.idGlobal)},addComponent:function(e){var t=this.cloneComponent(e);this.drawingList.push(t),this.activeFormItem(t)},cloneComponent:function(e){var t=JSON.parse(JSON.stringify(e)),a=t.__config__;return a.formId=++this.idGlobal,a.span=this.formConf.span,a.renderKey=+new Date,"colFormItem"===a.layout?(t.__vModel__="field".concat(this.idGlobal),void 0!==t.placeholder&&(t.placeholder+=a.label)):"rowFormItem"===a.layout&&(a.componentName="row".concat(this.idGlobal),a.gutter=this.formConf.gutter),zt=t,zt},AssembleFormData:function(){this.formData=Mt({fields:JSON.parse(JSON.stringify(this.drawingList))},this.formConf)},generate:function(e){var t=this["exec".concat(Object(A["d"])(this.operationType))];this.generateConf=e,t&&t(e)},execRun:function(e){this.AssembleFormData(),this.drawerVisible=!0},execDownload:function(e){var t=this.generateCode(),a=new Blob([t],{type:"text/plain;charset=utf-8"});Object(u["saveAs"])(a,e.fileName)},execCopy:function(e){document.getElementById("copyNode").click()},empty:function(){var e=this;this.$confirm("确定要清空所有组件吗？","提示",{type:"warning"}).then((function(){e.drawingList=[],e.idGlobal=100}))},drawingItemCopy:function(e,t){var a=JSON.parse(JSON.stringify(e));a=this.createIdAndKey(a),t.push(a),this.activeFormItem(a)},createIdAndKey:function(e){var t=this,a=e.__config__;return a.formId=++this.idGlobal,a.renderKey=+new Date,"colFormItem"===a.layout?e.__vModel__="field".concat(this.idGlobal):"rowFormItem"===a.layout&&(a.componentName="row".concat(this.idGlobal)),Array.isArray(a.children)&&(a.children=a.children.map((function(e){return t.createIdAndKey(e)}))),e},drawingItemDelete:function(e,t){var a=this;t.splice(e,1),this.$nextTick((function(){var e=a.drawingList.length;e&&a.activeFormItem(a.drawingList[e-1])}))},generateCode:function(){var e=this.generateConf.type;this.AssembleFormData();var t=D(Object(N["a"])(this.formData,e)),a=y(F(this.formData,e)),o=w(V(this.formData));return Ct.html(a+t+o,A["a"].html)},showJson:function(){this.AssembleFormData(),this.jsonDrawerVisible=!0},handlerSaveJSON:Object(Lt["a"])((function(e){var t=this;this.$refs[e].validate((function(e){if(e){var a=Ne();0!==a.fields.length?(t.selfForm.content=JSON.stringify(a),t.$emit("getFormConfigDataResult",t.selfForm)):t.$message.error("表单配置数据不能为空")}}))})),download:function(){this.dialogVisible=!0,this.showFileName=!0,this.operationType="download"},run:function(){this.dialogVisible=!0,this.showFileName=!1,this.operationType="run"},copy:function(){this.dialogVisible=!0,this.showFileName=!1,this.operationType="copy"},tagChange:function(e){var t=this;e=this.cloneComponent(e);var a=e.__config__;e.__vModel__=this.activeData.__vModel__,a.formId=this.activeId,a.span=this.activeData.__config__.span,this.activeData.__config__.tag=a.tag,this.activeData.__config__.tagIcon=a.tagIcon,this.activeData.__config__.document=a.document,Tt(this.activeData.__config__.defaultValue)===Tt(a.defaultValue)&&(a.defaultValue=this.activeData.__config__.defaultValue),Object.keys(e).forEach((function(a){void 0!==t.activeData[a]&&(e[a]=t.activeData[a])})),this.activeData=e,this.updateDrawingList(e,this.drawingList)},updateDrawingList:function(e,t){var a=this,o=t.findIndex((function(e){return e.__config__.formId===a.activeId}));o>-1?t.splice(o,1,e):t.forEach((function(t){Array.isArray(t.__config__.children)&&a.updateDrawingList(e,t.__config__.children)}))},refreshJson:function(e){this.drawingList=JSON.parse(JSON.stringify(e.fields)),delete e.fields,this.formConf=e}}},qt=Rt,At=(a("d89c"),Object(U["a"])(qt,i,l,!1,null,null,null));t["a"]=At.exports},"64b1":function(e,t,a){"use strict";a.r(t),t["default"]={default:function(e,t,a){return t.__slot__[a]}}},"65d4":function(e,t,a){},"6d7e":function(e,t,a){},"73ee":function(e,t,a){"use strict";a.r(t),t["default"]={"list-type":function(e,t,a){var o=[],n=t.__config__;return"picture-card"===t["list-type"]?o.push(e("i",{class:"el-icon-plus"})):o.push(e("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-upload"}},[n.buttonText])),n.showTip&&o.push(e("div",{slot:"tip",class:"el-upload__tip"},["只能上传不超过 ",n.fileSize,n.sizeUnit," 的",t.accept,"文件"])),o}}},8256:function(e,t,a){"use strict";var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"tinymce-container editor-container",class:{fullscreen:e.fullscreen}},[a("textarea",{staticClass:"tinymce-textarea",attrs:{id:e.tinymceId}}),e._v(" "),a("div",{staticClass:"editor-custom-btn-container"},[a("editorImage",{staticClass:"editor-upload-btn",attrs:{color:"#1890ff"},on:{successCBK:e.imageSuccessCBK}})],1)])},n=[],i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"upload-container"},[a("el-button",{style:{background:e.color,borderColor:e.color},attrs:{icon:"el-icon-upload",size:"mini",type:"primary"},on:{click:function(t){return e.modalPicTap("2")}}},[e._v(" upload")])],1)},l=[],c={name:"EditorSlideUpload",props:{color:{type:String,default:"#1890ff"}},data:function(){return{dialogVisible:!1,listObj:{},fileList:[]}},methods:{modalPicTap:function(e){var t=this;this.$modalUpload((function(e){var a=[];if(e.length>10)return this.$message.warning("最多选择10张图片！");e.map((function(e){a.push(e.sattDir)})),t.$emit("successCBK",a)}),e,"content")}}},r=c,s=(a("909c"),a("2877")),u=Object(s["a"])(r,i,l,!1,null,"710cd935",null),d=u.exports,p=["advlist anchor autolink autosave code codesample colorpicker colorpicker contextmenu directionality emoticons fullscreen hr image imagetools insertdatetime link lists media nonbreaking noneditable pagebreak paste preview print save searchreplace spellchecker tabfocus table template textcolor textpattern visualblocks visualchars wordcount"],_=p,f=["searchreplace bold italic underline strikethrough alignleft aligncenter alignright outdent indent  blockquote undo redo removeformat subscript superscript code codesample fontsizeselect  fontselect","hr bullist numlist link image charmap preview anchor pagebreak insertdatetime media table emoticons forecolor backcolor "],m=f,v={name:"Tinymce",components:{editorImage:d},props:{id:{type:String,default:function(){return"vue-tinymce-"+ +new Date+(1e3*Math.random()).toFixed(0)}},value:{type:String,default:""},toolbar:{type:Array,required:!1,default:function(){return[]}},menubar:{type:String,default:"file edit insert view format table"},height:{type:Number,required:!1,default:400}},data:function(){return{hasChange:!1,hasInit:!1,tinymceId:this.id,fullscreen:!1,languageTypeList:{en:"en",zh:"zh_CN"}}},computed:{language:function(){return this.languageTypeList["zh"]}},watch:{value:function(e){var t=this;!this.hasChange&&this.hasInit&&this.$nextTick((function(){return window.tinymce.get(t.tinymceId).setContent(e||"")}))},language:function(){var e=this;this.destroyTinymce(),this.$nextTick((function(){return e.initTinymce()}))}},mounted:function(){this.initTinymce()},activated:function(){this.initTinymce()},deactivated:function(){this.destroyTinymce()},destroyed:function(){this.destroyTinymce()},methods:{initTinymce:function(){var e=this,t=this;window.tinymce.init({language:this.language,selector:"#".concat(this.tinymceId),height:this.height,body_class:"panel-body ",object_resizing:!1,toolbar:this.toolbar.length>0?this.toolbar:m,menubar:this.menubar,plugins:_,end_container_on_empty_block:!0,powerpaste_word_import:"clean",code_dialog_height:450,code_dialog_width:1e3,advlist_bullet_styles:"square",advlist_number_styles:"default",imagetools_cors_hosts:["www.tinymce.com","codepen.io"],default_link_target:"_blank",link_title:!1,convert_urls:!1,nonbreaking_force_tab:!0,init_instance_callback:function(a){t.value&&a.setContent(t.value),t.hasInit=!0,a.on("NodeChange Change KeyUp SetContent",(function(){e.hasChange=!0,e.$emit("input",a.getContent())}))},setup:function(e){e.on("FullscreenStateChanged",(function(e){t.fullscreen=e.state}))}})},destroyTinymce:function(){var e=window.tinymce.get(this.tinymceId);this.fullscreen&&e.execCommand("mceFullScreen"),e&&e.destroy()},setContent:function(e){window.tinymce.get(this.tinymceId).setContent(e)},getContent:function(){window.tinymce.get(this.tinymceId).getContent()},imageSuccessCBK:function(e){var t=this,a=this;e.forEach((function(e){"video"==t.getFileType(e)?window.tinymce.get(a.tinymceId).insertContent('<video class="wscnph" src="'.concat(e,'" controls muted></video>')):window.tinymce.get(a.tinymceId).insertContent('<img class="wscnph" src="'.concat(e,'" />'))}))},getFileType:function(e){var t="",a="";try{var o=e.split(".");t=o[o.length-1]}catch(l){t=""}if(!t)return!1;t=t.toLocaleLowerCase();var n=["png","jpg","jpeg","bmp","gif"];if(a=n.find((function(e){return e===t})),a)return"image";var i=["mp4","m2v","mkv","rmvb","wmv","avi","flv","mov","m4v"];return a=i.find((function(e){return e===t})),a?"video":"other"}}},h=v,b=(a("f1d2"),Object(s["a"])(h,o,n,!1,null,"7190f699",null));t["a"]=b.exports},8446:function(e,t,a){"use strict";t["a"]={"el-input":"blur","el-input-number":"blur","el-select":"change","el-radio-group":"change","el-checkbox-group":"change","el-cascader":"change","el-time-picker":"change","el-date-picker":"change","el-rate":"change",tinymce:"blur","time-select":"change"}},"909c":function(e,t,a){"use strict";a("a9a9")},9255:function(module,__webpack_exports__,__webpack_require__){"use strict";function indent(e,t){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:2;if(0===t)return e;var o,n=t<0,i=[],l="";if(n)t*=-1,o=new RegExp("(^\\s{0,".concat(t*a,"})"),"g");else for(var c=0;c<t*a;c++)l+=" ";return e.split("\n").forEach((function(e){e=n?e.replace(o,""):l+e,i.push(e)})),i.join("\n")}function titleCase(e){return e.replace(/( |^)[a-z]/g,(function(e){return e.toUpperCase()}))}function camelCase(e){return e.replace(/-[a-z]/g,(function(e){return e.substr(-1).toUpperCase()}))}function isNumberStr(e){return/^[+-]?(0|([1-9]\d*))(\.\d+)?$/g.test(e)}__webpack_require__.d(__webpack_exports__,"d",(function(){return titleCase})),__webpack_require__.d(__webpack_exports__,"c",(function(){return isNumberStr})),__webpack_require__.d(__webpack_exports__,"b",(function(){return exportDefault})),__webpack_require__.d(__webpack_exports__,"a",(function(){return beautifierConf}));var exportDefault="export default ",beautifierConf={html:{indent_size:"2",indent_char:" ",max_preserve_newlines:"-1",preserve_newlines:!1,keep_array_indentation:!1,break_chained_methods:!1,indent_scripts:"separate",brace_style:"end-expand",space_before_conditional:!0,unescape_strings:!1,jslint_happy:!1,end_with_newline:!0,wrap_line_length:"110",indent_inner_html:!0,comma_first:!1,e4x:!0,indent_empty_lines:!0},js:{indent_size:"2",indent_char:" ",max_preserve_newlines:"-1",preserve_newlines:!1,keep_array_indentation:!1,break_chained_methods:!1,indent_scripts:"normal",brace_style:"end-expand",space_before_conditional:!0,unescape_strings:!1,jslint_happy:!0,end_with_newline:!0,wrap_line_length:"110",indent_inner_html:!0,comma_first:!1,e4x:!0,indent_empty_lines:!0}};function stringify(e){return JSON.stringify(e,(function(e,t){return"function"===typeof t?"".concat(t):t}))}function parse(str){JSON.parse(str,(function(k,v){return v.indexOf&&v.indexOf("function")>-1?eval("(".concat(v,")")):v}))}function jsonClone(e){return parse(stringify(e))}},a12a:function(e,t,a){},a16e:function(e,t,a){},a356:function(e,t,a){"use strict";var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e.formConf.fields.length>0?a("parser",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{"is-edit":1===e.isCreate,"form-conf":e.formConf,"form-edit-data":e.editData},on:{submit:e.handlerSubmit,resetForm:e.resetForm}}):e._e()],1)},n=[],i=a("92c6"),l=a("3fbe"),c={components:{parser:l["a"]},props:{formId:{type:Number,required:!0},isCreate:{type:Number,default:0},editData:{type:Object}},data:function(){return{loading:!1,formConf:{fields:[]}}},mounted:function(){this.handlerGetFormConfig(this.formId)},methods:{handlerGetFormConfig:function(e){var t=this;this.loading=!0;var a={id:e};i["b"](a).then((function(e){t.formConf=JSON.parse(e.content),t.loading=!1})).catch((function(){t.loading=!1}))},handlerSubmit:function(e){this.$emit("submit",e)},resetForm:function(e){this.$emit("resetForm",e)}}},r=c,s=a("2877"),u=Object(s["a"])(r,o,n,!1,null,"d54cf374",null);t["a"]=u.exports},a7fd:function(e,t,a){"use strict";a("1b31")},a938:function(e,t,a){"use strict";a.r(t),t["default"]={options:function(e,t,a){var o=[];return t.__slot__.options.forEach((function(t){o.push(e("el-option",{attrs:{label:t.label,value:t.value,disabled:t.disabled}}))})),o}}},a9a9:function(e,t,a){},afca:function(e,t,a){"use strict";a.r(t),t["default"]={options:function(e,t,a){var o=[];return t.__slot__.options.forEach((function(a){"button"===t.__config__.optionType?o.push(e("el-radio-button",{attrs:{label:a.value}},[a.label])):o.push(e("el-radio",{attrs:{label:a.value,border:t.border}},[a.label]))})),o}}},bd64:function(e,t,a){"use strict";a("e8c5")},cb74:function(e,t,a){"use strict";a("6d7e")},cc1a:function(e,t,a){"use strict";a.r(t),t["default"]={options:function(e,t,a){var o=[];return t.__slot__.options.forEach((function(a){"button"===t.__config__.optionType?o.push(e("el-checkbox-button",{attrs:{label:a.value}},[a.label])):o.push(e("el-checkbox",{attrs:{label:a.value,border:t.border}},[a.label]))})),o}}},cd5b:function(module,__webpack_exports__,__webpack_require__){"use strict";__webpack_require__.d(__webpack_exports__,"a",(function(){return makeUpJs}));var util__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("3022"),util__WEBPACK_IMPORTED_MODULE_0___default=__webpack_require__.n(util__WEBPACK_IMPORTED_MODULE_0__),_utils_index__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("ed08"),_ruleTrigger__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__("8446"),units={KB:"1024",MB:"1024 / 1024",GB:"1024 / 1024 / 1024"},confGlobal,inheritAttrs={file:"",dialog:"inheritAttrs: false,"};function makeUpJs(e,t){confGlobal=e=JSON.parse(JSON.stringify(e));var a=[],o=[],n=[],i=[],l=mixinMethod(t),c=[];e.fields.forEach((function(e){buildAttributes(e,a,o,n,l,i,c)}));var r=buildexport(e,t,a.join("\n"),o.join("\n"),n.join("\n"),c.join("\n"),i.join("\n"),l.join("\n"));return confGlobal=null,r}function buildAttributes(e,t,a,o,n,i,l){var c=e.__config__,r=e.__slot__;if(buildData(e,t),buildRules(e,a),(e.options||r&&r.options&&r.options.length)&&(buildOptions(e,o),"dynamic"===c.dataType)){var s="".concat(e.__vModel__,"Options"),u=Object(_utils_index__WEBPACK_IMPORTED_MODULE_1__["titleCase"])(s);buildOptionMethod("get".concat(u),s,n)}e.props&&e.props.props&&buildProps(e,i),e.action&&"el-upload"===c.tag&&(l.push("".concat(e.__vModel__,"Action: '").concat(e.action,"',\n      ").concat(e.__vModel__,"fileList: [],")),n.push(buildBeforeUpload(e)),e["auto-upload"]||n.push(buildSubmitUpload(e))),c.children&&c.children.forEach((function(e){buildAttributes(e,t,a,o,n,i,l)}))}function mixinMethod(e){var t=[],a={file:confGlobal.formBtns?{submitForm:"submitForm() {\n        this.$refs['".concat(confGlobal.formRef,"'].validate(valid => {\n          if(!valid) return\n          // TODO 提交表单\n        })\n      },"),resetForm:"resetForm() {\n        this.$refs['".concat(confGlobal.formRef,"'].resetFields()\n      },")}:null,dialog:{onOpen:"onOpen() {},",onClose:"onClose() {\n        this.$refs['".concat(confGlobal.formRef,"'].resetFields()\n      },"),close:"close() {\n        this.$emit('update:visible', false)\n      },",handelConfirm:"handelConfirm() {\n        this.$refs['".concat(confGlobal.formRef,"'].validate(valid => {\n          if(!valid) return\n          this.close()\n        })\n      },")}},o=a[e];return o&&Object.keys(o).forEach((function(e){t.push(o[e])})),t}function buildData(e,t){var a=e.__config__;if(void 0!==e.__vModel__){var o=JSON.stringify(a.defaultValue);t.push("".concat(e.__vModel__,": ").concat(o,","))}}function buildRules(scheme,ruleList){var config=scheme.__config__;if(void 0!==scheme.__vModel__){var rules=[];if(_ruleTrigger__WEBPACK_IMPORTED_MODULE_2__["a"][config.tag]){if(config.required){var type=Object(util__WEBPACK_IMPORTED_MODULE_0__["isArray"])(config.defaultValue)?"type: 'array',":"",message=Object(util__WEBPACK_IMPORTED_MODULE_0__["isArray"])(config.defaultValue)?"请至少选择一个".concat(config.label):scheme.placeholder;void 0===message&&(message="".concat(config.label,"不能为空")),rules.push("{ required: true, ".concat(type," message: '").concat(message,"', trigger: '").concat(_ruleTrigger__WEBPACK_IMPORTED_MODULE_2__["a"][config.tag],"' }"))}config.regList&&Object(util__WEBPACK_IMPORTED_MODULE_0__["isArray"])(config.regList)&&config.regList.forEach((function(item){item.pattern&&rules.push("{ pattern: ".concat(eval(item.pattern),", message: '").concat(item.message,"', trigger: '").concat(_ruleTrigger__WEBPACK_IMPORTED_MODULE_2__["a"][config.tag],"' }"))})),ruleList.push("".concat(scheme.__vModel__,": [").concat(rules.join(","),"],"))}}}function buildOptions(e,t){if(void 0!==e.__vModel__){var a=e.options;a||(a=e.__slot__.options),"dynamic"===e.__config__.dataType&&(a=[]);var o="".concat(e.__vModel__,"Options: ").concat(JSON.stringify(a),",");t.push(o)}}function buildProps(e,t){var a="".concat(e.__vModel__,"Props: ").concat(JSON.stringify(e.props.props),",");t.push(a)}function buildBeforeUpload(e){var t=e.__config__,a=units[t.sizeUnit],o="",n="",i=[];t.fileSize&&(o="let isRightSize = file.size / ".concat(a," < ").concat(t.fileSize,"\n    if(!isRightSize){\n      this.$message.error('文件大小超过 ").concat(t.fileSize).concat(t.sizeUnit,"')\n    }"),i.push("isRightSize")),e.accept&&(n="let isAccept = new RegExp('".concat(e.accept,"').test(file.type)\n    if(!isAccept){\n      this.$message.error('应该选择").concat(e.accept,"类型的文件')\n    }"),i.push("isAccept"));var l="".concat(e.__vModel__,"BeforeUpload(file) {\n    ").concat(o,"\n    ").concat(n,"\n    return ").concat(i.join("&&"),"\n  },");return i.length?l:""}function buildSubmitUpload(e){var t="submitUpload() {\n    this.$refs['".concat(e.__vModel__,"'].submit()\n  },");return t}function buildOptionMethod(e,t,a){var o="".concat(e,"() {\n    // TODO 发起请求获取数据\n    this.").concat(t,"\n  },");a.push(o)}function buildexport(e,t,a,o,n,i,l,c){var r="".concat(_utils_index__WEBPACK_IMPORTED_MODULE_1__["exportDefault"],"{\n  ").concat(inheritAttrs[t],"\n  components: {},\n  props: [],\n  data () {\n    return {\n      ").concat(e.formModel,": {\n        ").concat(a,"\n      },\n      ").concat(e.formRules,": {\n        ").concat(o,"\n      },\n      ").concat(i,"\n      ").concat(n,"\n      ").concat(l,"\n    }\n  },\n  computed: {},\n  watch: {},\n  created () {},\n  mounted () {},\n  methods: {\n    ").concat(c,"\n  }\n}");return r}},d89c:function(e,t,a){"use strict";a("a12a")},dc83:function(e,t,a){"use strict";a("dea0")},dea0:function(e,t,a){},e15e:function(e,t,a){},e8c5:function(e,t,a){},ec7f:function(module,__webpack_exports__,__webpack_require__){"use strict";var _vue_babel_helper_vue_jsx_merge_props__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("2638"),_vue_babel_helper_vue_jsx_merge_props__WEBPACK_IMPORTED_MODULE_0___default=__webpack_require__.n(_vue_babel_helper_vue_jsx_merge_props__WEBPACK_IMPORTED_MODULE_0__),_components_FormGenerator_components_render_render_js__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("15e7");function _typeof(e){return _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},_typeof(e)}function ownKeys(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,o)}return a}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(a),!0).forEach((function(t){_defineProperty(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):ownKeys(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function _defineProperty(e,t,a){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function _toPropertyKey(e){var t=_toPrimitive(e,"string");return"symbol"==_typeof(t)?t:t+""}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var o=a.call(e,t||"default");if("object"!=_typeof(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var ruleTrigger={"el-input":"blur","el-input-number":"blur","el-select":"change","el-radio-group":"change","el-checkbox-group":"change","el-cascader":"change","el-time-picker":"change","el-date-picker":"change","el-rate":"change"};function renderFrom(e){var t=this.formConfCopy;return e("el-row",{attrs:{gutter:t.gutter}},[e("el-form",_vue_babel_helper_vue_jsx_merge_props__WEBPACK_IMPORTED_MODULE_0___default()([{attrs:{size:t.size,"label-position":t.labelPosition,disabled:t.disabled,"label-width":"".concat(t.labelWidth,"px")},ref:t.formRef},{props:{model:this[t.formModel]}},{attrs:{rules:this[t.formRules]}}]),[renderFormItem.call(this,e,t.fields),t.formBtns&&formBtns.call(this,e)])])}function formBtns(e){return e("el-col",[e("el-form-item",{attrs:{size:"mini"}},[e("el-button",{attrs:{type:"primary"},on:{click:this.submitForm}},["提交"])])])}function renderFormItem(e,t){var a=this;return t.map((function(t){var o=t.__config__,n=layouts[o.layout];if(n)return n.call(a,e,t);throw new Error("没有与".concat(o.layout,"匹配的layout"))}))}function renderChildren(e,t){var a=t.__config__;return Array.isArray(a.children)?renderFormItem.call(this,e,a.children):null}function setValue(e,t,a){this.$set(t,"defaultValue",e),this.$set(this[this.formConf.formModel],a.__vModel__,e)}function buildListeners(e){var t=this,a=e.__config__,o=this.formConf.__methods__||{},n={};return Object.keys(o).forEach((function(e){n[e]=function(a){return o[e].call(t,a)}})),n.input=function(o){return setValue.call(t,o,a,e)},n}var layouts={colFormItem:function(e,t){var a=t.__config__,o=buildListeners.call(this,t),n=a.labelWidth?"".concat(a.labelWidth,"px"):null;return!1===a.showLabel&&(n="0"),a.tips&&!a.tipsIsLink?e("el-col",{attrs:{span:a.span}},[e("el-form-item",{attrs:{"label-width":n,prop:t.__vModel__,label:a.showLabel?a.label:""}},[e("el-tooltip",{attrs:{effect:"dark",placement:"top-start"},style:"padding:10px 5px 0 0;"},["            ",e("i",{class:"el-icon-warning-outline"}),"            ",e("div",{slot:"content",style:"max-width:400px;"},[a.tipsDesc]),"         "]),e(_components_FormGenerator_components_render_render_js__WEBPACK_IMPORTED_MODULE_1__["a"],{attrs:{conf:t},on:_objectSpread({},o)})])]):a.tips&&a.tipsIsLink?e("el-col",{attrs:{span:a.span}},[e("el-form-item",{attrs:{"label-width":n,prop:t.__vModel__,label:a.showLabel?a.label:""}},[e("el-tooltip",{attrs:{effect:"dark",placement:"top-start"},style:"padding:10px 5px 0 0;"},["            ",e("i",{class:"el-icon-warning-outline"}),"            ",e("div",{slot:"content",style:"max-width:400px;"},[e("a",{attrs:{href:a.tipsLink,target:"_blank"}},[a.tipsDesc])]),"         "]),e(_components_FormGenerator_components_render_render_js__WEBPACK_IMPORTED_MODULE_1__["a"],{attrs:{conf:t},on:_objectSpread({},o)})])]):e("el-col",{attrs:{span:a.span}},[e("el-form-item",{attrs:{"label-width":n,prop:t.__vModel__,label:a.showLabel?a.label:""}},[e(_components_FormGenerator_components_render_render_js__WEBPACK_IMPORTED_MODULE_1__["a"],{attrs:{conf:t},on:_objectSpread({},o)})])])},rowFormItem:function(e,t){var a=renderChildren.apply(this,arguments);return"flex"===t.type&&(a=e("el-row",{attrs:{type:t.type,justify:t.justify,align:t.align}},[a])),e("el-col",{attrs:{span:t.span}},[e("el-row",{attrs:{gutter:t.gutter}},[a])])}};__webpack_exports__["a"]={components:{render:_components_FormGenerator_components_render_render_js__WEBPACK_IMPORTED_MODULE_1__["a"]},props:{formConf:{type:Object,required:!0},formEditData:{type:Object},isEdit:{type:Boolean,default:!1}},data:function(){var e=this;this.isEdit&&this.formConf.fields.forEach((function(t){var a=e.formEditData[t.__vModel__];if(a&&(t.__config__.defaultValue=a),"el-select"===t.__config__.tag||"el-radio-group"===t.__config__.tag){var o=t.__slot__.options.filter((function(a){return a.value==e.formEditData[t.__vModel__]}));o.length>0&&(t.__config__.defaultValue=o[0].value)}}));var t=_defineProperty(_defineProperty({formConfCopy:JSON.parse(JSON.stringify(this.formConf))},this.formConf.formModel,{}),this.formConf.formRules,{});return this.initFormData(t.formConfCopy.fields,t[this.formConf.formModel]),this.buildRules(t.formConfCopy.fields,t[this.formConf.formRules]),t},methods:{initFormData:function(e,t){var a=this;e.forEach((function(e){var o=e.__config__;e.__vModel__&&(t[e.__vModel__]=o.defaultValue),o.children&&a.initFormData(o.children,t)}))},buildRules:function buildRules(componentList,rules){var _this5=this;componentList.forEach((function(cur){var config=cur.__config__;if(Array.isArray(config.regList)){if(config.required){var required={required:config.required,message:cur.placeholder};Array.isArray(config.defaultValue)&&(required.type="array",required.message="请至少选择一个".concat(config.label)),void 0===required.message&&(required.message="".concat(config.label,"不能为空")),config.regList.push(required)}rules[cur.__vModel__]=config.regList.map((function(item){return item.pattern&&(item.pattern=eval(item.pattern)),item.trigger=ruleTrigger&&ruleTrigger[config.tag],item}))}config.children&&_this5.buildRules(config.children,rules)}))},resetForm:function(){this.$emit("resetForm",this.formConf),this.formConfCopy=JSON.parse(JSON.stringify(this.formConf)),this.$refs[this.formConf.formRef].resetFields()},submitForm:function(){var e=this;this.$refs[this.formConf.formRef].validate((function(t){return!!t&&(e.$emit("submit",e[e.formConf.formModel]),!0)}))}},render:function(e){return renderFrom.call(this,e)}}},ecc7:function(e,t,a){"use strict";a("a16e")},f1d2:function(e,t,a){"use strict";a("e15e")}}]);