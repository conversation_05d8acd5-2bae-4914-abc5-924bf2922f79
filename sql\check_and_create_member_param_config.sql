-- 检查并创建会员参数配置表
-- 如果表不存在则创建，如果存在则跳过

-- 检查表是否存在
SELECT COUNT(*) as table_exists 
FROM information_schema.tables 
WHERE table_schema = DATABASE() 
AND table_name = 'system_member_param_config';

-- 创建会员参数设置表（如果不存在）
CREATE TABLE IF NOT EXISTS `system_member_param_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配置id',
  `config_type` int(11) NOT NULL COMMENT '配置类型：1-成长值设置，2-提现设置，3-佣金返现设置，4-奖励金设置',
  `source_type` varchar(50) DEFAULT NULL COMMENT '来源类型：自购商品、推广金额、会员充值等',
  `number` int(11) DEFAULT '0' COMMENT '每消费数量',
  `unit` varchar(20) DEFAULT NULL COMMENT '单位：元/件',
  `ratio` int(11) DEFAULT '0' COMMENT '获取比例',
  `withdraw_type` varchar(50) DEFAULT NULL COMMENT '提现类型：SVIP会员，VIP会员，普通会员',
  `withdraw_value` decimal(10,2) DEFAULT NULL COMMENT '提现规则',
  `min_amount` decimal(10,2) DEFAULT NULL COMMENT '提现金额限制：单次最小金额',
  `max_amount` decimal(10,2) DEFAULT NULL COMMENT '提现金额限制：单次最大金额',
  `max_times` int(11) DEFAULT NULL COMMENT '提现次数限制：每月最大次数',
  `fee_rate` decimal(10,2) DEFAULT NULL COMMENT '提现手续费：百分比',
  `auto_refund` tinyint(1) DEFAULT '0' COMMENT '是否SVIP自购返现：1-启用，0-禁用',
  `withdraw_levels` varchar(100) DEFAULT NULL COMMENT '提现等级限制：多个用逗号分隔，如"SVIP会员,VIP会员"',
  `rank_display_count` int(11) DEFAULT '20' COMMENT '榜单排名显示数量：如20表示TOP20',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用：1-启用，0-禁用',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_config_type` (`config_type`),
  KEY `idx_source_type` (`source_type`),
  KEY `idx_withdraw_type` (`withdraw_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员参数设置表';

-- 检查是否已有数据，如果没有则插入默认数据
INSERT IGNORE INTO `system_member_param_config` (`config_type`, `source_type`, `number`, `unit`, `ratio`, `status`, `sort`, `remark`)
VALUES
(1, '自购商品', 1, '件', 1, 1, 0, '购买单件商品获得1点成长值'),
(1, '会员充值', 1, '元', 1, 1, 1, '会员充值每元获得1点成长值'),
(1, '推广金额', 2, '元', 1, 1, 2, '推广成交每2元获得1点成长值');

-- 初始化默认数据 - 提现设置
INSERT IGNORE INTO `system_member_param_config` (`config_type`, `withdraw_levels`, `min_amount`, `max_amount`, `max_times`, `fee_rate`, `status`, `sort`, `remark`)
VALUES
(2, 'SVIP会员', 100.00, 10000.00, NULL, 0.00, 1, 0, '提现设置 - 仅SVIP会员可提现');

-- 初始化默认数据 - 佣金返现设置
INSERT IGNORE INTO `system_member_param_config` (`config_type`, `withdraw_type`, `ratio`, `auto_refund`, `status`, `sort`, `remark`)
VALUES
(3, 'SVIP会员', 10, 1, 1, 0, 'SVIP会员佣金返现比例10%'),
(3, 'VIP会员', 6, 0, 1, 1, 'VIP会员佣金返现比例6%'),
(3, '普通会员', 3, 0, 1, 2, '普通会员佣金返现比例3%');

-- 初始化默认数据 - 奖励金设置
INSERT IGNORE INTO `system_member_param_config` (`config_type`, `source_type`, `number`, `unit`, `ratio`, `rank_display_count`, `status`, `sort`, `remark`)
VALUES
(4, '推广普通会员升级为VIP', 200, '元', 0, 20, 1, 0, '推广普通会员升级为VIP后，一次奖励200元'),
(4, '推广会员充值', 10, '%', 0, 20, 1, 1, '推广会员充值奖励10%'),
(4, '推广新用户首单购买', 10, '%', 0, 20, 1, 2, '推广新用户首单购买，给予vip等级的首单奖励10%'),
(4, '周排行榜奖励', 500, '元', 0, 20, 1, 3, '周排行榜第一名奖励500元'),
(4, '周排行榜奖励', 300, '元', 0, 20, 1, 4, '周排行榜第二名奖励300元'),
(4, '周排行榜奖励', 200, '元', 0, 20, 1, 5, '周排行榜第三名奖励200元');

-- 显示创建结果
SELECT 'system_member_param_config表创建完成' as result;
SELECT COUNT(*) as total_configs FROM system_member_param_config;
