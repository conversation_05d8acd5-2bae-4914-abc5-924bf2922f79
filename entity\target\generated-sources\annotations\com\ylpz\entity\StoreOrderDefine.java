package com.ylpz.entity;

public interface StoreOrderDefine {
    String seckillId = "seckillId";
    String isChannel = "isChannel";
    String orderId = "orderId";
    String payTime = "payTime";
    String userPhone = "userPhone";
    String orderInfos = "orderInfos";
    String useIntegral = "useIntegral";
    String couponId = "couponId";
    String type = "type";
    String addressId = "addressId";
    String deliveryId = "deliveryId";
    String payType = "payType";
    String bargainId = "bargainId";
    String id = "id";
    String freightPrice = "freightPrice";
    String refundPrice = "refundPrice";
    String refundReasonTime = "refundReasonTime";
    String deliveryCode = "deliveryCode";
    String addressAuditStatus = "addressAuditStatus";
    String addressInfo = "addressInfo";
    String orderStatuses = "orderStatuses";
    String payUserId = "payUserId";
    String pinkId = "pinkId";
    String userAddress = "userAddress";
    String gainIntegral = "gainIntegral";
    String isRemind = "isRemind";
    String totalPostage = "totalPostage";
    String merId = "merId";
    String deliveryName = "deliveryName";
    String isAlterPrice = "isAlterPrice";
    String status = "status";
    String cartsId = "cartsId";
    String totalPrice = "totalPrice";
    String shippingType = "shippingType";
    String clerkId = "clerkId";
    String refundStatus = "refundStatus";
    String remark = "remark";
    String backIntegral = "backIntegral";
    String deductionPrice = "deductionPrice";
    String isMerCheck = "isMerCheck";
    String beforePayPrice = "beforePayPrice";
    String uid = "uid";
    String payPrice = "payPrice";
    String outTradeNo = "outTradeNo";
    String refundReasonWapImg = "refundReasonWapImg";
    String bargainUserId = "bargainUserId";
    String isSystemDel = "isSystemDel";
    String cost = "cost";
    String verifyCode = "verifyCode";
    String combinationId = "combinationId";
    String refundReason = "refundReason";
    String deliveryType = "deliveryType";
    String updateTime = "updateTime";
    String storeId = "storeId";
    String realName = "realName";
    String refundReasonWap = "refundReasonWap";
    String refundReasonWapExplain = "refundReasonWapExplain";
    String payPostage = "payPostage";
    String createTime = "createTime";
    String totalNum = "totalNum";
    String paid = "paid";
    String proTotalPrice = "proTotalPrice";
    String couponPrice = "couponPrice";
    String isDel = "isDel";
    String mark = "mark";
}
