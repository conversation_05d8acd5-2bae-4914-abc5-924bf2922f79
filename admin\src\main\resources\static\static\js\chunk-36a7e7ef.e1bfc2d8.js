(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-36a7e7ef"],{2638:function(t,e,n){"use strict";function i(){return i=Object.assign?Object.assign.bind():function(t){for(var e,n=1;n<arguments.length;n++)for(var i in e=arguments[n],e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t},i.apply(this,arguments)}var r=["attrs","props","domProps"],a=["class","style","directives"],o=["on","nativeOn"],c=function(t){return t.reduce((function(t,e){for(var n in e)if(t[n])if(-1!==r.indexOf(n))t[n]=i({},t[n],e[n]);else if(-1!==a.indexOf(n)){var c=t[n]instanceof Array?t[n]:[t[n]],s=e[n]instanceof Array?e[n]:[e[n]];t[n]=[].concat(c,s)}else if(-1!==o.indexOf(n))for(var l in e[n])if(t[n][l]){var f=t[n][l]instanceof Array?t[n][l]:[t[n][l]],u=e[n][l]instanceof Array?e[n][l]:[e[n][l]];t[n][l]=[].concat(f,u)}else t[n][l]=e[n][l];else if("hook"===n)for(var m in e[n])t[n][m]=t[n][m]?d(t[n][m],e[n][m]):e[n][m];else t[n]=e[n];else t[n]=e[n];return t}),{})},d=function(t,e){return function(){t&&t.apply(this,arguments),e&&e.apply(this,arguments)}};t.exports=c},"4be0":function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"divBox"},[n("el-card",{staticClass:"box-card"},[t.checkPermi(["admin:system:config:info"])?n("el-tabs",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],on:{"tab-click":t.handleTabClick},model:{value:t.activeNamel1,callback:function(e){t.activeNamel1=e},expression:"activeNamel1"}},t._l(t.treeList,(function(e,i){return n("el-tab-pane",{key:i,attrs:{label:e.name,name:e.id.toString()}},[[e.child.length>0?n("el-tabs",{attrs:{type:"border-card"},on:{"tab-click":t.handleItemTabClick},model:{value:t.activeNamel2,callback:function(e){t.activeNamel2=e},expression:"activeNamel2"}},t._l(e.child,(function(e,i){return n("el-tab-pane",{key:i,attrs:{label:e.name,name:e.extra}},[t.formConfChild.render?n("parser",{attrs:{"is-edit":t.formConfChild.isEdit,"form-conf":t.formConfChild.content,"form-edit-data":t.currentEditData},on:{submit:t.handlerSubmit}}):t._e()],1)})),1):n("span",[t.formConf.render?n("parser",{attrs:{"is-edit":t.formConf.isEdit,"form-conf":t.formConf.content,"form-edit-data":t.currentEditData},on:{submit:t.handlerSubmit}}):t._e()],1)]],2)})),1):t._e()],1)],1)},r=[],a=n("3fbe"),o=n("651a"),c=n("fca7"),d=n("92c6"),s=n("785a"),l=n("2b9b"),f=n("5317"),u=(n("9255"),n("e350")),m=n("61f7"),h={components:{Template:f["a"],parser:a["a"]},data:function(){return{loading:!1,formConf:{content:{fields:[]},id:null,render:!1,isEdit:!1},formConfChild:{content:{fields:[]},id:null,render:!1,isEdit:!1},activeNamel1:null,activeNamel2:"",treeList:[],editDataChild:{},isCreate:0,currentEditId:null,currentEditData:null,currentSelectedUploadFlag:null}},mounted:function(){this.handlerGetTreeList(),this.getCurrentUploadSelectedFlag()},methods:{checkPermi:u["a"],handleTabClick:function(t){this.activeNamel2=t.$children[0].panes[0].name,this.handlerGetLevel2FormConfig(this.activeNamel2)},handlerGetLevel1FormConfig:function(t){var e=this,n={id:t};this.currentEditId=t,this.formConf.content={fields:[]},this.formConf.render=!1,this.loading=!0,d["b"](n).then((function(t){var n=t.id,i=(t.name,t.info,t.content);e.formConf.content=JSON.parse(i),e.formConf.id=n,e.handlerGetSettingInfo(n,1),e.loading=!1})).catch((function(){e.loading=!1}))},handleItemTabClick:function(t,e){var n=t.name?t.name:t;if(!n)return this.$message.error("表单配置不正确，请关联正确表单后使用");this.handlerGetLevel2FormConfig(n)},handlerGetLevel2FormConfig:function(t){var e=this,n={id:t};this.currentEditId=t,this.formConfChild.content={fields:[]},this.formConfChild.render=!1,this.loading=!0,d["b"](n).then((function(t){var n=t.id,i=(t.name,t.info,t.content);e.formConfChild.content=JSON.parse(i),e.formConfChild.id=n,e.handlerGetSettingInfo(n,2),e.loading=!1})).catch((function(){e.loading=!1}))},handlerGetSettingInfo:function(t,e){var n=this;s["f"]({id:t}).then((function(t){n.currentEditData=t,1===e?(n.formConf.isEdit=null!==n.currentEditData,n.formConf.render=!0):(n.formConfChild.isEdit=null!==n.currentEditData,n.formConfChild.render=!0)}))},handlerSubmit:Object(m["a"])((function(t){this.handlerSave(t)})),handlerSave:function(t){var e=this,n=this.buildFormPram(t);s["g"](n).then((function(t){e.$message.success("添加数据成功")}))},handlerGetTreeList:function(){var t=this,e={type:this.$constants.categoryType[5].value,status:1};this.loading=!0,o["g"](e).then((function(e){t.treeList=t.handleAddArrt(e),t.treeList.length>0&&(t.activeNamel1=t.treeList[0].id.toString()),t.treeList.length>0&&t.treeList[0].child.length>0&&(t.activeNamel2=t.treeList[0].child[0].extra),t.activeNamel2&&t.handlerGetLevel2FormConfig(t.treeList[0].child[0].extra),t.loading=!1})).catch((function(){t.loading=!1}))},handleAddArrt:function(t){var e=c["addTreeListLabel"](t);return e},buildFormPram:function(t){var e={fields:[],id:this.currentEditId,sort:0,status:!0},n=[];return Object.keys(t).forEach((function(e){n.push({name:e,title:e,value:t[e]})})),e.fields=n,e},getCurrentUploadSelectedFlag:function(){var t=this;l["a"]({key:"uploadType"}).then((function(e){t.currentSelectedUploadFlag=parseInt(e)}))}}},p=h,v=n("2877"),b=Object(v["a"])(p,i,r,!1,null,"56f32a8d",null);e["default"]=b.exports},5317:function(t,e,n){"use strict";var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div")},r=[],a=n("2877"),o={},c=Object(a["a"])(o,i,r,!1,null,null,null);e["a"]=c.exports},"92c6":function(t,e,n){"use strict";n.d(e,"b",(function(){return r})),n.d(e,"c",(function(){return a})),n.d(e,"d",(function(){return o})),n.d(e,"a",(function(){return c})),n.d(e,"f",(function(){return d})),n.d(e,"g",(function(){return s})),n.d(e,"j",(function(){return l})),n.d(e,"h",(function(){return f})),n.d(e,"e",(function(){return u})),n.d(e,"i",(function(){return m}));var i=n("b775");function r(t){var e={id:t.id};return Object(i["a"])({url:"/admin/system/form/temp/info",method:"GET",params:e})}function a(t){var e={keywords:t.keywords,page:t.page,limit:t.limit};return Object(i["a"])({url:"/admin/system/form/temp/list",method:"GET",params:e})}function o(t){var e={content:t.content,info:t.info,name:t.name};return Object(i["a"])({url:"/admin/system/form/temp/save",method:"POST",data:e})}function c(t){var e={id:t.id},n={content:t.content,info:t.info,name:t.name};return Object(i["a"])({url:"/admin/system/form/temp/update",method:"POST",params:e,data:n})}function d(t){var e={sendType:t.sendType};return Object(i["a"])({url:"/admin/system/notification/list",method:"GET",params:e})}function s(t){return Object(i["a"])({url:"/admin/system/notification/routine/switch/".concat(t),method:"post"})}function l(t){return Object(i["a"])({url:"/admin/system/notification/wechat/switch/".concat(t),method:"post"})}function f(t){return Object(i["a"])({url:"/admin/system/notification/sms/switch/".concat(t),method:"post"})}function u(t){var e={detailType:t.type,id:t.id};return Object(i["a"])({url:"/admin/system/notification/detail",method:"get",params:e})}function m(t){var e={detailType:t.type,id:t.id,status:t.status,tempId:t.tempId};return Object(i["a"])({url:"/admin/system/notification/update",method:"post",data:e})}},fb9d:function(t,e,n){var i={"./el-button.js":"64b1","./el-checkbox-group.js":"cc1a","./el-input.js":"373c","./el-radio-group.js":"afca","./el-select.js":"a938","./el-upload.js":"73ee"};function r(t){var e=a(t);return n(e)}function a(t){var e=i[t];if(!(e+1)){var n=new Error("Cannot find module '"+t+"'");throw n.code="MODULE_NOT_FOUND",n}return e}r.keys=function(){return Object.keys(i)},r.resolve=a,t.exports=r,r.id="fb9d"}}]);