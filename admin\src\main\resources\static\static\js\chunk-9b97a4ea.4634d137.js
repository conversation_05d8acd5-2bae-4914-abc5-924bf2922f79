(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-9b97a4ea"],{"2d29":function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"divBox relative"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e.checkPermi(["admin:product:tabs:headers"])?a("el-tabs",{on:{"tab-click":e.seachList},model:{value:e.tableFrom.type,callback:function(t){e.$set(e.tableFrom,"type",t)},expression:"tableFrom.type"}},e._l(e.headeNum,(function(e,t){return a("el-tab-pane",{key:t,attrs:{label:e.name+"("+e.count+")",name:e.type.toString()}})})),1):e._e(),e._v(" "),a("div",{staticClass:"container mt-1"},[a("el-form",{attrs:{inline:"",size:"small"}},[a("el-form-item",{attrs:{label:"商品分类："}},[a("el-cascader",{staticClass:"selWidth mr20",attrs:{options:e.merCateList,props:e.props,clearable:"",size:"small"},on:{change:e.seachList},model:{value:e.tableFrom.cateId,callback:function(t){e.$set(e.tableFrom,"cateId",t)},expression:"tableFrom.cateId"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"商品搜索："}},[a("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入商品名称，关键字，商品ID",size:"small",clearable:""},model:{value:e.tableFrom.keywords,callback:function(t){e.$set(e.tableFrom,"keywords",t)},expression:"tableFrom.keywords"}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:product:list"],expression:"['admin:product:list']"}],attrs:{slot:"append",icon:"el-icon-search",size:"small"},on:{click:e.seachList},slot:"append"})],1)],1)],1)],1),e._v(" "),a("router-link",{attrs:{to:{path:"/store/list/creatProduct"}}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:product:save"],expression:"['admin:product:save']"}],staticClass:"mr10",attrs:{size:"small",type:"primary"}},[e._v("添加商品")])],1),e._v(" "),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:export:excel:product"],expression:"['admin:export:excel:product']"}],attrs:{size:"small",icon:"el-icon-upload2"},on:{click:e.exports}},[e._v("导出")])],1),e._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:e.tableData.data,size:"mini","highlight-current-row":!0,"header-cell-style":{fontWeight:"bold"}}},[a("el-table-column",{attrs:{type:"expand"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-form",{staticClass:"demo-table-expand",attrs:{"label-position":"left",inline:""}},[a("el-form-item",{attrs:{label:"商品分类："}},e._l(t.row.cateValues.split(","),(function(t,i){return a("span",{key:i,staticClass:"mr10"},[e._v(e._s(t))])})),0),e._v(" "),a("el-form-item",{attrs:{label:"市场价："}},[a("span",[e._v(e._s(t.row.otPrice))])]),e._v(" "),a("el-form-item",{attrs:{label:"成本价："}},[a("span",[e._v(e._s(t.row.cost))])]),e._v(" "),a("el-form-item",{attrs:{label:"收藏："}},[a("span",[e._v(e._s(t.row.collectCount))])]),e._v(" "),a("el-form-item",{attrs:{label:"虚拟销量："}},[a("span",[e._v(e._s(t.row.ficti))])])],1)]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"id",label:"ID","min-width":"50"}}),e._v(" "),a("el-table-column",{attrs:{label:"商品图","min-width":"80"},scopedSlots:e._u([{key:"default",fn:function(e){return[a("div",{staticClass:"demo-image__preview"},[a("el-image",{staticStyle:{width:"36px",height:"36px"},attrs:{src:e.row.image,"preview-src-list":[e.row.image]}})],1)]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"商品名称",prop:"storeName","min-width":"300","show-overflow-tooltip":!0}}),e._v(" "),a("el-table-column",{attrs:{prop:"price",label:"商品售价","min-width":"90",align:"center"}}),e._v(" "),a("el-table-column",{attrs:{prop:"sales",label:"销量","min-width":"90",align:"center"}}),e._v(" "),a("el-table-column",{attrs:{prop:"stock",label:"库存","min-width":"90",align:"center"}}),e._v(" "),a("el-table-column",{attrs:{prop:"sort",label:"排序","min-width":"70",align:"center"}}),e._v(" "),a("el-table-column",{attrs:{label:"添加时间","min-width":"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e._f("formatDate")(t.row.addTime)))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"状态","min-width":"120",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return e.checkPermi(["admin:product:up","admin:product:down"])?[a("el-switch",{attrs:{width:80,disabled:Number(e.tableFrom.type)>2,"active-value":!0,"inactive-value":!1,"active-text":"上架","inactive-text":t.row.saleTime?"定时上架":"下架"},on:{change:function(a){return e.onchangeIsShow(t.row)}},model:{value:t.row.isShow,callback:function(a){e.$set(t.row,"isShow",a)},expression:"scope.row.isShow"}}),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:t.row.saleTime,expression:"scope.row.saleTime"}]},[e._v("\n            上架时间："+e._s(e.parseTime(t.row.saleTime,"{y}-{m}-{d} {h}:{i}:{s}"))+"\n          ")])]:void 0}}],null,!0)}),e._v(" "),a("el-table-column",{attrs:{label:"操作","min-width":"150",fixed:"right",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("router-link",{attrs:{to:{path:"/store/list/creatProduct/"+t.row.id+"/1"}}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:product:info"],expression:"['admin:product:info']"}],staticClass:"mr10",attrs:{type:"text",size:"small"}},[e._v("详情")])],1),e._v(" "),a("router-link",{attrs:{to:{path:"/store/list/creatProduct/"+t.row.id}}},["5"!==e.tableFrom.type&&"1"!==e.tableFrom.type?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:product:update"],expression:"['admin:product:update']"}],staticClass:"mr10",attrs:{type:"text",size:"small"}},[e._v("编辑")]):e._e()],1),e._v(" "),"5"===e.tableFrom.type?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:product:restore"],expression:"['admin:product:restore']"}],attrs:{type:"text",size:"small"},on:{click:function(a){return e.handleRestore(t.row.id,t.$index)}}},[e._v("恢复商品")]):e._e(),e._v(" "),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:product:delete"],expression:"['admin:product:delete']"}],attrs:{type:"text",size:"small"},on:{click:function(a){return e.handleDelete(t.row.id,e.tableFrom.type)}}},[e._v(e._s("5"===e.tableFrom.type?"删除":"加入回收站"))])]}}])})],1),e._v(" "),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{"page-sizes":[20,40,60,80],"page-size":e.tableFrom.limit,"current-page":e.tableFrom.page,layout:"total, sizes, prev, pager, next, jumper",total:e.tableData.total},on:{"size-change":e.handleSizeChange,"current-change":e.pageChange}})],1)],1),e._v(" "),a("el-dialog",{staticClass:"taoBaoModal",attrs:{title:"复制淘宝、天猫、京东、苏宁",visible:e.dialogVisible,"close-on-click-modal":!1,width:"1200px","before-close":e.handleClose},on:{"update:visible":function(t){e.dialogVisible=t}}},[e.dialogVisible?a("tao-bao",{on:{handleCloseMod:e.handleCloseMod}}):e._e()],1)],1)},s=[],o=a("73f5"),l=(a("5f87"),a("ad7c")),n=a("e350"),r={name:"ProductList",components:{taoBao:l["a"]},data:function(){return{props:{children:"child",label:"name",value:"id",emitPath:!1},headeNum:[],listLoading:!0,tableData:{data:[],total:0},tableFrom:{page:1,limit:20,cateId:"",keywords:"",type:"1"},categoryList:[],merCateList:[],objectUrl:"//localhost/adminapi",dialogVisible:!1}},mounted:function(){this.goodHeade(),this.getList(),this.getCategorySelect(),this.checkedCities=this.$cache.local.has("goods_stroge")?this.$cache.local.getJSON("goods_stroge"):this.checkedCities},methods:{checkPermi:n["a"],handleRestore:function(e){var t=this;this.$modalSure("恢复商品").then((function(){Object(o["y"])(e).then((function(e){t.$message.success("操作成功"),t.goodHeade(),t.getList()}))}))},seachList:function(){this.tableFrom.page=1,this.getList()},handleClose:function(){this.dialogVisible=!1},handleCloseMod:function(e){this.dialogVisible=e,this.goodHeade(),this.getList()},onCopy:function(){this.dialogVisible=!0},exports:function(){Object(o["m"])({cateId:this.tableFrom.cateId,keywords:this.tableFrom.keywords,type:this.tableFrom.type}).then((function(e){window.location.href=e.fileName}))},goodHeade:function(){var e=this;Object(o["q"])().then((function(t){e.headeNum=t})).catch((function(t){e.$message.error(t.message)}))},getCategorySelect:function(){var e=this;Object(o["d"])({status:-1,type:1}).then((function(t){e.merCateList=t})).catch((function(t){e.$message.error(t.message)}))},getList:function(){var e=this;this.listLoading=!0,Object(o["p"])(this.tableFrom).then((function(t){e.tableData.data=t.list,e.tableData.total=t.total,e.listLoading=!1})).catch((function(t){e.listLoading=!1,e.$message.error(t.message)}))},pageChange:function(e){this.tableFrom.page=e,this.getList()},handleSizeChange:function(e){this.tableFrom.limit=e,this.getList()},handleDelete:function(e,t){var a=this;this.$modalSure("删除 id 为 ".concat(e," 的商品")).then((function(){var i=5==t?"delete":"recycle";Object(o["k"])(e,i).then((function(){a.$message.success("删除成功"),a.getList(),a.goodHeade()}))}))},onchangeIsShow:function(e){var t=this;e.isShow?Object(o["s"])(e.id).then((function(){t.$message.success("上架成功"),t.getList(),t.goodHeade()})).catch((function(){e.isShow=!e.isShow})):Object(o["h"])(e.id).then((function(){t.$message.success("下架成功"),t.getList(),t.goodHeade()})).catch((function(){e.isShow=!e.isShow}))}}},c=r,d=(a("e992"),a("2877")),m=Object(d["a"])(c,i,s,!1,null,"30b86cba",null);t["default"]=m.exports},e272:function(e,t,a){},e992:function(e,t,a){"use strict";a("e272")}}]);