(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-9a8ac32e"],{"0046":function(e,t,i){var n=i("6d8b"),a=n.each,o=n.createHashMap,r=i("4f85"),s=i("3301"),l=r.extend({type:"series.parallel",dependencies:["parallel"],visualColorAccessPath:"lineStyle.color",getInitialData:function(e,t){var i=this.getSource();return u(i,this),s(i,this)},getRawIndicesByActiveState:function(e){var t=this.coordinateSystem,i=this.getData(),n=[];return t.eachActiveState(i,(function(t,a){e===t&&n.push(i.getRawIndex(a))})),n},defaultOption:{zlevel:0,z:2,coordinateSystem:"parallel",parallelIndex:0,label:{show:!1},inactiveOpacity:.05,activeOpacity:1,lineStyle:{width:1,opacity:.45,type:"solid"},emphasis:{label:{show:!1}},progressive:500,smooth:!1,animationEasing:"linear"}});function u(e,t){if(!e.encodeDefine){var i=t.ecModel.getComponent("parallel",t.get("parallelIndex"));if(i){var n=e.encodeDefine=o();a(i.dimensions,(function(e){var t=c(e);n.set(e,t)}))}}}function c(e){return+e.replace("dim","")}e.exports=l},"004f":function(e,t,i){var n=i("6d8b"),a=i("72b6"),o=i("2306"),r=i("a15a"),s=r.createSymbol,l=i("f934"),u=i("cbb0"),c=a.extend({type:"visualMap.piecewise",doRender:function(){var e=this.group;e.removeAll();var t=this.visualMapModel,i=t.get("textGap"),a=t.textStyleModel,r=a.getFont(),s=a.getTextColor(),u=this._getItemAlign(),c=t.itemSize,d=this._getViewData(),h=d.endsText,p=n.retrieve(t.get("showLabel",!0),!h);function g(a){var l=a.piece,d=new o.Group;d.onclick=n.bind(this._onItemClick,this,l),this._enableHoverLink(d,a.indexInModelPieceList);var h=t.getRepresentValue(l);if(this._createItemSymbol(d,h,[0,0,c[0],c[1]]),p){var g=this.visualMapModel.getValueState(h);d.add(new o.Text({style:{x:"right"===u?-i:c[0]+i,y:c[1]/2,text:l.text,textVerticalAlign:"middle",textAlign:u,textFont:r,textFill:s,opacity:"outOfRange"===g?.5:1}}))}e.add(d)}h&&this._renderEndsText(e,h[0],c,p,u),n.each(d.viewPieceList,g,this),h&&this._renderEndsText(e,h[1],c,p,u),l.box(t.get("orient"),e,t.get("itemGap")),this.renderBackground(e),this.positionGroup(e)},_enableHoverLink:function(e,t){function i(e){var i=this.visualMapModel;i.option.hoverLink&&this.api.dispatchAction({type:e,batch:u.convertDataIndex(i.findTargetDataIndices(t))})}e.on("mouseover",n.bind(i,this,"highlight")).on("mouseout",n.bind(i,this,"downplay"))},_getItemAlign:function(){var e=this.visualMapModel,t=e.option;if("vertical"===t.orient)return u.getItemAlign(e,this.api,e.itemSize);var i=t.align;return i&&"auto"!==i||(i="left"),i},_renderEndsText:function(e,t,i,n,a){if(t){var r=new o.Group,s=this.visualMapModel.textStyleModel;r.add(new o.Text({style:{x:n?"right"===a?i[0]:0:i[0]/2,y:i[1]/2,textVerticalAlign:"middle",textAlign:n?a:"center",text:t,textFont:s.getFont(),textFill:s.getTextColor()}})),e.add(r)}},_getViewData:function(){var e=this.visualMapModel,t=n.map(e.getPieceList(),(function(e,t){return{piece:e,indexInModelPieceList:t}})),i=e.get("text"),a=e.get("orient"),o=e.get("inverse");return("horizontal"===a?o:!o)?t.reverse():i&&(i=i.slice().reverse()),{viewPieceList:t,endsText:i}},_createItemSymbol:function(e,t,i){e.add(s(this.getControllerVisual(t,"symbol"),i[0],i[1],i[2],i[3],this.getControllerVisual(t,"color")))},_onItemClick:function(e){var t=this.visualMapModel,i=t.option,a=n.clone(i.selected),o=t.getSelectedMapKey(e);"single"===i.selectedMode?(a[o]=!0,n.each(a,(function(e,t){a[t]=t===o}))):a[o]=!a[o],this.api.dispatchAction({type:"selectDataRange",from:this.uid,visualMapId:this.visualMapModel.id,selected:a})}}),d=c;e.exports=d},"007d":function(e,t,i){var n=i("3eba");i("cb8f"),i("a96b"),i("42f6"),n.registerAction({type:"showTip",event:"showTip",update:"tooltip:manuallyShowTip"},(function(){})),n.registerAction({type:"hideTip",event:"hideTip",update:"tooltip:manuallyHideTip"},(function(){}))},"00ba":function(e,t,i){var n=i("3eba"),a=i("e46b"),o=i("e0d3"),r=o.defaultEmphasis,s=n.extendSeriesModel({type:"series.funnel",init:function(e){s.superApply(this,"init",arguments),this.legendDataProvider=function(){return this.getRawData()},this._defaultLabelLine(e)},getInitialData:function(e,t){return a(this,["value"])},_defaultLabelLine:function(e){r(e,"labelLine",["show"]);var t=e.labelLine,i=e.emphasis.labelLine;t.show=t.show&&e.label.show,i.show=i.show&&e.emphasis.label.show},getDataParams:function(e){var t=this.getData(),i=s.superCall(this,"getDataParams",e),n=t.mapDimension("value"),a=t.getSum(n);return i.percent=a?+(t.get(n,e)/a*100).toFixed(2):0,i.$vars.push("percent"),i},defaultOption:{zlevel:0,z:2,legendHoverLink:!0,left:80,top:60,right:80,bottom:60,minSize:"0%",maxSize:"100%",sort:"descending",gap:0,funnelAlign:"center",label:{show:!0,position:"outer"},labelLine:{show:!0,length:20,lineStyle:{width:1,type:"solid"}},itemStyle:{borderColor:"#fff",borderWidth:1},emphasis:{label:{show:!0}}}}),l=s;e.exports=l},"00d8":function(e,t,i){var n=i("6d8b");function a(e,t){return t=t||[0,0],n.map([0,1],(function(i){var n=t[i],a=e[i]/2,o=[],r=[];return o[i]=n-a,r[i]=n+a,o[1-i]=r[1-i]=t[1-i],Math.abs(this.dataToPoint(o)[i]-this.dataToPoint(r)[i])}),this)}function o(e){var t=e.getBoundingRect();return{coordSys:{type:"geo",x:t.x,y:t.y,width:t.width,height:t.height,zoom:e.getZoom()},api:{coord:function(t){return e.dataToPoint(t)},size:n.bind(a,e)}}}e.exports=o},"0141":function(e,t,i){var n=i("6d8b"),a=i("9850"),o=i("6cc5"),r=i("5b87");function s(e,t,i,n){o.call(this,e),this.map=t;var a=r.load(t,i);this._nameCoordMap=a.nameCoordMap,this._regionsMap=a.regionsMap,this._invertLongitute=null==n||n,this.regions=a.regions,this._rect=a.boundingRect}function l(e,t,i,n){var a=i.geoModel,o=i.seriesModel,r=a?a.coordinateSystem:o?o.coordinateSystem||(o.getReferringComponents("geo")[0]||{}).coordinateSystem:null;return r===this?r[e](n):null}s.prototype={constructor:s,type:"geo",dimensions:["lng","lat"],containCoord:function(e){for(var t=this.regions,i=0;i<t.length;i++)if(t[i].contain(e))return!0;return!1},transformTo:function(e,t,i,n){var o=this.getBoundingRect(),r=this._invertLongitute;o=o.clone(),r&&(o.y=-o.y-o.height);var s=this._rawTransformable;if(s.transform=o.calculateTransform(new a(e,t,i,n)),s.decomposeTransform(),r){var l=s.scale;l[1]=-l[1]}s.updateTransform(),this._updateTransform()},getRegion:function(e){return this._regionsMap.get(e)},getRegionByCoord:function(e){for(var t=this.regions,i=0;i<t.length;i++)if(t[i].contain(e))return t[i]},addGeoCoord:function(e,t){this._nameCoordMap.set(e,t)},getGeoCoord:function(e){return this._nameCoordMap.get(e)},getBoundingRect:function(){return this._rect},dataToPoint:function(e,t,i){if("string"===typeof e&&(e=this.getGeoCoord(e)),e)return o.prototype.dataToPoint.call(this,e,t,i)},convertToPixel:n.curry(l,"dataToPoint"),convertFromPixel:n.curry(l,"pointToData")},n.mixin(s,o);var u=s;e.exports=u},"01ef":function(e,t){function i(e,t,i){var n=e.target,a=n.position;a[0]+=t,a[1]+=i,n.dirty()}function n(e,t,i,n){var a=e.target,o=e.zoomLimit,r=a.position,s=a.scale,l=e.zoom=e.zoom||1;if(l*=t,o){var u=o.min||0,c=o.max||1/0;l=Math.max(Math.min(c,l),u)}var d=l/e.zoom;e.zoom=l,r[0]-=(i-r[0])*(d-1),r[1]-=(n-r[1])*(d-1),s[0]*=d,s[1]*=d,a.dirty()}t.updateViewOnPan=i,t.updateViewOnZoom=n},"06c7":function(e,t,i){var n=i("6d8b"),a=i("4319"),o=i("31d9"),r=i("6179"),s=i("b1d4"),l=function(e,t){this.name=e||"",this.depth=0,this.height=0,this.parentNode=null,this.dataIndex=-1,this.children=[],this.viewChildren=[],this.hostTree=t};function u(e,t,i){this.root,this.data,this._nodes=[],this.hostModel=e,this.levelModels=n.map(t||[],(function(t){return new a(t,e,e.ecModel)})),this.leavesModel=new a(i||{},e,e.ecModel)}function c(e,t){var i=t.children;e.parentNode!==t&&(i.push(e),e.parentNode=t)}l.prototype={constructor:l,isRemoved:function(){return this.dataIndex<0},eachNode:function(e,t,i){"function"===typeof e&&(i=t,t=e,e=null),e=e||{},n.isString(e)&&(e={order:e});var a,o=e.order||"preorder",r=this[e.attr||"children"];"preorder"===o&&(a=t.call(i,this));for(var s=0;!a&&s<r.length;s++)r[s].eachNode(e,t,i);"postorder"===o&&t.call(i,this)},updateDepthAndHeight:function(e){var t=0;this.depth=e;for(var i=0;i<this.children.length;i++){var n=this.children[i];n.updateDepthAndHeight(e+1),n.height>t&&(t=n.height)}this.height=t+1},getNodeById:function(e){if(this.getId()===e)return this;for(var t=0,i=this.children,n=i.length;t<n;t++){var a=i[t].getNodeById(e);if(a)return a}},contains:function(e){if(e===this)return!0;for(var t=0,i=this.children,n=i.length;t<n;t++){var a=i[t].contains(e);if(a)return a}},getAncestors:function(e){var t=[],i=e?this:this.parentNode;while(i)t.push(i),i=i.parentNode;return t.reverse(),t},getValue:function(e){var t=this.hostTree.data;return t.get(t.getDimension(e||"value"),this.dataIndex)},setLayout:function(e,t){this.dataIndex>=0&&this.hostTree.data.setItemLayout(this.dataIndex,e,t)},getLayout:function(){return this.hostTree.data.getItemLayout(this.dataIndex)},getModel:function(e){if(!(this.dataIndex<0)){var t,i=this.hostTree,n=i.data.getItemModel(this.dataIndex),a=this.getLevelModel();return a||0!==this.children.length&&(0===this.children.length||!1!==this.isExpand)||(t=this.getLeavesModel()),n.getModel(e,(a||t||i.hostModel).getModel(e))}},getLevelModel:function(){return(this.hostTree.levelModels||[])[this.depth]},getLeavesModel:function(){return this.hostTree.leavesModel},setVisual:function(e,t){this.dataIndex>=0&&this.hostTree.data.setItemVisual(this.dataIndex,e,t)},getVisual:function(e,t){return this.hostTree.data.getItemVisual(this.dataIndex,e,t)},getRawIndex:function(){return this.hostTree.data.getRawIndex(this.dataIndex)},getId:function(){return this.hostTree.data.getId(this.dataIndex)},isAncestorOf:function(e){var t=e.parentNode;while(t){if(t===this)return!0;t=t.parentNode}return!1},isDescendantOf:function(e){return e!==this&&e.isAncestorOf(this)}},u.prototype={constructor:u,type:"tree",eachNode:function(e,t,i){this.root.eachNode(e,t,i)},getNodeByDataIndex:function(e){var t=this.data.getRawIndex(e);return this._nodes[t]},getNodeByName:function(e){return this.root.getNodeByName(e)},update:function(){for(var e=this.data,t=this._nodes,i=0,n=t.length;i<n;i++)t[i].dataIndex=-1;for(i=0,n=e.count();i<n;i++)t[e.getRawIndex(i)].dataIndex=i},clearLayouts:function(){this.data.clearItemLayouts()}},u.createTree=function(e,t,i){var a=new u(t,i.levels,i.leaves),d=[],h=1;function p(e,t){var i=e.value;h=Math.max(h,n.isArray(i)?i.length:1),d.push(e);var o=new l(e.name,a);t?c(o,t):a.root=o,a._nodes.push(o);var r=e.children;if(r)for(var s=0;s<r.length;s++)p(r[s],o)}p(e),a.root.updateDepthAndHeight(0);var g=s(d,{coordDimensions:["value"],dimensionsCount:h}),f=new r(g,t);return f.initData(d),o({mainData:f,struct:a,structAttr:"tree"}),a.update(),a};var d=u;e.exports=d},"06ea":function(e,t,i){var n=i("4e08"),a=(n.__DEV__,i("6d8b")),o=i("eaea"),r=i("5f14"),s=i("60e3"),l=i("3842"),u=l.reformIntervals,c=o.extend({type:"visualMap.piecewise",defaultOption:{selected:null,minOpen:!1,maxOpen:!1,align:"auto",itemWidth:20,itemHeight:14,itemSymbol:"roundRect",pieceList:null,categories:null,splitNumber:5,selectedMode:"multiple",itemGap:10,hoverLink:!0,showLabel:null},optionUpdated:function(e,t){c.superApply(this,"optionUpdated",arguments),this._pieceList=[],this.resetExtent();var i=this._mode=this._determineMode();d[this._mode].call(this),this._resetSelected(e,t);var n=this.option.categories;this.resetVisual((function(e,t){"categories"===i?(e.mappingMethod="category",e.categories=a.clone(n)):(e.dataExtent=this.getExtent(),e.mappingMethod="piecewise",e.pieceList=a.map(this._pieceList,(function(e){e=a.clone(e);return"inRange"!==t&&(e.visual=null),e})))}))},completeVisualOption:function(){var e=this.option,t={},i=r.listVisualTypes(),n=this.isCategory();function l(e,t,i){return e&&e[t]&&(a.isObject(e[t])?e[t].hasOwnProperty(i):e[t]===i)}a.each(e.pieces,(function(e){a.each(i,(function(i){e.hasOwnProperty(i)&&(t[i]=1)}))})),a.each(t,(function(t,i){var o=0;a.each(this.stateList,(function(t){o|=l(e,t,i)||l(e.target,t,i)}),this),!o&&a.each(this.stateList,(function(t){(e[t]||(e[t]={}))[i]=s.get(i,"inRange"===t?"active":"inactive",n)}))}),this),o.prototype.completeVisualOption.apply(this,arguments)},_resetSelected:function(e,t){var i=this.option,n=this._pieceList,o=(t?i:e).selected||{};if(i.selected=o,a.each(n,(function(e,t){var i=this.getSelectedMapKey(e);o.hasOwnProperty(i)||(o[i]=!0)}),this),"single"===i.selectedMode){var r=!1;a.each(n,(function(e,t){var i=this.getSelectedMapKey(e);o[i]&&(r?o[i]=!1:r=!0)}),this)}},getSelectedMapKey:function(e){return"categories"===this._mode?e.value+"":e.index+""},getPieceList:function(){return this._pieceList},_determineMode:function(){var e=this.option;return e.pieces&&e.pieces.length>0?"pieces":this.option.categories?"categories":"splitNumber"},setSelected:function(e){this.option.selected=a.clone(e)},getValueState:function(e){var t=r.findPieceIndex(e,this._pieceList);return null!=t&&this.option.selected[this.getSelectedMapKey(this._pieceList[t])]?"inRange":"outOfRange"},findTargetDataIndices:function(e){var t=[];return this.eachTargetSeries((function(i){var n=[],a=i.getData();a.each(this.getDataDimension(a),(function(t,i){var a=r.findPieceIndex(t,this._pieceList);a===e&&n.push(i)}),this),t.push({seriesId:i.id,dataIndex:n})}),this),t},getRepresentValue:function(e){var t;if(this.isCategory())t=e.value;else if(null!=e.value)t=e.value;else{var i=e.interval||[];t=i[0]===-1/0&&i[1]===1/0?0:(i[0]+i[1])/2}return t},getVisualMeta:function(e){if(!this.isCategory()){var t=[],i=[],n=this,o=this._pieceList.slice();if(o.length){var r=o[0].interval[0];r!==-1/0&&o.unshift({interval:[-1/0,r]}),r=o[o.length-1].interval[1],r!==1/0&&o.push({interval:[r,1/0]})}else o.push({interval:[-1/0,1/0]});var s=-1/0;return a.each(o,(function(e){var t=e.interval;t&&(t[0]>s&&l([s,t[0]],"outOfRange"),l(t.slice()),s=t[1])}),this),{stops:t,outerColors:i}}function l(a,o){var r=n.getRepresentValue({interval:a});o||(o=n.getValueState(r));var s=e(r,o);a[0]===-1/0?i[0]=s:a[1]===1/0?i[1]=s:t.push({value:a[0],color:s},{value:a[1],color:s})}}}),d={splitNumber:function(){var e=this.option,t=this._pieceList,i=Math.min(e.precision,20),n=this.getExtent(),o=e.splitNumber;o=Math.max(parseInt(o,10),1),e.splitNumber=o;var r=(n[1]-n[0])/o;while(+r.toFixed(i)!==r&&i<5)i++;e.precision=i,r=+r.toFixed(i);var s=0;e.minOpen&&t.push({index:s++,interval:[-1/0,n[0]],close:[0,0]});for(var l=n[0],c=s+o;s<c;l+=r){var d=s===o-1?n[1]:l+r;t.push({index:s++,interval:[l,d],close:[1,1]})}e.maxOpen&&t.push({index:s++,interval:[n[1],1/0],close:[0,0]}),u(t),a.each(t,(function(e){e.text=this.formatValueText(e.interval)}),this)},categories:function(){var e=this.option;a.each(e.categories,(function(e){this._pieceList.push({text:this.formatValueText(e,!0),value:e})}),this),h(e,this._pieceList)},pieces:function(){var e=this.option,t=this._pieceList;a.each(e.pieces,(function(e,i){a.isObject(e)||(e={value:e});var n={text:"",index:i};if(null!=e.label&&(n.text=e.label),e.hasOwnProperty("value")){var o=n.value=e.value;n.interval=[o,o],n.close=[1,1]}else{for(var s=n.interval=[],l=n.close=[0,0],u=[1,0,1],c=[-1/0,1/0],d=[],h=0;h<2;h++){for(var p=[["gte","gt","min"],["lte","lt","max"]][h],g=0;g<3&&null==s[h];g++)s[h]=e[p[g]],l[h]=u[g],d[h]=2===g;null==s[h]&&(s[h]=c[h])}d[0]&&s[1]===1/0&&(l[0]=0),d[1]&&s[0]===-1/0&&(l[1]=0),s[0]===s[1]&&l[0]&&l[1]&&(n.value=s[0])}n.visual=r.retrieveVisuals(e),t.push(n)}),this),h(e,t),u(t),a.each(t,(function(e){var t=e.close,i=[["<","≤"][t[1]],[">","≥"][t[0]]];e.text=e.text||this.formatValueText(null!=e.value?e.value:e.interval,!1,i)}),this)}};function h(e,t){var i=e.inverse;("vertical"===e.orient?!i:i)&&t.reverse()}var p=c;e.exports=p},"07d7":function(e,t,i){var n=i("6d8b"),a=i("41ef"),o=i("607d"),r=i("22d1"),s=i("eda2"),l=n.each,u=s.toCamelCase,c=["","-webkit-","-moz-","-o-"],d="position:absolute;display:block;border-style:solid;white-space:nowrap;z-index:9999999;";function h(e){var t="cubic-bezier(0.23, 1, 0.32, 1)",i="left "+e+"s "+t+",top "+e+"s "+t;return n.map(c,(function(e){return e+"transition:"+i})).join(";")}function p(e){var t=[],i=e.get("fontSize"),n=e.getTextColor();return n&&t.push("color:"+n),t.push("font:"+e.getFont()),i&&t.push("line-height:"+Math.round(3*i/2)+"px"),l(["decoration","align"],(function(i){var n=e.get(i);n&&t.push("text-"+i+":"+n)})),t.join(";")}function g(e){var t=[],i=e.get("transitionDuration"),n=e.get("backgroundColor"),o=e.getModel("textStyle"),c=e.get("padding");return i&&t.push(h(i)),n&&(r.canvasSupported?t.push("background-Color:"+n):(t.push("background-Color:#"+a.toHex(n)),t.push("filter:alpha(opacity=70)"))),l(["width","color","radius"],(function(i){var n="border-"+i,a=u(n),o=e.get(a);null!=o&&t.push(n+":"+o+("color"===i?"":"px"))})),t.push(p(o)),null!=c&&t.push("padding:"+s.normalizeCssArray(c).join("px ")+"px"),t.join(";")+";"}function f(e,t){if(r.wxa)return null;var i=document.createElement("div"),n=this._zr=t.getZr();this.el=i,this._x=t.getWidth()/2,this._y=t.getHeight()/2,e.appendChild(i),this._container=e,this._show=!1,this._hideTimeout;var a=this;i.onmouseenter=function(){a._enterable&&(clearTimeout(a._hideTimeout),a._show=!0),a._inContent=!0},i.onmousemove=function(t){if(t=t||window.event,!a._enterable){var i=n.handler;o.normalizeEvent(e,t,!0),i.dispatch("mousemove",t)}},i.onmouseleave=function(){a._enterable&&a._show&&a.hideLater(a._hideDelay),a._inContent=!1}}f.prototype={constructor:f,_enterable:!0,update:function(){var e=this._container,t=e.currentStyle||document.defaultView.getComputedStyle(e),i=e.style;"absolute"!==i.position&&"absolute"!==t.position&&(i.position="relative")},show:function(e){clearTimeout(this._hideTimeout);var t=this.el;t.style.cssText=d+g(e)+";left:"+this._x+"px;top:"+this._y+"px;"+(e.get("extraCssText")||""),t.style.display=t.innerHTML?"block":"none",t.style.pointerEvents=this._enterable?"auto":"none",this._show=!0},setContent:function(e){this.el.innerHTML=null==e?"":e},setEnterable:function(e){this._enterable=e},getSize:function(){var e=this.el;return[e.clientWidth,e.clientHeight]},moveTo:function(e,t){var i,n=this._zr;n&&n.painter&&(i=n.painter.getViewportRootOffset())&&(e+=i.offsetLeft,t+=i.offsetTop);var a=this.el.style;a.left=e+"px",a.top=t+"px",this._x=e,this._y=t},hide:function(){this.el.style.display="none",this._show=!1},hideLater:function(e){!this._show||this._inContent&&this._enterable||(e?(this._hideDelay=e,this._show=!1,this._hideTimeout=setTimeout(n.bind(this.hide,this),e)):this.hide())},isShow:function(){return this._show},getOuterSize:function(){var e=this.el.clientWidth,t=this.el.clientHeight;if(document.defaultView&&document.defaultView.getComputedStyle){var i=document.defaultView.getComputedStyle(this.el);i&&(e+=parseInt(i.paddingLeft,10)+parseInt(i.paddingRight,10)+parseInt(i.borderLeftWidth,10)+parseInt(i.borderRightWidth,10),t+=parseInt(i.paddingTop,10)+parseInt(i.paddingBottom,10)+parseInt(i.borderTopWidth,10)+parseInt(i.borderBottomWidth,10))}return{width:e,height:t}}};var m=f;e.exports=m},"07e6":function(e,t,i){i("4d85"),i("a753")},"0817":function(e,t,i){var n=i("3eba");i("f306"),i("0046"),i("60d7");var a=i("ab71");n.registerVisual(a)},"085d":function(e,t,i){var n=i("3eba");i("bd92"),i("19e2");var a=i("eabf"),o=i("4c99"),r=i("09b1");n.registerPreprocessor(a),n.registerVisual(o),n.registerLayout(r)},"08c3":function(e,t,i){var n=i("6d8b"),a=i("84ce"),o=function(e,t,i,n){a.call(this,e,t,i),this.type=n||"value",this.model=null};o.prototype={constructor:o,getLabelModel:function(){return this.model.getModel("label")},isHorizontal:function(){return"horizontal"===this.model.get("orient")}},n.inherits(o,a);var r=o;e.exports=r},"09b1":function(e,t,i){var n=i("2306"),a=n.subPixelOptimize,o=i("cccd"),r=i("3842"),s=r.parsePercent,l=i("6d8b"),u=l.retrieve2,c="undefined"!==typeof Float32Array?Float32Array:Array,d={seriesType:"candlestick",plan:o(),reset:function(e){var t=e.coordinateSystem,i=e.getData(),n=p(e,i),o=0,r=1,s=["x","y"],l=i.mapDimension(s[o]),u=i.mapDimension(s[r],!0),d=u[0],g=u[1],f=u[2],m=u[3];if(i.setLayout({candleWidth:n,isSimpleBox:n<=1.3}),!(null==l||u.length<4))return{progress:e.pipelineContext.large?y:v};function v(e,i){var s;while(null!=(s=e.next())){var u=i.get(l,s),c=i.get(d,s),p=i.get(g,s),v=i.get(f,s),y=i.get(m,s),x=Math.min(c,p),_=Math.max(c,p),b=A(x,u),w=A(_,u),S=A(v,u),M=A(y,u),I=[];T(I,w,0),T(I,b,1),I.push(L(M),L(w),L(S),L(b)),i.setItemLayout(s,{sign:h(i,s,c,p,g),initBaseline:c>p?w[r]:b[r],ends:I,brushRect:D(v,y,u)})}function A(e,i){var n=[];return n[o]=i,n[r]=e,isNaN(i)||isNaN(e)?[NaN,NaN]:t.dataToPoint(n)}function T(e,t,i){var r=t.slice(),s=t.slice();r[o]=a(r[o]+n/2,1,!1),s[o]=a(s[o]-n/2,1,!0),i?e.push(r,s):e.push(s,r)}function D(e,t,i){var a=A(e,i),s=A(t,i);return a[o]-=n/2,s[o]-=n/2,{x:a[0],y:a[1],width:r?n:s[0]-a[0],height:r?s[1]-a[1]:n}}function L(e){return e[o]=a(e[o],1),e}}function y(e,i){var n,a,s=new c(5*e.count),u=0,p=[],v=[];while(null!=(a=e.next())){var y=i.get(l,a),x=i.get(d,a),_=i.get(g,a),b=i.get(f,a),w=i.get(m,a);isNaN(y)||isNaN(b)||isNaN(w)?(s[u++]=NaN,u+=4):(s[u++]=h(i,a,x,_,g),p[o]=y,p[r]=b,n=t.dataToPoint(p,null,v),s[u++]=n?n[0]:NaN,s[u++]=n?n[1]:NaN,p[r]=w,n=t.dataToPoint(p,null,v),s[u++]=n?n[1]:NaN)}i.setLayout("largePoints",s)}}};function h(e,t,i,n,a){var o;return o=i>n?-1:i<n?1:t>0?e.get(a,t-1)<=n?1:-1:1,o}function p(e,t){var i,n=e.getBaseAxis(),a="category"===n.type?n.getBandWidth():(i=n.getExtent(),Math.abs(i[1]-i[0])/t.count()),o=s(u(e.get("barMaxWidth"),a),a),r=s(u(e.get("barMinWidth"),1),a),l=e.get("barWidth");return null!=l?s(l,a):Math.max(Math.min(a/2,o),r)}e.exports=d},"0a6d":function(e,t,i){i("6932"),i("3a56"),i("7dcf"),i("3790"),i("2325"),i("a18f"),i("32a1"),i("2c17"),i("9e87")},"0b4b":function(e,t,i){i("d28f"),i("f14c"),i("0ee7"),i("ebf9")},"0c12":function(e,t){function i(){}function n(e,t,i,n){for(var a=0,o=t.length,r=0,s=0;a<o;a++){var l=t[a];if(l.removed){for(u=[],c=s;c<s+l.count;c++)u.push(c);l.indices=u,s+=l.count}else{for(var u=[],c=r;c<r+l.count;c++)u.push(c);l.indices=u,r+=l.count,l.added||(s+=l.count)}}return t}function a(e){return{newPos:e.newPos,components:e.components.slice(0)}}i.prototype={diff:function(e,t,i){i||(i=function(e,t){return e===t}),this.equals=i;var o=this;e=e.slice(),t=t.slice();var r=t.length,s=e.length,l=1,u=r+s,c=[{newPos:-1,components:[]}],d=this.extractCommon(c[0],t,e,0);if(c[0].newPos+1>=r&&d+1>=s){for(var h=[],p=0;p<t.length;p++)h.push(p);return[{indices:h,count:t.length}]}function g(){for(var i=-1*l;i<=l;i+=2){var u,d=c[i-1],h=c[i+1],p=(h?h.newPos:0)-i;d&&(c[i-1]=void 0);var g=d&&d.newPos+1<r,f=h&&0<=p&&p<s;if(g||f){if(!g||f&&d.newPos<h.newPos?(u=a(h),o.pushComponent(u.components,void 0,!0)):(u=d,u.newPos++,o.pushComponent(u.components,!0,void 0)),p=o.extractCommon(u,t,e,i),u.newPos+1>=r&&p+1>=s)return n(o,u.components,t,e);c[i]=u}else c[i]=void 0}l++}while(l<=u){var f=g();if(f)return f}},pushComponent:function(e,t,i){var n=e[e.length-1];n&&n.added===t&&n.removed===i?e[e.length-1]={count:n.count+1,added:t,removed:i}:e.push({count:1,added:t,removed:i})},extractCommon:function(e,t,i,n){var a=t.length,o=i.length,r=e.newPos,s=r-n,l=0;while(r+1<a&&s+1<o&&this.equals(t[r+1],i[s+1]))r++,s++,l++;return l&&e.components.push({count:l}),e.newPos=r,s},tokenize:function(e){return e.slice()},join:function(e){return e.slice()}};var o=new i;function r(e,t,i){return o.diff(e,t,i)}e.exports=r},"0c41":function(e,t,i){var n=i("6d8b"),a=i("4a01"),o=i("01ef"),r=i("c526"),s=r.onIrrelevantElement,l=i("2306"),u=i("5b87"),c=i("8918"),d=c.getUID;function h(e,t){var i=e.getItemStyle(),n=e.get("areaColor");return null!=n&&(i.fill=n),i}function p(e,t,i,a,o){i.off("click"),i.off("mousedown"),t.get("selectedMode")&&(i.on("mousedown",(function(){e._mouseDownFlag=!0})),i.on("click",(function(r){if(e._mouseDownFlag){e._mouseDownFlag=!1;var s=r.target;while(!s.__regions)s=s.parent;if(s){var l={type:("geo"===t.mainType?"geo":"map")+"ToggleSelect",batch:n.map(s.__regions,(function(e){return{name:e.name,from:o.uid}}))};l[t.mainType+"Id"]=t.id,a.dispatchAction(l),g(t,i)}}})))}function g(e,t){t.eachChild((function(t){n.each(t.__regions,(function(i){t.trigger(e.isSelected(i.name)?"emphasis":"normal")}))}))}function f(e,t){var i=new l.Group;this.uid=d("ec_map_draw"),this._controller=new a(e.getZr()),this._controllerHost={target:t?i:null},this.group=i,this._updateGroup=t,this._mouseDownFlag,this._mapName,this._initialized,i.add(this._regionsGroup=new l.Group),i.add(this._backgroundGroup=new l.Group)}f.prototype={constructor:f,draw:function(e,t,i,a,o){var r="geo"===e.mainType,s=e.getData&&e.getData();r&&t.eachComponent({mainType:"series",subType:"map"},(function(t){s||t.getHostGeoModel()!==e||(s=t.getData())}));var u=e.coordinateSystem;this._updateBackground(u);var c=this._regionsGroup,d=this.group,f=u.scale,m={position:u.position,scale:f};!c.childAt(0)||o?d.attr(m):l.updateProps(d,m,e),c.removeAll();var v=["itemStyle"],y=["emphasis","itemStyle"],x=["label"],_=["emphasis","label"],b=n.createHashMap();n.each(u.regions,(function(t){var i=b.get(t.name)||b.set(t.name,new l.Group),a=new l.CompoundPath({shape:{paths:[]}});i.add(a);var o,u=e.getRegionModel(t.name)||e,d=u.getModel(v),p=u.getModel(y),g=h(d,f),m=h(p,f),w=u.getModel(x),S=u.getModel(_);if(s){o=s.indexOfName(t.name);var M=s.getItemVisual(o,"color",!0);M&&(g.fill=M)}n.each(t.geometries,(function(e){if("polygon"===e.type){a.shape.paths.push(new l.Polygon({shape:{points:e.exterior}}));for(var t=0;t<(e.interiors?e.interiors.length:0);t++)a.shape.paths.push(new l.Polygon({shape:{points:e.interiors[t]}}))}})),a.setStyle(g),a.style.strokeNoScale=!0,a.culling=!0;var I=w.get("show"),A=S.get("show"),T=s&&isNaN(s.get(s.mapDimension("value"),o)),D=s&&s.getItemLayout(o);if(r||T&&(I||A)||D&&D.showLabel){var L,C=r?t.name:o;(!s||o>=0)&&(L=e);var P=new l.Text({position:t.center.slice(),scale:[1/f[0],1/f[1]],z2:10,silent:!0});l.setLabelStyle(P.style,P.hoverStyle={},w,S,{labelFetcher:L,labelDataIndex:C,defaultText:t.name,useInsideStyle:!1},{textAlign:"center",textVerticalAlign:"middle"}),i.add(P)}if(s)s.setItemGraphicEl(o,i);else{u=e.getRegionModel(t.name);a.eventData={componentType:"geo",componentIndex:e.componentIndex,geoIndex:e.componentIndex,name:t.name,region:u&&u.option||{}}}var N=i.__regions||(i.__regions=[]);N.push(t),l.setHoverStyle(i,m,{hoverSilentOnTouch:!!e.get("selectedMode")}),c.add(i)})),this._updateController(e,t,i),p(this,e,c,i,a),g(e,c)},remove:function(){this._regionsGroup.removeAll(),this._backgroundGroup.removeAll(),this._controller.dispose(),this._mapName&&u.removeGraphic(this._mapName,this.uid),this._mapName=null,this._controllerHost={}},_updateBackground:function(e){var t=e.map;this._mapName!==t&&n.each(u.makeGraphic(t,this.uid),(function(e){this._backgroundGroup.add(e)}),this),this._mapName=t},_updateController:function(e,t,i){var a=e.coordinateSystem,r=this._controller,l=this._controllerHost;l.zoomLimit=e.get("scaleLimit"),l.zoom=a.getZoom(),r.enable(e.get("roam")||!1);var u=e.mainType;function c(){var t={type:"geoRoam",componentType:u};return t[u+"Id"]=e.id,t}r.off("pan").on("pan",(function(e){this._mouseDownFlag=!1,o.updateViewOnPan(l,e.dx,e.dy),i.dispatchAction(n.extend(c(),{dx:e.dx,dy:e.dy}))}),this),r.off("zoom").on("zoom",(function(e){if(this._mouseDownFlag=!1,o.updateViewOnZoom(l,e.scale,e.originX,e.originY),i.dispatchAction(n.extend(c(),{zoom:e.scale,originX:e.originX,originY:e.originY})),this._updateGroup){var t=this.group.scale;this._regionsGroup.traverse((function(e){"text"===e.type&&e.attr("scale",[1/t[0],1/t[1]])}))}}),this),r.setPointerChecker((function(t,n,o){return a.getViewRectAfterRoam().contain(n,o)&&!s(t,i,e)}))}};var m=f;e.exports=m},"0e0f":function(e,t,i){var n=i("5f14"),a=i("6d8b");function o(e,t){e.eachSeriesByType("sankey",(function(e){var t=e.getGraph(),i=t.nodes;if(i.length){var o=1/0,r=-1/0;a.each(i,(function(e){var t=e.getLayout().value;t<o&&(o=t),t>r&&(r=t)})),a.each(i,(function(t){var i=new n({type:"color",mappingMethod:"linear",dataExtent:[o,r],visual:e.get("color")}),a=i.mapValueToVisual(t.getLayout().value);t.setVisual("color",a);var s=t.getModel(),l=s.get("itemStyle.color");null!=l&&t.setVisual("color",l)}))}}))}e.exports=o},"0ee7":function(e,t,i){var n=i("6d8b"),a=i("2306"),o=i("f934"),r=i("5e97"),s=a.Group,l=["width","height"],u=["x","y"],c=r.extend({type:"legend.scroll",newlineDisabled:!0,init:function(){c.superCall(this,"init"),this._currentIndex=0,this.group.add(this._containerGroup=new s),this._containerGroup.add(this.getContentGroup()),this.group.add(this._controllerGroup=new s),this._showController},resetInner:function(){c.superCall(this,"resetInner"),this._controllerGroup.removeAll(),this._containerGroup.removeClipPath(),this._containerGroup.__rectSize=null},renderInner:function(e,t,i,o){var r=this;c.superCall(this,"renderInner",e,t,i,o);var s=this._controllerGroup,l=t.get("pageIconSize",!0);n.isArray(l)||(l=[l,l]),d("pagePrev",0);var u=t.getModel("pageTextStyle");function d(e,i){var u=e+"DataIndex",c=a.createIcon(t.get("pageIcons",!0)[t.getOrient().name][i],{onclick:n.bind(r._pageGo,r,u,t,o)},{x:-l[0]/2,y:-l[1]/2,width:l[0],height:l[1]});c.name=e,s.add(c)}s.add(new a.Text({name:"pageText",style:{textFill:u.getTextColor(),font:u.getFont(),textVerticalAlign:"middle",textAlign:"center"},silent:!0})),d("pageNext",1)},layoutInner:function(e,t,i,r){var s=this.getContentGroup(),c=this._containerGroup,d=this._controllerGroup,h=e.getOrient().index,p=l[h],g=l[1-h],f=u[1-h];o.box(e.get("orient"),s,e.get("itemGap"),h?i.width:null,h?null:i.height),o.box("horizontal",d,e.get("pageButtonItemGap",!0));var m=s.getBoundingRect(),v=d.getBoundingRect(),y=this._showController=m[p]>i[p],x=[-m.x,-m.y];r||(x[h]=s.position[h]);var _=[0,0],b=[-v.x,-v.y],w=n.retrieve2(e.get("pageButtonGap",!0),e.get("itemGap",!0));if(y){var S=e.get("pageButtonPosition",!0);"end"===S?b[h]+=i[p]-v[p]:_[h]+=v[p]+w}b[1-h]+=m[g]/2-v[g]/2,s.attr("position",x),c.attr("position",_),d.attr("position",b);var M=this.group.getBoundingRect();M={x:0,y:0};if(M[p]=y?i[p]:m[p],M[g]=Math.max(m[g],v[g]),M[f]=Math.min(0,v[f]+b[1-h]),c.__rectSize=i[p],y){var I={x:0,y:0};I[p]=Math.max(i[p]-v[p]-w,0),I[g]=M[g],c.setClipPath(new a.Rect({shape:I})),c.__rectSize=I[p]}else d.eachChild((function(e){e.attr({invisible:!0,silent:!0})}));var A=this._getPageInfo(e);return null!=A.pageIndex&&a.updateProps(s,{position:A.contentPosition},!!y&&e),this._updatePageInfoView(e,A),M},_pageGo:function(e,t,i){var n=this._getPageInfo(t)[e];null!=n&&i.dispatchAction({type:"legendScroll",scrollDataIndex:n,legendId:t.id})},_updatePageInfoView:function(e,t){var i=this._controllerGroup;n.each(["pagePrev","pageNext"],(function(n){var a=null!=t[n+"DataIndex"],o=i.childOfName(n);o&&(o.setStyle("fill",a?e.get("pageIconColor",!0):e.get("pageIconInactiveColor",!0)),o.cursor=a?"pointer":"default")}));var a=i.childOfName("pageText"),o=e.get("pageFormatter"),r=t.pageIndex,s=null!=r?r+1:0,l=t.pageCount;a&&o&&a.setStyle("text",n.isString(o)?o.replace("{current}",s).replace("{total}",l):o({current:s,total:l}))},_getPageInfo:function(e){var t=e.get("scrollDataIndex",!0),i=this.getContentGroup(),n=this._containerGroup.__rectSize,a=e.getOrient().index,o=l[a],r=u[a],s=this._findTargetItemIndex(t),c=i.children(),d=c[s],h=c.length,p=h?1:0,g={contentPosition:i.position.slice(),pageCount:p,pageIndex:p-1,pagePrevDataIndex:null,pageNextDataIndex:null};if(!d)return g;var f=_(d);g.contentPosition[a]=-f.s;for(var m=s+1,v=f,y=f,x=null;m<=h;++m)x=_(c[m]),(!x&&y.e>v.s+n||x&&!b(x,v.s))&&(v=y.i>v.i?y:x,v&&(null==g.pageNextDataIndex&&(g.pageNextDataIndex=v.i),++g.pageCount)),y=x;for(m=s-1,v=f,y=f,x=null;m>=-1;--m)x=_(c[m]),x&&b(y,x.s)||!(v.i<y.i)||(y=v,null==g.pagePrevDataIndex&&(g.pagePrevDataIndex=v.i),++g.pageCount,++g.pageIndex),v=x;return g;function _(e){if(e){var t=e.getBoundingRect(),i=t[r]+e.position[a];return{s:i,e:i+t[o],i:e.__legendDataIndex}}}function b(e,t){return e.e>=t&&e.s<=t+n}},_findTargetItemIndex:function(e){var t,i=this.getContentGroup();return this._showController?i.eachChild((function(i,n){i.__legendDataIndex===e&&(t=n)})):t=0,t}}),d=c;e.exports=d},"0f55":function(e,t,i){var n=i("6d8b"),a=i("84ce"),o=function(e,t,i,n,o){a.call(this,e,t,i),this.type=n||"value",this.axisIndex=o};o.prototype={constructor:o,model:null,isHorizontal:function(){return"horizontal"!==this.coordinateSystem.getModel().get("layout")}},n.inherits(o,a);var r=o;e.exports=r},"0fd3":function(e,t,i){var n=i("2306"),a=i("7e5b"),o=i("6d8b"),r=i("a15a"),s=r.createSymbol,l=i("401b"),u=i("4a3f");function c(e,t,i){n.Group.call(this),this.add(this.createLine(e,t,i)),this._updateEffectSymbol(e,t)}var d=c.prototype;d.createLine=function(e,t,i){return new a(e,t,i)},d._updateEffectSymbol=function(e,t){var i=e.getItemModel(t),n=i.getModel("effect"),a=n.get("symbolSize"),r=n.get("symbol");o.isArray(a)||(a=[a,a]);var l=n.get("color")||e.getItemVisual(t,"color"),u=this.childAt(1);this._symbolType!==r&&(this.remove(u),u=s(r,-.5,-.5,1,1,l),u.z2=100,u.culling=!0,this.add(u)),u&&(u.setStyle("shadowColor",l),u.setStyle(n.getItemStyle(["color"])),u.attr("scale",a),u.setColor(l),u.attr("scale",a),this._symbolType=r,this._updateEffectAnimation(e,n,t))},d._updateEffectAnimation=function(e,t,i){var n=this.childAt(1);if(n){var a=this,r=e.getItemLayout(i),s=1e3*t.get("period"),l=t.get("loop"),u=t.get("constantSpeed"),c=o.retrieve(t.get("delay"),(function(t){return t/e.count()*s/3})),d="function"===typeof c;if(n.ignore=!0,this.updateAnimationPoints(n,r),u>0&&(s=this.getLineLength(n)/u*1e3),s!==this._period||l!==this._loop){n.stopAnimation();var h=c;d&&(h=c(i)),n.__t>0&&(h=-s*n.__t),n.__t=0;var p=n.animate("",l).when(s,{__t:1}).delay(h).during((function(){a.updateSymbolPosition(n)}));l||p.done((function(){a.remove(n)})),p.start()}this._period=s,this._loop=l}},d.getLineLength=function(e){return l.dist(e.__p1,e.__cp1)+l.dist(e.__cp1,e.__p2)},d.updateAnimationPoints=function(e,t){e.__p1=t[0],e.__p2=t[1],e.__cp1=t[2]||[(t[0][0]+t[1][0])/2,(t[0][1]+t[1][1])/2]},d.updateData=function(e,t,i){this.childAt(0).updateData(e,t,i),this._updateEffectSymbol(e,t)},d.updateSymbolPosition=function(e){var t=e.__p1,i=e.__p2,n=e.__cp1,a=e.__t,o=e.position,r=u.quadraticAt,s=u.quadraticDerivativeAt;o[0]=r(t[0],n[0],i[0],a),o[1]=r(t[1],n[1],i[1],a);var l=s(t[0],n[0],i[0],a),c=s(t[1],n[1],i[1],a);e.rotation=-Math.atan2(c,l)-Math.PI/2,e.ignore=!1},d.updateLayout=function(e,t){this.childAt(0).updateLayout(e,t);var i=e.getItemModel(t).getModel("effect");this._updateEffectAnimation(e,i,t)},o.inherits(c,n.Group);var h=c;e.exports=h},"10cc":function(e,t,i){var n=i("3eba"),a=i("6d8b"),o=i("9850"),r=i("2b8c"),s=i("a890"),l=i("88b3"),u=i("bd9e"),c=["inBrush","outOfBrush"],d="__ecBrushSelect",h="__ecInBrushSelectEvent",p=n.PRIORITY.VISUAL.BRUSH;function g(e,t,i,n,a){if(a){var o=e.getZr();if(!o[h]){o[d]||(o[d]=f);var r=l.createOrUpdate(o,d,i,t);r(e,n)}}}function f(e,t){if(!e.isDisposed()){var i=e.getZr();i[h]=!0,e.dispatchAction({type:"brushSelect",batch:t}),i[h]=!1}}function m(e,t,i,n){for(var a=0,o=t.length;a<o;a++){var r=t[a];if(e[r.brushType](n,i,r.selectors,r))return!0}}function v(e){var t=e.brushSelector;if(a.isString(t)){var i=[];return a.each(s,(function(e,n){i[n]=function(i,n,a,o){var r=n.getItemLayout(i);return e[t](r,a,o)}})),i}if(a.isFunction(t)){var n={};return a.each(s,(function(e,i){n[i]=t})),n}return t}function y(e,t){var i=e.option.seriesIndex;return null!=i&&"all"!==i&&(a.isArray(i)?a.indexOf(i,t)<0:t!==i)}function x(e){var t=e.selectors={};return a.each(s[e.brushType],(function(i,n){t[n]=function(n){return i(n,t,e)}})),e}n.registerLayout(p,(function(e,t,i){e.eachComponent({mainType:"brush"},(function(t){i&&"takeGlobalCursor"===i.type&&t.setBrushOption("brush"===i.key?i.brushOption:{brushType:!1});var n=t.brushTargetManager=new u(t.option,e);n.setInputRanges(t.areas,e)}))})),n.registerVisual(p,(function(e,t,i){var n,o,s=[];e.eachComponent({mainType:"brush"},(function(t,i){var l={brushId:t.id,brushIndex:i,brushName:t.name,areas:a.clone(t.areas),selected:[]};s.push(l);var u=t.option,d=u.brushLink,h=[],p=[],g=[],f=0;i||(n=u.throttleType,o=u.throttleDelay);var b=a.map(t.areas,(function(e){return x(a.defaults({boundingRect:_[e.brushType](e)},e))})),w=r.createVisualMappings(t.option,c,(function(e){e.mappingMethod="fixed"}));function S(e){return"all"===d||h[e]}function M(e){return!!e.length}function I(e,t){var i=e.coordinateSystem;f|=i.hasAxisBrushed(),S(t)&&i.eachActiveState(e.getData(),(function(e,t){"active"===e&&(p[t]=1)}))}function A(i,n,o){var r=v(i);if(r&&!y(t,n)&&(a.each(b,(function(n){r[n.brushType]&&t.brushTargetManager.controlSeries(n,i,e)&&o.push(n),f|=M(o)})),S(n)&&M(o))){var s=i.getData();s.each((function(e){m(r,o,s,e)&&(p[e]=1)}))}}a.isArray(d)&&a.each(d,(function(e){h[e]=1})),e.eachSeries((function(e,t){var i=g[t]=[];"parallel"===e.subType?I(e,t,i):A(e,t,i)})),e.eachSeries((function(e,t){var i={seriesId:e.id,seriesIndex:t,seriesName:e.name,dataIndex:[]};l.selected.push(i);var n=v(e),a=g[t],o=e.getData(),s=S(t)?function(e){return p[e]?(i.dataIndex.push(o.getRawIndex(e)),"inBrush"):"outOfBrush"}:function(e){return m(n,a,o,e)?(i.dataIndex.push(o.getRawIndex(e)),"inBrush"):"outOfBrush"};(S(t)?f:M(a))&&r.applyVisual(c,w,o,s)}))})),g(t,n,o,s,i)}));var _={lineX:a.noop,lineY:a.noop,rect:function(e){return b(e.range)},polygon:function(e){for(var t,i=e.range,n=0,a=i.length;n<a;n++){t=t||[[1/0,-1/0],[1/0,-1/0]];var o=i[n];o[0]<t[0][0]&&(t[0][0]=o[0]),o[0]>t[0][1]&&(t[0][1]=o[0]),o[1]<t[1][0]&&(t[1][0]=o[1]),o[1]>t[1][1]&&(t[1][1]=o[1])}return t&&b(t)}};function b(e){return new o(e[0][0],e[1][0],e[0][1]-e[0][0],e[1][1]-e[1][0])}},1111:function(e,t,i){var n=i("3eba");i("67a8"),i("4784");var a=i("7f96"),o=i("87c3");n.registerVisual(a("effectScatter","circle")),n.registerLayout(o("effectScatter"))},1466:function(e,t,i){var n=i("3eba"),a=i("2306"),o=i("6d8b"),r=i("a15a");function s(e){return o.isArray(e)||(e=[+e,+e]),e}var l=n.extendChartView({type:"radar",render:function(e,t,i){var n=e.coordinateSystem,l=this.group,u=e.getData(),c=this._data;function d(e,t){var i=e.getItemVisual(t,"symbol")||"circle",n=e.getItemVisual(t,"color");if("none"!==i){var a=s(e.getItemVisual(t,"symbolSize")),o=r.createSymbol(i,-1,-1,2,2,n);return o.attr({style:{strokeNoScale:!0},z2:100,scale:[a[0]/2,a[1]/2]}),o}}function h(t,i,n,o,r,s){n.removeAll();for(var l=0;l<i.length-1;l++){var u=d(o,r);u&&(u.__dimIdx=l,t[l]?(u.attr("position",t[l]),a[s?"initProps":"updateProps"](u,{position:i[l]},e,r)):u.attr("position",i[l]),n.add(u))}}function p(e){return o.map(e,(function(e){return[n.cx,n.cy]}))}u.diff(c).add((function(t){var i=u.getItemLayout(t);if(i){var n=new a.Polygon,o=new a.Polyline,r={shape:{points:i}};n.shape.points=p(i),o.shape.points=p(i),a.initProps(n,r,e,t),a.initProps(o,r,e,t);var s=new a.Group,l=new a.Group;s.add(o),s.add(n),s.add(l),h(o.shape.points,i,l,u,t,!0),u.setItemGraphicEl(t,s)}})).update((function(t,i){var n=c.getItemGraphicEl(i),o=n.childAt(0),r=n.childAt(1),s=n.childAt(2),l={shape:{points:u.getItemLayout(t)}};l.shape.points&&(h(o.shape.points,l.shape.points,s,u,t,!1),a.updateProps(o,l,e),a.updateProps(r,l,e),u.setItemGraphicEl(t,n))})).remove((function(e){l.remove(c.getItemGraphicEl(e))})).execute(),u.eachItemGraphicEl((function(e,t){var i=u.getItemModel(t),n=e.childAt(0),r=e.childAt(1),s=e.childAt(2),c=u.getItemVisual(t,"color");l.add(e),n.useStyle(o.defaults(i.getModel("lineStyle").getLineStyle(),{fill:"none",stroke:c})),n.hoverStyle=i.getModel("emphasis.lineStyle").getLineStyle();var d=i.getModel("areaStyle"),h=i.getModel("emphasis.areaStyle"),p=d.isEmpty()&&d.parentModel.isEmpty(),g=h.isEmpty()&&h.parentModel.isEmpty();g=g&&p,r.ignore=p,r.useStyle(o.defaults(d.getAreaStyle(),{fill:c,opacity:.7})),r.hoverStyle=h.getAreaStyle();var f=i.getModel("itemStyle").getItemStyle(["color"]),m=i.getModel("emphasis.itemStyle").getItemStyle(),v=i.getModel("label"),y=i.getModel("emphasis.label");function x(){r.attr("ignore",g)}function _(){r.attr("ignore",p)}s.eachChild((function(e){e.setStyle(f),e.hoverStyle=o.clone(m),a.setLabelStyle(e.style,e.hoverStyle,v,y,{labelFetcher:u.hostModel,labelDataIndex:t,labelDimIndex:e.__dimIdx,defaultText:u.get(u.dimensions[e.__dimIdx],t),autoColor:c,isRectText:!0})})),e.off("mouseover").off("mouseout").off("normal").off("emphasis"),e.on("emphasis",x).on("mouseover",x).on("normal",_).on("mouseout",_),a.setHoverStyle(e)})),this._data=u},remove:function(){this.group.removeAll(),this._data=null},dispose:function(){}});e.exports=l},"15af":function(e,t,i){var n=i("3eba");i("cb69"),i("abff");var a=i("7f96"),o=i("87c3");i("01ed"),n.registerVisual(a("scatter","circle")),n.registerLayout(o("scatter"))},1748:function(e,t,i){var n=i("3eba"),a=i("6d8b"),o=i("71ad"),r=i("4319"),s=i("2023"),l=o.valueAxis;function u(e,t){return a.defaults({show:t},e)}var c=n.extendComponentModel({type:"radar",optionUpdated:function(){var e=this.get("boundaryGap"),t=this.get("splitNumber"),i=this.get("scale"),n=this.get("axisLine"),o=this.get("axisTick"),l=this.get("axisLabel"),u=this.get("name"),c=this.get("name.show"),d=this.get("name.formatter"),h=this.get("nameGap"),p=this.get("triggerEvent"),g=a.map(this.get("indicator")||[],(function(g){null!=g.max&&g.max>0&&!g.min?g.min=0:null!=g.min&&g.min<0&&!g.max&&(g.max=0);var f=u;if(null!=g.color&&(f=a.defaults({color:g.color},u)),g=a.merge(a.clone(g),{boundaryGap:e,splitNumber:t,scale:i,axisLine:n,axisTick:o,axisLabel:l,name:g.text,nameLocation:"end",nameGap:h,nameTextStyle:f,triggerEvent:p},!1),c||(g.name=""),"string"===typeof d){var m=g.name;g.name=d.replace("{value}",null!=m?m:"")}else"function"===typeof d&&(g.name=d(g.name,g));var v=a.extend(new r(g,null,this.ecModel),s);return v.mainType="radar",v.componentIndex=this.componentIndex,v}),this);this.getIndicatorModels=function(){return g}},defaultOption:{zlevel:0,z:0,center:["50%","50%"],radius:"75%",startAngle:90,name:{show:!0},boundaryGap:[0,0],splitNumber:5,nameGap:15,scale:!1,shape:"polygon",axisLine:a.merge({lineStyle:{color:"#bbb"}},l.axisLine),axisLabel:u(l.axisLabel,!1),axisTick:u(l.axisTick,!1),splitLine:u(l.splitLine,!0),splitArea:u(l.splitArea,!0),indicator:[]}}),d=c;e.exports=d},1792:function(e,t){var i={"南海诸岛":[32,80],"广东":[0,-10],"香港":[10,5],"澳门":[-10,10],"天津":[5,5]};function n(e,t){if("china"===e){var n=i[t.name];if(n){var a=t.center;a[0]+=n[0]/10.5,a[1]+=-n[1]/14}}}e.exports=n},"17b8":function(e,t,i){var n=i("3014"),a=n.extend({type:"series.bar",dependencies:["grid","polar"],brushSelector:"rect",getProgressive:function(){return!!this.get("large")&&this.get("progressive")},getProgressiveThreshold:function(){var e=this.get("progressiveThreshold"),t=this.get("largeThreshold");return t>e&&(e=t),e}});e.exports=a},1953:function(e,t,i){var n=i("2449"),a=n.extend({type:"markLine",defaultOption:{zlevel:0,z:5,symbol:["circle","arrow"],symbolSize:[8,16],precision:2,tooltip:{trigger:"item"},label:{show:!0,position:"end"},lineStyle:{type:"dashed"},emphasis:{label:{show:!0},lineStyle:{width:3}},animationEasing:"linear"}});e.exports=a},"19e2":function(e,t,i){var n=i("6d8b"),a=i("e887"),o=i("2306"),r=i("cbe5"),s=["itemStyle"],l=["emphasis","itemStyle"],u=["color","color0","borderColor","borderColor0"],c=a.extend({type:"candlestick",render:function(e,t,i){this._updateDrawMode(e),this._isLargeDraw?this._renderLarge(e):this._renderNormal(e)},incrementalPrepareRender:function(e,t,i){this._clear(),this._updateDrawMode(e)},incrementalRender:function(e,t,i,n){this._isLargeDraw?this._incrementalRenderLarge(e,t):this._incrementalRenderNormal(e,t)},_updateDrawMode:function(e){var t=e.pipelineContext.large;(null==this._isLargeDraw||t^this._isLargeDraw)&&(this._isLargeDraw=t,this._clear())},_renderNormal:function(e){var t=e.getData(),i=this._data,n=this.group,a=t.getLayout("isSimpleBox");this._data||n.removeAll(),t.diff(i).add((function(i){if(t.hasValue(i)){var r,s=t.getItemLayout(i);r=h(s,i,!0),o.initProps(r,{shape:{points:s.ends}},e,i),p(r,t,i,a),n.add(r),t.setItemGraphicEl(i,r)}})).update((function(r,s){var l=i.getItemGraphicEl(s);if(t.hasValue(r)){var u=t.getItemLayout(r);l?o.updateProps(l,{shape:{points:u.ends}},e,r):l=h(u,r),p(l,t,r,a),n.add(l),t.setItemGraphicEl(r,l)}else n.remove(l)})).remove((function(e){var t=i.getItemGraphicEl(e);t&&n.remove(t)})).execute(),this._data=t},_renderLarge:function(e){this._clear(),m(e,this.group)},_incrementalRenderNormal:function(e,t){var i,n=t.getData(),a=n.getLayout("isSimpleBox");while(null!=(i=e.next())){var o,r=n.getItemLayout(i);o=h(r,i),p(o,n,i,a),o.incremental=!0,this.group.add(o)}},_incrementalRenderLarge:function(e,t){m(t,this.group,!0)},remove:function(e){this._clear()},_clear:function(){this.group.removeAll(),this._data=null},dispose:n.noop}),d=r.extend({type:"normalCandlestickBox",shape:{},buildPath:function(e,t){var i=t.points;this.__simpleBox?(e.moveTo(i[4][0],i[4][1]),e.lineTo(i[6][0],i[6][1])):(e.moveTo(i[0][0],i[0][1]),e.lineTo(i[1][0],i[1][1]),e.lineTo(i[2][0],i[2][1]),e.lineTo(i[3][0],i[3][1]),e.closePath(),e.moveTo(i[4][0],i[4][1]),e.lineTo(i[5][0],i[5][1]),e.moveTo(i[6][0],i[6][1]),e.lineTo(i[7][0],i[7][1]))}});function h(e,t,i){var n=e.ends;return new d({shape:{points:i?g(n,e):n},z2:100})}function p(e,t,i,n){var a=t.getItemModel(i),r=a.getModel(s),c=t.getItemVisual(i,"color"),d=t.getItemVisual(i,"borderColor")||c,h=r.getItemStyle(u);e.useStyle(h),e.style.strokeNoScale=!0,e.style.fill=c,e.style.stroke=d,e.__simpleBox=n;var p=a.getModel(l).getItemStyle();o.setHoverStyle(e,p)}function g(e,t){return n.map(e,(function(e){return e=e.slice(),e[1]=t.initBaseline,e}))}var f=r.extend({type:"largeCandlestickBox",shape:{},buildPath:function(e,t){for(var i=t.points,n=0;n<i.length;)if(this.__sign===i[n++]){var a=i[n++];e.moveTo(a,i[n++]),e.lineTo(a,i[n++])}else n+=3}});function m(e,t,i){var n=e.getData(),a=n.getLayout("largePoints"),o=new f({shape:{points:a},__sign:1});t.add(o);var r=new f({shape:{points:a},__sign:-1});t.add(r),v(1,o,e,n),v(-1,r,e,n),i&&(o.incremental=!0,r.incremental=!0)}function v(e,t,i,n){var a=e>0?"P":"N",o=n.getVisual("borderColor"+a)||n.getVisual("color"+a),r=i.getModel(s).getItemStyle(u);t.useStyle(r),t.style.fill=null,t.style.stroke=o}var y=c;e.exports=y},"1ab3":function(e,t,i){var n=i("6d8b"),a=i("2306"),o=i("e887");function r(e,t,i,n){var a=t.getData(),o=this.dataIndex,r=a.getName(o),l=t.get("selectedOffset");n.dispatchAction({type:"pieToggleSelect",from:e,name:r,seriesId:t.id}),a.each((function(e){s(a.getItemGraphicEl(e),a.getItemLayout(e),t.isSelected(a.getName(e)),l,i)}))}function s(e,t,i,n,a){var o=(t.startAngle+t.endAngle)/2,r=Math.cos(o),s=Math.sin(o),l=i?n:0,u=[r*l,s*l];a?e.animate().when(200,{position:u}).start("bounceOut"):e.attr("position",u)}function l(e,t){a.Group.call(this);var i=new a.Sector({z2:2}),n=new a.Polyline,o=new a.Text;function r(){n.ignore=n.hoverIgnore,o.ignore=o.hoverIgnore}function s(){n.ignore=n.normalIgnore,o.ignore=o.normalIgnore}this.add(i),this.add(n),this.add(o),this.updateData(e,t,!0),this.on("emphasis",r).on("normal",s).on("mouseover",r).on("mouseout",s)}var u=l.prototype;u.updateData=function(e,t,i){var o=this.childAt(0),r=e.hostModel,l=e.getItemModel(t),u=e.getItemLayout(t),c=n.extend({},u);if(c.label=null,i){o.setShape(c);var d=r.getShallow("animationType");"scale"===d?(o.shape.r=u.r0,a.initProps(o,{shape:{r:u.r}},r,t)):(o.shape.endAngle=u.startAngle,a.updateProps(o,{shape:{endAngle:u.endAngle}},r,t))}else a.updateProps(o,{shape:c},r,t);var h=e.getItemVisual(t,"color");o.useStyle(n.defaults({lineJoin:"bevel",fill:h},l.getModel("itemStyle").getItemStyle())),o.hoverStyle=l.getModel("emphasis.itemStyle").getItemStyle();var p=l.getShallow("cursor");function g(){o.stopAnimation(!0),o.animateTo({shape:{r:u.r+r.get("hoverOffset")}},300,"elasticOut")}function f(){o.stopAnimation(!0),o.animateTo({shape:{r:u.r}},300,"elasticOut")}p&&o.attr("cursor",p),s(this,e.getItemLayout(t),r.isSelected(null,t),r.get("selectedOffset"),r.get("animation")),o.off("mouseover").off("mouseout").off("emphasis").off("normal"),l.get("hoverAnimation")&&r.isAnimationEnabled()&&o.on("mouseover",g).on("mouseout",f).on("emphasis",g).on("normal",f),this._updateLabel(e,t),a.setHoverStyle(this)},u._updateLabel=function(e,t){var i=this.childAt(1),n=this.childAt(2),o=e.hostModel,r=e.getItemModel(t),s=e.getItemLayout(t),l=s.label,u=e.getItemVisual(t,"color");a.updateProps(i,{shape:{points:l.linePoints||[[l.x,l.y],[l.x,l.y],[l.x,l.y]]}},o,t),a.updateProps(n,{style:{x:l.x,y:l.y}},o,t),n.attr({rotation:l.rotation,origin:[l.x,l.y],z2:10});var c=r.getModel("label"),d=r.getModel("emphasis.label"),h=r.getModel("labelLine"),p=r.getModel("emphasis.labelLine");u=e.getItemVisual(t,"color");a.setLabelStyle(n.style,n.hoverStyle={},c,d,{labelFetcher:e.hostModel,labelDataIndex:t,defaultText:e.getName(t),autoColor:u,useInsideStyle:!!l.inside},{textAlign:l.textAlign,textVerticalAlign:l.verticalAlign,opacity:e.getItemVisual(t,"opacity")}),n.ignore=n.normalIgnore=!c.get("show"),n.hoverIgnore=!d.get("show"),i.ignore=i.normalIgnore=!h.get("show"),i.hoverIgnore=!p.get("show"),i.setStyle({stroke:u,opacity:e.getItemVisual(t,"opacity")}),i.setStyle(h.getModel("lineStyle").getLineStyle()),i.hoverStyle=p.getModel("lineStyle").getLineStyle();var g=h.get("smooth");g&&!0===g&&(g=.4),i.setShape({smooth:g})},n.inherits(l,a.Group);var c=o.extend({type:"pie",init:function(){var e=new a.Group;this._sectorGroup=e},render:function(e,t,i,a){if(!a||a.from!==this.uid){var o=e.getData(),s=this._data,u=this.group,c=t.get("animation"),d=!s,h=e.get("animationType"),p=n.curry(r,this.uid,e,c,i),g=e.get("selectedMode");if(o.diff(s).add((function(e){var t=new l(o,e);d&&"scale"!==h&&t.eachChild((function(e){e.stopAnimation(!0)})),g&&t.on("click",p),o.setItemGraphicEl(e,t),u.add(t)})).update((function(e,t){var i=s.getItemGraphicEl(t);i.updateData(o,e),i.off("click"),g&&i.on("click",p),u.add(i),o.setItemGraphicEl(e,i)})).remove((function(e){var t=s.getItemGraphicEl(e);u.remove(t)})).execute(),c&&d&&o.count()>0&&"scale"!==h){var f=o.getItemLayout(0),m=Math.max(i.getWidth(),i.getHeight())/2,v=n.bind(u.removeClipPath,u);u.setClipPath(this._createClipPath(f.cx,f.cy,m,f.startAngle,f.clockwise,v,e))}else u.removeClipPath();this._data=o}},dispose:function(){},_createClipPath:function(e,t,i,n,o,r,s){var l=new a.Sector({shape:{cx:e,cy:t,r0:0,r:i,startAngle:n,endAngle:n,clockwise:o}});return a.initProps(l,{shape:{endAngle:n+(o?1:-1)*Math.PI*2}},s,r),l},containPoint:function(e,t){var i=t.getData(),n=i.getItemLayout(0);if(n){var a=e[0]-n.cx,o=e[1]-n.cy,r=Math.sqrt(a*a+o*o);return r<=n.r&&r>=n.r0}}}),d=c;e.exports=d},"1c5f":function(e,t,i){var n=i("401b");function a(e){var t=e.coordinateSystem;if(!t||"view"===t.type){var i=e.getGraph();i.eachNode((function(e){var t=e.getModel();e.setLayout([+t.get("x"),+t.get("y")])})),o(i)}}function o(e){e.eachEdge((function(e){var t=e.getModel().get("lineStyle.curveness")||0,i=n.clone(e.node1.getLayout()),a=n.clone(e.node2.getLayout()),o=[i,a];+t&&o.push([(i[0]+a[0])/2-(i[1]-a[1])*t,(i[1]+a[1])/2-(a[0]-i[0])*t]),e.setLayout(o)}))}t.simpleLayout=a,t.simpleLayoutEdge=o},"1f0e":function(e,t,i){var n=i("cbe5"),a=n.extend({type:"echartsGaugePointer",shape:{angle:0,width:10,r:10,x:0,y:0},buildPath:function(e,t){var i=Math.cos,n=Math.sin,a=t.r,o=t.width,r=t.angle,s=t.x-i(r)*o*(o>=a/3?1:2),l=t.y-n(r)*o*(o>=a/3?1:2);r=t.angle-Math.PI/2,e.moveTo(s,l),e.lineTo(t.x+i(r)*o,t.y+n(r)*o),e.lineTo(t.x+i(t.angle)*a,t.y+n(t.angle)*a),e.lineTo(t.x-i(r)*o,t.y-n(r)*o),e.lineTo(s,l)}});e.exports=a},"1f1a":function(e,t,i){var n=i("6d8b"),a=i("e0d3"),o=i("6cb7"),r=i("4319"),s=i("7023"),l=i("eeea"),u=o.extend({type:"geo",coordinateSystem:null,layoutMode:"box",init:function(e){o.prototype.init.apply(this,arguments),a.defaultEmphasis(e,"label",["show"])},optionUpdated:function(){var e=this.option,t=this;e.regions=l.getFilledRegions(e.regions,e.map,e.nameMap),this._optionModelMap=n.reduce(e.regions||[],(function(e,i){return i.name&&e.set(i.name,new r(i,t)),e}),n.createHashMap()),this.updateSelectedMap(e.regions)},defaultOption:{zlevel:0,z:0,show:!0,left:"center",top:"center",aspectScale:null,silent:!1,map:"",boundingCoords:null,center:null,zoom:1,scaleLimit:null,label:{show:!1,color:"#000"},itemStyle:{borderWidth:.5,borderColor:"#444",color:"#eee"},emphasis:{label:{show:!0,color:"rgb(100,0,0)"},itemStyle:{color:"rgba(255,215,0,0.8)"}},regions:[]},getRegionModel:function(e){return this._optionModelMap.get(e)||new r(null,this,this.ecModel)},getFormattedLabel:function(e,t){var i=this.getRegionModel(e),n=i.get("label."+t+".formatter"),a={name:e};return"function"===typeof n?(a.status=t,n(a)):"string"===typeof n?n.replace("{a}",null!=e?e:""):void 0},setZoom:function(e){this.option.zoom=e},setCenter:function(e){this.option.center=e}});n.mixin(u,s);var c=u;e.exports=c},2145:function(e,t){var i={};function n(e,t){i[e]=t}function a(e){return i[e]}t.register=n,t.get=a},2163:function(e,t,i){var n=i("4f85"),a=i("06c7"),o=i("eda2"),r=o.encodeHTML,s=n.extend({type:"series.tree",layoutInfo:null,layoutMode:"box",getInitialData:function(e){var t={name:e.name,children:e.data},i=e.leaves||{},n={};n.leaves=i;var o=a.createTree(t,this,n),r=0;o.eachNode("preorder",(function(e){e.depth>r&&(r=e.depth)}));var s=e.expandAndCollapse,l=s&&e.initialTreeDepth>=0?e.initialTreeDepth:r;return o.root.eachNode("preorder",(function(e){var t=e.hostTree.data.getRawDataItem(e.dataIndex);e.isExpand=t&&null!=t.collapsed?!t.collapsed:e.depth<=l})),o.data},getOrient:function(){var e=this.get("orient");return"horizontal"===e?e="LR":"vertical"===e&&(e="TB"),e},setZoom:function(e){this.option.zoom=e},setCenter:function(e){this.option.center=e},formatTooltip:function(e){var t=this.getData().tree,i=t.root.children[0],n=t.getNodeByDataIndex(e),a=n.getValue(),o=n.name;while(n&&n!==i)o=n.parentNode.name+"."+o,n=n.parentNode;return r(o+(isNaN(a)||null==a?"":" : "+a))},defaultOption:{zlevel:0,z:2,coordinateSystem:"view",left:"12%",top:"12%",right:"12%",bottom:"12%",layout:"orthogonal",roam:!1,nodeScaleRatio:.4,center:null,zoom:1,orient:"LR",symbol:"emptyCircle",symbolSize:7,expandAndCollapse:!0,initialTreeDepth:2,lineStyle:{color:"#ccc",width:1.5,curveness:.5},itemStyle:{color:"lightsteelblue",borderColor:"#c23531",borderWidth:1.5},label:{show:!0,color:"#555"},leaves:{label:{show:!0}},animationEasing:"linear",animationDuration:700,animationDurationUpdate:1e3}});e.exports=s},"217c":function(e,t,i){var n=i("6d8b"),a=i("6cb7");i("df3a");var o=a.extend({type:"parallel",dependencies:["parallelAxis"],coordinateSystem:null,dimensions:null,parallelAxisIndex:null,layoutMode:"box",defaultOption:{zlevel:0,z:0,left:80,top:60,right:80,bottom:60,layout:"horizontal",axisExpandable:!1,axisExpandCenter:null,axisExpandCount:0,axisExpandWidth:50,axisExpandRate:17,axisExpandDebounce:50,axisExpandSlideTriggerArea:[-.15,.05,.4],axisExpandTriggerOn:"click",parallelAxisDefault:null},init:function(){a.prototype.init.apply(this,arguments),this.mergeOption({})},mergeOption:function(e){var t=this.option;e&&n.merge(t,e,!0),this._initDimensions()},contains:function(e,t){var i=e.get("parallelIndex");return null!=i&&t.getComponent("parallel",i)===this},setAxisExpand:function(e){n.each(["axisExpandable","axisExpandCenter","axisExpandCount","axisExpandWidth","axisExpandWindow"],(function(t){e.hasOwnProperty(t)&&(this.option[t]=e[t])}),this)},_initDimensions:function(){var e=this.dimensions=[],t=this.parallelAxisIndex=[],i=n.filter(this.dependentModels.parallelAxis,(function(e){return(e.get("parallelIndex")||0)===this.componentIndex}),this);n.each(i,(function(i){e.push("dim"+i.get("dim")),t.push(i.componentIndex)}))}});e.exports=o},"22da":function(e,t,i){var n=i("f934");function a(e){e.hierNode={defaultAncestor:null,ancestor:e,prelim:0,modifier:0,change:0,shift:0,i:0,thread:null};var t,i,n=[e];while(t=n.pop())if(i=t.children,t.isExpand&&i.length)for(var a=i.length,o=a-1;o>=0;o--){var r=i[o];r.hierNode={defaultAncestor:null,ancestor:r,prelim:0,modifier:0,change:0,shift:0,i:o,thread:null},n.push(r)}}function o(e,t){var i=e.isExpand?e.children:[],n=e.parentNode.children,a=e.hierNode.i?n[e.hierNode.i-1]:null;if(i.length){c(e);var o=(i[0].hierNode.prelim+i[i.length-1].hierNode.prelim)/2;a?(e.hierNode.prelim=a.hierNode.prelim+t(e,a),e.hierNode.modifier=e.hierNode.prelim-o):e.hierNode.prelim=o}else a&&(e.hierNode.prelim=a.hierNode.prelim+t(e,a));e.parentNode.hierNode.defaultAncestor=d(e,a,e.parentNode.hierNode.defaultAncestor||n[0],t)}function r(e){var t=e.hierNode.prelim+e.parentNode.hierNode.modifier;e.setLayout({x:t},!0),e.hierNode.modifier+=e.parentNode.hierNode.modifier}function s(e){return arguments.length?e:m}function l(e,t){var i={};return e-=Math.PI/2,i.x=t*Math.cos(e),i.y=t*Math.sin(e),i}function u(e,t){return n.getLayoutRect(e.getBoxLayoutParams(),{width:t.getWidth(),height:t.getHeight()})}function c(e){var t=e.children,i=t.length,n=0,a=0;while(--i>=0){var o=t[i];o.hierNode.prelim+=n,o.hierNode.modifier+=n,a+=o.hierNode.change,n+=o.hierNode.shift+a}}function d(e,t,i,n){if(t){var a=e,o=e,r=o.parentNode.children[0],s=t,l=a.hierNode.modifier,u=o.hierNode.modifier,c=r.hierNode.modifier,d=s.hierNode.modifier;while(s=h(s),o=p(o),s&&o){a=h(a),r=p(r),a.hierNode.ancestor=e;var m=s.hierNode.prelim+d-o.hierNode.prelim-u+n(s,o);m>0&&(f(g(s,e,i),e,m),u+=m,l+=m),d+=s.hierNode.modifier,u+=o.hierNode.modifier,l+=a.hierNode.modifier,c+=r.hierNode.modifier}s&&!h(a)&&(a.hierNode.thread=s,a.hierNode.modifier+=d-l),o&&!p(r)&&(r.hierNode.thread=o,r.hierNode.modifier+=u-c,i=e)}return i}function h(e){var t=e.children;return t.length&&e.isExpand?t[t.length-1]:e.hierNode.thread}function p(e){var t=e.children;return t.length&&e.isExpand?t[0]:e.hierNode.thread}function g(e,t,i){return e.hierNode.ancestor.parentNode===t.parentNode?e.hierNode.ancestor:i}function f(e,t,i){var n=i/(t.hierNode.i-e.hierNode.i);t.hierNode.change-=n,t.hierNode.shift+=i,t.hierNode.modifier+=i,t.hierNode.prelim+=i,e.hierNode.change+=n}function m(e,t){return e.parentNode===t.parentNode?1:2}t.init=a,t.firstWalk=o,t.secondWalk=r,t.separation=s,t.radialCoordinate=l,t.getViewRect=u},2325:function(e,t,i){var n=i("6d8b"),a=i("607d"),o=i("2306"),r=i("88b3"),s=i("7dcf"),l=i("3842"),u=i("f934"),c=i("ef6a"),d=o.Rect,h=l.linearMap,p=l.asc,g=n.bind,f=n.each,m=7,v=1,y=30,x="horizontal",_="vertical",b=5,w=["line","bar","candlestick","scatter"],S=s.extend({type:"dataZoom.slider",init:function(e,t){this._displayables={},this._orient,this._range,this._handleEnds,this._size,this._handleWidth,this._handleHeight,this._location,this._dragging,this._dataShadowInfo,this.api=t},render:function(e,t,i,n){S.superApply(this,"render",arguments),r.createOrUpdate(this,"_dispatchZoomAction",this.dataZoomModel.get("throttle"),"fixRate"),this._orient=e.get("orient"),!1!==this.dataZoomModel.get("show")?(n&&"dataZoom"===n.type&&n.from===this.uid||this._buildView(),this._updateView()):this.group.removeAll()},remove:function(){S.superApply(this,"remove",arguments),r.clear(this,"_dispatchZoomAction")},dispose:function(){S.superApply(this,"dispose",arguments),r.clear(this,"_dispatchZoomAction")},_buildView:function(){var e=this.group;e.removeAll(),this._resetLocation(),this._resetInterval();var t=this._displayables.barGroup=new o.Group;this._renderBackground(),this._renderHandle(),this._renderDataShadow(),e.add(t),this._positionGroup()},_resetLocation:function(){var e=this.dataZoomModel,t=this.api,i=this._findCoordRect(),a={width:t.getWidth(),height:t.getHeight()},o=this._orient===x?{right:a.width-i.x-i.width,top:a.height-y-m,width:i.width,height:y}:{right:m,top:i.y,width:y,height:i.height},r=u.getLayoutParams(e.option);n.each(["right","top","width","height"],(function(e){"ph"===r[e]&&(r[e]=o[e])}));var s=u.getLayoutRect(r,a,e.padding);this._location={x:s.x,y:s.y},this._size=[s.width,s.height],this._orient===_&&this._size.reverse()},_positionGroup:function(){var e=this.group,t=this._location,i=this._orient,n=this.dataZoomModel.getFirstTargetAxisModel(),a=n&&n.get("inverse"),o=this._displayables.barGroup,r=(this._dataShadowInfo||{}).otherAxisInverse;o.attr(i!==x||a?i===x&&a?{scale:r?[-1,1]:[-1,-1]}:i!==_||a?{scale:r?[-1,-1]:[-1,1],rotation:Math.PI/2}:{scale:r?[1,-1]:[1,1],rotation:Math.PI/2}:{scale:r?[1,1]:[1,-1]});var s=e.getBoundingRect([o]);e.attr("position",[t.x-s.x,t.y-s.y])},_getViewExtent:function(){return[0,this._size[0]]},_renderBackground:function(){var e=this.dataZoomModel,t=this._size,i=this._displayables.barGroup;i.add(new d({silent:!0,shape:{x:0,y:0,width:t[0],height:t[1]},style:{fill:e.get("backgroundColor")},z2:-40})),i.add(new d({shape:{x:0,y:0,width:t[0],height:t[1]},style:{fill:"transparent"},z2:0,onclick:n.bind(this._onClickPanelClick,this)}))},_renderDataShadow:function(){var e=this._dataShadowInfo=this._prepareDataShadowInfo();if(e){var t=this._size,i=e.series,a=i.getRawData(),r=i.getShadowDim?i.getShadowDim():e.otherDim;if(null!=r){var s=a.getDataExtent(r),l=.3*(s[1]-s[0]);s=[s[0]-l,s[1]+l];var u,c=[0,t[1]],d=[0,t[0]],p=[[t[0],0],[0,0]],g=[],f=d[1]/(a.count()-1),m=0,v=Math.round(a.count()/t[0]);a.each([r],(function(e,t){if(v>0&&t%v)m+=f;else{var i=null==e||isNaN(e)||""===e,n=i?0:h(e,s,c,!0);i&&!u&&t?(p.push([p[p.length-1][0],0]),g.push([g[g.length-1][0],0])):!i&&u&&(p.push([m,0]),g.push([m,0])),p.push([m,n]),g.push([m,n]),m+=f,u=i}}));var y=this.dataZoomModel;this._displayables.barGroup.add(new o.Polygon({shape:{points:p},style:n.defaults({fill:y.get("dataBackgroundColor")},y.getModel("dataBackground.areaStyle").getAreaStyle()),silent:!0,z2:-20})),this._displayables.barGroup.add(new o.Polyline({shape:{points:g},style:y.getModel("dataBackground.lineStyle").getLineStyle(),silent:!0,z2:-19}))}}},_prepareDataShadowInfo:function(){var e=this.dataZoomModel,t=e.get("showDataShadow");if(!1!==t){var i,a=this.ecModel;return e.eachTargetAxis((function(o,r){var s=e.getAxisProxy(o.name,r).getTargetSeriesModels();n.each(s,(function(e){if(!i&&!(!0!==t&&n.indexOf(w,e.get("type"))<0)){var s,l=a.getComponent(o.axis,r).axis,u=M(o.name),c=e.coordinateSystem;null!=u&&c.getOtherAxis&&(s=c.getOtherAxis(l).inverse),u=e.getData().mapDimension(u),i={thisAxis:l,series:e,thisDim:o.name,otherDim:u,otherAxisInverse:s}}}),this)}),this),i}},_renderHandle:function(){var e=this._displayables,t=e.handles=[],i=e.handleLabels=[],n=this._displayables.barGroup,r=this._size,s=this.dataZoomModel;n.add(e.filler=new d({draggable:!0,cursor:I(this._orient),drift:g(this._onDragMove,this,"all"),onmousemove:function(e){a.stop(e.event)},ondragstart:g(this._showDataInfo,this,!0),ondragend:g(this._onDragEnd,this),onmouseover:g(this._showDataInfo,this,!0),onmouseout:g(this._showDataInfo,this,!1),style:{fill:s.get("fillerColor"),textPosition:"inside"}})),n.add(new d(o.subPixelOptimizeRect({silent:!0,shape:{x:0,y:0,width:r[0],height:r[1]},style:{stroke:s.get("dataBackgroundColor")||s.get("borderColor"),lineWidth:v,fill:"rgba(0,0,0,0)"}}))),f([0,1],(function(e){var r=o.createIcon(s.get("handleIcon"),{cursor:I(this._orient),draggable:!0,drift:g(this._onDragMove,this,e),onmousemove:function(e){a.stop(e.event)},ondragend:g(this._onDragEnd,this),onmouseover:g(this._showDataInfo,this,!0),onmouseout:g(this._showDataInfo,this,!1)},{x:-1,y:0,width:2,height:2}),u=r.getBoundingRect();this._handleHeight=l.parsePercent(s.get("handleSize"),this._size[1]),this._handleWidth=u.width/u.height*this._handleHeight,r.setStyle(s.getModel("handleStyle").getItemStyle());var c=s.get("handleColor");null!=c&&(r.style.fill=c),n.add(t[e]=r);var d=s.textStyleModel;this.group.add(i[e]=new o.Text({silent:!0,invisible:!0,style:{x:0,y:0,text:"",textVerticalAlign:"middle",textAlign:"center",textFill:d.getTextColor(),textFont:d.getFont()},z2:10}))}),this)},_resetInterval:function(){var e=this._range=this.dataZoomModel.getPercentRange(),t=this._getViewExtent();this._handleEnds=[h(e[0],[0,100],t,!0),h(e[1],[0,100],t,!0)]},_updateInterval:function(e,t){var i=this.dataZoomModel,n=this._handleEnds,a=this._getViewExtent(),o=i.findRepresentativeAxisProxy().getMinMaxSpan(),r=[0,100];c(t,n,a,i.get("zoomLock")?"all":e,null!=o.minSpan?h(o.minSpan,r,a,!0):null,null!=o.maxSpan?h(o.maxSpan,r,a,!0):null);var s=this._range,l=this._range=p([h(n[0],a,r,!0),h(n[1],a,r,!0)]);return!s||s[0]!==l[0]||s[1]!==l[1]},_updateView:function(e){var t=this._displayables,i=this._handleEnds,n=p(i.slice()),a=this._size;f([0,1],(function(e){var n=t.handles[e],o=this._handleHeight;n.attr({scale:[o/2,o/2],position:[i[e],a[1]/2-o/2]})}),this),t.filler.setShape({x:n[0],y:0,width:n[1]-n[0],height:a[1]}),this._updateDataInfo(e)},_updateDataInfo:function(e){var t=this.dataZoomModel,i=this._displayables,n=i.handleLabels,a=this._orient,r=["",""];if(t.get("showDetail")){var s=t.findRepresentativeAxisProxy();if(s){var l=s.getAxisModel().axis,u=this._range,c=e?s.calculateDataWindow({start:u[0],end:u[1]}).valueWindow:s.getDataValueWindow();r=[this._formatLabel(c[0],l),this._formatLabel(c[1],l)]}}var d=p(this._handleEnds.slice());function h(e){var t=o.getTransform(i.handles[e].parent,this.group),s=o.transformDirection(0===e?"right":"left",t),l=this._handleWidth/2+b,u=o.applyTransform([d[e]+(0===e?-l:l),this._size[1]/2],t);n[e].setStyle({x:u[0],y:u[1],textVerticalAlign:a===x?"middle":s,textAlign:a===x?s:"center",text:r[e]})}h.call(this,0),h.call(this,1)},_formatLabel:function(e,t){var i=this.dataZoomModel,a=i.get("labelFormatter"),o=i.get("labelPrecision");null!=o&&"auto"!==o||(o=t.getPixelPrecision());var r=null==e||isNaN(e)?"":"category"===t.type||"time"===t.type?t.scale.getLabel(Math.round(e)):e.toFixed(Math.min(o,20));return n.isFunction(a)?a(e,r):n.isString(a)?a.replace("{value}",r):r},_showDataInfo:function(e){e=this._dragging||e;var t=this._displayables.handleLabels;t[0].attr("invisible",!e),t[1].attr("invisible",!e)},_onDragMove:function(e,t,i){this._dragging=!0;var n=this._displayables.barGroup.getLocalTransform(),a=o.applyTransform([t,i],n,!0),r=this._updateInterval(e,a[0]),s=this.dataZoomModel.get("realtime");this._updateView(!s),r&&s&&this._dispatchZoomAction()},_onDragEnd:function(){this._dragging=!1,this._showDataInfo(!1);var e=this.dataZoomModel.get("realtime");!e&&this._dispatchZoomAction()},_onClickPanelClick:function(e){var t=this._size,i=this._displayables.barGroup.transformCoordToLocal(e.offsetX,e.offsetY);if(!(i[0]<0||i[0]>t[0]||i[1]<0||i[1]>t[1])){var n=this._handleEnds,a=(n[0]+n[1])/2,o=this._updateInterval("all",i[0]-a);this._updateView(),o&&this._dispatchZoomAction()}},_dispatchZoomAction:function(){var e=this._range;this.api.dispatchAction({type:"dataZoom",from:this.uid,dataZoomId:this.dataZoomModel.id,start:e[0],end:e[1]})},_findCoordRect:function(){var e;if(f(this.getTargetCoordInfo(),(function(t){if(!e&&t.length){var i=t[0].model.coordinateSystem;e=i.getRect&&i.getRect()}})),!e){var t=this.api.getWidth(),i=this.api.getHeight();e={x:.2*t,y:.2*i,width:.6*t,height:.6*i}}return e}});function M(e){var t={x:"y",y:"x",radius:"angle",angle:"radius"};return t[e]}function I(e){return"vertical"===e?"ns-resize":"ew-resize"}var A=S;e.exports=A},"237f":function(e,t,i){var n=i("6d8b"),a=i("6179"),o=i("7368"),r=i("31d9"),s=i("b1d4"),l=i("2039"),u=i("3301");function c(e,t,i,c,d){for(var h=new o(c),p=0;p<e.length;p++)h.addNode(n.retrieve(e[p].id,e[p].name,p),p);var g=[],f=[],m=0;for(p=0;p<t.length;p++){var v=t[p],y=v.source,x=v.target;h.addEdge(y,x,m)&&(f.push(v),g.push(n.retrieve(v.id,y+" > "+x)),m++)}var _,b=i.get("coordinateSystem");if("cartesian2d"===b||"polar"===b)_=u(e,i);else{var w=l.get(b),S=w&&"view"!==w.type&&w.dimensions||[];n.indexOf(S,"value")<0&&S.concat(["value"]);var M=s(e,{coordDimensions:S});_=new a(M,i),_.initData(e)}var I=new a(["value"],i);return I.initData(f,g),d&&d(_,I),r({mainData:_,struct:h,structAttr:"graph",datas:{node:_,edge:I},datasAttr:{node:"data",edge:"edgeData"}}),h.update(),h}e.exports=c},"23e0":function(e,t,i){var n=i("6d8b"),a=i("7887"),o=i("89e3"),r=i("3842"),s=i("697e"),l=s.getScaleExtent,u=s.niceScaleExtent,c=i("2039");function d(e,t,i){this._model=e,this.dimensions=[],this._indicatorAxes=n.map(e.getIndicatorModels(),(function(e,t){var i="indicator_"+t,n=new a(i,new o);return n.name=e.get("name"),n.model=e,e.axis=n,this.dimensions.push(i),n}),this),this.resize(e,i),this.cx,this.cy,this.r,this.r0,this.startAngle}d.prototype.getIndicatorAxes=function(){return this._indicatorAxes},d.prototype.dataToPoint=function(e,t){var i=this._indicatorAxes[t];return this.coordToPoint(i.dataToCoord(e),t)},d.prototype.coordToPoint=function(e,t){var i=this._indicatorAxes[t],n=i.angle,a=this.cx+e*Math.cos(n),o=this.cy-e*Math.sin(n);return[a,o]},d.prototype.pointToData=function(e){var t=e[0]-this.cx,i=e[1]-this.cy,n=Math.sqrt(t*t+i*i);t/=n,i/=n;for(var a,o=Math.atan2(-i,t),r=1/0,s=-1,l=0;l<this._indicatorAxes.length;l++){var u=this._indicatorAxes[l],c=Math.abs(o-u.angle);c<r&&(a=u,s=l,r=c)}return[s,+(a&&a.coodToData(n))]},d.prototype.resize=function(e,t){var i=e.get("center"),a=t.getWidth(),o=t.getHeight(),s=Math.min(a,o)/2;this.cx=r.parsePercent(i[0],a),this.cy=r.parsePercent(i[1],o),this.startAngle=e.get("startAngle")*Math.PI/180;var l=e.get("radius");"string"!==typeof l&&"number"!==typeof l||(l=[0,l]),this.r0=r.parsePercent(l[0],s),this.r=r.parsePercent(l[1],s),n.each(this._indicatorAxes,(function(e,t){e.setExtent(this.r0,this.r);var i=this.startAngle+t*Math.PI*2/this._indicatorAxes.length;i=Math.atan2(Math.sin(i),Math.cos(i)),e.angle=i}),this)},d.prototype.update=function(e,t){var i=this._indicatorAxes,a=this._model;n.each(i,(function(e){e.scale.setExtent(1/0,-1/0)})),e.eachSeriesByType("radar",(function(t,o){if("radar"===t.get("coordinateSystem")&&e.getComponent("radar",t.get("radarIndex"))===a){var r=t.getData();n.each(i,(function(e){e.scale.unionExtentFromData(r,r.mapDimension(e.dim))}))}}),this);var o=a.get("splitNumber");function s(e){var t=Math.pow(10,Math.floor(Math.log(e)/Math.LN10)),i=e/t;return 2===i?i=5:i*=2,i*t}n.each(i,(function(e,t){var i=l(e.scale,e.model);u(e.scale,e.model);var n=e.model,a=e.scale,c=n.getMin(),d=n.getMax(),h=a.getInterval();if(null!=c&&null!=d)a.setExtent(+c,+d),a.setInterval((d-c)/o);else if(null!=c){var p;do{p=c+h*o,a.setExtent(+c,p),a.setInterval(h),h=s(h)}while(p<i[1]&&isFinite(p)&&isFinite(i[1]))}else if(null!=d){var g;do{g=d-h*o,a.setExtent(g,+d),a.setInterval(h),h=s(h)}while(g>i[0]&&isFinite(g)&&isFinite(i[0]))}else{var f=a.getTicks().length-1;f>o&&(h=s(h));var m=Math.round((i[0]+i[1])/2/h)*h,v=Math.round(o/2);a.setExtent(r.round(m-v*h),r.round(m+(o-v)*h)),a.setInterval(h)}}))},d.dimensions=[],d.create=function(e,t){var i=[];return e.eachComponent("radar",(function(n){var a=new d(n,e,t);i.push(a),n.coordinateSystem=a})),e.eachSeriesByType("radar",(function(e){"radar"===e.get("coordinateSystem")&&(e.coordinateSystem=i[e.get("radarIndex")||0])})),i},c.register("radar",d);var h=d;e.exports=h},"23ee":function(e,t,i){var n=i("3eba");i("879e"),i("9704"),i("d747");var a=i("675a"),o=i("7f96"),r=i("2943"),s=i("de6e"),l=i("d357"),u=i("adda"),c=i("5866"),d=i("7b0c");n.registerProcessor(a),n.registerVisual(o("graph","circle",null)),n.registerVisual(r),n.registerVisual(s),n.registerLayout(l),n.registerLayout(u),n.registerLayout(c),n.registerCoordinateSystem("graphView",{create:d})},2449:function(e,t,i){var n=i("4e08"),a=(n.__DEV__,i("3eba")),o=i("6d8b"),r=i("22d1"),s=i("e0d3"),l=i("eda2"),u=i("38a2"),c=l.addCommas,d=l.encodeHTML;function h(e){s.defaultEmphasis(e,"label",["show"])}var p=a.extendComponentModel({type:"marker",dependencies:["series","grid","polar","geo"],init:function(e,t,i,n){this.mergeDefaultAndTheme(e,i),this.mergeOption(e,i,n.createdBySelf,!0)},isAnimationEnabled:function(){if(r.node)return!1;var e=this.__hostSeries;return this.getShallow("animation")&&e&&e.isAnimationEnabled()},mergeOption:function(e,t,i,n){var a=this.constructor,r=this.mainType+"Model";i||t.eachSeries((function(e){var i=e.get(this.mainType,!0),s=e[r];i&&i.data?(s?s.mergeOption(i,t,!0):(n&&h(i),o.each(i.data,(function(e){e instanceof Array?(h(e[0]),h(e[1])):h(e)})),s=new a(i,this,t),o.extend(s,{mainType:this.mainType,seriesIndex:e.seriesIndex,name:e.name,createdBySelf:!0}),s.__hostSeries=e),e[r]=s):e[r]=null}),this)},formatTooltip:function(e){var t=this.getData(),i=this.getRawValue(e),n=o.isArray(i)?o.map(i,c).join(", "):c(i),a=t.getName(e),r=d(this.name);return(null!=i||a)&&(r+="<br />"),a&&(r+=d(a),null!=i&&(r+=" : ")),null!=i&&(r+=d(n)),r},getData:function(){return this._data},setData:function(e){this._data=e}});o.mixin(p,u);var g=p;e.exports=g},"24b9":function(e,t,i){var n=i("f934"),a=i("3842"),o=a.parsePercent,r=a.linearMap;function s(e,t){return n.getLayoutRect(e.getBoxLayoutParams(),{width:t.getWidth(),height:t.getHeight()})}function l(e,t){for(var i=e.mapDimension("value"),n=e.mapArray(i,(function(e){return e})),a=[],o="ascending"===t,r=0,s=e.count();r<s;r++)a[r]=r;return"function"===typeof t?a.sort(t):"none"!==t&&a.sort((function(e,t){return o?n[e]-n[t]:n[t]-n[e]})),a}function u(e){e.each((function(t){var i,n,a,o,r=e.getItemModel(t),s=r.getModel("label"),l=s.get("position"),u=r.getModel("labelLine"),c=e.getItemLayout(t),d=c.points,h="inner"===l||"inside"===l||"center"===l;if(h)n=(d[0][0]+d[1][0]+d[2][0]+d[3][0])/4,a=(d[0][1]+d[1][1]+d[2][1]+d[3][1])/4,i="center",o=[[n,a],[n,a]];else{var p,g,f,m=u.get("length");"left"===l?(p=(d[3][0]+d[0][0])/2,g=(d[3][1]+d[0][1])/2,f=p-m,n=f-5,i="right"):(p=(d[1][0]+d[2][0])/2,g=(d[1][1]+d[2][1])/2,f=p+m,n=f+5,i="left");var v=g;o=[[p,g],[f,v]],a=v}c.label={linePoints:o,x:n,y:a,verticalAlign:"middle",textAlign:i,inside:h}}))}function c(e,t,i){e.eachSeriesByType("funnel",(function(e){var i=e.getData(),n=i.mapDimension("value"),a=e.get("sort"),c=s(e,t),d=l(i,a),h=[o(e.get("minSize"),c.width),o(e.get("maxSize"),c.width)],p=i.getDataExtent(n),g=e.get("min"),f=e.get("max");null==g&&(g=Math.min(p[0],0)),null==f&&(f=p[1]);var m=e.get("funnelAlign"),v=e.get("gap"),y=(c.height-v*(i.count()-1))/i.count(),x=c.y,_=function(e,t){var a,o=i.get(n,e)||0,s=r(o,[g,f],h,!0);switch(m){case"left":a=c.x;break;case"center":a=c.x+(c.width-s)/2;break;case"right":a=c.x+c.width-s;break}return[[a,t],[a+s,t]]};"ascending"===a&&(y=-y,v=-v,x+=c.height,d=d.reverse());for(var b=0;b<d.length;b++){var w=d[b],S=d[b+1],M=i.getItemModel(w),I=M.get("itemStyle.height");null==I?I=y:(I=o(I,c.height),"ascending"===a&&(I=-I));var A=_(w,x),T=_(S,x+I);x+=I+v,i.setItemLayout(w,{points:A.concat(T.slice().reverse())})}u(i)}))}e.exports=c},"255c":function(e,t,i){var n=i("3eba"),a=i("d4d1"),o=a.Polygon,r=i("2306"),s=i("6d8b"),l=s.bind,u=s.extend,c=i("80f0"),d=n.extendChartView({type:"themeRiver",init:function(){this._layers=[]},render:function(e,t,i){var n=e.getData(),a=this.group,s=e.getLayerSeries(),d=n.getLayout("layoutInfo"),p=d.rect,g=d.boundaryGap;function f(e){return e.name}a.attr("position",[0,p.y+g[0]]);var m=new c(this._layersSeries||[],s,f,f),v={};function y(t,i,l){var c=this._layers;if("remove"!==t){for(var d,p,g,f=[],m=[],y=s[i].indices,x=0;x<y.length;x++){var _=n.getItemLayout(y[x]),b=_.x,w=_.y0,S=_.y;f.push([b,w]),m.push([b,w+S]),d=n.getItemVisual(y[x],"color")}var M=n.getItemLayout(y[0]),I=n.getItemModel(y[x-1]),A=I.getModel("label"),T=A.get("margin");if("add"===t){var D=v[i]=new r.Group;p=new o({shape:{points:f,stackedOnPoints:m,smooth:.4,stackedOnSmooth:.4,smoothConstraint:!1},z2:0}),g=new r.Text({style:{x:M.x-T,y:M.y0+M.y/2}}),D.add(p),D.add(g),a.add(D),p.setClipPath(h(p.getBoundingRect(),e,(function(){p.removeClipPath()})))}else{D=c[l];p=D.childAt(0),g=D.childAt(1),a.add(D),v[i]=D,r.updateProps(p,{shape:{points:f,stackedOnPoints:m}},e),r.updateProps(g,{style:{x:M.x-T,y:M.y0+M.y/2}},e)}var L=I.getModel("emphasis.itemStyle"),C=I.getModel("itemStyle");r.setTextStyle(g.style,A,{text:A.get("show")?e.getFormattedLabel(y[x-1],"normal")||n.getName(y[x-1]):null,textVerticalAlign:"middle"}),p.setStyle(u({fill:d},C.getItemStyle(["color"]))),r.setHoverStyle(p,L.getItemStyle())}else a.remove(c[i])}m.add(l(y,this,"add")).update(l(y,this,"update")).remove(l(y,this,"remove")).execute(),this._layersSeries=s,this._layers=v},dispose:function(){}});function h(e,t,i){var n=new r.Rect({shape:{x:e.x-10,y:e.y-10,width:0,height:e.height+20}});return r.initProps(n,{shape:{width:e.width+20,height:e.height+20}},t,i),n}e.exports=d},"292e":function(e,t,i){var n=i("3842"),a=n.parsePercent,o=n.linearMap,r=i("bb70"),s=i("6d8b"),l=2*Math.PI,u=Math.PI/180;function c(e,t,i,n){t.eachSeriesByType(e,(function(e){var t=e.getData(),n=t.mapDimension("value"),c=e.get("center"),d=e.get("radius");s.isArray(d)||(d=[0,d]),s.isArray(c)||(c=[c,c]);var h=i.getWidth(),p=i.getHeight(),g=Math.min(h,p),f=a(c[0],h),m=a(c[1],p),v=a(d[0],g/2),y=a(d[1],g/2),x=-e.get("startAngle")*u,_=e.get("minAngle")*u,b=0;t.each(n,(function(e){!isNaN(e)&&b++}));var w=t.getSum(n),S=Math.PI/(w||b)*2,M=e.get("clockwise"),I=e.get("roseType"),A=e.get("stillShowZeroSum"),T=t.getDataExtent(n);T[0]=0;var D=l,L=0,C=x,P=M?1:-1;if(t.each(n,(function(e,i){var n;if(isNaN(e))t.setItemLayout(i,{angle:NaN,startAngle:NaN,endAngle:NaN,clockwise:M,cx:f,cy:m,r0:v,r:I?NaN:y});else{n="area"!==I?0===w&&A?S:e*S:l/b,n<_?(n=_,D-=_):L+=e;var a=C+P*n;t.setItemLayout(i,{angle:n,startAngle:C,endAngle:a,clockwise:M,cx:f,cy:m,r0:v,r:I?o(e,T,[v,y]):y}),C=a}})),D<l&&b)if(D<=.001){var N=l/b;t.each(n,(function(e,i){if(!isNaN(e)){var n=t.getItemLayout(i);n.angle=N,n.startAngle=x+P*i*N,n.endAngle=x+P*(i+1)*N}}))}else S=D/L,C=x,t.each(n,(function(e,i){if(!isNaN(e)){var n=t.getItemLayout(i),a=n.angle===_?_:e*S;n.startAngle=C,n.endAngle=C+P*a,C+=P*a}}));r(e,y,h,p)}))}e.exports=c},2943:function(e,t){function i(e){var t={};e.eachSeriesByType("graph",(function(e){var i=e.getCategoriesData(),n=e.getData(),a={};i.each((function(n){var o=i.getName(n);a["ec-"+o]=n;var r=i.getItemModel(n),s=r.get("itemStyle.color")||e.getColorFromPalette(o,t);i.setItemVisual(n,"color",s)})),i.count()&&n.each((function(e){var t=n.getItemModel(e),o=t.getShallow("category");null!=o&&("string"===typeof o&&(o=a["ec-"+o]),n.getItemVisual(e,"color",!0)||n.setItemVisual(e,"color",i.getItemVisual(o,"color")))}))}))}e.exports=i},"29a9":function(e,t,i){var n=i("3eba"),a=i("b336");i("bc5f"),i("ab05"),i("06ea"),i("004f"),i("d6ef"),n.registerPreprocessor(a)},"2b8c":function(e,t,i){var n=i("6d8b"),a=i("5f14"),o=n.each;function r(e){if(e)for(var t in e)if(e.hasOwnProperty(t))return!0}function s(e,t,i){var r={};return o(t,(function(t){var l=r[t]=s();o(e[t],(function(e,o){if(a.isValidType(o)){var r={type:o,visual:e};i&&i(r,t),l[o]=new a(r),"opacity"===o&&(r=n.clone(r),r.type="colorAlpha",l.__hidden.__alphaForOpacity=new a(r))}}))})),r;function s(){var e=function(){};e.prototype.__hidden=e.prototype;var t=new e;return t}}function l(e,t,i){var a;n.each(i,(function(e){t.hasOwnProperty(e)&&r(t[e])&&(a=!0)})),a&&n.each(i,(function(i){t.hasOwnProperty(i)&&r(t[i])?e[i]=n.clone(t[i]):delete e[i]}))}function u(e,t,i,o,r,s){var l,u={};function c(e){return i.getItemVisual(l,e)}function d(e,t){i.setItemVisual(l,e,t)}function h(e,n){l=null==s?e:n;var a=i.getRawDataItem(l);if(!a||!1!==a.visualMap)for(var h=o.call(r,e),p=t[h],g=u[h],f=0,m=g.length;f<m;f++){var v=g[f];p[v]&&p[v].applyVisual(e,c,d)}}n.each(e,(function(e){var i=a.prepareVisualTypes(t[e]);u[e]=i})),null==s?i.each(h):i.each([s],h)}function c(e,t,i,o){var r={};function s(e,n){function a(e){return n.getItemVisual(l,e)}function s(e,t){n.setItemVisual(l,e,t)}var l;null!=o&&(o=n.getDimension(o));while(null!=(l=e.next())){var u=n.getRawDataItem(l);if(!u||!1!==u.visualMap)for(var c=null!=o?n.get(o,l,!0):l,d=i(c),h=t[d],p=r[d],g=0,f=p.length;g<f;g++){var m=p[g];h[m]&&h[m].applyVisual(c,a,s)}}}return n.each(e,(function(e){var i=a.prepareVisualTypes(t[e]);r[e]=i})),{progress:s}}t.createVisualMappings=s,t.replaceVisualOption=l,t.applyVisual=u,t.incrementalApplyVisual=c},"2c17":function(e,t,i){var n=i("3eba"),a=i("6d8b"),o=a.createHashMap,r=a.each;n.registerProcessor({getTargetSeries:function(e){var t=o();return e.eachComponent("dataZoom",(function(e){e.eachTargetAxis((function(e,i,n){var a=n.getAxisProxy(e.name,i);r(a.getTargetSeriesModels(),(function(e){t.set(e.uid,e)}))}))})),t},modifyOutputEnd:!0,overallReset:function(e,t){e.eachComponent("dataZoom",(function(e){e.eachTargetAxis((function(e,i,n){n.getAxisProxy(e.name,i).reset(n,t)})),e.eachTargetAxis((function(e,i,n){n.getAxisProxy(e.name,i).filterData(n,t)}))})),e.eachComponent("dataZoom",(function(e){var t=e.findRepresentativeAxisProxy(),i=t.getDataPercentWindow(),n=t.getDataValueWindow();e.setRawRange({start:i[0],end:i[1],startValue:n[0],endValue:n[1]},!0)}))}})},"2cfc":function(e,t,i){var n=i("3eba");i("4338"),i("bcbe"),i("c62c"),i("cb8f"),i("f138"),n.extendComponentView({type:"single"})},"2f31":function(e,t,i){var n=i("3eba"),a=i("ae75");i("10cc"),i("f31f"),i("c2dd"),i("b8ec"),i("fecb"),n.registerPreprocessor(a)},"2f91":function(e,t){var i=["itemStyle","borderColor"];function n(e,t){var n=e.get("color");e.eachRawSeriesByType("boxplot",(function(t){var a=n[t.seriesIndex%n.length],o=t.getData();o.setVisual({legendSymbol:"roundRect",color:t.get(i)||a}),e.isSeriesFiltered(t)||o.each((function(e){var t=o.getItemModel(e);o.setItemVisual(e,{color:t.get(i,!0)})}))}))}e.exports=n},3014:function(e,t,i){var n=i("4f85"),a=i("3301"),o=n.extend({type:"series.__base_bar__",getInitialData:function(e,t){return a(this.getSource(),this)},getMarkerPosition:function(e){var t=this.coordinateSystem;if(t){var i=t.dataToPoint(t.clampData(e)),n=this.getData(),a=n.getLayout("offset"),o=n.getLayout("size"),r=t.getBaseAxis().isHorizontal()?0:1;return i[r]+=a+o/2,i}return[NaN,NaN]},defaultOption:{zlevel:0,z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,barMinHeight:0,barMinAngle:0,large:!1,largeThreshold:400,progressive:3e3,progressiveChunkMode:"mod",itemStyle:{},emphasis:{}}});e.exports=o},"307a":function(e,t,i){var n=i("6d8b"),a=i("eaea"),o=i("3842"),r=[20,140],s=a.extend({type:"visualMap.continuous",defaultOption:{align:"auto",calculable:!1,range:null,realtime:!0,itemHeight:null,itemWidth:null,hoverLink:!0,hoverLinkDataSize:null,hoverLinkOnHandle:null},optionUpdated:function(e,t){s.superApply(this,"optionUpdated",arguments),this.resetExtent(),this.resetVisual((function(e){e.mappingMethod="linear",e.dataExtent=this.getExtent()})),this._resetRange()},resetItemSize:function(){s.superApply(this,"resetItemSize",arguments);var e=this.itemSize;"horizontal"===this._orient&&e.reverse(),(null==e[0]||isNaN(e[0]))&&(e[0]=r[0]),(null==e[1]||isNaN(e[1]))&&(e[1]=r[1])},_resetRange:function(){var e=this.getExtent(),t=this.option.range;!t||t.auto?(e.auto=1,this.option.range=e):n.isArray(t)&&(t[0]>t[1]&&t.reverse(),t[0]=Math.max(t[0],e[0]),t[1]=Math.min(t[1],e[1]))},completeVisualOption:function(){a.prototype.completeVisualOption.apply(this,arguments),n.each(this.stateList,(function(e){var t=this.option.controller[e].symbolSize;t&&t[0]!==t[1]&&(t[0]=0)}),this)},setSelected:function(e){this.option.range=e.slice(),this._resetRange()},getSelected:function(){var e=this.getExtent(),t=o.asc((this.get("range")||[]).slice());return t[0]>e[1]&&(t[0]=e[1]),t[1]>e[1]&&(t[1]=e[1]),t[0]<e[0]&&(t[0]=e[0]),t[1]<e[0]&&(t[1]=e[0]),t},getValueState:function(e){var t=this.option.range,i=this.getExtent();return(t[0]<=i[0]||t[0]<=e)&&(t[1]>=i[1]||e<=t[1])?"inRange":"outOfRange"},findTargetDataIndices:function(e){var t=[];return this.eachTargetSeries((function(i){var n=[],a=i.getData();a.each(this.getDataDimension(a),(function(t,i){e[0]<=t&&t<=e[1]&&n.push(i)}),this),t.push({seriesId:i.id,dataIndex:n})}),this),t},getVisualMeta:function(e){var t=l(this,"outOfRange",this.getExtent()),i=l(this,"inRange",this.option.range.slice()),n=[];function a(t,i){n.push({value:t,color:e(t,i)})}for(var o=0,r=0,s=i.length,u=t.length;r<u&&(!i.length||t[r]<=i[0]);r++)t[r]<i[o]&&a(t[r],"outOfRange");for(var c=1;o<s;o++,c=0)c&&n.length&&a(i[o],"outOfRange"),a(i[o],"inRange");for(c=1;r<u;r++)(!i.length||i[i.length-1]<t[r])&&(c&&(n.length&&a(n[n.length-1].value,"outOfRange"),c=0),a(t[r],"outOfRange"));var d=n.length;return{stops:n,outerColors:[d?n[0].color:"transparent",d?n[d-1].color:"transparent"]}}});function l(e,t,i){if(i[0]===i[1])return i.slice();for(var n=200,a=(i[1]-i[0])/n,o=i[0],r=[],s=0;s<=n&&o<i[1];s++)r.push(o),o+=a;return r.push(i[1]),r}var u=s;e.exports=u},"307b":function(e,t,i){var n=i("6d8b");function a(e,t){var i=this.getAxis(),n=t instanceof Array?t[0]:t,a=(e instanceof Array?e[0]:e)/2;return"category"===i.type?i.getBandWidth():Math.abs(i.dataToCoord(n-a)-i.dataToCoord(n+a))}function o(e){var t=e.getRect();return{coordSys:{type:"singleAxis",x:t.x,y:t.y,width:t.width,height:t.height},api:{coord:function(t){return e.dataToPoint(t)},size:n.bind(a,e)}}}e.exports=o},"307d":function(e,t,i){var n=i("6d8b"),a=i("6179"),o=i("3842"),r=i("923d"),s=i("73ca"),l=i("88f0"),u=function(e,t,i,a){var o=e.getData(),s=a.type;if(!n.isArray(a)&&("min"===s||"max"===s||"average"===s||"median"===s||null!=a.xAxis||null!=a.yAxis)){var l,u;if(null!=a.yAxis||null!=a.xAxis)l=null!=a.yAxis?"y":"x",t.getAxis(l),u=n.retrieve(a.yAxis,a.xAxis);else{var c=r.getAxisInfo(a,o,t,e);l=c.valueDataDim,c.valueAxis,u=r.numCalculate(o,l,s)}var d="x"===l?0:1,h=1-d,p=n.clone(a),g={};p.type=null,p.coord=[],g.coord=[],p.coord[h]=-1/0,g.coord[h]=1/0;var f=i.get("precision");f>=0&&"number"===typeof u&&(u=+u.toFixed(Math.min(f,20))),p.coord[d]=g.coord[d]=u,a=[p,g,{type:s,valueIndex:a.valueIndex,value:u}]}return a=[r.dataTransform(e,a[0]),r.dataTransform(e,a[1]),n.extend({},a[2])],a[2].type=a[2].type||"",n.merge(a[2],a[0]),n.merge(a[2],a[1]),a};function c(e){return!isNaN(e)&&!isFinite(e)}function d(e,t,i,n){var a=1-e,o=n.dimensions[e];return c(t[a])&&c(i[a])&&t[e]===i[e]&&n.getAxis(o).containData(t[e])}function h(e,t){if("cartesian2d"===e.type){var i=t[0].coord,n=t[1].coord;if(i&&n&&(d(1,i,n,e)||d(0,i,n,e)))return!0}return r.dataFilter(e,t[0])&&r.dataFilter(e,t[1])}function p(e,t,i,n,a){var r,s=n.coordinateSystem,l=e.getItemModel(t),u=o.parsePercent(l.get("x"),a.getWidth()),d=o.parsePercent(l.get("y"),a.getHeight());if(isNaN(u)||isNaN(d)){if(n.getMarkerPosition)r=n.getMarkerPosition(e.getValues(e.dimensions,t));else{var h=s.dimensions,p=e.get(h[0],t),g=e.get(h[1],t);r=s.dataToPoint([p,g])}if("cartesian2d"===s.type){var f=s.getAxis("x"),m=s.getAxis("y");h=s.dimensions;c(e.get(h[0],t))?r[0]=f.toGlobalCoord(f.getExtent()[i?0:1]):c(e.get(h[1],t))&&(r[1]=m.toGlobalCoord(m.getExtent()[i?0:1]))}isNaN(u)||(r[0]=u),isNaN(d)||(r[1]=d)}else r=[u,d];e.setItemLayout(t,r)}var g=l.extend({type:"markLine",updateTransform:function(e,t,i){t.eachSeries((function(e){var t=e.markLineModel;if(t){var n=t.getData(),a=t.__from,o=t.__to;a.each((function(t){p(a,t,!0,e,i),p(o,t,!1,e,i)})),n.each((function(e){n.setItemLayout(e,[a.getItemLayout(e),o.getItemLayout(e)])})),this.markerGroupMap.get(e.id).updateLayout()}}),this)},renderSeries:function(e,t,i,a){var o=e.coordinateSystem,r=e.id,l=e.getData(),u=this.markerGroupMap,c=u.get(r)||u.set(r,new s);this.group.add(c.group);var d=f(o,e,t),h=d.from,g=d.to,m=d.line;t.__from=h,t.__to=g,t.setData(m);var v=t.get("symbol"),y=t.get("symbolSize");function x(t,i,n){var o=t.getItemModel(i);p(t,i,n,e,a),t.setItemVisual(i,{symbolSize:o.get("symbolSize")||y[n?0:1],symbol:o.get("symbol",!0)||v[n?0:1],color:o.get("itemStyle.color")||l.getVisual("color")})}n.isArray(v)||(v=[v,v]),"number"===typeof y&&(y=[y,y]),d.from.each((function(e){x(h,e,!0),x(g,e,!1)})),m.each((function(e){var t=m.getItemModel(e).get("lineStyle.color");m.setItemVisual(e,{color:t||h.getItemVisual(e,"color")}),m.setItemLayout(e,[h.getItemLayout(e),g.getItemLayout(e)]),m.setItemVisual(e,{fromSymbolSize:h.getItemVisual(e,"symbolSize"),fromSymbol:h.getItemVisual(e,"symbol"),toSymbolSize:g.getItemVisual(e,"symbolSize"),toSymbol:g.getItemVisual(e,"symbol")})})),c.updateData(m),d.line.eachItemGraphicEl((function(e,i){e.traverse((function(e){e.dataModel=t}))})),c.__keep=!0,c.group.silent=t.get("silent")||e.get("silent")}});function f(e,t,i){var o;o=e?n.map(e&&e.dimensions,(function(e){var i=t.getData().getDimensionInfo(t.getData().mapDimension(e))||{};return n.defaults({name:e},i)})):[{name:"value",type:"float"}];var s=new a(o,i),l=new a(o,i),c=new a([],i),d=n.map(i.get("data"),n.curry(u,t,e,i));e&&(d=n.filter(d,n.curry(h,e)));var p=e?r.dimValueGetter:function(e){return e.value};return s.initData(n.map(d,(function(e){return e[0]})),null,p),l.initData(n.map(d,(function(e){return e[1]})),null,p),c.initData(n.map(d,(function(e){return e[2]}))),c.hasItemOption=!0,{from:s,to:l,line:c}}e.exports=g},"311a":function(e,t,i){var n=i("3eba");i("d01c"),i("5b69"),i("bdc0");var a=i("81ac"),o=i("0e0f");n.registerLayout(a),n.registerVisual(o)},"313e":function(e,t,i){var n=i("3eba");(function(){for(var e in n){if(null==n||!n.hasOwnProperty(e)||"default"===e||"__esModule"===e)return;t[e]=n[e]}})();var a=i("b719");(function(){for(var e in a){if(null==a||!a.hasOwnProperty(e)||"default"===e||"__esModule"===e)return;t[e]=a[e]}})(),i("0352"),i("ef97"),i("94b1"),i("c037"),i("15af"),i("8deb"),i("675c"),i("ef97a"),i("dae1"),i("23ee"),i("07e6"),i("a4b1"),i("0817"),i("311a"),i("fa52"),i("085d"),i("1111"),i("a7e2"),i("5ce2"),i("aadf"),i("6c12f"),i("d716"),i("e057"),i("7f59"),i("cd12"),i("0b4b"),i("007d"),i("cb8f"),i("2f73"),i("d070"),i("f306"),i("2cfc"),i("2f31"),i("9390"),i("627c"),i("0a6d"),i("5450"),i("db0e"),i("95a8"),i("7e32"),i("ee95"),i("b11c"),i("f170"),i("8ee0")},"31d9":function(e,t,i){var n=i("6d8b"),a=n.each,o="\0__link_datas",r="\0__link_mainData";function s(e){var t=e.mainData,i=e.datas;i||(i={main:t},e.datasAttr={main:"data"}),e.datas=e.mainData=null,p(t,i,e),a(i,(function(i){a(t.TRANSFERABLE_METHODS,(function(t){i.wrapMethod(t,n.curry(l,e))}))})),t.wrapMethod("cloneShallow",n.curry(c,e)),a(t.CHANGABLE_METHODS,(function(i){t.wrapMethod(i,n.curry(u,e))})),n.assert(i[t.dataType]===t)}function l(e,t){if(h(this)){var i=n.extend({},this[o]);i[this.dataType]=t,p(t,i,e)}else g(t,this.dataType,this[r],e);return t}function u(e,t){return e.struct&&e.struct.update(this),t}function c(e,t){return a(t[o],(function(i,n){i!==t&&g(i.cloneShallow(),n,t,e)})),t}function d(e){var t=this[r];return null==e||null==t?t:t[o][e]}function h(e){return e[r]===e}function p(e,t,i){e[o]={},a(t,(function(t,n){g(t,n,e,i)}))}function g(e,t,i,n){i[o][t]=e,e[r]=i,e.dataType=t,n.struct&&(e[n.structAttr]=n.struct,n.struct[n.datasAttr[t]]=e),e.getLinkedData=d}var f=s;e.exports=f},"320a":function(e,t,i){for(var n=i("6d8b"),a=i("f279"),o=[126,25],r=[[[0,3.5],[7,11.2],[15,11.9],[30,7],[42,.7],[52,.7],[56,7.7],[59,.7],[64,.7],[64,0],[5,0],[0,3.5]],[[13,16.1],[19,14.7],[16,21.7],[11,23.1],[13,16.1]],[[12,32.2],[14,38.5],[15,38.5],[13,32.2],[12,32.2]],[[16,47.6],[12,53.2],[13,53.2],[18,47.6],[16,47.6]],[[6,64.4],[8,70],[9,70],[8,64.4],[6,64.4]],[[23,82.6],[29,79.8],[30,79.8],[25,82.6],[23,82.6]],[[37,70.7],[43,62.3],[44,62.3],[39,70.7],[37,70.7]],[[48,51.1],[51,45.5],[53,45.5],[50,51.1],[48,51.1]],[[51,35],[51,28.7],[53,28.7],[53,35],[51,35]],[[52,22.4],[55,17.5],[56,17.5],[53,22.4],[52,22.4]],[[58,12.6],[62,7],[63,7],[60,12.6],[58,12.6]],[[0,3.5],[0,93.1],[64,93.1],[64,0],[63,0],[63,92.4],[1,92.4],[1,3.5],[0,3.5]]],s=0;s<r.length;s++)for(var l=0;l<r[s].length;l++)r[s][l][0]/=10.5,r[s][l][1]/=-14,r[s][l][0]+=o[0],r[s][l][1]+=o[1];function u(e,t){"china"===e&&t.push(new a("南海诸岛",n.map(r,(function(e){return{type:"polygon",exterior:e}})),o))}e.exports=u},"32a1":function(e,t,i){var n=i("6d8b"),a=i("7dcf"),o=i("ef6a"),r=i("5576"),s=n.bind,l=a.extend({type:"dataZoom.inside",init:function(e,t){this._range},render:function(e,t,i,a){l.superApply(this,"render",arguments),this._range=e.getPercentRange(),n.each(this.getTargetCoordInfo(),(function(t,a){var o=n.map(t,(function(e){return r.generateCoordId(e.model)}));n.each(t,(function(t){var l=t.model,c={};n.each(["pan","zoom","scrollMove"],(function(e){c[e]=s(u[e],this,t,a)}),this),r.register(i,{coordId:r.generateCoordId(l),allCoordIds:o,containsPoint:function(e,t,i){return l.coordinateSystem.containPoint([t,i])},dataZoomId:e.id,dataZoomModel:e,getRange:c})}),this)}),this)},dispose:function(){r.unregister(this.api,this.dataZoomModel.id),l.superApply(this,"dispose",arguments),this._range=null}}),u={zoom:function(e,t,i,n){var a=this._range,r=a.slice(),s=e.axisModels[0];if(s){var l=d[t](null,[n.originX,n.originY],s,i,e),u=(l.signal>0?l.pixelStart+l.pixelLength-l.pixel:l.pixel-l.pixelStart)/l.pixelLength*(r[1]-r[0])+r[0],c=Math.max(1/n.scale,0);r[0]=(r[0]-u)*c+u,r[1]=(r[1]-u)*c+u;var h=this.dataZoomModel.findRepresentativeAxisProxy().getMinMaxSpan();return o(0,r,[0,100],0,h.minSpan,h.maxSpan),this._range=r,a[0]!==r[0]||a[1]!==r[1]?r:void 0}},pan:c((function(e,t,i,n,a,o){var r=d[n]([o.oldX,o.oldY],[o.newX,o.newY],t,a,i);return r.signal*(e[1]-e[0])*r.pixel/r.pixelLength})),scrollMove:c((function(e,t,i,n,a,o){var r=d[n]([0,0],[o.scrollDelta,o.scrollDelta],t,a,i);return r.signal*(e[1]-e[0])*o.scrollDelta}))};function c(e){return function(t,i,n,a){var r=this._range,s=r.slice(),l=t.axisModels[0];if(l){var u=e(s,l,t,i,n,a);return o(u,s,[0,100],"all"),this._range=s,r[0]!==s[0]||r[1]!==s[1]?s:void 0}}}var d={grid:function(e,t,i,n,a){var o=i.axis,r={},s=a.model.coordinateSystem.getRect();return e=e||[0,0],"x"===o.dim?(r.pixel=t[0]-e[0],r.pixelLength=s.width,r.pixelStart=s.x,r.signal=o.inverse?1:-1):(r.pixel=t[1]-e[1],r.pixelLength=s.height,r.pixelStart=s.y,r.signal=o.inverse?-1:1),r},polar:function(e,t,i,n,a){var o=i.axis,r={},s=a.model.coordinateSystem,l=s.getRadiusAxis().getExtent(),u=s.getAngleAxis().getExtent();return e=e?s.pointToCoord(e):[0,0],t=s.pointToCoord(t),"radiusAxis"===i.mainType?(r.pixel=t[0]-e[0],r.pixelLength=l[1]-l[0],r.pixelStart=l[0],r.signal=o.inverse?1:-1):(r.pixel=t[1]-e[1],r.pixelLength=u[1]-u[0],r.pixelStart=u[0],r.signal=o.inverse?-1:1),r},singleAxis:function(e,t,i,n,a){var o=i.axis,r=a.model.coordinateSystem.getRect(),s={};return e=e||[0,0],"horizontal"===o.orient?(s.pixel=t[0]-e[0],s.pixelLength=r.width,s.pixelStart=r.x,s.signal=o.inverse?1:-1):(s.pixel=t[1]-e[1],s.pixelLength=r.height,s.pixelStart=r.y,s.signal=o.inverse?-1:1),s}},h=l;e.exports=h},3329:function(e,t,i){var n=i("3eba"),a=i("6d8b"),o=i("2306"),r=i("a15a"),s=r.createSymbol,l=i("3842"),u=l.parsePercent,c=l.isNumeric,d=i("e7aa"),h=d.setLabel,p=["itemStyle","borderWidth"],g=[{xy:"x",wh:"width",index:0,posDesc:["left","right"]},{xy:"y",wh:"height",index:1,posDesc:["top","bottom"]}],f=new o.Circle,m=n.extendChartView({type:"pictorialBar",render:function(e,t,i){var n=this.group,a=e.getData(),o=this._data,r=e.coordinateSystem,s=r.getBaseAxis(),l=!!s.isHorizontal(),u=r.grid.getRect(),c={ecSize:{width:i.getWidth(),height:i.getHeight()},seriesModel:e,coordSys:r,coordSysExtent:[[u.x,u.x+u.width],[u.y,u.y+u.height]],isHorizontal:l,valueDim:g[+l],categoryDim:g[1-l]};return a.diff(o).add((function(e){if(a.hasValue(e)){var t=D(a,e),i=v(a,e,t,c),o=N(a,c,i);a.setItemGraphicEl(e,o),n.add(o),O(o,c,i)}})).update((function(e,t){var i=o.getItemGraphicEl(t);if(a.hasValue(e)){var r=D(a,e),s=v(a,e,r,c),l=k(a,s);i&&l!==i.__pictorialShapeStr&&(n.remove(i),a.setItemGraphicEl(e,null),i=null),i?R(i,c,s):i=N(a,c,s,!0),a.setItemGraphicEl(e,i),i.__pictorialSymbolMeta=s,n.add(i),O(i,c,s)}else n.remove(i)})).remove((function(e){var t=o.getItemGraphicEl(e);t&&V(o,e,t.__pictorialSymbolMeta.animationModel,t)})).execute(),this._data=a,this.group},dispose:a.noop,remove:function(e,t){var i=this.group,n=this._data;e.get("animation")?n&&n.eachItemGraphicEl((function(t){V(n,t.dataIndex,e,t)})):i.removeAll()}});function v(e,t,i,n){var o=e.getItemLayout(t),r=i.get("symbolRepeat"),s=i.get("symbolClip"),l=i.get("symbolPosition")||"start",c=i.get("symbolRotate"),d=(c||0)*Math.PI/180||0,h=i.get("symbolPatternSize")||2,p=i.isAnimationEnabled(),g={dataIndex:t,layout:o,itemModel:i,symbolType:e.getItemVisual(t,"symbol")||"circle",color:e.getItemVisual(t,"color"),symbolClip:s,symbolRepeat:r,symbolRepeatDirection:i.get("symbolRepeatDirection"),symbolPatternSize:h,rotation:d,animationModel:p?i:null,hoverAnimation:p&&i.get("hoverAnimation"),z2:i.getShallow("z",!0)||0};y(i,r,o,n,g),_(e,t,o,r,s,g.boundingLength,g.pxSign,h,n,g),b(i,g.symbolScale,d,n,g);var f=g.symbolSize,m=i.get("symbolOffset");return a.isArray(m)&&(m=[u(m[0],f[0]),u(m[1],f[1])]),w(i,f,o,r,s,m,l,g.valueLineWidth,g.boundingLength,g.repeatCutLength,n,g),g}function y(e,t,i,n,o){var r,s=n.valueDim,l=e.get("symbolBoundingData"),u=n.coordSys.getOtherAxis(n.coordSys.getBaseAxis()),c=u.toGlobalCoord(u.dataToCoord(0)),d=1-+(i[s.wh]<=0);if(a.isArray(l)){var h=[x(u,l[0])-c,x(u,l[1])-c];h[1]<h[0]&&h.reverse(),r=h[d]}else r=null!=l?x(u,l)-c:t?n.coordSysExtent[s.index][d]-c:i[s.wh];o.boundingLength=r,t&&(o.repeatCutLength=i[s.wh]),o.pxSign=r>0?1:r<0?-1:0}function x(e,t){return e.toGlobalCoord(e.dataToCoord(e.scale.parse(t)))}function _(e,t,i,n,o,r,s,l,c,d){var h=c.valueDim,p=c.categoryDim,g=Math.abs(i[p.wh]),f=e.getItemVisual(t,"symbolSize");a.isArray(f)?f=f.slice():(null==f&&(f="100%"),f=[f,f]),f[p.index]=u(f[p.index],g),f[h.index]=u(f[h.index],n?g:Math.abs(r)),d.symbolSize=f;var m=d.symbolScale=[f[0]/l,f[1]/l];m[h.index]*=(c.isHorizontal?-1:1)*s}function b(e,t,i,n,a){var o=e.get(p)||0;o&&(f.attr({scale:t.slice(),rotation:i}),f.updateTransform(),o/=f.getLineScale(),o*=t[n.valueDim.index]),a.valueLineWidth=o}function w(e,t,i,n,o,r,s,l,d,h,p,g){var f=p.categoryDim,m=p.valueDim,v=g.pxSign,y=Math.max(t[m.index]+l,0),x=y;if(n){var _=Math.abs(d),b=a.retrieve(e.get("symbolMargin"),"15%")+"",w=!1;b.lastIndexOf("!")===b.length-1&&(w=!0,b=b.slice(0,b.length-1)),b=u(b,t[m.index]);var S=Math.max(y+2*b,0),M=w?0:2*b,I=c(n),A=I?n:B((_+M)/S),T=_-A*y;b=T/2/(w?A:A-1),S=y+2*b,M=w?0:2*b,I||"fixed"===n||(A=h?B((Math.abs(h)+M)/S):0),x=A*S-M,g.repeatTimes=A,g.symbolMargin=b}var D=v*(x/2),L=g.pathPosition=[];L[f.index]=i[f.wh]/2,L[m.index]="start"===s?D:"end"===s?d-D:d/2,r&&(L[0]+=r[0],L[1]+=r[1]);var C=g.bundlePosition=[];C[f.index]=i[f.xy],C[m.index]=i[m.xy];var P=g.barRectShape=a.extend({},i);P[m.wh]=v*Math.max(Math.abs(i[m.wh]),Math.abs(L[m.index]+D)),P[f.wh]=i[f.wh];var N=g.clipShape={};N[f.xy]=-i[f.xy],N[f.wh]=p.ecSize[f.wh],N[m.xy]=0,N[m.wh]=i[m.wh]}function S(e){var t=e.symbolPatternSize,i=s(e.symbolType,-t/2,-t/2,t,t,e.color);return i.attr({culling:!0}),"image"!==i.type&&i.setStyle({strokeNoScale:!0}),i}function M(e,t,i,n){var a=e.__pictorialBundle,o=i.symbolSize,r=i.valueLineWidth,s=i.pathPosition,l=t.valueDim,u=i.repeatTimes||0,c=0,d=o[t.valueDim.index]+r+2*i.symbolMargin;for(E(e,(function(e){e.__pictorialAnimationIndex=c,e.__pictorialRepeatTimes=u,c<u?z(e,null,g(c),i,n):z(e,null,{scale:[0,0]},i,n,(function(){a.remove(e)})),P(e,i),c++}));c<u;c++){var h=S(i);h.__pictorialAnimationIndex=c,h.__pictorialRepeatTimes=u,a.add(h);var p=g(c);z(h,{position:p.position,scale:[0,0]},{scale:p.scale,rotation:p.rotation},i,n),h.on("mouseover",f).on("mouseout",m),P(h,i)}function g(e){var t=s.slice(),n=i.pxSign,a=e;return("start"===i.symbolRepeatDirection?n>0:n<0)&&(a=u-1-e),t[l.index]=d*(a-u/2+.5)+s[l.index],{position:t,scale:i.symbolScale.slice(),rotation:i.rotation}}function f(){E(e,(function(e){e.trigger("emphasis")}))}function m(){E(e,(function(e){e.trigger("normal")}))}}function I(e,t,i,n){var a=e.__pictorialBundle,o=e.__pictorialMainPath;function r(){this.trigger("emphasis")}function s(){this.trigger("normal")}o?z(o,null,{position:i.pathPosition.slice(),scale:i.symbolScale.slice(),rotation:i.rotation},i,n):(o=e.__pictorialMainPath=S(i),a.add(o),z(o,{position:i.pathPosition.slice(),scale:[0,0],rotation:i.rotation},{scale:i.symbolScale.slice()},i,n),o.on("mouseover",r).on("mouseout",s)),P(o,i)}function A(e,t,i){var n=a.extend({},t.barRectShape),r=e.__pictorialBarRect;r?z(r,null,{shape:n},t,i):(r=e.__pictorialBarRect=new o.Rect({z2:2,shape:n,silent:!0,style:{stroke:"transparent",fill:"transparent",lineWidth:0}}),e.add(r))}function T(e,t,i,n){if(i.symbolClip){var r=e.__pictorialClipPath,s=a.extend({},i.clipShape),l=t.valueDim,u=i.animationModel,c=i.dataIndex;if(r)o.updateProps(r,{shape:s},u,c);else{s[l.wh]=0,r=new o.Rect({shape:s}),e.__pictorialBundle.setClipPath(r),e.__pictorialClipPath=r;var d={};d[l.wh]=i.clipShape[l.wh],o[n?"updateProps":"initProps"](r,{shape:d},u,c)}}}function D(e,t){var i=e.getItemModel(t);return i.getAnimationDelayParams=L,i.isAnimationEnabled=C,i}function L(e){return{index:e.__pictorialAnimationIndex,count:e.__pictorialRepeatTimes}}function C(){return this.parentModel.isAnimationEnabled()&&!!this.getShallow("animation")}function P(e,t){e.off("emphasis").off("normal");var i=t.symbolScale.slice();t.hoverAnimation&&e.on("emphasis",(function(){this.animateTo({scale:[1.1*i[0],1.1*i[1]]},400,"elasticOut")})).on("normal",(function(){this.animateTo({scale:i.slice()},400,"elasticOut")}))}function N(e,t,i,n){var a=new o.Group,r=new o.Group;return a.add(r),a.__pictorialBundle=r,r.attr("position",i.bundlePosition.slice()),i.symbolRepeat?M(a,t,i):I(a,t,i),A(a,i,n),T(a,t,i,n),a.__pictorialShapeStr=k(e,i),a.__pictorialSymbolMeta=i,a}function R(e,t,i){var n=i.animationModel,a=i.dataIndex,r=e.__pictorialBundle;o.updateProps(r,{position:i.bundlePosition.slice()},n,a),i.symbolRepeat?M(e,t,i,!0):I(e,t,i,!0),A(e,i,!0),T(e,t,i,!0)}function V(e,t,i,n){var r=n.__pictorialBarRect;r&&(r.style.text=null);var s=[];E(n,(function(e){s.push(e)})),n.__pictorialMainPath&&s.push(n.__pictorialMainPath),n.__pictorialClipPath&&(i=null),a.each(s,(function(e){o.updateProps(e,{scale:[0,0]},i,t,(function(){n.parent&&n.parent.remove(n)}))})),e.setItemGraphicEl(t,null)}function k(e,t){return[e.getItemVisual(t.dataIndex,"symbol")||"none",!!t.symbolRepeat,!!t.symbolClip].join(":")}function E(e,t,i){a.each(e.__pictorialBundle.children(),(function(n){n!==e.__pictorialBarRect&&t.call(i,n)}))}function z(e,t,i,n,a,r){t&&e.attr(t),n.symbolClip&&!a?i&&e.attr(i):i&&o[a?"updateProps":"initProps"](e,i,n.animationModel,n.dataIndex,r)}function O(e,t,i){var n=i.color,r=i.dataIndex,s=i.itemModel,l=s.getModel("itemStyle").getItemStyle(["color"]),u=s.getModel("emphasis.itemStyle").getItemStyle(),c=s.getShallow("cursor");E(e,(function(e){e.setColor(n),e.setStyle(a.defaults({fill:n,opacity:i.opacity},l)),o.setHoverStyle(e,u),c&&(e.cursor=c),e.z2=i.z2}));var d={},p=t.valueDim.posDesc[+(i.boundingLength>0)],g=e.__pictorialBarRect;h(g.style,d,s,n,t.seriesModel,r,p),o.setHoverStyle(g,d)}function B(e){var t=Math.round(e);return Math.abs(e-t)<1e-4?t:Math.ceil(e)}var G=m;e.exports=G},"340d":function(e,t,i){var n=i("6d8b"),a=i("e887"),o=i("4e47"),r=i("80f0"),s="sunburstRootToNode",l=a.extend({type:"sunburst",init:function(){},render:function(e,t,i,a){var s=this;this.seriesModel=e,this.api=i,this.ecModel=t;var l=e.getData(),u=l.tree.root,c=e.getViewRoot(),d=this.group,h=e.get("renderLabelForZeroData"),p=[];c.eachNode((function(e){p.push(e)}));var g=this._oldChildren||[];if(v(p,g),_(u,c),a&&a.highlight&&a.highlight.piece){var f=e.getShallow("highlightPolicy");a.highlight.piece.onEmphasis(f)}else if(a&&a.unhighlight){var m=this.virtualPiece;!m&&u.children.length&&(m=u.children[0].piece),m&&m.onNormal()}function v(e,t){function i(e){return e.getId()}function a(i,n){var a=null==i?null:e[i],o=null==n?null:t[n];y(a,o)}0===e.length&&0===t.length||new r(t,e,i,i).add(a).update(a).remove(n.curry(a,null)).execute()}function y(i,n){if(h||!i||i.getValue()||(i=null),i!==u&&n!==u)if(n&&n.piece)i?(n.piece.updateData(!1,i,"normal",e,t),l.setItemGraphicEl(i.dataIndex,n.piece)):x(n);else if(i){var a=new o(i,e,t);d.add(a),l.setItemGraphicEl(i.dataIndex,a)}}function x(e){e&&e.piece&&(d.remove(e.piece),e.piece=null)}function _(i,n){if(n.depth>0){s.virtualPiece?s.virtualPiece.updateData(!1,i,"normal",e,t):(s.virtualPiece=new o(i,e,t),d.add(s.virtualPiece)),n.piece._onclickEvent&&n.piece.off("click",n.piece._onclickEvent);var a=function(e){s._rootToNode(n.parentNode)};n.piece._onclickEvent=a,s.virtualPiece.on("click",a)}else s.virtualPiece&&(d.remove(s.virtualPiece),s.virtualPiece=null)}this._initEvents(),this._oldChildren=p},dispose:function(){},_initEvents:function(){var e=this,t=function(t){var i=!1,n=e.seriesModel.getViewRoot();n.eachNode((function(n){if(!i&&n.piece&&n.piece.childAt(0)===t.target){var a=n.getModel().get("nodeClick");if("rootToNode"===a)e._rootToNode(n);else if("link"===a){var o=n.getModel(),r=o.get("link");if(r){var s=o.get("target",!0)||"_blank";window.open(r,s)}}i=!0}}))};this.group._onclickEvent&&this.group.off("click",this.group._onclickEvent),this.group.on("click",t),this.group._onclickEvent=t},_rootToNode:function(e){e!==this.seriesModel.getViewRoot()&&this.api.dispatchAction({type:s,from:this.uid,seriesId:this.seriesModel.id,targetNode:e})},containPoint:function(e,t){var i=t.getData(),n=i.getItemLayout(0);if(n){var a=e[0]-n.cx,o=e[1]-n.cy,r=Math.sqrt(a*a+o*o);return r<=n.r&&r>=n.r0}}}),u=l;e.exports=u},"347f":function(e,t,i){var n=i("6d8b"),a=i("9850"),o=i("1687"),r=i("2306"),s=i("f934"),l=i("933b"),u=i("08c3"),c=i("a15a"),d=c.createSymbol,h=i("697e"),p=i("3842"),g=i("eda2"),f=g.encodeHTML,m=n.bind,v=n.each,y=Math.PI,x=l.extend({type:"timeline.slider",init:function(e,t){this.api=t,this._axis,this._viewRect,this._timer,this._currentPointer,this._mainGroup,this._labelGroup},render:function(e,t,i,n){if(this.model=e,this.api=i,this.ecModel=t,this.group.removeAll(),e.get("show",!0)){var a=this._layout(e,i),o=this._createGroup("mainGroup"),r=this._createGroup("labelGroup"),s=this._axis=this._createAxis(a,e);e.formatTooltip=function(e){return f(s.scale.getLabel(e))},v(["AxisLine","AxisTick","Control","CurrentPointer"],(function(t){this["_render"+t](a,o,s,e)}),this),this._renderAxisLabel(a,r,s,e),this._position(a,e)}this._doPlayStop()},remove:function(){this._clearTimer(),this.group.removeAll()},dispose:function(){this._clearTimer()},_layout:function(e,t){var i=e.get("label.position"),n=e.get("orient"),a=_(e,t);null==i||"auto"===i?i="horizontal"===n?a.y+a.height/2<t.getHeight()/2?"-":"+":a.x+a.width/2<t.getWidth()/2?"+":"-":isNaN(i)&&(i={horizontal:{top:"-",bottom:"+"},vertical:{left:"-",right:"+"}}[n][i]);var o,r,s,l,u={horizontal:"center",vertical:i>=0||"+"===i?"left":"right"},c={horizontal:i>=0||"+"===i?"top":"bottom",vertical:"middle"},d={horizontal:0,vertical:y/2},h="vertical"===n?a.height:a.width,p=e.getModel("controlStyle"),g=p.get("show",!0),f=g?p.get("itemSize"):0,m=g?p.get("itemGap"):0,v=f+m,x=e.get("label.rotate")||0;x=x*y/180;var b=p.get("position",!0),w=g&&p.get("showPlayBtn",!0),S=g&&p.get("showPrevBtn",!0),M=g&&p.get("showNextBtn",!0),I=0,A=h;return"left"===b||"bottom"===b?(w&&(o=[0,0],I+=v),S&&(r=[I,0],I+=v),M&&(s=[A-f,0],A-=v)):(w&&(o=[A-f,0],A-=v),S&&(r=[0,0],I+=v),M&&(s=[A-f,0],A-=v)),l=[I,A],e.get("inverse")&&l.reverse(),{viewRect:a,mainLength:h,orient:n,rotation:d[n],labelRotation:x,labelPosOpt:i,labelAlign:e.get("label.align")||u[n],labelBaseline:e.get("label.verticalAlign")||e.get("label.baseline")||c[n],playPosition:o,prevBtnPosition:r,nextBtnPosition:s,axisExtent:l,controlSize:f,controlGap:m}},_position:function(e,t){var i=this._mainGroup,n=this._labelGroup,a=e.viewRect;if("vertical"===e.orient){var r=o.create(),s=a.x,l=a.y+a.height;o.translate(r,r,[-s,-l]),o.rotate(r,r,-y/2),o.translate(r,r,[s,l]),a=a.clone(),a.applyTransform(r)}var u=v(a),c=v(i.getBoundingRect()),d=v(n.getBoundingRect()),h=i.position,p=n.position;p[0]=h[0]=u[0][0];var g=e.labelPosOpt;if(isNaN(g)){var f="+"===g?0:1;x(h,c,u,1,f),x(p,d,u,1,1-f)}else{f=g>=0?0:1;x(h,c,u,1,f),p[1]=h[1]+g}function m(e){var t=e.position;e.origin=[u[0][0]-t[0],u[1][0]-t[1]]}function v(e){return[[e.x,e.x+e.width],[e.y,e.y+e.height]]}function x(e,t,i,n,a){e[n]+=i[n][a]-t[n][a]}i.attr("position",h),n.attr("position",p),i.rotation=n.rotation=e.rotation,m(i),m(n)},_createAxis:function(e,t){var i=t.getData(),n=t.get("axisType"),a=h.createScaleByModel(t,n);a.getTicks=function(){return i.mapArray(["value"],(function(e){return e}))};var o=i.getDataExtent("value");a.setExtent(o[0],o[1]),a.niceTicks();var r=new u("value",a,e.axisExtent,n);return r.model=t,r},_createGroup:function(e){var t=this["_"+e]=new r.Group;return this.group.add(t),t},_renderAxisLine:function(e,t,i,a){var o=i.getExtent();a.get("lineStyle.show")&&t.add(new r.Line({shape:{x1:o[0],y1:0,x2:o[1],y2:0},style:n.extend({lineCap:"round"},a.getModel("lineStyle").getLineStyle()),silent:!0,z2:1}))},_renderAxisTick:function(e,t,i,n){var a=n.getData(),o=i.scale.getTicks();v(o,(function(e){var o=i.dataToCoord(e),s=a.getItemModel(e),l=s.getModel("itemStyle"),u=s.getModel("emphasis.itemStyle"),c={position:[o,0],onclick:m(this._changeTimeline,this,e)},d=w(s,l,t,c);r.setHoverStyle(d,u.getItemStyle()),s.get("tooltip")?(d.dataIndex=e,d.dataModel=n):d.dataIndex=d.dataModel=null}),this)},_renderAxisLabel:function(e,t,i,n){var a=i.getLabelModel();if(a.get("show")){var o=n.getData(),s=i.getViewLabels();v(s,(function(n){var a=n.tickValue,s=o.getItemModel(a),l=s.getModel("label"),u=s.getModel("emphasis.label"),c=i.dataToCoord(n.tickValue),d=new r.Text({position:[c,0],rotation:e.labelRotation-e.rotation,onclick:m(this._changeTimeline,this,a),silent:!1});r.setTextStyle(d.style,l,{text:n.formattedLabel,textAlign:e.labelAlign,textVerticalAlign:e.labelBaseline}),t.add(d),r.setHoverStyle(d,r.setTextStyle({},u))}),this)}},_renderControl:function(e,t,i,n){var a=e.controlSize,o=e.rotation,s=n.getModel("controlStyle").getItemStyle(),l=n.getModel("emphasis.controlStyle").getItemStyle(),u=[0,-a/2,a,a],c=n.getPlayState(),d=n.get("inverse",!0);function h(e,i,c,d){if(e){var h={position:e,origin:[a/2,0],rotation:d?-o:0,rectHover:!0,style:s,onclick:c},p=b(n,i,u,h);t.add(p),r.setHoverStyle(p,l)}}h(e.nextBtnPosition,"controlStyle.nextIcon",m(this._changeTimeline,this,d?"-":"+")),h(e.prevBtnPosition,"controlStyle.prevIcon",m(this._changeTimeline,this,d?"+":"-")),h(e.playPosition,"controlStyle."+(c?"stopIcon":"playIcon"),m(this._handlePlayClick,this,!c),!0)},_renderCurrentPointer:function(e,t,i,n){var a=n.getData(),o=n.getCurrentIndex(),r=a.getItemModel(o).getModel("checkpointStyle"),s=this,l={onCreate:function(e){e.draggable=!0,e.drift=m(s._handlePointerDrag,s),e.ondragend=m(s._handlePointerDragend,s),S(e,o,i,n,!0)},onUpdate:function(e){S(e,o,i,n)}};this._currentPointer=w(r,r,this._mainGroup,{},this._currentPointer,l)},_handlePlayClick:function(e){this._clearTimer(),this.api.dispatchAction({type:"timelinePlayChange",playState:e,from:this.uid})},_handlePointerDrag:function(e,t,i){this._clearTimer(),this._pointerChangeTimeline([i.offsetX,i.offsetY])},_handlePointerDragend:function(e){this._pointerChangeTimeline([e.offsetX,e.offsetY],!0)},_pointerChangeTimeline:function(e,t){var i=this._toAxisCoord(e)[0],n=this._axis,a=p.asc(n.getExtent().slice());i>a[1]&&(i=a[1]),i<a[0]&&(i=a[0]),this._currentPointer.position[0]=i,this._currentPointer.dirty();var o=this._findNearestTick(i),r=this.model;(t||o!==r.getCurrentIndex()&&r.get("realtime"))&&this._changeTimeline(o)},_doPlayStop:function(){function e(){var e=this.model;this._changeTimeline(e.getCurrentIndex()+(e.get("rewind",!0)?-1:1))}this._clearTimer(),this.model.getPlayState()&&(this._timer=setTimeout(m(e,this),this.model.get("playInterval")))},_toAxisCoord:function(e){var t=this._mainGroup.getLocalTransform();return r.applyTransform(e,t,!0)},_findNearestTick:function(e){var t,i=this.model.getData(),n=1/0,a=this._axis;return i.each(["value"],(function(i,o){var r=a.dataToCoord(i),s=Math.abs(r-e);s<n&&(n=s,t=o)})),t},_clearTimer:function(){this._timer&&(clearTimeout(this._timer),this._timer=null)},_changeTimeline:function(e){var t=this.model.getCurrentIndex();"+"===e?e=t+1:"-"===e&&(e=t-1),this.api.dispatchAction({type:"timelineChange",currentIndex:e,from:this.uid})}});function _(e,t){return s.getLayoutRect(e.getBoxLayoutParams(),{width:t.getWidth(),height:t.getHeight()},e.get("padding"))}function b(e,t,i,o){var s=r.makePath(e.get(t).replace(/^path:\/\//,""),n.clone(o||{}),new a(i[0],i[1],i[2],i[3]),"center");return s}function w(e,t,i,a,o,r){var s=t.get("color");if(o)o.setColor(s),i.add(o),r&&r.onUpdate(o);else{var l=e.get("symbol");o=d(l,-1,-1,2,2,s),o.setStyle("strokeNoScale",!0),i.add(o),r&&r.onCreate(o)}var u=t.getItemStyle(["color","symbol","symbolSize"]);o.setStyle(u),a=n.merge({rectHover:!0,z2:100},a,!0);var c=e.get("symbolSize");c=c instanceof Array?c.slice():[+c,+c],c[0]/=2,c[1]/=2,a.scale=c;var h=e.get("symbolOffset");if(h){var g=a.position=a.position||[0,0];g[0]+=p.parsePercent(h[0],c[0]),g[1]+=p.parsePercent(h[1],c[1])}var f=e.get("symbolRotate");return a.rotation=(f||0)*Math.PI/180||0,o.attr(a),o.updateTransform(),o}function S(e,t,i,n,a){if(!e.dragging){var o=n.getModel("checkpointStyle"),r=i.dataToCoord(n.getData().get(["value"],t));a||!o.get("animation",!0)?e.attr({position:[r,0]}):(e.stopAnimation(!0),e.animateTo({position:[r,0]},o.get("animationDuration",!0),o.get("animationEasing",!0)))}}e.exports=x},3790:function(e,t,i){var n=i("3a56"),a=n.extend({type:"dataZoom.slider",layoutMode:"box",defaultOption:{show:!0,right:"ph",top:"ph",width:"ph",height:"ph",left:null,bottom:null,backgroundColor:"rgba(47,69,84,0)",dataBackground:{lineStyle:{color:"#2f4554",width:.5,opacity:.3},areaStyle:{color:"rgba(47,69,84,0.3)",opacity:.3}},borderColor:"#ddd",fillerColor:"rgba(167,183,204,0.4)",handleIcon:"M8.2,13.6V3.9H6.3v9.7H3.1v14.9h3.3v9.7h1.8v-9.7h3.3V13.6H8.2z M9.7,24.4H4.8v-1.4h4.9V24.4z M9.7,19.1H4.8v-1.4h4.9V19.1z",handleSize:"100%",handleStyle:{color:"#a7b7cc"},labelPrecision:null,labelFormatter:null,showDetail:!0,showDataShadow:"auto",realtime:!0,zoomLock:!1,textStyle:{color:"#333"}}}),o=a;e.exports=o},3942:function(e,t,i){var n=i("3eba"),a=i("6d8b");n.registerAction({type:"timelineChange",event:"timelineChanged",update:"prepareAndUpdate"},(function(e,t){var i=t.getComponent("timeline");return i&&null!=e.currentIndex&&(i.setCurrentIndex(e.currentIndex),!i.get("loop",!0)&&i.isIndexMax()&&i.setPlayState(!1)),t.resetOption("timeline"),a.defaults({currentIndex:i.option.currentIndex},e)})),n.registerAction({type:"timelinePlayChange",event:"timelinePlayChanged",update:"update"},(function(e,t){var i=t.getComponent("timeline");i&&null!=e.playState&&i.setPlayState(e.playState)}))},3970:function(e,t,i){var n=i("4f85"),a=i("3301"),o=i("2039"),r=n.extend({type:"series.heatmap",getInitialData:function(e,t){return a(this.getSource(),this,{generateCoord:"value"})},preventIncremental:function(){var e=o.get(this.get("coordinateSystem"));if(e&&e.dimensions)return"lng"===e.dimensions[0]&&"lat"===e.dimensions[1]},defaultOption:{coordinateSystem:"cartesian2d",zlevel:0,z:2,geoIndex:0,blurSize:30,pointSize:20,maxOpacity:1,minOpacity:0}});e.exports=r},"3a56":function(e,t,i){var n=i("4e08"),a=(n.__DEV__,i("3eba")),o=i("6d8b"),r=i("22d1"),s=i("e0d3"),l=i("50e5"),u=i("cc39"),c=o.each,d=l.eachAxisDim,h=a.extendComponentModel({type:"dataZoom",dependencies:["xAxis","yAxis","zAxis","radiusAxis","angleAxis","singleAxis","series"],defaultOption:{zlevel:0,z:4,orient:null,xAxisIndex:null,yAxisIndex:null,filterMode:"filter",throttle:null,start:0,end:100,startValue:null,endValue:null,minSpan:null,maxSpan:null,minValueSpan:null,maxValueSpan:null,rangeMode:null},init:function(e,t,i){this._dataIntervalByAxis={},this._dataInfo={},this._axisProxies={},this.textStyleModel,this._autoThrottle=!0,this._rangePropMode=["percent","percent"];var n=p(e);this.mergeDefaultAndTheme(e,i),this.doInit(n)},mergeOption:function(e){var t=p(e);o.merge(this.option,e,!0),this.doInit(t)},doInit:function(e){var t=this.option;r.canvasSupported||(t.realtime=!1),this._setDefaultThrottle(e),g(this,e),c([["start","startValue"],["end","endValue"]],(function(e,i){"value"===this._rangePropMode[i]&&(t[e[0]]=null)}),this),this.textStyleModel=this.getModel("textStyle"),this._resetTarget(),this._giveAxisProxies()},_giveAxisProxies:function(){var e=this._axisProxies;this.eachTargetAxis((function(t,i,n,a){var o=this.dependentModels[t.axis][i],r=o.__dzAxisProxy||(o.__dzAxisProxy=new u(t.name,i,this,a));e[t.name+"_"+i]=r}),this)},_resetTarget:function(){var e=this.option,t=this._judgeAutoMode();d((function(t){var i=t.axisIndex;e[i]=s.normalizeToArray(e[i])}),this),"axisIndex"===t?this._autoSetAxisIndex():"orient"===t&&this._autoSetOrient()},_judgeAutoMode:function(){var e=this.option,t=!1;d((function(i){null!=e[i.axisIndex]&&(t=!0)}),this);var i=e.orient;return null==i&&t?"orient":t?void 0:(null==i&&(e.orient="horizontal"),"axisIndex")},_autoSetAxisIndex:function(){var e=!0,t=this.get("orient",!0),i=this.option,n=this.dependentModels;if(e){var a="vertical"===t?"y":"x";n[a+"Axis"].length?(i[a+"AxisIndex"]=[0],e=!1):c(n.singleAxis,(function(n){e&&n.get("orient",!0)===t&&(i.singleAxisIndex=[n.componentIndex],e=!1)}))}e&&d((function(t){if(e){var n=[],a=this.dependentModels[t.axis];if(a.length&&!n.length)for(var o=0,r=a.length;o<r;o++)"category"===a[o].get("type")&&n.push(o);i[t.axisIndex]=n,n.length&&(e=!1)}}),this),e&&this.ecModel.eachSeries((function(e){this._isSeriesHasAllAxesTypeOf(e,"value")&&d((function(t){var n=i[t.axisIndex],a=e.get(t.axisIndex),r=e.get(t.axisId),s=e.ecModel.queryComponents({mainType:t.axis,index:a,id:r})[0];a=s.componentIndex,o.indexOf(n,a)<0&&n.push(a)}))}),this)},_autoSetOrient:function(){var e;this.eachTargetAxis((function(t){!e&&(e=t.name)}),this),this.option.orient="y"===e?"vertical":"horizontal"},_isSeriesHasAllAxesTypeOf:function(e,t){var i=!0;return d((function(n){var a=e.get(n.axisIndex),o=this.dependentModels[n.axis][a];o&&o.get("type")===t||(i=!1)}),this),i},_setDefaultThrottle:function(e){if(e.hasOwnProperty("throttle")&&(this._autoThrottle=!1),this._autoThrottle){var t=this.ecModel.option;this.option.throttle=t.animation&&t.animationDurationUpdate>0?100:20}},getFirstTargetAxisModel:function(){var e;return d((function(t){if(null==e){var i=this.get(t.axisIndex);i.length&&(e=this.dependentModels[t.axis][i[0]])}}),this),e},eachTargetAxis:function(e,t){var i=this.ecModel;d((function(n){c(this.get(n.axisIndex),(function(a){e.call(t,n,a,this,i)}),this)}),this)},getAxisProxy:function(e,t){return this._axisProxies[e+"_"+t]},getAxisModel:function(e,t){var i=this.getAxisProxy(e,t);return i&&i.getAxisModel()},setRawRange:function(e,t){var i=this.option;c([["start","startValue"],["end","endValue"]],(function(t){null==e[t[0]]&&null==e[t[1]]||(i[t[0]]=e[t[0]],i[t[1]]=e[t[1]])}),this),!t&&g(this,e)},getPercentRange:function(){var e=this.findRepresentativeAxisProxy();if(e)return e.getDataPercentWindow()},getValueRange:function(e,t){if(null!=e||null!=t)return this.getAxisProxy(e,t).getDataValueWindow();var i=this.findRepresentativeAxisProxy();return i?i.getDataValueWindow():void 0},findRepresentativeAxisProxy:function(e){if(e)return e.__dzAxisProxy;var t=this._axisProxies;for(var i in t)if(t.hasOwnProperty(i)&&t[i].hostedBy(this))return t[i];for(var i in t)if(t.hasOwnProperty(i)&&!t[i].hostedBy(this))return t[i]},getRangePropMode:function(){return this._rangePropMode.slice()}});function p(e){var t={};return c(["start","end","startValue","endValue","throttle"],(function(i){e.hasOwnProperty(i)&&(t[i]=e[i])})),t}function g(e,t){var i=e._rangePropMode,n=e.get("rangeMode");c([["start","startValue"],["end","endValue"]],(function(e,a){var o=null!=t[e[0]],r=null!=t[e[1]];o&&!r?i[a]="percent":!o&&r?i[a]="value":n?i[a]=n[a]:o&&(i[a]="percent")}))}var f=h;e.exports=f},"3cd6":function(e,t,i){var n=i("6d8b"),a=i("48a9"),o=i("607d"),r=i("72b6"),s=i("2306"),l=i("3842"),u=i("ef6a"),c=i("cbb0"),d=i("e0d3"),h=l.linearMap,p=n.each,g=Math.min,f=Math.max,m=12,v=6,y=r.extend({type:"visualMap.continuous",init:function(){y.superApply(this,"init",arguments),this._shapes={},this._dataInterval=[],this._handleEnds=[],this._orient,this._useHandle,this._hoverLinkDataIndices=[],this._dragging,this._hovering},doRender:function(e,t,i,n){n&&"selectDataRange"===n.type&&n.from===this.uid||this._buildView()},_buildView:function(){this.group.removeAll();var e=this.visualMapModel,t=this.group;this._orient=e.get("orient"),this._useHandle=e.get("calculable"),this._resetInterval(),this._renderBar(t);var i=e.get("text");this._renderEndsText(t,i,0),this._renderEndsText(t,i,1),this._updateView(!0),this.renderBackground(t),this._updateView(),this._enableHoverLinkToSeries(),this._enableHoverLinkFromSeries(),this.positionGroup(t)},_renderEndsText:function(e,t,i){if(t){var n=t[1-i];n=null!=n?n+"":"";var a=this.visualMapModel,o=a.get("textGap"),r=a.itemSize,l=this._shapes.barGroup,u=this._applyTransform([r[0]/2,0===i?-o:r[1]+o],l),c=this._applyTransform(0===i?"bottom":"top",l),d=this._orient,h=this.visualMapModel.textStyleModel;this.group.add(new s.Text({style:{x:u[0],y:u[1],textVerticalAlign:"horizontal"===d?"middle":c,textAlign:"horizontal"===d?c:"center",text:n,textFont:h.getFont(),textFill:h.getTextColor()}}))}},_renderBar:function(e){var t=this.visualMapModel,i=this._shapes,a=t.itemSize,o=this._orient,r=this._useHandle,s=c.getItemAlign(t,this.api,a),l=i.barGroup=this._createBarGroup(s);l.add(i.outOfRange=x()),l.add(i.inRange=x(null,r?M(this._orient):null,n.bind(this._dragHandle,this,"all",!1),n.bind(this._dragHandle,this,"all",!0)));var u=t.textStyleModel.getTextRect("国"),d=f(u.width,u.height);r&&(i.handleThumbs=[],i.handleLabels=[],i.handleLabelPoints=[],this._createHandle(l,0,a,d,o,s),this._createHandle(l,1,a,d,o,s)),this._createIndicator(l,a,d,o),e.add(l)},_createHandle:function(e,t,i,a,r){var l=n.bind(this._dragHandle,this,t,!1),u=n.bind(this._dragHandle,this,t,!0),c=x(_(t,a),M(this._orient),l,u);c.position[0]=i[0],e.add(c);var d=this.visualMapModel.textStyleModel,h=new s.Text({draggable:!0,drift:l,onmousemove:function(e){o.stop(e.event)},ondragend:u,style:{x:0,y:0,text:"",textFont:d.getFont(),textFill:d.getTextColor()}});this.group.add(h);var p=["horizontal"===r?a/2:1.5*a,"horizontal"===r?0===t?-1.5*a:1.5*a:0===t?-a/2:a/2],g=this._shapes;g.handleThumbs[t]=c,g.handleLabelPoints[t]=p,g.handleLabels[t]=h},_createIndicator:function(e,t,i,n){var a=x([[0,0]],"move");a.position[0]=t[0],a.attr({invisible:!0,silent:!0}),e.add(a);var o=this.visualMapModel.textStyleModel,r=new s.Text({silent:!0,invisible:!0,style:{x:0,y:0,text:"",textFont:o.getFont(),textFill:o.getTextColor()}});this.group.add(r);var l=["horizontal"===n?i/2:v+3,0],u=this._shapes;u.indicator=a,u.indicatorLabel=r,u.indicatorLabelPoint=l},_dragHandle:function(e,t,i,n){if(this._useHandle){if(this._dragging=!t,!t){var a=this._applyTransform([i,n],this._shapes.barGroup,!0);this._updateInterval(e,a[1]),this._updateView()}t===!this.visualMapModel.get("realtime")&&this.api.dispatchAction({type:"selectDataRange",from:this.uid,visualMapId:this.visualMapModel.id,selected:this._dataInterval.slice()}),t?!this._hovering&&this._clearHoverLinkToSeries():S(this.visualMapModel)&&this._doHoverLinkToSeries(this._handleEnds[e],!1)}},_resetInterval:function(){var e=this.visualMapModel,t=this._dataInterval=e.getSelected(),i=e.getExtent(),n=[0,e.itemSize[1]];this._handleEnds=[h(t[0],i,n,!0),h(t[1],i,n,!0)]},_updateInterval:function(e,t){t=t||0;var i=this.visualMapModel,n=this._handleEnds,a=[0,i.itemSize[1]];u(t,n,a,e,0);var o=i.getExtent();this._dataInterval=[h(n[0],a,o,!0),h(n[1],a,o,!0)]},_updateView:function(e){var t=this.visualMapModel,i=t.getExtent(),n=this._shapes,a=[0,t.itemSize[1]],o=e?a:this._handleEnds,r=this._createBarVisual(this._dataInterval,i,o,"inRange"),s=this._createBarVisual(i,i,a,"outOfRange");n.inRange.setStyle({fill:r.barColor,opacity:r.opacity}).setShape("points",r.barPoints),n.outOfRange.setStyle({fill:s.barColor,opacity:s.opacity}).setShape("points",s.barPoints),this._updateHandle(o,r)},_createBarVisual:function(e,t,i,n){var o={forceState:n,convertOpacityToAlpha:!0},r=this._makeColorGradient(e,o),s=[this.getControllerVisual(e[0],"symbolSize",o),this.getControllerVisual(e[1],"symbolSize",o)],l=this._createBarPoints(i,s);return{barColor:new a(0,0,0,1,r),barPoints:l,handlesColor:[r[0].color,r[r.length-1].color]}},_makeColorGradient:function(e,t){var i=100,n=[],a=(e[1]-e[0])/i;n.push({color:this.getControllerVisual(e[0],"color",t),offset:0});for(var o=1;o<i;o++){var r=e[0]+a*o;if(r>e[1])break;n.push({color:this.getControllerVisual(r,"color",t),offset:o/i})}return n.push({color:this.getControllerVisual(e[1],"color",t),offset:1}),n},_createBarPoints:function(e,t){var i=this.visualMapModel.itemSize;return[[i[0]-t[0],e[0]],[i[0],e[0]],[i[0],e[1]],[i[0]-t[1],e[1]]]},_createBarGroup:function(e){var t=this._orient,i=this.visualMapModel.get("inverse");return new s.Group("horizontal"!==t||i?"horizontal"===t&&i?{scale:"bottom"===e?[-1,1]:[1,1],rotation:-Math.PI/2}:"vertical"!==t||i?{scale:"left"===e?[1,1]:[-1,1]}:{scale:"left"===e?[1,-1]:[-1,-1]}:{scale:"bottom"===e?[1,1]:[-1,1],rotation:Math.PI/2})},_updateHandle:function(e,t){if(this._useHandle){var i=this._shapes,n=this.visualMapModel,a=i.handleThumbs,o=i.handleLabels;p([0,1],(function(r){var l=a[r];l.setStyle("fill",t.handlesColor[r]),l.position[1]=e[r];var u=s.applyTransform(i.handleLabelPoints[r],s.getTransform(l,this.group));o[r].setStyle({x:u[0],y:u[1],text:n.formatValueText(this._dataInterval[r]),textVerticalAlign:"middle",textAlign:this._applyTransform("horizontal"===this._orient?0===r?"bottom":"top":"left",i.barGroup)})}),this)}},_showIndicator:function(e,t,i,n){var a=this.visualMapModel,o=a.getExtent(),r=a.itemSize,l=[0,r[1]],u=h(e,o,l,!0),c=this._shapes,d=c.indicator;if(d){d.position[1]=u,d.attr("invisible",!1),d.setShape("points",b(!!i,n,u,r[1]));var p={convertOpacityToAlpha:!0},g=this.getControllerVisual(e,"color",p);d.setStyle("fill",g);var f=s.applyTransform(c.indicatorLabelPoint,s.getTransform(d,this.group)),m=c.indicatorLabel;m.attr("invisible",!1);var v=this._applyTransform("left",c.barGroup),y=this._orient;m.setStyle({text:(i||"")+a.formatValueText(t),textVerticalAlign:"horizontal"===y?v:"middle",textAlign:"horizontal"===y?"center":v,x:f[0],y:f[1]})}},_enableHoverLinkToSeries:function(){var e=this;this._shapes.barGroup.on("mousemove",(function(t){if(e._hovering=!0,!e._dragging){var i=e.visualMapModel.itemSize,n=e._applyTransform([t.offsetX,t.offsetY],e._shapes.barGroup,!0,!0);n[1]=g(f(0,n[1]),i[1]),e._doHoverLinkToSeries(n[1],0<=n[0]&&n[0]<=i[0])}})).on("mouseout",(function(){e._hovering=!1,!e._dragging&&e._clearHoverLinkToSeries()}))},_enableHoverLinkFromSeries:function(){var e=this.api.getZr();this.visualMapModel.option.hoverLink?(e.on("mouseover",this._hoverLinkFromSeriesMouseOver,this),e.on("mouseout",this._hideIndicator,this)):this._clearHoverLinkFromSeries()},_doHoverLinkToSeries:function(e,t){var i=this.visualMapModel,n=i.itemSize;if(i.option.hoverLink){var a=[0,n[1]],o=i.getExtent();e=g(f(a[0],e),a[1]);var r=w(i,o,a),s=[e-r,e+r],l=h(e,a,o,!0),u=[h(s[0],a,o,!0),h(s[1],a,o,!0)];s[0]<a[0]&&(u[0]=-1/0),s[1]>a[1]&&(u[1]=1/0),t&&(u[0]===-1/0?this._showIndicator(l,u[1],"< ",r):u[1]===1/0?this._showIndicator(l,u[0],"> ",r):this._showIndicator(l,l,"≈ ",r));var p=this._hoverLinkDataIndices,m=[];(t||S(i))&&(m=this._hoverLinkDataIndices=i.findTargetDataIndices(u));var v=d.compressBatches(p,m);this._dispatchHighDown("downplay",c.convertDataIndex(v[0])),this._dispatchHighDown("highlight",c.convertDataIndex(v[1]))}},_hoverLinkFromSeriesMouseOver:function(e){var t=e.target,i=this.visualMapModel;if(t&&null!=t.dataIndex){var n=this.ecModel.getSeriesByIndex(t.seriesIndex);if(i.isTargetSeries(n)){var a=n.getData(t.dataType),o=a.get(i.getDataDimension(a),t.dataIndex,!0);isNaN(o)||this._showIndicator(o,o)}}},_hideIndicator:function(){var e=this._shapes;e.indicator&&e.indicator.attr("invisible",!0),e.indicatorLabel&&e.indicatorLabel.attr("invisible",!0)},_clearHoverLinkToSeries:function(){this._hideIndicator();var e=this._hoverLinkDataIndices;this._dispatchHighDown("downplay",c.convertDataIndex(e)),e.length=0},_clearHoverLinkFromSeries:function(){this._hideIndicator();var e=this.api.getZr();e.off("mouseover",this._hoverLinkFromSeriesMouseOver),e.off("mouseout",this._hideIndicator)},_applyTransform:function(e,t,i,a){var o=s.getTransform(t,a?null:this.group);return s[n.isArray(e)?"applyTransform":"transformDirection"](e,o,i)},_dispatchHighDown:function(e,t){t&&t.length&&this.api.dispatchAction({type:e,batch:t})},dispose:function(){this._clearHoverLinkFromSeries(),this._clearHoverLinkToSeries()},remove:function(){this._clearHoverLinkFromSeries(),this._clearHoverLinkToSeries()}});function x(e,t,i,n){return new s.Polygon({shape:{points:e},draggable:!!i,cursor:t,drift:i,onmousemove:function(e){o.stop(e.event)},ondragend:n})}function _(e,t){return 0===e?[[0,0],[t,0],[t,-t]]:[[0,0],[t,0],[t,t]]}function b(e,t,i,n){return e?[[0,-g(t,f(i,0))],[v,0],[0,g(t,f(n-i,0))]]:[[0,0],[5,-5],[5,5]]}function w(e,t,i){var n=m/2,a=e.get("hoverLinkDataSize");return a&&(n=h(a,t,i,!0)/2),n}function S(e){var t=e.get("hoverLinkOnHandle");return!!(null==t?e.get("realtime"):t)}function M(e){return"vertical"===e?"ns-resize":"ew-resize"}var I=y;e.exports=I},"3f8e":function(e,t,i){var n=i("8727"),a=n.createElement,o=i("20c8"),r=i("9850"),s=i("1687"),l=i("e86a"),u=i("a73c"),c=i("76a5"),d=o.CMD,h=Array.prototype.join,p="none",g=Math.round,f=Math.sin,m=Math.cos,v=Math.PI,y=2*Math.PI,x=180/v,_=1e-4;function b(e){return g(1e4*e)/1e4}function w(e){return e<_&&e>-_}function S(e,t){var i=t?e.textFill:e.fill;return null!=i&&i!==p}function M(e,t){var i=t?e.textStroke:e.stroke;return null!=i&&i!==p}function I(e,t){t&&A(e,"transform","matrix("+h.call(t,",")+")")}function A(e,t,i){(!i||"linear"!==i.type&&"radial"!==i.type)&&e.setAttribute(t,i)}function T(e,t,i){e.setAttributeNS("http://www.w3.org/1999/xlink",t,i)}function D(e,t,i,n){if(S(t,i)){var a=i?t.textFill:t.fill;a="transparent"===a?p:a,"none"!==e.getAttribute("clip-path")&&a===p&&(a="rgba(0, 0, 0, 0.002)"),A(e,"fill",a),A(e,"fill-opacity",null!=t.fillOpacity?t.fillOpacity*t.opacity:t.opacity)}else A(e,"fill",p);if(M(t,i)){var o=i?t.textStroke:t.stroke;o="transparent"===o?p:o,A(e,"stroke",o);var r=i?t.textStrokeWidth:t.lineWidth,s=!i&&t.strokeNoScale?n.getLineScale():1;A(e,"stroke-width",r/s),A(e,"paint-order",i?"stroke":"fill"),A(e,"stroke-opacity",null!=t.strokeOpacity?t.strokeOpacity:t.opacity);var l=t.lineDash;l?(A(e,"stroke-dasharray",t.lineDash.join(",")),A(e,"stroke-dashoffset",g(t.lineDashOffset||0))):A(e,"stroke-dasharray",""),t.lineCap&&A(e,"stroke-linecap",t.lineCap),t.lineJoin&&A(e,"stroke-linejoin",t.lineJoin),t.miterLimit&&A(e,"stroke-miterlimit",t.miterLimit)}else A(e,"stroke",p)}function L(e){for(var t=[],i=e.data,n=e.len(),a=0;a<n;){var o=i[a++],r="",s=0;switch(o){case d.M:r="M",s=2;break;case d.L:r="L",s=2;break;case d.Q:r="Q",s=4;break;case d.C:r="C",s=6;break;case d.A:var l=i[a++],u=i[a++],c=i[a++],h=i[a++],p=i[a++],_=i[a++],S=i[a++],M=i[a++],I=Math.abs(_),A=w(I-y)&&!w(I),T=!1;T=I>=y||!w(I)&&(_>-v&&_<0||_>v)===!!M;var D=b(l+c*m(p)),L=b(u+h*f(p));A&&(_=M?y-1e-4:1e-4-y,T=!0,9===a&&t.push("M",D,L));var C=b(l+c*m(p+_)),P=b(u+h*f(p+_));t.push("A",b(c),b(h),g(S*x),+T,+M,C,P);break;case d.Z:r="Z";break;case d.R:C=b(i[a++]),P=b(i[a++]);var N=b(i[a++]),R=b(i[a++]);t.push("M",C,P,"L",C+N,P,"L",C+N,P+R,"L",C,P+R,"L",C,P);break}r&&t.push(r);for(var V=0;V<s;V++)t.push(b(i[a++]))}return t.join(" ")}var C={brush:function(e){var t=e.style,i=e.__svgEl;i||(i=a("path"),e.__svgEl=i),e.path||e.createPathProxy();var n=e.path;if(e.__dirtyPath){n.beginPath(),n.subPixelOptimize=!1,e.buildPath(n,e.shape),e.__dirtyPath=!1;var o=L(n);o.indexOf("NaN")<0&&A(i,"d",o)}D(i,t,!1,e),I(i,e.transform),null!=t.text&&V(e,e.getBoundingRect())}},P={brush:function(e){var t=e.style,i=t.image;if(i instanceof HTMLImageElement){var n=i.src;i=n}if(i){var o=t.x||0,r=t.y||0,s=t.width,l=t.height,u=e.__svgEl;u||(u=a("image"),e.__svgEl=u),i!==e.__imageSrc&&(T(u,"href",i),e.__imageSrc=i),A(u,"width",s),A(u,"height",l),A(u,"x",o),A(u,"y",r),I(u,e.transform),null!=t.text&&V(e,e.getBoundingRect())}}},N={},R=new r,V=function(e,t,i){var n=e.style;e.__dirty&&u.normalizeTextStyle(n,!0);var o=n.text;if(null!=o){o+="";var r,d,h=e.__textSvgEl;h||(h=a("text"),e.__textSvgEl=h);var p=n.textPosition,g=n.textDistance,f=n.textAlign||"left";"number"===typeof n.fontSize&&(n.fontSize+="px");var m=n.font||[n.fontStyle||"",n.fontWeight||"",n.fontSize||"",n.fontFamily||""].join(" ")||l.DEFAULT_FONT,v=k(n.textVerticalAlign);i=l.getBoundingRect(o,m,f,v,n.textPadding,n.textLineHeight);var y=i.lineHeight;if(p instanceof Array)r=t.x+p[0],d=t.y+p[1];else{var x=l.adjustTextPositionOnRect(p,t,g);r=x.x,d=x.y,v=k(x.textVerticalAlign),f=x.textAlign}A(h,"alignment-baseline",v),m&&(h.style.font=m);var _=n.textPadding;if(A(h,"x",r),A(h,"y",d),D(h,n,!0,e),e instanceof c||e.style.transformText)I(h,e.transform);else{if(e.transform)R.copy(t),R.applyTransform(e.transform),t=R;else{var b=e.transformCoordToGlobal(t.x,t.y);t.x=b[0],t.y=b[1],e.transform=s.identity(s.create())}var w=n.textOrigin;"center"===w?(r=i.width/2+r,d=i.height/2+d):w&&(r=w[0]+r,d=w[1]+d);var S=-n.textRotation||0,M=s.create();s.rotate(M,M,S);b=[e.transform[4],e.transform[5]];s.translate(M,M,b),I(h,M)}var T=o.split("\n"),L=T.length,C=f;"left"===C?(C="start",_&&(r+=_[3])):"right"===C?(C="end",_&&(r-=_[1])):"center"===C&&(C="middle",_&&(r+=(_[3]-_[1])/2));var P=0;if("after-edge"===v?(P=-i.height+y,_&&(P-=_[2])):"middle"===v?(P=(-i.height+y)/2,_&&(d+=(_[0]-_[2])/2)):_&&(P+=_[0]),e.__text!==o||e.__textFont!==m){var N=e.__tspanList||[];e.__tspanList=N;for(var V=0;V<L;V++){var E=N[V];E?E.innerHTML="":(E=N[V]=a("tspan"),h.appendChild(E),A(E,"alignment-baseline",v),A(E,"text-anchor",C)),A(E,"x",r),A(E,"y",d+V*y+P),E.appendChild(document.createTextNode(T[V]))}for(;V<N.length;V++)h.removeChild(N[V]);N.length=L,e.__text=o,e.__textFont=m}else if(e.__tspanList.length){var z=e.__tspanList.length;for(V=0;V<z;++V){E=e.__tspanList[V];E&&(A(E,"x",r),A(E,"y",d+V*y+P))}}}};function k(e){return"middle"===e?"middle":"bottom"===e?"after-edge":"hanging"}N.drawRectText=V,N.brush=function(e){var t=e.style;null!=t.text&&(t.textPosition=[0,0],V(e,{x:t.x||0,y:t.y||0,width:0,height:0},e.getBoundingRect()))},t.path=C,t.image=P,t.text=N},"414c":function(e,t,i){var n=i("3a56"),a=n.extend({type:"dataZoom.select"});e.exports=a},"42f6":function(e,t,i){var n=i("3eba"),a=i("6d8b"),o=i("22d1"),r=i("07d7"),s=i("82f9"),l=i("eda2"),u=i("3842"),c=i("2306"),d=i("133d"),h=i("f934"),p=i("4319"),g=i("17d6"),f=i("697e"),m=i("ff2e"),v=i("e0d3"),y=v.getTooltipRenderMode,x=a.bind,_=a.each,b=u.parsePercent,w=new c.Rect({shape:{x:-1,y:-1,width:2,height:2}}),S=n.extendComponentView({type:"tooltip",init:function(e,t){if(!o.node){var i,n=e.getComponent("tooltip"),a=n.get("renderMode");this._renderMode=y(a),"html"===this._renderMode?(i=new r(t.getDom(),t),this._newLine="<br/>"):(i=new s(t),this._newLine="\n"),this._tooltipContent=i}},render:function(e,t,i){if(!o.node){this.group.removeAll(),this._tooltipModel=e,this._ecModel=t,this._api=i,this._lastDataByCoordSys=null,this._alwaysShowContent=e.get("alwaysShowContent");var n=this._tooltipContent;n.update(),n.setEnterable(e.get("enterable")),this._initGlobalListener(),this._keepShow()}},_initGlobalListener:function(){var e=this._tooltipModel,t=e.get("triggerOn");g.register("itemTooltip",this._api,x((function(e,i,n){"none"!==t&&(t.indexOf(e)>=0?this._tryShow(i,n):"leave"===e&&this._hide(n))}),this))},_keepShow:function(){var e=this._tooltipModel,t=this._ecModel,i=this._api;if(null!=this._lastX&&null!=this._lastY&&"none"!==e.get("triggerOn")){var n=this;clearTimeout(this._refreshUpdateTimeout),this._refreshUpdateTimeout=setTimeout((function(){n.manuallyShowTip(e,t,i,{x:n._lastX,y:n._lastY})}))}},manuallyShowTip:function(e,t,i,n){if(n.from!==this.uid&&!o.node){var a=I(n,i);this._ticket="";var r=n.dataByCoordSys;if(n.tooltip&&null!=n.x&&null!=n.y){var s=w;s.position=[n.x,n.y],s.update(),s.tooltip=n.tooltip,this._tryShow({offsetX:n.x,offsetY:n.y,target:s},a)}else if(r)this._tryShow({offsetX:n.x,offsetY:n.y,position:n.position,event:{},dataByCoordSys:n.dataByCoordSys,tooltipOption:n.tooltipOption},a);else if(null!=n.seriesIndex){if(this._manuallyAxisShowTip(e,t,i,n))return;var l=d(n,t),u=l.point[0],c=l.point[1];null!=u&&null!=c&&this._tryShow({offsetX:u,offsetY:c,position:n.position,target:l.el,event:{}},a)}else null!=n.x&&null!=n.y&&(i.dispatchAction({type:"updateAxisPointer",x:n.x,y:n.y}),this._tryShow({offsetX:n.x,offsetY:n.y,position:n.position,target:i.getZr().findHover(n.x,n.y).target,event:{}},a))}},manuallyHideTip:function(e,t,i,n){var a=this._tooltipContent;!this._alwaysShowContent&&this._tooltipModel&&a.hideLater(this._tooltipModel.get("hideDelay")),this._lastX=this._lastY=null,n.from!==this.uid&&this._hide(I(n,i))},_manuallyAxisShowTip:function(e,t,i,n){var a=n.seriesIndex,o=n.dataIndex,r=t.getComponent("axisPointer").coordSysAxesInfo;if(null!=a&&null!=o&&null!=r){var s=t.getSeriesByIndex(a);if(s){var l=s.getData();e=M([l.getItemModel(o),s,(s.coordinateSystem||{}).model,e]);if("axis"===e.get("trigger"))return i.dispatchAction({type:"updateAxisPointer",seriesIndex:a,dataIndex:o,position:n.position}),!0}}},_tryShow:function(e,t){var i=e.target,n=this._tooltipModel;if(n){this._lastX=e.offsetX,this._lastY=e.offsetY;var a=e.dataByCoordSys;a&&a.length?this._showAxisTooltip(a,e):i&&null!=i.dataIndex?(this._lastDataByCoordSys=null,this._showSeriesItemTooltip(e,i,t)):i&&i.tooltip?(this._lastDataByCoordSys=null,this._showComponentItemTooltip(e,i,t)):(this._lastDataByCoordSys=null,this._hide(t))}},_showOrMove:function(e,t){var i=e.get("showDelay");t=a.bind(t,this),clearTimeout(this._showTimout),i>0?this._showTimout=setTimeout(t,i):t()},_showAxisTooltip:function(e,t){var i=this._ecModel,n=this._tooltipModel,o=[t.offsetX,t.offsetY],r=[],s=[],u=M([t.tooltipOption,n]),c=this._renderMode,d=this._newLine,h={};_(e,(function(e){_(e.dataByAxis,(function(e){var t=i.getComponent(e.axisDim+"Axis",e.axisIndex),n=e.value,o=[];if(t&&null!=n){var u=m.getValueLabel(n,t.axis,i,e.seriesDataIndices,e.valueLabelOpt);a.each(e.seriesDataIndices,(function(r){var l=i.getSeriesByIndex(r.seriesIndex),d=r.dataIndexInside,p=l&&l.getDataParams(d);if(p.axisDim=e.axisDim,p.axisIndex=e.axisIndex,p.axisType=e.axisType,p.axisId=e.axisId,p.axisValue=f.getAxisRawValue(t.axis,n),p.axisValueLabel=u,p){s.push(p);var g,m=l.formatTooltip(d,!0,null,c);if(a.isObject(m)){g=m.html;var v=m.markers;a.merge(h,v)}else g=m;o.push(g)}}));var p=u;"html"!==c?r.push(o.join(d)):r.push((p?l.encodeHTML(p)+d:"")+o.join(d))}}))}),this),r.reverse(),r=r.join(this._newLine+this._newLine);var p=t.position;this._showOrMove(u,(function(){this._updateContentNotChangedOnAxis(e)?this._updatePosition(u,p,o[0],o[1],this._tooltipContent,s):this._showTooltipContent(u,r,s,Math.random(),o[0],o[1],p,void 0,h)}))},_showSeriesItemTooltip:function(e,t,i){var n=this._ecModel,o=t.seriesIndex,r=n.getSeriesByIndex(o),s=t.dataModel||r,l=t.dataIndex,u=t.dataType,c=s.getData(),d=M([c.getItemModel(l),s,r&&(r.coordinateSystem||{}).model,this._tooltipModel]),h=d.get("trigger");if(null==h||"item"===h){var p,g,f=s.getDataParams(l,u),m=s.formatTooltip(l,!1,u,this._renderMode);a.isObject(m)?(p=m.html,g=m.markers):(p=m,g=null);var v="item_"+s.name+"_"+l;this._showOrMove(d,(function(){this._showTooltipContent(d,p,f,v,e.offsetX,e.offsetY,e.position,e.target,g)})),i({type:"showTip",dataIndexInside:l,dataIndex:c.getRawIndex(l),seriesIndex:o,from:this.uid})}},_showComponentItemTooltip:function(e,t,i){var n=t.tooltip;if("string"===typeof n){var a=n;n={content:a,formatter:a}}var o=new p(n,this._tooltipModel,this._ecModel),r=o.get("content"),s=Math.random();this._showOrMove(o,(function(){this._showTooltipContent(o,r,o.get("formatterParams")||{},s,e.offsetX,e.offsetY,e.position,t)})),i({type:"showTip",from:this.uid})},_showTooltipContent:function(e,t,i,n,a,o,r,s,u){if(this._ticket="",e.get("showContent")&&e.get("show")){var c=this._tooltipContent,d=e.get("formatter");r=r||e.get("position");var h=t;if(d&&"string"===typeof d)h=l.formatTpl(d,i,!0);else if("function"===typeof d){var p=x((function(t,n){t===this._ticket&&(c.setContent(n,u,e),this._updatePosition(e,r,a,o,c,i,s))}),this);this._ticket=n,h=d(i,n,p)}c.setContent(h,u,e),c.show(e),this._updatePosition(e,r,a,o,c,i,s)}},_updatePosition:function(e,t,i,n,o,r,s){var l=this._api.getWidth(),u=this._api.getHeight();t=t||e.get("position");var c=o.getSize(),d=e.get("align"),p=e.get("verticalAlign"),g=s&&s.getBoundingRect().clone();if(s&&g.applyTransform(s.transform),"function"===typeof t&&(t=t([i,n],r,o.el,g,{viewSize:[l,u],contentSize:c.slice()})),a.isArray(t))i=b(t[0],l),n=b(t[1],u);else if(a.isObject(t)){t.width=c[0],t.height=c[1];var f=h.getLayoutRect(t,{width:l,height:u});i=f.x,n=f.y,d=null,p=null}else if("string"===typeof t&&s){var m=D(t,g,c);i=m[0],n=m[1]}else{m=A(i,n,o,l,u,d?null:20,p?null:20);i=m[0],n=m[1]}if(d&&(i-=L(d)?c[0]/2:"right"===d?c[0]:0),p&&(n-=L(p)?c[1]/2:"bottom"===p?c[1]:0),e.get("confine")){m=T(i,n,o,l,u);i=m[0],n=m[1]}o.moveTo(i,n)},_updateContentNotChangedOnAxis:function(e){var t=this._lastDataByCoordSys,i=!!t&&t.length===e.length;return i&&_(t,(function(t,n){var a=t.dataByAxis||{},o=e[n]||{},r=o.dataByAxis||[];i&=a.length===r.length,i&&_(a,(function(e,t){var n=r[t]||{},a=e.seriesDataIndices||[],o=n.seriesDataIndices||[];i&=e.value===n.value&&e.axisType===n.axisType&&e.axisId===n.axisId&&a.length===o.length,i&&_(a,(function(e,t){var n=o[t];i&=e.seriesIndex===n.seriesIndex&&e.dataIndex===n.dataIndex}))}))})),this._lastDataByCoordSys=e,!!i},_hide:function(e){this._lastDataByCoordSys=null,e({type:"hideTip",from:this.uid})},dispose:function(e,t){o.node||(this._tooltipContent.hide(),g.unregister("itemTooltip",t))}});function M(e){var t=e.pop();while(e.length){var i=e.pop();i&&(p.isInstance(i)&&(i=i.get("tooltip",!0)),"string"===typeof i&&(i={formatter:i}),t=new p(i,t,t.ecModel))}return t}function I(e,t){return e.dispatchAction||a.bind(t.dispatchAction,t)}function A(e,t,i,n,a,o,r){var s=i.getOuterSize(),l=s.width,u=s.height;return null!=o&&(e+l+o>n?e-=l+o:e+=o),null!=r&&(t+u+r>a?t-=u+r:t+=r),[e,t]}function T(e,t,i,n,a){var o=i.getOuterSize(),r=o.width,s=o.height;return e=Math.min(e+r,n)-r,t=Math.min(t+s,a)-s,e=Math.max(e,0),t=Math.max(t,0),[e,t]}function D(e,t,i){var n=i[0],a=i[1],o=5,r=0,s=0,l=t.width,u=t.height;switch(e){case"inside":r=t.x+l/2-n/2,s=t.y+u/2-a/2;break;case"top":r=t.x+l/2-n/2,s=t.y-a-o;break;case"bottom":r=t.x+l/2-n/2,s=t.y+u+o;break;case"left":r=t.x-n-o,s=t.y+u/2-a/2;break;case"right":r=t.x+l+o,s=t.y+u/2-a/2}return[r,s]}function L(e){return"center"===e||"middle"===e}e.exports=S},4338:function(e,t,i){var n=i("4bf6"),a=i("2039");function o(e,t){var i=[];return e.eachComponent("singleAxis",(function(a,o){var r=new n(a,e,t);r.name="single_"+o,r.resize(a,t),a.coordinateSystem=r,i.push(r)})),e.eachSeries((function(t){if("singleAxis"===t.get("coordinateSystem")){var i=e.queryComponents({mainType:"singleAxis",index:t.get("singleAxisIndex"),id:t.get("singleAxisId")})[0];t.coordinateSystem=i&&i.coordinateSystem}})),i}a.register("single",{create:o,dimensions:n.prototype.dimensions})},"43b8":function(e,t,i){var n=i("2449"),a=n.extend({type:"markArea",defaultOption:{zlevel:0,z:1,tooltip:{trigger:"item"},animation:!1,label:{show:!0,position:"top"},itemStyle:{borderWidth:0},emphasis:{label:{show:!0,position:"top"}}}});e.exports=a},4411:function(e,t,i){for(var n=i("3eba"),a=i("55ac"),o=function(){},r=["treemapZoomToNode","treemapRender","treemapMove"],s=0;s<r.length;s++)n.registerAction({type:r[s],update:"updateView"},o);n.registerAction({type:"treemapRootToNode",update:"updateView"},(function(e,t){function i(t,i){var n=["treemapZoomToNode","treemapRootToNode"],o=a.retrieveTargetInfo(e,n,t);if(o){var r=t.getViewRoot();r&&(e.direction=a.aboveViewRoot(r,o.node)?"rollUp":"drillDown"),t.resetViewRoot(o.node)}}t.eachComponent({mainType:"series",subType:"treemap",query:e},i)}))},"44fb":function(e,t,i){var n=i("3eba"),a=i("55ac"),o="sunburstRootToNode";n.registerAction({type:o,update:"updateView"},(function(e,t){function i(t,i){var n=a.retrieveTargetInfo(e,[o],t);if(n){var r=t.getViewRoot();r&&(e.direction=a.aboveViewRoot(r,n.node)?"rollUp":"drillDown"),t.resetViewRoot(n.node)}}t.eachComponent({mainType:"series",subType:"sunburst",query:e},i)}));var r="sunburstHighlight";n.registerAction({type:r,update:"updateView"},(function(e,t){function i(t,i){var n=a.retrieveTargetInfo(e,[r],t);n&&(e.highlight=n.node)}t.eachComponent({mainType:"series",subType:"sunburst",query:e},i)}));var s="sunburstUnhighlight";n.registerAction({type:s,update:"updateView"},(function(e,t){function i(t,i){e.unhighlight=!0}t.eachComponent({mainType:"series",subType:"sunburst",query:e},i)}))},4527:function(e,t,i){var n=i("2306"),a=i("6d8b");function o(e,t,i){n.Group.call(this),this._createPolyline(e,t,i)}var r=o.prototype;r._createPolyline=function(e,t,i){var a=e.getItemLayout(t),o=new n.Polyline({shape:{points:a}});this.add(o),this._updateCommonStl(e,t,i)},r.updateData=function(e,t,i){var a=e.hostModel,o=this.childAt(0),r={shape:{points:e.getItemLayout(t)}};n.updateProps(o,r,a,t),this._updateCommonStl(e,t,i)},r._updateCommonStl=function(e,t,i){var o=this.childAt(0),r=e.getItemModel(t),s=e.getItemVisual(t,"color"),l=i&&i.lineStyle,u=i&&i.hoverLineStyle;i&&!e.hasItemOption||(l=r.getModel("lineStyle").getLineStyle(),u=r.getModel("emphasis.lineStyle").getLineStyle()),o.useStyle(a.defaults({strokeNoScale:!0,fill:"none",stroke:s},l)),o.hoverStyle=u,n.setHoverStyle(this)},r.updateLayout=function(e,t){var i=this.childAt(0);i.setShape("points",e.getItemLayout(t))},a.inherits(o,n.Group);var s=o;e.exports=s},4650:function(e,t,i){var n=i("3eba"),a=i("6d8b");function o(e,t,i){var n,o={},r="toggleSelected"===e;return i.eachComponent("legend",(function(i){r&&null!=n?i[n?"select":"unSelect"](t.name):(i[e](t.name),n=i.isSelected(t.name));var s=i.getData();a.each(s,(function(e){var t=e.get("name");if("\n"!==t&&""!==t){var n=i.isSelected(t);o.hasOwnProperty(t)?o[t]=o[t]&&n:o[t]=n}}))})),{name:t.name,selected:o}}n.registerAction("legendToggleSelect","legendselectchanged",a.curry(o,"toggleSelected")),n.registerAction("legendSelect","legendselected",a.curry(o,"select")),n.registerAction("legendUnSelect","legendunselected",a.curry(o,"unSelect"))},"471e":function(e,t){function i(e){var t=e.getRect(),i=e.getRangeInfo();return{coordSys:{type:"calendar",x:t.x,y:t.y,width:t.width,height:t.height,cellWidth:e.getCellWidth(),cellHeight:e.getCellHeight(),rangeInfo:{start:i.start,end:i.end,weeks:i.weeks,dayCount:i.allDay}},api:{coord:function(t,i){return e.dataToPoint(t,i)}}}}e.exports=i},4784:function(e,t,i){var n=i("3eba"),a=i("f706"),o=i("c8ef"),r=i("1687"),s=i("87c3"),l=n.extendChartView({type:"effectScatter",init:function(){this._symbolDraw=new a(o)},render:function(e,t,i){var n=e.getData(),a=this._symbolDraw;a.updateData(n),this.group.add(a.group)},updateTransform:function(e,t,i){var n=e.getData();this.group.dirty();var a=s().reset(e);a.progress&&a.progress({start:0,end:n.count()},n),this._symbolDraw.updateLayout(n)},_updateGroupTransform:function(e){var t=e.coordinateSystem;t&&t.getRoamTransform&&(this.group.transform=r.clone(t.getRoamTransform()),this.group.decomposeTransform())},remove:function(e,t){this._symbolDraw&&this._symbolDraw.remove(t)},dispose:function(){}});e.exports=l},"480e":function(e,t,i){var n=i("4e08"),a=(n.__DEV__,i("3eba")),o=i("2306"),r=i("cd84"),s=i("6d8b");function l(e,t,i){var n=e[1]-e[0];t=s.map(t,(function(t){return{interval:[(t.interval[0]-e[0])/n,(t.interval[1]-e[0])/n]}}));var a=t.length,o=0;return function(e){for(var n=o;n<a;n++){var r=t[n].interval;if(r[0]<=e&&e<=r[1]){o=n;break}}if(n===a)for(n=o-1;n>=0;n--){r=t[n].interval;if(r[0]<=e&&e<=r[1]){o=n;break}}return n>=0&&n<a&&i[n]}}function u(e,t){var i=e[1]-e[0];return t=[(t[0]-e[0])/i,(t[1]-e[0])/i],function(e){return e>=t[0]&&e<=t[1]}}function c(e){var t=e.dimensions;return"lng"===t[0]&&"lat"===t[1]}var d=a.extendChartView({type:"heatmap",render:function(e,t,i){var n;t.eachComponent("visualMap",(function(t){t.eachTargetSeries((function(i){i===e&&(n=t)}))})),this.group.removeAll(),this._incrementalDisplayable=null;var a=e.coordinateSystem;"cartesian2d"===a.type||"calendar"===a.type?this._renderOnCartesianAndCalendar(e,i,0,e.getData().count()):c(a)&&this._renderOnGeo(a,e,n,i)},incrementalPrepareRender:function(e,t,i){this.group.removeAll()},incrementalRender:function(e,t,i,n){var a=t.coordinateSystem;a&&this._renderOnCartesianAndCalendar(t,n,e.start,e.end,!0)},_renderOnCartesianAndCalendar:function(e,t,i,n,a){var r,l,u=e.coordinateSystem;if("cartesian2d"===u.type){var c=u.getAxis("x"),d=u.getAxis("y");r=c.getBandWidth(),l=d.getBandWidth()}for(var h=this.group,p=e.getData(),g="itemStyle",f="emphasis.itemStyle",m="label",v="emphasis.label",y=e.getModel(g).getItemStyle(["color"]),x=e.getModel(f).getItemStyle(),_=e.getModel(m),b=e.getModel(v),w=u.type,S="cartesian2d"===w?[p.mapDimension("x"),p.mapDimension("y"),p.mapDimension("value")]:[p.mapDimension("time"),p.mapDimension("value")],M=i;M<n;M++){var I;if("cartesian2d"===w){if(isNaN(p.get(S[2],M)))continue;var A=u.dataToPoint([p.get(S[0],M),p.get(S[1],M)]);I=new o.Rect({shape:{x:A[0]-r/2,y:A[1]-l/2,width:r,height:l},style:{fill:p.getItemVisual(M,"color"),opacity:p.getItemVisual(M,"opacity")}})}else{if(isNaN(p.get(S[1],M)))continue;I=new o.Rect({z2:1,shape:u.dataToRect([p.get(S[0],M)]).contentShape,style:{fill:p.getItemVisual(M,"color"),opacity:p.getItemVisual(M,"opacity")}})}var T=p.getItemModel(M);p.hasItemOption&&(y=T.getModel(g).getItemStyle(["color"]),x=T.getModel(f).getItemStyle(),_=T.getModel(m),b=T.getModel(v));var D=e.getRawValue(M),L="-";D&&null!=D[2]&&(L=D[2]),o.setLabelStyle(y,x,_,b,{labelFetcher:e,labelDataIndex:M,defaultText:L,isRectText:!0}),I.setStyle(y),o.setHoverStyle(I,p.hasItemOption?x:s.extend({},x)),I.incremental=a,a&&(I.useHoverLayer=!0),h.add(I),p.setItemGraphicEl(M,I)}},_renderOnGeo:function(e,t,i,n){var a=i.targetVisuals.inRange,s=i.targetVisuals.outOfRange,c=t.getData(),d=this._hmLayer||this._hmLayer||new r;d.blurSize=t.get("blurSize"),d.pointSize=t.get("pointSize"),d.minOpacity=t.get("minOpacity"),d.maxOpacity=t.get("maxOpacity");var h=e.getViewRect().clone(),p=e.getRoamTransform();h.applyTransform(p);var g=Math.max(h.x,0),f=Math.max(h.y,0),m=Math.min(h.width+h.x,n.getWidth()),v=Math.min(h.height+h.y,n.getHeight()),y=m-g,x=v-f,_=[c.mapDimension("lng"),c.mapDimension("lat"),c.mapDimension("value")],b=c.mapArray(_,(function(t,i,n){var a=e.dataToPoint([t,i]);return a[0]-=g,a[1]-=f,a.push(n),a})),w=i.getExtent(),S="visualMap.continuous"===i.type?u(w,i.option.range):l(w,i.getPieceList(),i.option.selected);d.update(b,y,x,a.color.getNormalizer(),{inRange:a.color.getColorMapper(),outOfRange:s.color.getColorMapper()},S);var M=new o.Image({style:{width:y,height:x,x:g,y:f,image:d.canvas},silent:!0});this.group.add(M)},dispose:function(){}});e.exports=d},"480f":function(e,t,i){var n=i("4a3f"),a=i("401b"),o=[],r=[],s=[],l=n.quadraticAt,u=a.distSquare,c=Math.abs;function d(e,t,i){for(var n,a=e[0],d=e[1],h=e[2],p=1/0,g=i*i,f=.1,m=.1;m<=.9;m+=.1){o[0]=l(a[0],d[0],h[0],m),o[1]=l(a[1],d[1],h[1],m);var v=c(u(o,t)-g);v<p&&(p=v,n=m)}for(var y=0;y<32;y++){var x=n+f;r[0]=l(a[0],d[0],h[0],n),r[1]=l(a[1],d[1],h[1],n),s[0]=l(a[0],d[0],h[0],x),s[1]=l(a[1],d[1],h[1],x);v=u(r,t)-g;if(c(v)<.01)break;var _=u(s,t)-g;f/=2,v<0?_>=0?n+=f:n-=f:_>=0?n-=f:n+=f}return n}function h(e,t){var i=[],o=n.quadraticSubdivide,r=[[],[],[]],s=[[],[]],l=[];function u(e){var t=e.getVisual("symbolSize");return t instanceof Array&&(t=(t[0]+t[1])/2),t}t/=2,e.eachEdge((function(e,n){var c=e.getLayout(),h=e.getVisual("fromSymbol"),p=e.getVisual("toSymbol");c.__original||(c.__original=[a.clone(c[0]),a.clone(c[1])],c[2]&&c.__original.push(a.clone(c[2])));var g=c.__original;if(null!=c[2]){if(a.copy(r[0],g[0]),a.copy(r[1],g[2]),a.copy(r[2],g[1]),h&&"none"!==h){var f=u(e.node1),m=d(r,g[0],f*t);o(r[0][0],r[1][0],r[2][0],m,i),r[0][0]=i[3],r[1][0]=i[4],o(r[0][1],r[1][1],r[2][1],m,i),r[0][1]=i[3],r[1][1]=i[4]}if(p&&"none"!==p){f=u(e.node2),m=d(r,g[1],f*t);o(r[0][0],r[1][0],r[2][0],m,i),r[1][0]=i[1],r[2][0]=i[2],o(r[0][1],r[1][1],r[2][1],m,i),r[1][1]=i[1],r[2][1]=i[2]}a.copy(c[0],r[0]),a.copy(c[1],r[2]),a.copy(c[2],r[1])}else{if(a.copy(s[0],g[0]),a.copy(s[1],g[1]),a.sub(l,s[1],s[0]),a.normalize(l,l),h&&"none"!==h){f=u(e.node1);a.scaleAndAdd(s[0],s[0],l,f*t)}if(p&&"none"!==p){f=u(e.node2);a.scaleAndAdd(s[1],s[1],l,-f*t)}a.copy(c[0],s[0]),a.copy(c[1],s[1])}}))}e.exports=h},"49e8":function(e,t,i){var n=i("3eba"),a=i("6d8b"),o=i("d81e"),r=o.updateCenterAndZoom;n.registerAction({type:"geoRoam",event:"geoRoam",update:"updateTransform"},(function(e,t){var i=e.componentType||"series";t.eachComponent({mainType:i,query:e},(function(t){var n=t.coordinateSystem;if("geo"===n.type){var o=r(n,e,t.get("scaleLimit"));t.setCenter&&t.setCenter(o.center),t.setZoom&&t.setZoom(o.zoom),"series"===i&&a.each(t.seriesGroup,(function(e){e.setCenter(o.center),e.setZoom(o.zoom)}))}}))}))},"4a01":function(e,t,i){var n=i("6d8b"),a=i("1fab"),o=i("607d"),r=i("a4fe");function s(e){this.pointerChecker,this._zr=e,this._opt={};var t=n.bind,i=t(l,this),o=t(u,this),r=t(c,this),s=t(d,this),p=t(h,this);a.call(this),this.setPointerChecker=function(e){this.pointerChecker=e},this.enable=function(t,a){this.disable(),this._opt=n.defaults(n.clone(a)||{},{zoomOnMouseWheel:!0,moveOnMouseMove:!0,moveOnMouseWheel:!1,preventDefaultMouseMove:!0}),null==t&&(t=!0),!0!==t&&"move"!==t&&"pan"!==t||(e.on("mousedown",i),e.on("mousemove",o),e.on("mouseup",r)),!0!==t&&"scale"!==t&&"zoom"!==t||(e.on("mousewheel",s),e.on("pinch",p))},this.disable=function(){e.off("mousedown",i),e.off("mousemove",o),e.off("mouseup",r),e.off("mousewheel",s),e.off("pinch",p)},this.dispose=this.disable,this.isDragging=function(){return this._dragging},this.isPinching=function(){return this._pinching}}function l(e){if(!(o.isMiddleOrRightButtonOnMouseUpDown(e)||e.target&&e.target.draggable)){var t=e.offsetX,i=e.offsetY;this.pointerChecker&&this.pointerChecker(e,t,i)&&(this._x=t,this._y=i,this._dragging=!0)}}function u(e){if(this._dragging&&f("moveOnMouseMove",e,this._opt)&&"pinch"!==e.gestureEvent&&!r.isTaken(this._zr,"globalPan")){var t=e.offsetX,i=e.offsetY,n=this._x,a=this._y,s=t-n,l=i-a;this._x=t,this._y=i,this._opt.preventDefaultMouseMove&&o.stop(e.event),g(this,"pan","moveOnMouseMove",e,{dx:s,dy:l,oldX:n,oldY:a,newX:t,newY:i})}}function c(e){o.isMiddleOrRightButtonOnMouseUpDown(e)||(this._dragging=!1)}function d(e){var t=f("zoomOnMouseWheel",e,this._opt),i=f("moveOnMouseWheel",e,this._opt),n=e.wheelDelta,a=Math.abs(n),o=e.offsetX,r=e.offsetY;if(0!==n&&(t||i)){if(t){var s=a>3?1.4:a>1?1.2:1.1,l=n>0?s:1/s;p(this,"zoom","zoomOnMouseWheel",e,{scale:l,originX:o,originY:r})}if(i){var u=Math.abs(n),c=(n>0?1:-1)*(u>3?.4:u>1?.15:.05);p(this,"scrollMove","moveOnMouseWheel",e,{scrollDelta:c,originX:o,originY:r})}}}function h(e){if(!r.isTaken(this._zr,"globalPan")){var t=e.pinchScale>1?1.1:1/1.1;p(this,"zoom",null,e,{scale:t,originX:e.pinchX,originY:e.pinchY})}}function p(e,t,i,n,a){e.pointerChecker&&e.pointerChecker(n,a.originX,a.originY)&&(o.stop(n.event),g(e,t,i,n,a))}function g(e,t,i,a,o){o.isAvailableBehavior=n.bind(f,null,i,a),e.trigger(t,o)}function f(e,t,i){var a=i[e];return!e||a&&(!n.isString(a)||t.event[a+"Key"])}n.mixin(s,a);var m=s;e.exports=m},"4ab1":function(e,t,i){var n=i("8727"),a=n.createElement,o=i("6d8b"),r=i("cbe5"),s=i("0da8"),l=i("76a5"),u=i("3f8e"),c=u.path,d=u.image,h=u.text,p="0",g="1";function f(e,t,i,n,a){this._zrId=e,this._svgRoot=t,this._tagNames="string"===typeof i?[i]:i,this._markLabel=n,this._domName=a||"_dom",this.nextId=0}f.prototype.createElement=a,f.prototype.getDefs=function(e){var t=this._svgRoot,i=this._svgRoot.getElementsByTagName("defs");return 0===i.length?e?(i=t.insertBefore(this.createElement("defs"),t.firstChild),i.contains||(i.contains=function(e){var t=i.children;if(!t)return!1;for(var n=t.length-1;n>=0;--n)if(t[n]===e)return!0;return!1}),i):null:i[0]},f.prototype.update=function(e,t){if(e){var i=this.getDefs(!1);if(e[this._domName]&&i.contains(e[this._domName]))"function"===typeof t&&t(e);else{var n=this.add(e);n&&(e[this._domName]=n)}}},f.prototype.addDom=function(e){var t=this.getDefs(!0);t.appendChild(e)},f.prototype.removeDom=function(e){var t=this.getDefs(!1);t&&e[this._domName]&&(t.removeChild(e[this._domName]),e[this._domName]=null)},f.prototype.getDoms=function(){var e=this.getDefs(!1);if(!e)return[];var t=[];return o.each(this._tagNames,(function(i){var n=e.getElementsByTagName(i);t=t.concat([].slice.call(n))})),t},f.prototype.markAllUnused=function(){var e=this.getDoms(),t=this;o.each(e,(function(e){e[t._markLabel]=p}))},f.prototype.markUsed=function(e){e&&(e[this._markLabel]=g)},f.prototype.removeUnused=function(){var e=this.getDefs(!1);if(e){var t=this.getDoms(),i=this;o.each(t,(function(t){t[i._markLabel]!==g&&e.removeChild(t)}))}},f.prototype.getSvgProxy=function(e){return e instanceof r?c:e instanceof s?d:e instanceof l?h:c},f.prototype.getTextSvgElement=function(e){return e.__textSvgEl},f.prototype.getSvgElement=function(e){return e.__svgEl};var m=f;e.exports=m},"4b08":function(e,t,i){var n=i("7dcf"),a=n.extend({type:"dataZoom.select"});e.exports=a},"4bf6":function(e,t,i){var n=i("66fc"),a=i("697e"),o=i("f934"),r=o.getLayoutRect,s=i("6d8b"),l=s.each;function u(e,t,i){this.dimension="single",this.dimensions=["single"],this._axis=null,this._rect,this._init(e,t,i),this.model=e}u.prototype={type:"singleAxis",axisPointerEnabled:!0,constructor:u,_init:function(e,t,i){var o=this.dimension,r=new n(o,a.createScaleByModel(e),[0,0],e.get("type"),e.get("position")),s="category"===r.type;r.onBand=s&&e.get("boundaryGap"),r.inverse=e.get("inverse"),r.orient=e.get("orient"),e.axis=r,r.model=e,r.coordinateSystem=this,this._axis=r},update:function(e,t){e.eachSeries((function(e){if(e.coordinateSystem===this){var t=e.getData();l(t.mapDimension(this.dimension,!0),(function(e){this._axis.scale.unionExtentFromData(t,e)}),this),a.niceScaleExtent(this._axis.scale,this._axis.model)}}),this)},resize:function(e,t){this._rect=r({left:e.get("left"),top:e.get("top"),right:e.get("right"),bottom:e.get("bottom"),width:e.get("width"),height:e.get("height")},{width:t.getWidth(),height:t.getHeight()}),this._adjustAxis()},getRect:function(){return this._rect},_adjustAxis:function(){var e=this._rect,t=this._axis,i=t.isHorizontal(),n=i?[0,e.width]:[0,e.height],a=t.reverse?1:0;t.setExtent(n[a],n[1-a]),this._updateAxisTransform(t,i?e.x:e.y)},_updateAxisTransform:function(e,t){var i=e.getExtent(),n=i[0]+i[1],a=e.isHorizontal();e.toGlobalCoord=a?function(e){return e+t}:function(e){return n-e+t},e.toLocalCoord=a?function(e){return e-t}:function(e){return n-e+t}},getAxis:function(){return this._axis},getBaseAxis:function(){return this._axis},getAxes:function(){return[this._axis]},getTooltipAxes:function(){return{baseAxes:[this.getAxis()]}},containPoint:function(e){var t=this.getRect(),i=this.getAxis(),n=i.orient;return"horizontal"===n?i.contain(i.toLocalCoord(e[0]))&&e[1]>=t.y&&e[1]<=t.y+t.height:i.contain(i.toLocalCoord(e[1]))&&e[0]>=t.y&&e[0]<=t.y+t.height},pointToData:function(e){var t=this.getAxis();return[t.coordToData(t.toLocalCoord(e["horizontal"===t.orient?0:1]))]},dataToPoint:function(e){var t=this.getAxis(),i=this.getRect(),n=[],a="horizontal"===t.orient?0:1;return e instanceof Array&&(e=e[0]),n[a]=t.toGlobalCoord(t.dataToCoord(+e)),n[1-a]=0===a?i.y+i.height/2:i.x+i.width/2,n}};var c=u;e.exports=c},"4c86":function(e,t,i){var n=i("6d8b"),a=n.each,o=i("bda7"),r=i("e0d3"),s=r.makeInner,l=i("320a"),u=i("1792"),c=i("6bd4"),d=i("a7f2"),h=s(),p={load:function(e,t){var i=h(t).parsed;if(i)return i;var n,r=t.specialAreas||{},s=t.geoJSON;try{n=s?o(s):[]}catch(p){throw new Error("Invalid geoJson format\n"+p.message)}return a(n,(function(t){var i=t.name;u(e,t),c(e,t),d(e,t);var n=r[i];n&&t.transformTo(n.left,n.top,n.width,n.height)})),l(e,n),h(t).parsed={regions:n,boundingRect:g(n)}}};function g(e){for(var t,i=0;i<e.length;i++){var n=e[i].getBoundingRect();t=t||n.clone(),t.union(n)}return t}e.exports=p},"4c99":function(e,t,i){var n=i("cccd"),a=["itemStyle","borderColor"],o=["itemStyle","borderColor0"],r=["itemStyle","color"],s=["itemStyle","color0"],l={seriesType:"candlestick",plan:n(),performRawSeries:!0,reset:function(e,t){var i=e.getData(),n=e.pipelineContext.large;if(i.setVisual({legendSymbol:"roundRect",colorP:u(1,e),colorN:u(-1,e),borderColorP:c(1,e),borderColorN:c(-1,e)}),!t.isSeriesFiltered(e))return!n&&{progress:l};function l(e,t){var i;while(null!=(i=e.next())){var n=t.getItemModel(i),a=t.getItemLayout(i).sign;t.setItemVisual(i,{color:u(a,n),borderColor:c(a,n)})}}function u(e,t){return t.get(e>0?r:s)}function c(e,t){return t.get(e>0?a:o)}}};e.exports=l},"4d62":function(e,t,i){var n=i("2306"),a=i("6d8b"),o=i("e887");function r(e,t){n.Group.call(this);var i=new n.Polygon,a=new n.Polyline,o=new n.Text;function r(){a.ignore=a.hoverIgnore,o.ignore=o.hoverIgnore}function s(){a.ignore=a.normalIgnore,o.ignore=o.normalIgnore}this.add(i),this.add(a),this.add(o),this.updateData(e,t,!0),this.on("emphasis",r).on("normal",s).on("mouseover",r).on("mouseout",s)}var s=r.prototype,l=["itemStyle","opacity"];s.updateData=function(e,t,i){var o=this.childAt(0),r=e.hostModel,s=e.getItemModel(t),u=e.getItemLayout(t),c=e.getItemModel(t).get(l);c=null==c?1:c,o.useStyle({}),i?(o.setShape({points:u.points}),o.setStyle({opacity:0}),n.initProps(o,{style:{opacity:c}},r,t)):n.updateProps(o,{style:{opacity:c},shape:{points:u.points}},r,t);var d=s.getModel("itemStyle"),h=e.getItemVisual(t,"color");o.setStyle(a.defaults({lineJoin:"round",fill:h},d.getItemStyle(["opacity"]))),o.hoverStyle=d.getModel("emphasis").getItemStyle(),this._updateLabel(e,t),n.setHoverStyle(this)},s._updateLabel=function(e,t){var i=this.childAt(1),a=this.childAt(2),o=e.hostModel,r=e.getItemModel(t),s=e.getItemLayout(t),l=s.label,u=e.getItemVisual(t,"color");n.updateProps(i,{shape:{points:l.linePoints||l.linePoints}},o,t),n.updateProps(a,{style:{x:l.x,y:l.y}},o,t),a.attr({rotation:l.rotation,origin:[l.x,l.y],z2:10});var c=r.getModel("label"),d=r.getModel("emphasis.label"),h=r.getModel("labelLine"),p=r.getModel("emphasis.labelLine");u=e.getItemVisual(t,"color");n.setLabelStyle(a.style,a.hoverStyle={},c,d,{labelFetcher:e.hostModel,labelDataIndex:t,defaultText:e.getName(t),autoColor:u,useInsideStyle:!!l.inside},{textAlign:l.textAlign,textVerticalAlign:l.verticalAlign}),a.ignore=a.normalIgnore=!c.get("show"),a.hoverIgnore=!d.get("show"),i.ignore=i.normalIgnore=!h.get("show"),i.hoverIgnore=!p.get("show"),i.setStyle({stroke:u}),i.setStyle(h.getModel("lineStyle").getLineStyle()),i.hoverStyle=p.getModel("lineStyle").getLineStyle()},a.inherits(r,n.Group);var u=o.extend({type:"funnel",render:function(e,t,i){var n=e.getData(),a=this._data,o=this.group;n.diff(a).add((function(e){var t=new r(n,e);n.setItemGraphicEl(e,t),o.add(t)})).update((function(e,t){var i=a.getItemGraphicEl(t);i.updateData(n,e),o.add(i),n.setItemGraphicEl(e,i)})).remove((function(e){var t=a.getItemGraphicEl(e);o.remove(t)})).execute(),this._data=n},remove:function(){this.group.removeAll(),this._data=null},dispose:function(){}}),c=u;e.exports=c},"4d85":function(e,t,i){var n=i("e46b"),a=i("4f85"),o=i("6d8b"),r=a.extend({type:"series.gauge",getInitialData:function(e,t){var i=e.data||[];return o.isArray(i)||(i=[i]),e.data=i,n(this,["value"])},defaultOption:{zlevel:0,z:2,center:["50%","50%"],legendHoverLink:!0,radius:"75%",startAngle:225,endAngle:-45,clockwise:!0,min:0,max:100,splitNumber:10,axisLine:{show:!0,lineStyle:{color:[[.2,"#91c7ae"],[.8,"#63869e"],[1,"#c23531"]],width:30}},splitLine:{show:!0,length:30,lineStyle:{color:"#eee",width:2,type:"solid"}},axisTick:{show:!0,splitNumber:5,length:8,lineStyle:{color:"#eee",width:1,type:"solid"}},axisLabel:{show:!0,distance:5,color:"auto"},pointer:{show:!0,length:"80%",width:8},itemStyle:{color:"auto"},title:{show:!0,offsetCenter:[0,"-40%"],color:"#333",fontSize:15},detail:{show:!0,backgroundColor:"rgba(0,0,0,0)",borderWidth:0,borderColor:"#ccc",width:100,height:null,padding:[5,10],offsetCenter:[0,"40%"],color:"auto",fontSize:30}}}),s=r;e.exports=s},"4e10":function(e,t,i){var n=i("6d8b"),a=i("e46b"),o=i("4f85"),r=i("eda2"),s=r.encodeHTML,l=r.addCommas,u=i("7023"),c=i("2b17"),d=c.retrieveRawAttr,h=i("5b87"),p=o.extend({type:"series.map",dependencies:["geo"],layoutMode:"box",needsDrawMap:!1,seriesGroup:[],getInitialData:function(e){for(var t=a(this,["value"]),i=t.mapDimension("value"),o=n.createHashMap(),r=[],s=[],l=0,u=t.count();l<u;l++){var c=t.getName(l);o.set(c,!0),r.push({name:c,value:t.get(i,l),selected:d(t,l,"selected")})}var p=h.load(this.getMapType(),this.option.nameMap);return n.each(p.regions,(function(e){var t=e.name;o.get(t)||(r.push({name:t}),s.push(t))})),this.updateSelectedMap(r),t.appendValues([],s),t},getHostGeoModel:function(){var e=this.option.geoIndex;return null!=e?this.dependentModels.geo[e]:null},getMapType:function(){return(this.getHostGeoModel()||this).option.map},getRawValue:function(e){var t=this.getData();return t.get(t.mapDimension("value"),e)},getRegionModel:function(e){var t=this.getData();return t.getItemModel(t.indexOfName(e))},formatTooltip:function(e){for(var t=this.getData(),i=l(this.getRawValue(e)),n=t.getName(e),a=this.seriesGroup,o=[],r=0;r<a.length;r++){var u=a[r].originalData.indexOfName(n),c=t.mapDimension("value");isNaN(a[r].originalData.get(c,u))||o.push(s(a[r].name))}return o.join(", ")+"<br />"+s(n+" : "+i)},getTooltipPosition:function(e){if(null!=e){var t=this.getData().getName(e),i=this.coordinateSystem,n=i.getRegion(t);return n&&i.dataToPoint(n.center)}},setZoom:function(e){this.option.zoom=e},setCenter:function(e){this.option.center=e},defaultOption:{zlevel:0,z:2,coordinateSystem:"geo",map:"",left:"center",top:"center",aspectScale:.75,showLegendSymbol:!0,dataRangeHoverLink:!0,boundingCoords:null,center:null,zoom:1,scaleLimit:null,label:{show:!1,color:"#000"},itemStyle:{borderWidth:.5,borderColor:"#444",areaColor:"#eee"},emphasis:{label:{show:!0,color:"rgb(100,0,0)"},itemStyle:{areaColor:"rgba(255,215,0,0.8)"}}}});n.mixin(p,u);var g=p;e.exports=g},"4e47":function(e,t,i){var n=i("6d8b"),a=i("2306"),o={NONE:"none",DESCENDANT:"descendant",ANCESTOR:"ancestor",SELF:"self"},r=2,s=4;function l(e,t,i){a.Group.call(this);var n=new a.Sector({z2:r});n.seriesIndex=t.seriesIndex;var o=new a.Text({z2:s,silent:e.getModel("label").get("silent")});function l(){o.ignore=o.hoverIgnore}function u(){o.ignore=o.normalIgnore}this.add(n),this.add(o),this.updateData(!0,e,"normal",t,i),this.on("emphasis",l).on("normal",u).on("mouseover",l).on("mouseout",u)}var u=l.prototype;u.updateData=function(e,t,i,o,r){this.node=t,t.piece=this,o=o||this._seriesModel,r=r||this._ecModel;var s=this.childAt(0);s.dataIndex=t.dataIndex;var l=t.getModel(),u=t.getLayout(),c=n.extend({},u);c.label=null;var h=d(t,o,r);g(t,o,h);var p,f=l.getModel("itemStyle").getItemStyle();if("normal"===i)p=f;else{var m=l.getModel(i+".itemStyle").getItemStyle();p=n.merge(m,f)}p=n.defaults({lineJoin:"bevel",fill:p.fill||h},p),e?(s.setShape(c),s.shape.r=u.r0,a.updateProps(s,{shape:{r:u.r}},o,t.dataIndex),s.useStyle(p)):"object"===typeof p.fill&&p.fill.type||"object"===typeof s.style.fill&&s.style.fill.type?(a.updateProps(s,{shape:c},o),s.useStyle(p)):a.updateProps(s,{shape:c,style:p},o),this._updateLabel(o,h,i);var v=l.getShallow("cursor");if(v&&s.attr("cursor",v),e){var y=o.getShallow("highlightPolicy");this._initEvents(s,t,o,y)}this._seriesModel=o||this._seriesModel,this._ecModel=r||this._ecModel},u.onEmphasis=function(e){var t=this;this.node.hostTree.root.eachNode((function(i){i.piece&&(t.node===i?i.piece.updateData(!1,i,"emphasis"):p(i,t.node,e)?i.piece.childAt(0).trigger("highlight"):e!==o.NONE&&i.piece.childAt(0).trigger("downplay"))}))},u.onNormal=function(){this.node.hostTree.root.eachNode((function(e){e.piece&&e.piece.updateData(!1,e,"normal")}))},u.onHighlight=function(){this.updateData(!1,this.node,"highlight")},u.onDownplay=function(){this.updateData(!1,this.node,"downplay")},u._updateLabel=function(e,t,i){var o=this.node.getModel(),r=o.getModel("label"),s="normal"===i||"emphasis"===i?r:o.getModel(i+".label"),l=o.getModel("emphasis.label"),u=n.retrieve(e.getFormattedLabel(this.node.dataIndex,"normal",null,null,"label"),this.node.name);!1===I("show")&&(u="");var c=this.node.getLayout(),d=s.get("minAngle");null==d&&(d=r.get("minAngle")),d=d/180*Math.PI;var h=c.endAngle-c.startAngle;null!=d&&Math.abs(h)<d&&(u="");var p=this.childAt(1);a.setLabelStyle(p.style,p.hoverStyle||{},r,l,{defaultText:s.getShallow("show")?u:null,autoColor:t,useInsideStyle:!0});var g,f=(c.startAngle+c.endAngle)/2,m=Math.cos(f),v=Math.sin(f),y=I("position"),x=I("distance")||0,_=I("align");"outside"===y?(g=c.r+x,_=f>Math.PI/2?"right":"left"):_&&"center"!==_?"left"===_?(g=c.r0+x,f>Math.PI/2&&(_="right")):"right"===_&&(g=c.r-x,f>Math.PI/2&&(_="left")):(g=(c.r+c.r0)/2,_="center"),p.attr("style",{text:u,textAlign:_,textVerticalAlign:I("verticalAlign")||"middle",opacity:I("opacity")});var b=g*m+c.cx,w=g*v+c.cy;p.attr("position",[b,w]);var S=I("rotate"),M=0;function I(e){var t=s.get(e);return null==t?r.get(e):t}"radial"===S?(M=-f,M<-Math.PI/2&&(M+=Math.PI)):"tangential"===S?(M=Math.PI/2-f,M>Math.PI/2?M-=Math.PI:M<-Math.PI/2&&(M+=Math.PI)):"number"===typeof S&&(M=S*Math.PI/180),p.attr("rotation",M)},u._initEvents=function(e,t,i,n){e.off("mouseover").off("mouseout").off("emphasis").off("normal");var a=this,o=function(){a.onEmphasis(n)},r=function(){a.onNormal()},s=function(){a.onDownplay()},l=function(){a.onHighlight()};i.isAnimationEnabled()&&e.on("mouseover",o).on("mouseout",r).on("emphasis",o).on("normal",r).on("downplay",s).on("highlight",l)},n.inherits(l,a.Group);var c=l;function d(e,t,i){var n=e.getVisual("color"),a=e.getVisual("visualMeta");a&&0!==a.length||(n=null);var o=e.getModel("itemStyle").get("color");if(o)return o;if(n)return n;if(0===e.depth)return i.option.color[0];var r=i.option.color.length;return o=i.option.color[h(e)%r],o}function h(e){var t=e;while(t.depth>1)t=t.parentNode;var i=e.getAncestors()[0];return n.indexOf(i.children,t)}function p(e,t,i){return i!==o.NONE&&(i===o.SELF?e===t:i===o.ANCESTOR?e===t||e.isAncestorOf(t):e===t||e.isDescendantOf(t))}function g(e,t,i){var n=t.getData();n.setItemVisual(e.dataIndex,"color",i)}e.exports=c},"4e9f":function(e,t,i){var n=i("22d1"),a=i("29a8"),o=i("2145"),r=a.toolbox.saveAsImage;function s(e){this.model=e}s.defaultOption={show:!0,icon:"M4.7,22.9L29.3,45.5L54.7,23.4M4.6,43.6L4.6,58L53.8,58L53.8,43.6M29.2,45.1L29.2,0",title:r.title,type:"png",name:"",excludeComponents:["toolbox"],pixelRatio:1,lang:r.lang.slice()},s.prototype.unusable=!n.canvasSupported;var l=s.prototype;l.onclick=function(e,t){var i=this.model,a=i.get("name")||e.get("title.0.text")||"echarts",o=document.createElement("a"),r=i.get("type",!0)||"png";o.download=a+"."+r,o.target="_blank";var s=t.getConnectedDataURL({type:r,backgroundColor:i.get("backgroundColor",!0)||e.get("backgroundColor")||"#fff",excludeComponents:i.get("excludeComponents"),pixelRatio:i.get("pixelRatio")});if(o.href=s,"function"!==typeof MouseEvent||n.browser.ie||n.browser.edge)if(window.navigator.msSaveOrOpenBlob){var l=atob(s.split(",")[1]),u=l.length,c=new Uint8Array(u);while(u--)c[u]=l.charCodeAt(u);var d=new Blob([c]);window.navigator.msSaveOrOpenBlob(d,a+"."+r)}else{var h=i.get("lang"),p='<body style="margin:0;"><img src="'+s+'" style="max-width:100%;" title="'+(h&&h[0]||"")+'" /></body>',g=window.open();g.document.write(p)}else{var f=new MouseEvent("click",{view:window,bubbles:!0,cancelable:!1});o.dispatchEvent(f)}},o.register("saveAsImage",s);var u=s;e.exports=u},"50e5":function(e,t,i){var n=i("6d8b"),a=i("eda2"),o=["x","y","z","radius","angle","single"],r=["cartesian2d","polar","singleAxis"];function s(e){return n.indexOf(r,e)>=0}function l(e,t){e=e.slice();var i=n.map(e,a.capitalFirst);t=(t||[]).slice();var o=n.map(t,a.capitalFirst);return function(a,r){n.each(e,(function(e,n){for(var s={name:e,capital:i[n]},l=0;l<t.length;l++)s[t[l]]=e+o[l];a.call(r,s)}))}}var u=l(o,["axisIndex","axis","index","id"]);function c(e,t,i){return function(i){var n,s={nodes:[],records:{}};if(t((function(e){s.records[e.name]={}})),!i)return s;r(i,s);do{n=!1,e(l)}while(n);function l(e){!a(e,s)&&o(e,s)&&(r(e,s),n=!0)}return s};function a(e,t){return n.indexOf(t.nodes,e)>=0}function o(e,a){var o=!1;return t((function(t){n.each(i(e,t)||[],(function(e){a.records[t.name][e]&&(o=!0)}))})),o}function r(e,a){a.nodes.push(e),t((function(t){n.each(i(e,t)||[],(function(e){a.records[t.name][e]=!0}))}))}}t.isCoordSupported=s,t.createNameEach=l,t.eachAxisDim=u,t.createLinkedNodesFinder=c},"527a":function(e,t,i){var n=i("6d8b"),a=i("3842");function o(e,t){e.eachSeriesByType("themeRiver",(function(e){var t=e.getData(),i=e.coordinateSystem,n={},o=i.getRect();n.rect=o;var s=e.get("boundaryGap"),l=i.getAxis();if(n.boundaryGap=s,"horizontal"===l.orient){s[0]=a.parsePercent(s[0],o.height),s[1]=a.parsePercent(s[1],o.height);var u=o.height-s[0]-s[1];r(t,e,u)}else{s[0]=a.parsePercent(s[0],o.width),s[1]=a.parsePercent(s[1],o.width);var c=o.width-s[0]-s[1];r(t,e,c)}t.setLayout("layoutInfo",n)}))}function r(e,t,i){if(e.count())for(var a,o=t.coordinateSystem,r=t.getLayerSeries(),l=e.mapDimension("single"),u=e.mapDimension("value"),c=n.map(r,(function(t){return n.map(t.indices,(function(t){var i=o.dataToPoint(e.get(l,t));return i[1]=e.get(u,t),i}))})),d=s(c),h=d.y0,p=i/d.max,g=r.length,f=r[0].indices.length,m=0;m<f;++m){a=h[m]*p,e.setItemLayout(r[0].indices[m],{layerIndex:0,x:c[0][m][0],y0:a,y:c[0][m][1]*p});for(var v=1;v<g;++v)a+=c[v-1][m][1]*p,e.setItemLayout(r[v].indices[m],{layerIndex:v,x:c[v][m][0],y0:a,y:c[v][m][1]*p})}}function s(e){for(var t=e.length,i=e[0].length,n=[],a=[],o=0,r={},s=0;s<i;++s){for(var l=0,u=0;l<t;++l)u+=e[l][s][1];u>o&&(o=u),n.push(u)}for(var c=0;c<i;++c)a[c]=(o-n[c])/2;o=0;for(var d=0;d<i;++d){var h=n[d]+a[d];h>o&&(o=h)}return r.y0=a,r.max=o,r}e.exports=o},5450:function(e,t,i){i("7419"),i("29a9")},"54fb":function(e,t){function i(e){e.eachSeriesByType("map",(function(e){var t=e.get("color"),i=e.getModel("itemStyle"),n=i.get("areaColor"),a=i.get("color")||t[e.seriesIndex%t.length];e.getData().setVisual({areaColor:n,color:a})}))}e.exports=i},5522:function(e,t,i){i("23e0"),i("1748"),i("6c12")},5576:function(e,t,i){var n=i("6d8b"),a=i("4a01"),o=i("88b3"),r="\0_ec_dataZoom_roams";function s(e,t){var i=c(e),a=t.dataZoomId,r=t.coordId;n.each(i,(function(e,i){var o=e.dataZoomInfos;o[a]&&n.indexOf(t.allCoordIds,r)<0&&(delete o[a],e.count--)})),h(i);var s=i[r];s||(s=i[r]={coordId:r,dataZoomInfos:{},count:0},s.controller=d(e,s),s.dispatchAction=n.curry(p,e)),!s.dataZoomInfos[a]&&s.count++,s.dataZoomInfos[a]=t;var l=g(s.dataZoomInfos);s.controller.enable(l.controlType,l.opt),s.controller.setPointerChecker(t.containsPoint),o.createOrUpdate(s,"dispatchAction",t.dataZoomModel.get("throttle",!0),"fixRate")}function l(e,t){var i=c(e);n.each(i,(function(e){e.controller.dispose();var i=e.dataZoomInfos;i[t]&&(delete i[t],e.count--)})),h(i)}function u(e){return e.type+"\0_"+e.id}function c(e){var t=e.getZr();return t[r]||(t[r]={})}function d(e,t){var i=new a(e.getZr());return n.each(["pan","zoom","scrollMove"],(function(e){i.on(e,(function(i){var a=[];n.each(t.dataZoomInfos,(function(n){if(i.isAvailableBehavior(n.dataZoomModel.option)){var o=(n.getRange||{})[e],r=o&&o(t.controller,i);!n.dataZoomModel.get("disabled",!0)&&r&&a.push({dataZoomId:n.dataZoomId,start:r[0],end:r[1]})}})),a.length&&t.dispatchAction(a)}))})),i}function h(e){n.each(e,(function(t,i){t.count||(t.controller.dispose(),delete e[i])}))}function p(e,t){e.dispatchAction({type:"dataZoom",batch:t})}function g(e){var t,i="type_",a={type_true:2,type_move:1,type_false:0,type_undefined:-1},o=!0;return n.each(e,(function(e){var n=e.dataZoomModel,r=!n.get("disabled",!0)&&(!n.get("zoomLock",!0)||"move");a[i+r]>a[i+t]&&(t=r),o&=n.get("preventDefaultMouseMove",!0)})),{controlType:t,opt:{zoomOnMouseWheel:!0,moveOnMouseMove:!0,moveOnMouseWheel:!0,preventDefaultMouseMove:!!o}}}t.register=s,t.unregister=l,t.generateCoordId=u},"55ac":function(e,t,i){var n=i("6d8b");function a(e,t,i){if(e&&n.indexOf(t,e.type)>=0){var a=i.getData().tree.root,o=e.targetNode;if("string"===typeof o&&(o=a.getNodeById(o)),o&&a.contains(o))return{node:o};var r=e.targetNodeId;if(null!=r&&(o=a.getNodeById(r)))return{node:o}}}function o(e){var t=[];while(e)e=e.parentNode,e&&t.push(e);return t.reverse()}function r(e,t){var i=o(e);return n.indexOf(i,t)>=0}function s(e,t){var i=[];while(e){var n=e.dataIndex;i.push({name:e.name,dataIndex:n,value:t.getRawValue(n)}),e=e.parentNode}return i.reverse(),i}t.retrieveTargetInfo=a,t.getPathToRoot=o,t.aboveViewRoot=r,t.wrapTreePathInfo=s},5866:function(e,t,i){var n=i("ef2b"),a=n.forceLayout,o=i("1c5f"),r=o.simpleLayout,s=i("94e4"),l=s.circularLayout,u=i("3842"),c=u.linearMap,d=i("401b"),h=i("6d8b");function p(e){e.eachSeriesByType("graph",(function(e){var t=e.coordinateSystem;if(!t||"view"===t.type)if("force"===e.get("layout")){var i=e.preservedPoints||{},n=e.getGraph(),o=n.data,s=n.edgeData,u=e.getModel("force"),p=u.get("initLayout");e.preservedPoints?o.each((function(e){var t=o.getId(e);o.setItemLayout(e,i[t]||[NaN,NaN])})):p&&"none"!==p?"circular"===p&&l(e):r(e);var g=o.getDataExtent("value"),f=s.getDataExtent("value"),m=u.get("repulsion"),v=u.get("edgeLength");h.isArray(m)||(m=[m,m]),h.isArray(v)||(v=[v,v]),v=[v[1],v[0]];var y=o.mapArray("value",(function(e,t){var i=o.getItemLayout(t),n=c(e,g,m);return isNaN(n)&&(n=(m[0]+m[1])/2),{w:n,rep:n,fixed:o.getItemModel(t).get("fixed"),p:!i||isNaN(i[0])||isNaN(i[1])?null:i}})),x=s.mapArray("value",(function(e,t){var i=n.getEdgeByIndex(t),a=c(e,f,v);return isNaN(a)&&(a=(v[0]+v[1])/2),{n1:y[i.node1.dataIndex],n2:y[i.node2.dataIndex],d:a,curveness:i.getModel().get("lineStyle.curveness")||0}})),_=(t=e.coordinateSystem,t.getBoundingRect()),b=a(y,x,{rect:_,gravity:u.get("gravity")}),w=b.step;b.step=function(e){for(var t=0,a=y.length;t<a;t++)y[t].fixed&&d.copy(y[t].p,n.getNodeByIndex(t).getLayout());w((function(t,a,r){for(var s=0,l=t.length;s<l;s++)t[s].fixed||n.getNodeByIndex(s).setLayout(t[s].p),i[o.getId(s)]=t[s].p;for(s=0,l=a.length;s<l;s++){var u=a[s],c=n.getEdgeByIndex(s),h=u.n1.p,p=u.n2.p,g=c.getLayout();g=g?g.slice():[],g[0]=g[0]||[],g[1]=g[1]||[],d.copy(g[0],h),d.copy(g[1],p),+u.curveness&&(g[2]=[(h[0]+p[0])/2-(h[1]-p[1])*u.curveness,(h[1]+p[1])/2-(p[0]-h[0])*u.curveness]),c.setLayout(g)}e&&e(r)}))},e.forceLayout=b,e.preservedPoints=i,b.step()}else e.forceLayout=null}))}e.exports=p},"5b69":function(e,t,i){var n=i("2306"),a=i("3eba"),o=i("6d8b"),r=["itemStyle","opacity"],s=["lineStyle","opacity"];function l(e,t){return e.getVisual("opacity")||e.getModel().get(t)}function u(e,t,i){var n=e.getGraphicEl(),a=l(e,t);null!=i&&(null==a&&(a=1),a*=i),n.downplay&&n.downplay(),n.traverse((function(e){"group"!==e.type&&e.setStyle("opacity",a)}))}function c(e,t){var i=l(e,t),n=e.getGraphicEl();n.highlight&&n.highlight(),n.traverse((function(e){"group"!==e.type&&e.setStyle("opacity",i)}))}var d=n.extendShape({shape:{x1:0,y1:0,x2:0,y2:0,cpx1:0,cpy1:0,cpx2:0,cpy2:0,extent:0,orient:""},buildPath:function(e,t){var i=t.extent,n=t.orient;"vertical"===n?(e.moveTo(t.x1,t.y1),e.bezierCurveTo(t.cpx1,t.cpy1,t.cpx2,t.cpy2,t.x2,t.y2),e.lineTo(t.x2+i,t.y2),e.bezierCurveTo(t.cpx2+i,t.cpy2,t.cpx1+i,t.cpy1,t.x1+i,t.y1)):(e.moveTo(t.x1,t.y1),e.bezierCurveTo(t.cpx1,t.cpy1,t.cpx2,t.cpy2,t.x2,t.y2),e.lineTo(t.x2,t.y2+i),e.bezierCurveTo(t.cpx2,t.cpy2+i,t.cpx1,t.cpy1+i,t.x1,t.y1+i)),e.closePath()}}),h=a.extendChartView({type:"sankey",_model:null,_focusAdjacencyDisabled:!1,render:function(e,t,i){var a=this,o=e.getGraph(),r=this.group,s=e.layoutInfo,l=s.width,u=s.height,c=e.getData(),h=e.getData("edge"),g=e.get("orient");this._model=e,r.removeAll(),r.attr("position",[s.x,s.y]),o.eachEdge((function(t){var i=new d;i.dataIndex=t.dataIndex,i.seriesIndex=e.seriesIndex,i.dataType="edge";var a,o,s,c,p,f,m,v,y=t.getModel("lineStyle"),x=y.get("curveness"),_=t.node1.getLayout(),b=t.node1.getModel(),w=b.get("localX"),S=b.get("localY"),M=t.node2.getLayout(),I=t.node2.getModel(),A=I.get("localX"),T=I.get("localY"),D=t.getLayout();switch(i.shape.extent=Math.max(1,D.dy),i.shape.orient=g,"vertical"===g?(a=(null!=w?w*l:_.x)+D.sy,o=(null!=S?S*u:_.y)+_.dy,s=(null!=A?A*l:M.x)+D.ty,c=null!=T?T*u:M.y,p=a,f=o*(1-x)+c*x,m=s,v=o*x+c*(1-x)):(a=(null!=w?w*l:_.x)+_.dx,o=(null!=S?S*u:_.y)+D.sy,s=null!=A?A*l:M.x,c=(null!=T?T*u:M.y)+D.ty,p=a*(1-x)+s*x,f=o,m=a*x+s*(1-x),v=c),i.setShape({x1:a,y1:o,x2:s,y2:c,cpx1:p,cpy1:f,cpx2:m,cpy2:v}),i.setStyle(y.getItemStyle()),i.style.fill){case"source":i.style.fill=t.node1.getVisual("color");break;case"target":i.style.fill=t.node2.getVisual("color");break}n.setHoverStyle(i,t.getModel("emphasis.lineStyle").getItemStyle()),r.add(i),h.setItemGraphicEl(t.dataIndex,i)})),o.eachNode((function(t){var i=t.getLayout(),a=t.getModel(),o=a.get("localX"),s=a.get("localY"),d=a.getModel("label"),h=a.getModel("emphasis.label"),p=new n.Rect({shape:{x:null!=o?o*l:i.x,y:null!=s?s*u:i.y,width:i.dx,height:i.dy},style:a.getModel("itemStyle").getItemStyle()}),g=t.getModel("emphasis.itemStyle").getItemStyle();n.setLabelStyle(p.style,g,d,h,{labelFetcher:e,labelDataIndex:t.dataIndex,defaultText:t.id,isRectText:!0}),p.setStyle("fill",t.getVisual("color")),n.setHoverStyle(p,g),r.add(p),c.setItemGraphicEl(t.dataIndex,p),p.dataType="node"})),c.eachItemGraphicEl((function(t,n){var o=c.getItemModel(n);o.get("draggable")&&(t.drift=function(t,o){a._focusAdjacencyDisabled=!0,this.shape.x+=t,this.shape.y+=o,this.dirty(),i.dispatchAction({type:"dragNode",seriesId:e.id,dataIndex:c.getRawIndex(n),localX:this.shape.x/l,localY:this.shape.y/u})},t.ondragend=function(){a._focusAdjacencyDisabled=!1},t.draggable=!0,t.cursor="move"),o.get("focusNodeAdjacency")&&(t.off("mouseover").on("mouseover",(function(){a._focusAdjacencyDisabled||i.dispatchAction({type:"focusNodeAdjacency",seriesId:e.id,dataIndex:t.dataIndex})})),t.off("mouseout").on("mouseout",(function(){a._focusAdjacencyDisabled||i.dispatchAction({type:"unfocusNodeAdjacency",seriesId:e.id})})))})),h.eachItemGraphicEl((function(t,n){var o=h.getItemModel(n);o.get("focusNodeAdjacency")&&(t.off("mouseover").on("mouseover",(function(){a._focusAdjacencyDisabled||i.dispatchAction({type:"focusNodeAdjacency",seriesId:e.id,edgeDataIndex:t.dataIndex})})),t.off("mouseout").on("mouseout",(function(){a._focusAdjacencyDisabled||i.dispatchAction({type:"unfocusNodeAdjacency",seriesId:e.id})})))})),!this._data&&e.get("animation")&&r.setClipPath(p(r.getBoundingRect(),e,(function(){r.removeClipPath()}))),this._data=e.getData()},dispose:function(){},focusNodeAdjacency:function(e,t,i,n){var a=this._model.getData(),l=a.graph,d=n.dataIndex,h=a.getItemModel(d),p=n.edgeDataIndex;if(null!=d||null!=p){var g=l.getNodeByIndex(d),f=l.getEdgeByIndex(p);if(l.eachNode((function(e){u(e,r,.1)})),l.eachEdge((function(e){u(e,s,.1)})),g){c(g,r);var m=h.get("focusNodeAdjacency");"outEdges"===m?o.each(g.outEdges,(function(e){e.dataIndex<0||(c(e,s),c(e.node2,r))})):"inEdges"===m?o.each(g.inEdges,(function(e){e.dataIndex<0||(c(e,s),c(e.node1,r))})):"allEdges"===m&&o.each(g.edges,(function(e){e.dataIndex<0||(c(e,s),c(e.node1,r),c(e.node2,r))}))}f&&(c(f,s),c(f.node1,r),c(f.node2,r))}},unfocusNodeAdjacency:function(e,t,i,n){var a=this._model.getGraph();a.eachNode((function(e){u(e,r)})),a.eachEdge((function(e){u(e,s)}))}});function p(e,t,i){var a=new n.Rect({shape:{x:e.x-10,y:e.y-10,width:0,height:e.height+20}});return n.initProps(a,{shape:{width:e.width+20,height:e.height+20}},t,i),a}e.exports=h},"5b87":function(e,t,i){var n=i("4e08"),a=(n.__DEV__,i("6d8b")),o=a.each,r=a.createHashMap,s=i("ec34"),l=i("4c86"),u=i("c92f"),c=i("9850"),d={geoJSON:l,svg:u},h={load:function(e,t){var i,n=[],a=r(),s=r(),l=g(e);return o(l,(function(r){var l=d[r.type].load(e,r);o(l.regions,(function(e){var i=e.name;t&&t.hasOwnProperty(i)&&(e=e.cloneShallow(i=t[i])),n.push(e),a.set(i,e),s.set(i,e.center)}));var u=l.boundingRect;u&&(i?i.union(u):i=u.clone())})),{regions:n,regionsMap:a,nameCoordMap:s,boundingRect:i||new c(0,0,0,0)}},makeGraphic:p("makeGraphic"),removeGraphic:p("removeGraphic")};function p(e){return function(t,i){var n=g(t),a=[];return o(n,(function(n){var o=d[n.type][e];o&&a.push(o(t,n,i))})),a}}function g(e){var t=s.retrieveMap(e)||[];return t}e.exports=h},"5ce2":function(e,t,i){i("3970"),i("480e")},"5e97":function(e,t,i){var n=i("4e08"),a=(n.__DEV__,i("3eba")),o=i("6d8b"),r=i("a15a"),s=r.createSymbol,l=i("2306"),u=i("7919"),c=u.makeBackground,d=i("f934"),h=o.curry,p=o.each,g=l.Group,f=a.extendComponentView({type:"legend.plain",newlineDisabled:!1,init:function(){this.group.add(this._contentGroup=new g),this._backgroundEl,this._isFirstRender=!0},getContentGroup:function(){return this._contentGroup},render:function(e,t,i){var n=this._isFirstRender;if(this._isFirstRender=!1,this.resetInner(),e.get("show",!0)){var a=e.get("align");a&&"auto"!==a||(a="right"===e.get("left")&&"vertical"===e.get("orient")?"right":"left"),this.renderInner(a,e,t,i);var r=e.getBoxLayoutParams(),s={width:i.getWidth(),height:i.getHeight()},l=e.get("padding"),u=d.getLayoutRect(r,s,l),h=this.layoutInner(e,a,u,n),p=d.getLayoutRect(o.defaults({width:h.width,height:h.height},r),s,l);this.group.attr("position",[p.x-h.x,p.y-h.y]),this.group.add(this._backgroundEl=c(h,e))}},resetInner:function(){this.getContentGroup().removeAll(),this._backgroundEl&&this.group.remove(this._backgroundEl)},renderInner:function(e,t,i,n){var a=this.getContentGroup(),r=o.createHashMap(),s=t.get("selectedMode"),l=[];i.eachRawSeries((function(e){!e.get("legendHoverLink")&&l.push(e.id)})),p(t.getData(),(function(o,u){var c=o.get("name");if(this.newlineDisabled||""!==c&&"\n"!==c){var d=i.getSeriesByName(c)[0];if(!r.get(c))if(d){var p=d.getData(),f=p.getVisual("color");"function"===typeof f&&(f=f(d.getDataParams(0)));var x=p.getVisual("legendSymbol")||"roundRect",_=p.getVisual("symbol"),b=this._createItem(c,u,o,t,x,_,e,f,s);b.on("click",h(m,c,n)).on("mouseover",h(v,d.name,null,n,l)).on("mouseout",h(y,d.name,null,n,l)),r.set(c,!0)}else i.eachRawSeries((function(i){if(!r.get(c)&&i.legendDataProvider){var a=i.legendDataProvider(),d=a.indexOfName(c);if(d<0)return;var p=a.getItemVisual(d,"color"),g="roundRect",f=this._createItem(c,u,o,t,g,null,e,p,s);f.on("click",h(m,c,n)).on("mouseover",h(v,null,c,n,l)).on("mouseout",h(y,null,c,n,l)),r.set(c,!0)}}),this)}else a.add(new g({newline:!0}))}),this)},_createItem:function(e,t,i,n,a,r,u,c,d){var h=n.get("itemWidth"),p=n.get("itemHeight"),f=n.get("inactiveColor"),m=n.get("symbolKeepAspect"),v=n.isSelected(e),y=new g,x=i.getModel("textStyle"),_=i.get("icon"),b=i.getModel("tooltip"),w=b.parentModel;if(a=_||a,y.add(s(a,0,0,h,p,v?c:f,null==m||m)),!_&&r&&(r!==a||"none"===r)){var S=.8*p;"none"===r&&(r="circle"),y.add(s(r,(h-S)/2,(p-S)/2,S,S,v?c:f,null==m||m))}var M="left"===u?h+5:-5,I=u,A=n.get("formatter"),T=e;"string"===typeof A&&A?T=A.replace("{name}",null!=e?e:""):"function"===typeof A&&(T=A(e)),y.add(new l.Text({style:l.setTextStyle({},x,{text:T,x:M,y:p/2,textFill:v?x.getTextColor():f,textAlign:I,textVerticalAlign:"middle"})}));var D=new l.Rect({shape:y.getBoundingRect(),invisible:!0,tooltip:b.get("show")?o.extend({content:e,formatter:w.get("formatter",!0)||function(){return e},formatterParams:{componentType:"legend",legendIndex:n.componentIndex,name:e,$vars:["name"]}},b.option):null});return y.add(D),y.eachChild((function(e){e.silent=!0})),D.silent=!d,this.getContentGroup().add(y),l.setHoverStyle(y),y.__legendDataIndex=t,y},layoutInner:function(e,t,i){var n=this.getContentGroup();d.box(e.get("orient"),n,e.get("itemGap"),i.width,i.height);var a=n.getBoundingRect();return n.attr("position",[-a.x,-a.y]),this.group.getBoundingRect()},remove:function(){this.getContentGroup().removeAll(),this._isFirstRender=!0}});function m(e,t){t.dispatchAction({type:"legendToggleSelect",name:e})}function v(e,t,i,n){var a=i.getZr().storage.getDisplayList()[0];a&&a.useHoverLayer||i.dispatchAction({type:"highlight",seriesName:e,name:t,excludeSeriesId:n})}function y(e,t,i,n){var a=i.getZr().storage.getDisplayList()[0];a&&a.useHoverLayer||i.dispatchAction({type:"downplay",seriesName:e,name:t,excludeSeriesId:n})}e.exports=f},"5f14":function(e,t,i){var n=i("6d8b"),a=i("41ef"),o=i("3842"),r=o.linearMap,s=n.each,l=n.isObject,u=-1,c=function(e){var t=e.mappingMethod,i=e.type,a=this.option=n.clone(e);this.type=i,this.mappingMethod=t,this._normalizeData=S[t];var o=d[i];this.applyVisual=o.applyVisual,this.getColorMapper=o.getColorMapper,this._doMap=o._doMap[t],"piecewise"===t?(g(a),h(a)):"category"===t?a.categories?p(a):g(a,!0):(n.assert("linear"!==t||a.dataExtent),g(a))};c.prototype={constructor:c,mapValueToVisual:function(e){var t=this._normalizeData(e);return this._doMap(t,e)},getNormalizer:function(){return n.bind(this._normalizeData,this)}};var d=c.visualHandlers={color:{applyVisual:v("color"),getColorMapper:function(){var e=this.option;return n.bind("category"===e.mappingMethod?function(e,t){return!t&&(e=this._normalizeData(e)),y.call(this,e)}:function(t,i,n){var o=!!n;return!i&&(t=this._normalizeData(t)),n=a.fastLerp(t,e.parsedVisual,n),o?n:a.stringify(n,"rgba")},this)},_doMap:{linear:function(e){return a.stringify(a.fastLerp(e,this.option.parsedVisual),"rgba")},category:y,piecewise:function(e,t){var i=b.call(this,t);return null==i&&(i=a.stringify(a.fastLerp(e,this.option.parsedVisual),"rgba")),i},fixed:x}},colorHue:f((function(e,t){return a.modifyHSL(e,t)})),colorSaturation:f((function(e,t){return a.modifyHSL(e,null,t)})),colorLightness:f((function(e,t){return a.modifyHSL(e,null,null,t)})),colorAlpha:f((function(e,t){return a.modifyAlpha(e,t)})),opacity:{applyVisual:v("opacity"),_doMap:_([0,1])},liftZ:{applyVisual:v("liftZ"),_doMap:{linear:x,category:x,piecewise:x,fixed:x}},symbol:{applyVisual:function(e,t,i){var a=this.mapValueToVisual(e);if(n.isString(a))i("symbol",a);else if(l(a))for(var o in a)a.hasOwnProperty(o)&&i(o,a[o])},_doMap:{linear:m,category:y,piecewise:function(e,t){var i=b.call(this,t);return null==i&&(i=m.call(this,e)),i},fixed:x}},symbolSize:{applyVisual:v("symbolSize"),_doMap:_([0,1])}};function h(e){var t=e.pieceList;e.hasSpecialVisual=!1,n.each(t,(function(t,i){t.originIndex=i,null!=t.visual&&(e.hasSpecialVisual=!0)}))}function p(e){var t=e.categories,i=e.visual,a=e.categoryMap={};if(s(t,(function(e,t){a[e]=t})),!n.isArray(i)){var o=[];n.isObject(i)?s(i,(function(e,t){var i=a[t];o[null!=i?i:u]=e})):o[u]=i,i=w(e,o)}for(var r=t.length-1;r>=0;r--)null==i[r]&&(delete a[t[r]],t.pop())}function g(e,t){var i=e.visual,a=[];n.isObject(i)?s(i,(function(e){a.push(e)})):null!=i&&a.push(i);var o={color:1,symbol:1};t||1!==a.length||o.hasOwnProperty(e.type)||(a[1]=a[0]),w(e,a)}function f(e){return{applyVisual:function(t,i,n){t=this.mapValueToVisual(t),n("color",e(i("color"),t))},_doMap:_([0,1])}}function m(e){var t=this.option.visual;return t[Math.round(r(e,[0,1],[0,t.length-1],!0))]||{}}function v(e){return function(t,i,n){n(e,this.mapValueToVisual(t))}}function y(e){var t=this.option.visual;return t[this.option.loop&&e!==u?e%t.length:e]}function x(){return this.option.visual[0]}function _(e){return{linear:function(t){return r(t,e,this.option.visual,!0)},category:y,piecewise:function(t,i){var n=b.call(this,i);return null==n&&(n=r(t,e,this.option.visual,!0)),n},fixed:x}}function b(e){var t=this.option,i=t.pieceList;if(t.hasSpecialVisual){var n=c.findPieceIndex(e,i),a=i[n];if(a&&a.visual)return a.visual[this.type]}}function w(e,t){return e.visual=t,"color"===e.type&&(e.parsedVisual=n.map(t,(function(e){return a.parse(e)}))),t}var S={linear:function(e){return r(e,this.option.dataExtent,[0,1],!0)},piecewise:function(e){var t=this.option.pieceList,i=c.findPieceIndex(e,t,!0);if(null!=i)return r(i,[0,t.length-1],[0,1],!0)},category:function(e){var t=this.option.categories?this.option.categoryMap[e]:e;return null==t?u:t},fixed:n.noop};function M(e,t,i){return e?t<=i:t<i}c.listVisualTypes=function(){var e=[];return n.each(d,(function(t,i){e.push(i)})),e},c.addVisualHandler=function(e,t){d[e]=t},c.isValidType=function(e){return d.hasOwnProperty(e)},c.eachVisual=function(e,t,i){n.isObject(e)?n.each(e,t,i):t.call(i,e)},c.mapVisual=function(e,t,i){var a,o=n.isArray(e)?[]:n.isObject(e)?{}:(a=!0,null);return c.eachVisual(e,(function(e,n){var r=t.call(i,e,n);a?o=r:o[n]=r})),o},c.retrieveVisuals=function(e){var t,i={};return e&&s(d,(function(n,a){e.hasOwnProperty(a)&&(i[a]=e[a],t=!0)})),t?i:null},c.prepareVisualTypes=function(e){if(l(e)){var t=[];s(e,(function(e,i){t.push(i)})),e=t}else{if(!n.isArray(e))return[];e=e.slice()}return e.sort((function(e,t){return"color"===t&&"color"!==e&&0===e.indexOf("color")?1:-1})),e},c.dependsOn=function(e,t){return"color"===t?!(!e||0!==e.indexOf(t)):e===t},c.findPieceIndex=function(e,t,i){for(var n,a=1/0,o=0,r=t.length;o<r;o++){var s=t[o].value;if(null!=s){if(s===e||"string"===typeof s&&s===e+"")return o;i&&d(s,o)}}for(o=0,r=t.length;o<r;o++){var l=t[o],u=l.interval,c=l.close;if(u){if(u[0]===-1/0){if(M(c[1],e,u[1]))return o}else if(u[1]===1/0){if(M(c[0],u[0],e))return o}else if(M(c[0],u[0],e)&&M(c[1],e,u[1]))return o;i&&d(u[0],o),i&&d(u[1],o)}}if(i)return e===1/0?t.length-1:e===-1/0?0:n;function d(t,i){var o=Math.abs(t-e);o<a&&(a=o,n=i)}};var I=c;e.exports=I},"60d7":function(e,t,i){var n=i("2306"),a=i("e887"),o=.3,r=a.extend({type:"parallel",init:function(){this._dataGroup=new n.Group,this.group.add(this._dataGroup),this._data,this._initialized},render:function(e,t,i,a){var o=this._dataGroup,r=e.getData(),h=this._data,p=e.coordinateSystem,g=p.dimensions,f=c(e);function m(e){var t=u(r,o,e,g,p);d(t,r,e,f)}function v(t,i){var o=h.getItemGraphicEl(i),s=l(r,t,g,p);r.setItemGraphicEl(t,o);var u=a&&!1===a.animation?null:e;n.updateProps(o,{shape:{points:s}},u,t),d(o,r,t,f)}function y(e){var t=h.getItemGraphicEl(e);o.remove(t)}if(r.diff(h).add(m).update(v).remove(y).execute(),!this._initialized){this._initialized=!0;var x=s(p,e,(function(){setTimeout((function(){o.removeClipPath()}))}));o.setClipPath(x)}this._data=r},incrementalPrepareRender:function(e,t,i){this._initialized=!0,this._data=null,this._dataGroup.removeAll()},incrementalRender:function(e,t,i){for(var n=t.getData(),a=t.coordinateSystem,o=a.dimensions,r=c(t),s=e.start;s<e.end;s++){var l=u(n,this._dataGroup,s,o,a);l.incremental=!0,d(l,n,s,r)}},dispose:function(){},remove:function(){this._dataGroup&&this._dataGroup.removeAll(),this._data=null}});function s(e,t,i){var a=e.model,o=e.getRect(),r=new n.Rect({shape:{x:o.x,y:o.y,width:o.width,height:o.height}}),s="horizontal"===a.get("layout")?"width":"height";return r.setShape(s,0),n.initProps(r,{shape:{width:o.width,height:o.height}},t,i),r}function l(e,t,i,n){for(var a=[],o=0;o<i.length;o++){var r=i[o],s=e.get(e.mapDimension(r),t);h(s,n.getAxis(r).type)||a.push(n.dataToPoint(s,r))}return a}function u(e,t,i,a,o){var r=l(e,i,a,o),s=new n.Polyline({shape:{points:r},silent:!0,z2:10});return t.add(s),e.setItemGraphicEl(i,s),s}function c(e){var t=e.get("smooth",!0);return!0===t&&(t=o),{lineStyle:e.getModel("lineStyle").getLineStyle(),smooth:null!=t?t:o}}function d(e,t,i,n){var a=n.lineStyle;if(t.hasItemOption){var o=t.getItemModel(i).getModel("lineStyle");a=o.getLineStyle()}e.useStyle(a);var r=e.style;r.fill=null,r.stroke=t.getItemVisual(i,"color"),r.opacity=t.getItemVisual(i,"opacity"),n.smooth&&(e.shape.smooth=n.smooth)}function h(e,t){return"category"===t?null==e:null==e||isNaN(e)}var p=r;e.exports=p},"60e3":function(e,t,i){var n=i("6d8b"),a={get:function(e,t,i){var a=n.clone((o[e]||{})[t]);return i&&n.isArray(a)?a[a.length-1]:a}},o={color:{active:["#006edd","#e0ffff"],inactive:["rgba(0,0,0,0)"]},colorHue:{active:[0,360],inactive:[0,0]},colorSaturation:{active:[.3,1],inactive:[0,0]},colorLightness:{active:[.9,.5],inactive:[0,0]},colorAlpha:{active:[.3,1],inactive:[0,0]},opacity:{active:[.3,1],inactive:[0,0]},symbol:{active:["circle","roundRect","diamond"],inactive:["none"]},symbolSize:{active:[10,50],inactive:[0,0]}},r=a;e.exports=r},"627c":function(e,t,i){var n=i("3eba"),a=i("2306"),o=i("f934"),r=o.getLayoutRect;n.extendComponentModel({type:"title",layoutMode:{type:"box",ignoreSize:!0},defaultOption:{zlevel:0,z:6,show:!0,text:"",target:"blank",subtext:"",subtarget:"blank",left:0,top:0,backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderWidth:0,padding:5,itemGap:10,textStyle:{fontSize:18,fontWeight:"bolder",color:"#333"},subtextStyle:{color:"#aaa"}}}),n.extendComponentView({type:"title",render:function(e,t,i){if(this.group.removeAll(),e.get("show")){var n=this.group,o=e.getModel("textStyle"),s=e.getModel("subtextStyle"),l=e.get("textAlign"),u=e.get("textBaseline"),c=new a.Text({style:a.setTextStyle({},o,{text:e.get("text"),textFill:o.getTextColor()},{disableBox:!0}),z2:10}),d=c.getBoundingRect(),h=e.get("subtext"),p=new a.Text({style:a.setTextStyle({},s,{text:h,textFill:s.getTextColor(),y:d.height+e.get("itemGap"),textVerticalAlign:"top"},{disableBox:!0}),z2:10}),g=e.get("link"),f=e.get("sublink"),m=e.get("triggerEvent",!0);c.silent=!g&&!m,p.silent=!f&&!m,g&&c.on("click",(function(){window.open(g,"_"+e.get("target"))})),f&&p.on("click",(function(){window.open(f,"_"+e.get("subtarget"))})),c.eventData=p.eventData=m?{componentType:"title",componentIndex:e.componentIndex}:null,n.add(c),h&&n.add(p);var v=n.getBoundingRect(),y=e.getBoxLayoutParams();y.width=v.width,y.height=v.height;var x=r(y,{width:i.getWidth(),height:i.getHeight()},e.get("padding"));l||(l=e.get("left")||e.get("right"),"middle"===l&&(l="center"),"right"===l?x.x+=x.width:"center"===l&&(x.x+=x.width/2)),u||(u=e.get("top")||e.get("bottom"),"center"===u&&(u="middle"),"bottom"===u?x.y+=x.height:"middle"===u&&(x.y+=x.height/2),u=u||"top"),n.attr("position",[x.x,x.y]);var _={textAlign:l,textVerticalAlign:u};c.setStyle(_),p.setStyle(_),v=n.getBoundingRect();var b=x.margin,w=e.getItemStyle(["color","opacity"]);w.fill=e.get("backgroundColor");var S=new a.Rect({shape:{x:v.x-b[3],y:v.y-b[0],width:v.width+b[1]+b[3],height:v.height+b[0]+b[2],r:e.get("borderRadius")},style:w,silent:!0});a.subPixelOptimizeRect(S),n.add(S)}}})},6569:function(e,t,i){var n=i("6d8b"),a=i("e0d3");function o(e){r(e),s(e)}function r(e){if(!e.parallel){var t=!1;n.each(e.series,(function(e){e&&"parallel"===e.type&&(t=!0)})),t&&(e.parallel=[{}])}}function s(e){var t=a.normalizeToArray(e.parallelAxis);n.each(t,(function(t){if(n.isObject(t)){var i=t.parallelIndex||0,o=a.normalizeToArray(e.parallel)[i];o&&o.parallelAxisDefault&&n.merge(t,o.parallelAxisDefault,!1)}}))}e.exports=o},6582:function(e,t,i){var n=i("cccd"),a={seriesType:"lines",plan:n(),reset:function(e){var t=e.coordinateSystem,i=e.get("polyline"),n=e.pipelineContext.large;function a(a,o){var r=[];if(n){var s,l=a.end-a.start;if(i){for(var u=0,c=a.start;c<a.end;c++)u+=e.getLineCoordsCount(c);s=new Float32Array(l+2*u)}else s=new Float32Array(4*l);var d=0,h=[];for(c=a.start;c<a.end;c++){var p=e.getLineCoords(c,r);i&&(s[d++]=p);for(var g=0;g<p;g++)h=t.dataToPoint(r[g],!1,h),s[d++]=h[0],s[d++]=h[1]}o.setLayout("linesPoints",s)}else for(c=a.start;c<a.end;c++){var f=o.getItemModel(c),m=(p=e.getLineCoords(c,r),[]);if(i)for(var v=0;v<p;v++)m.push(t.dataToPoint(r[v]));else{m[0]=t.dataToPoint(r[0]),m[1]=t.dataToPoint(r[1]);var y=f.get("lineStyle.curveness");+y&&(m[2]=[(m[0][0]+m[1][0])/2-(m[0][1]-m[1][1])*y,(m[0][1]+m[1][1])/2-(m[1][0]-m[0][0])*y])}o.setItemLayout(c,m)}}return{progress:a}}};e.exports=a},"66a4":function(e,t,i){var n=i("6d8b");function a(e){var t=e&&e.timeline;n.isArray(t)||(t=t?[t]:[]),n.each(t,(function(e){e&&o(e)}))}function o(e){var t=e.type,i={number:"value",time:"time"};if(i[t]&&(e.axisType=i[t],delete e.type),r(e),s(e,"controlPosition")){var a=e.controlStyle||(e.controlStyle={});s(a,"position")||(a.position=e.controlPosition),"none"!==a.position||s(a,"show")||(a.show=!1,delete a.position),delete e.controlPosition}n.each(e.data||[],(function(e){n.isObject(e)&&!n.isArray(e)&&(!s(e,"value")&&s(e,"name")&&(e.value=e.name),r(e))}))}function r(e){var t=e.itemStyle||(e.itemStyle={}),i=t.emphasis||(t.emphasis={}),a=e.label||e.label||{},o=a.normal||(a.normal={}),r={normal:1,emphasis:1};n.each(a,(function(e,t){r[t]||s(o,t)||(o[t]=e)})),i.label&&!s(a,"emphasis")&&(a.emphasis=i.label,delete i.label)}function s(e,t){return e.hasOwnProperty(t)}e.exports=a},"66fc":function(e,t,i){var n=i("6d8b"),a=i("84ce"),o=function(e,t,i,n,o){a.call(this,e,t,i),this.type=n||"value",this.position=o||"bottom",this.orient=null};o.prototype={constructor:o,model:null,isHorizontal:function(){var e=this.position;return"top"===e||"bottom"===e},pointToData:function(e,t){return this.coordinateSystem.pointToData(e,t)[0]},toGlobalCoord:null,toLocalCoord:null},n.inherits(o,a);var r=o;e.exports=r},"675a":function(e,t){function i(e){var t=e.findComponents({mainType:"legend"});t&&t.length&&e.eachSeriesByType("graph",(function(e){var i=e.getCategoriesData(),n=e.getGraph(),a=n.data,o=i.mapArray(i.getName);a.filterSelf((function(e){var i=a.getItemModel(e),n=i.getShallow("category");if(null!=n){"number"===typeof n&&(n=o[n]);for(var r=0;r<t.length;r++)if(!t[r].isSelected(n))return!1}return!0}))}),this)}e.exports=i},"675c":function(e,t,i){var n=i("3eba");i("4e10"),i("a666"),i("49e8"),i("eeea");var a=i("cee1"),o=i("54fb"),r=i("f6ed"),s=i("9442"),l=i("7782");n.registerLayout(a),n.registerVisual(o),n.registerProcessor(n.PRIORITY.PROCESSOR.STATISTIC,r),n.registerPreprocessor(s),l("map",[{type:"mapToggleSelect",event:"mapselectchanged",method:"toggleSelected"},{type:"mapSelect",event:"mapselected",method:"select"},{type:"mapUnSelect",event:"mapunselected",method:"unSelect"}])},"67a8":function(e,t,i){var n=i("3301"),a=i("4f85"),o=a.extend({type:"series.effectScatter",dependencies:["grid","polar"],getInitialData:function(e,t){return n(this.getSource(),this)},brushSelector:"point",defaultOption:{coordinateSystem:"cartesian2d",zlevel:0,z:2,legendHoverLink:!0,effectType:"ripple",progressive:0,showEffectOn:"render",rippleEffect:{period:4,scale:2.5,brushType:"fill"},symbolSize:10}});e.exports=o},"67cc":function(e,t,i){var n=i("4e08"),a=(n.__DEV__,i("3eba")),o=i("6d8b"),r=i("2306"),s=i("e7aa"),l=s.setLabel,u=i("4319"),c=i("b5c7"),d=i("cbe5"),h=["itemStyle","barBorderWidth"];o.extend(u.prototype,c);var p=a.extendChartView({type:"bar",render:function(e,t,i){this._updateDrawMode(e);var n=e.get("coordinateSystem");return"cartesian2d"!==n&&"polar"!==n||(this._isLargeDraw?this._renderLarge(e,t,i):this._renderNormal(e,t,i)),this.group},incrementalPrepareRender:function(e,t,i){this._clear(),this._updateDrawMode(e)},incrementalRender:function(e,t,i,n){this._incrementalRenderLarge(e,t)},_updateDrawMode:function(e){var t=e.pipelineContext.large;(null==this._isLargeDraw||t^this._isLargeDraw)&&(this._isLargeDraw=t,this._clear())},_renderNormal:function(e,t,i){var n,a=this.group,o=e.getData(),s=this._data,l=e.coordinateSystem,u=l.getBaseAxis();"cartesian2d"===l.type?n=u.isHorizontal():"polar"===l.type&&(n="angle"===u.dim);var c=e.isAnimationEnabled()?e:null;o.diff(s).add((function(t){if(o.hasValue(t)){var i=o.getItemModel(t),r=v[l.type](o,t,i),s=g[l.type](o,t,i,r,n,c);o.setItemGraphicEl(t,s),a.add(s),y(s,o,t,i,r,e,n,"polar"===l.type)}})).update((function(t,i){var u=s.getItemGraphicEl(i);if(o.hasValue(t)){var d=o.getItemModel(t),h=v[l.type](o,t,d);u?r.updateProps(u,{shape:h},c,t):u=g[l.type](o,t,d,h,n,c,!0),o.setItemGraphicEl(t,u),a.add(u),y(u,o,t,d,h,e,n,"polar"===l.type)}else a.remove(u)})).remove((function(e){var t=s.getItemGraphicEl(e);"cartesian2d"===l.type?t&&f(e,c,t):t&&m(e,c,t)})).execute(),this._data=o},_renderLarge:function(e,t,i){this._clear(),b(e,this.group)},_incrementalRenderLarge:function(e,t){b(t,this.group,!0)},dispose:o.noop,remove:function(e){this._clear(e)},_clear:function(e){var t=this.group,i=this._data;e&&e.get("animation")&&i&&!this._isLargeDraw?i.eachItemGraphicEl((function(t){"sector"===t.type?m(t.dataIndex,e,t):f(t.dataIndex,e,t)})):t.removeAll(),this._data=null}}),g={cartesian2d:function(e,t,i,n,a,s,l){var u=new r.Rect({shape:o.extend({},n)});if(s){var c=u.shape,d=a?"height":"width",h={};c[d]=0,h[d]=n[d],r[l?"updateProps":"initProps"](u,{shape:h},s,t)}return u},polar:function(e,t,i,n,a,s,l){var u=n.startAngle<n.endAngle,c=new r.Sector({shape:o.defaults({clockwise:u},n)});if(s){var d=c.shape,h=a?"r":"endAngle",p={};d[h]=a?0:n.startAngle,p[h]=n[h],r[l?"updateProps":"initProps"](c,{shape:p},s,t)}return c}};function f(e,t,i){i.style.text=null,r.updateProps(i,{shape:{width:0}},t,e,(function(){i.parent&&i.parent.remove(i)}))}function m(e,t,i){i.style.text=null,r.updateProps(i,{shape:{r:i.shape.r0}},t,e,(function(){i.parent&&i.parent.remove(i)}))}var v={cartesian2d:function(e,t,i){var n=e.getItemLayout(t),a=x(i,n),o=n.width>0?1:-1,r=n.height>0?1:-1;return{x:n.x+o*a/2,y:n.y+r*a/2,width:n.width-o*a,height:n.height-r*a}},polar:function(e,t,i){var n=e.getItemLayout(t);return{cx:n.cx,cy:n.cy,r0:n.r0,r:n.r,startAngle:n.startAngle,endAngle:n.endAngle}}};function y(e,t,i,n,a,s,u,c){var d=t.getItemVisual(i,"color"),h=t.getItemVisual(i,"opacity"),p=n.getModel("itemStyle"),g=n.getModel("emphasis.itemStyle").getBarItemStyle();c||e.setShape("r",p.get("barBorderRadius")||0),e.useStyle(o.defaults({fill:d,opacity:h},p.getBarItemStyle()));var f=n.getShallow("cursor");f&&e.attr("cursor",f);var m=u?a.height>0?"bottom":"top":a.width>0?"left":"right";c||l(e.style,g,n,d,s,i,m),r.setHoverStyle(e,g)}function x(e,t){var i=e.get(h)||0;return Math.min(i,Math.abs(t.width),Math.abs(t.height))}var _=d.extend({type:"largeBar",shape:{points:[]},buildPath:function(e,t){for(var i=t.points,n=this.__startPoint,a=this.__valueIdx,o=0;o<i.length;o+=2)n[this.__valueIdx]=i[o+a],e.moveTo(n[0],n[1]),e.lineTo(i[o],i[o+1])}});function b(e,t,i){var n=e.getData(),a=[],o=n.getLayout("valueAxisHorizontal")?1:0;a[1-o]=n.getLayout("valueAxisStart");var r=new _({shape:{points:n.getLayout("largePoints")},incremental:!!i,__startPoint:a,__valueIdx:o});t.add(r),w(r,e,n)}function w(e,t,i){var n=i.getVisual("borderColor")||i.getVisual("color"),a=t.getModel("itemStyle").getItemStyle(["color","borderColor"]);e.useStyle(a),e.style.fill=null,e.style.stroke=n,e.style.lineWidth=i.getLayout("barWidth")}e.exports=p},6932:function(e,t,i){var n=i("6cb7");n.registerSubTypeDefaulter("dataZoom",(function(){return"slider"}))},"6a4c":function(e,t,i){var n=i("4527"),a=i("6d8b"),o=i("0fd3"),r=i("401b");function s(e,t,i){o.call(this,e,t,i),this._lastFrame=0,this._lastFramePercent=0}var l=s.prototype;l.createLine=function(e,t,i){return new n(e,t,i)},l.updateAnimationPoints=function(e,t){this._points=t;for(var i=[0],n=0,a=1;a<t.length;a++){var o=t[a-1],s=t[a];n+=r.dist(o,s),i.push(n)}if(0!==n){for(a=0;a<i.length;a++)i[a]/=n;this._offsets=i,this._length=n}},l.getLineLength=function(e){return this._length},l.updateSymbolPosition=function(e){var t=e.__t,i=this._points,n=this._offsets,a=i.length;if(n){var o=this._lastFrame;if(t<this._lastFramePercent){var s=Math.min(o+1,a-1);for(l=s;l>=0;l--)if(n[l]<=t)break;l=Math.min(l,a-2)}else{for(var l=o;l<a;l++)if(n[l]>t)break;l=Math.min(l-1,a-2)}r.lerp(e.position,i[l],i[l+1],(t-n[l])/(n[l+1]-n[l]));var u=i[l+1][0]-i[l][0],c=i[l+1][1]-i[l][1];e.rotation=-Math.atan2(c,u)-Math.PI/2,this._lastFrame=l,this._lastFramePercent=t,e.ignore=!1}},a.inherits(s,o);var u=s;e.exports=u},"6bd4":function(e,t){var i={Russia:[100,60],"United States":[-99,38],"United States of America":[-99,38]};function n(e,t){if("world"===e){var n=i[t.name];if(n){var a=t.center;a[0]=n[0],a[1]=n[1]}}}e.exports=n},"6c12":function(e,t,i){var n=i("4e08"),a=(n.__DEV__,i("3eba")),o=i("6d8b"),r=i("fab22"),s=i("2306"),l=["axisLine","axisTickLabel","axisName"],u=a.extendComponentView({type:"radar",render:function(e,t,i){var n=this.group;n.removeAll(),this._buildAxes(e),this._buildSplitLineAndArea(e)},_buildAxes:function(e){var t=e.coordinateSystem,i=t.getIndicatorAxes(),n=o.map(i,(function(e){var i=new r(e.model,{position:[t.cx,t.cy],rotation:e.angle,labelDirection:-1,tickDirection:-1,nameDirection:1});return i}));o.each(n,(function(e){o.each(l,e.add,e),this.group.add(e.getGroup())}),this)},_buildSplitLineAndArea:function(e){var t=e.coordinateSystem,i=t.getIndicatorAxes();if(i.length){var n=e.get("shape"),a=e.getModel("splitLine"),r=e.getModel("splitArea"),l=a.getModel("lineStyle"),u=r.getModel("areaStyle"),c=a.get("show"),d=r.get("show"),h=l.get("color"),p=u.get("color");h=o.isArray(h)?h:[h],p=o.isArray(p)?p:[p];var g=[],f=[];if("circle"===n)for(var m=i[0].getTicksCoords(),v=t.cx,y=t.cy,x=0;x<m.length;x++){if(c){var _=D(g,h,x);g[_].push(new s.Circle({shape:{cx:v,cy:y,r:m[x].coord}}))}if(d&&x<m.length-1){_=D(f,p,x);f[_].push(new s.Ring({shape:{cx:v,cy:y,r0:m[x].coord,r:m[x+1].coord}}))}}else{var b,w=o.map(i,(function(e,i){var n=e.getTicksCoords();return b=null==b?n.length-1:Math.min(n.length-1,b),o.map(n,(function(e){return t.coordToPoint(e.coord,i)}))})),S=[];for(x=0;x<=b;x++){for(var M=[],I=0;I<i.length;I++)M.push(w[I][x]);if(M[0]&&M.push(M[0].slice()),c){_=D(g,h,x);g[_].push(new s.Polyline({shape:{points:M}}))}if(d&&S){_=D(f,p,x-1);f[_].push(new s.Polygon({shape:{points:M.concat(S)}}))}S=M.slice().reverse()}}var A=l.getLineStyle(),T=u.getAreaStyle();o.each(f,(function(e,t){this.group.add(s.mergePath(e,{style:o.defaults({stroke:"none",fill:p[t%p.length]},T),silent:!0}))}),this),o.each(g,(function(e,t){this.group.add(s.mergePath(e,{style:o.defaults({fill:"none",stroke:h[t%h.length]},A),silent:!0}))}),this)}function D(e,t,i){var n=i%t.length;return e[n]=e[n]||[],n}}});e.exports=u},"6c12f":function(e,t,i){var n=i("3eba");i("2cfc"),i("adf4"),i("255c");var a=i("527a"),o=i("71b2"),r=i("d3f47");n.registerLayout(a),n.registerVisual(o),n.registerProcessor(r("themeRiver"))},"6cc5":function(e,t,i){var n=i("6d8b"),a=i("401b"),o=i("1687"),r=i("9850"),s=i("0cde"),l=a.applyTransform;function u(){s.call(this)}function c(e){this.name=e,this.zoomLimit,s.call(this),this._roamTransformable=new u,this._rawTransformable=new u,this._center,this._zoom}function d(e,t,i,n){var a=i.seriesModel,o=a?a.coordinateSystem:null;return o===this?o[e](n):null}n.mixin(u,s),c.prototype={constructor:c,type:"view",dimensions:["x","y"],setBoundingRect:function(e,t,i,n){return this._rect=new r(e,t,i,n),this._rect},getBoundingRect:function(){return this._rect},setViewRect:function(e,t,i,n){this.transformTo(e,t,i,n),this._viewRect=new r(e,t,i,n)},transformTo:function(e,t,i,n){var a=this.getBoundingRect(),o=this._rawTransformable;o.transform=a.calculateTransform(new r(e,t,i,n)),o.decomposeTransform(),this._updateTransform()},setCenter:function(e){e&&(this._center=e,this._updateCenterAndZoom())},setZoom:function(e){e=e||1;var t=this.zoomLimit;t&&(null!=t.max&&(e=Math.min(t.max,e)),null!=t.min&&(e=Math.max(t.min,e))),this._zoom=e,this._updateCenterAndZoom()},getDefaultCenter:function(){var e=this.getBoundingRect(),t=e.x+e.width/2,i=e.y+e.height/2;return[t,i]},getCenter:function(){return this._center||this.getDefaultCenter()},getZoom:function(){return this._zoom||1},getRoamTransform:function(){return this._roamTransformable.getLocalTransform()},_updateCenterAndZoom:function(){var e=this._rawTransformable.getLocalTransform(),t=this._roamTransformable,i=this.getDefaultCenter(),n=this.getCenter(),o=this.getZoom();n=a.applyTransform([],n,e),i=a.applyTransform([],i,e),t.origin=n,t.position=[i[0]-n[0],i[1]-n[1]],t.scale=[o,o],this._updateTransform()},_updateTransform:function(){var e=this._roamTransformable,t=this._rawTransformable;t.parent=e,e.updateTransform(),t.updateTransform(),o.copy(this.transform||(this.transform=[]),t.transform||o.create()),this._rawTransform=t.getLocalTransform(),this.invTransform=this.invTransform||[],o.invert(this.invTransform,this.transform),this.decomposeTransform()},getViewRect:function(){return this._viewRect},getViewRectAfterRoam:function(){var e=this.getBoundingRect().clone();return e.applyTransform(this.transform),e},dataToPoint:function(e,t,i){var n=t?this._rawTransform:this.transform;return i=i||[],n?l(i,e,n):a.copy(i,e)},pointToData:function(e){var t=this.invTransform;return t?l([],e,t):[e[0],e[1]]},convertToPixel:n.curry(d,"dataToPoint"),convertFromPixel:n.curry(d,"pointToData"),containPoint:function(e){return this.getViewRectAfterRoam().contain(e[0],e[1])}},n.mixin(c,s);var h=c;e.exports=h},"6cd8":function(e,t,i){var n=i("6d8b"),a=i("2306"),o=i("1418"),r=i("22da"),s=r.radialCoordinate,l=i("3eba"),u=i("e263"),c=i("6cc5"),d=i("01ef"),h=i("4a01"),p=i("c526"),g=p.onIrrelevantElement,f=l.extendChartView({type:"tree",init:function(e,t){this._oldTree,this._mainGroup=new a.Group,this._controller=new h(t.getZr()),this._controllerHost={target:this.group},this.group.add(this._mainGroup)},render:function(e,t,i,n){var a=e.getData(),o=e.layoutInfo,r=this._mainGroup,s=e.get("layout");"radial"===s?r.attr("position",[o.x+o.width/2,o.y+o.height/2]):r.attr("position",[o.x,o.y]),this._updateViewCoordSys(e),this._updateController(e,t,i);var l=this._data,u={expandAndCollapse:e.get("expandAndCollapse"),layout:s,orient:e.getOrient(),curvature:e.get("lineStyle.curveness"),symbolRotate:e.get("symbolRotate"),symbolOffset:e.get("symbolOffset"),hoverAnimation:e.get("hoverAnimation"),useNameLabel:!0,fadeIn:!0};a.diff(l).add((function(t){m(a,t)&&y(a,t,null,r,e,u)})).update((function(t,i){var n=l.getItemGraphicEl(i);m(a,t)?y(a,t,n,r,e,u):n&&x(l,i,n,r,e,u)})).remove((function(t){var i=l.getItemGraphicEl(t);i&&x(l,t,i,r,e,u)})).execute(),this._nodeScaleRatio=e.get("nodeScaleRatio"),this._updateNodeAndLinkScale(e),!0===u.expandAndCollapse&&a.eachItemGraphicEl((function(t,n){t.off("click").on("click",(function(){i.dispatchAction({type:"treeExpandAndCollapse",seriesId:e.id,dataIndex:n})}))})),this._data=a},_updateViewCoordSys:function(e){var t=e.getData(),i=[];t.each((function(e){var n=t.getItemLayout(e);!n||isNaN(n.x)||isNaN(n.y)||i.push([+n.x,+n.y])}));var n=[],a=[];u.fromPoints(i,n,a),a[0]-n[0]===0&&(a[0]+=1,n[0]-=1),a[1]-n[1]===0&&(a[1]+=1,n[1]-=1);var o=e.coordinateSystem=new c;o.zoomLimit=e.get("scaleLimit"),o.setBoundingRect(n[0],n[1],a[0]-n[0],a[1]-n[1]),o.setCenter(e.get("center")),o.setZoom(e.get("zoom")),this.group.attr({position:o.position,scale:o.scale}),this._viewCoordSys=o},_updateController:function(e,t,i){var n=this._controller,a=this._controllerHost,o=this.group;n.setPointerChecker((function(t,n,a){var r=o.getBoundingRect();return r.applyTransform(o.transform),r.contain(n,a)&&!g(t,i,e)})),n.enable(e.get("roam")),a.zoomLimit=e.get("scaleLimit"),a.zoom=e.coordinateSystem.getZoom(),n.off("pan").off("zoom").on("pan",(function(t){d.updateViewOnPan(a,t.dx,t.dy),i.dispatchAction({seriesId:e.id,type:"treeRoam",dx:t.dx,dy:t.dy})}),this).on("zoom",(function(t){d.updateViewOnZoom(a,t.scale,t.originX,t.originY),i.dispatchAction({seriesId:e.id,type:"treeRoam",zoom:t.scale,originX:t.originX,originY:t.originY}),this._updateNodeAndLinkScale(e)}),this)},_updateNodeAndLinkScale:function(e){var t=e.getData(),i=this._getNodeGlobalScale(e),n=[i,i];t.eachItemGraphicEl((function(e,t){e.attr("scale",n)}))},_getNodeGlobalScale:function(e){var t=e.coordinateSystem;if("view"!==t.type)return 1;var i=this._nodeScaleRatio,n=t.scale,a=n&&n[0]||1,o=t.getZoom(),r=(o-1)*i+1;return r/a},dispose:function(){this._controller&&this._controller.dispose(),this._controllerHost={}},remove:function(){this._mainGroup.removeAll(),this._data=null}});function m(e,t){var i=e.getItemLayout(t);return i&&!isNaN(i.x)&&!isNaN(i.y)&&"none"!==e.getItemVisual(t,"symbol")}function v(e,t,i){return i.itemModel=t,i.itemStyle=t.getModel("itemStyle").getItemStyle(),i.hoverItemStyle=t.getModel("emphasis.itemStyle").getItemStyle(),i.lineStyle=t.getModel("lineStyle").getLineStyle(),i.labelModel=t.getModel("label"),i.hoverLabelModel=t.getModel("emphasis.label"),!1===e.isExpand&&0!==e.children.length?i.symbolInnerColor=i.itemStyle.fill:i.symbolInnerColor="#fff",i}function y(e,t,i,r,s,l){var u=!i,c=e.tree.getNodeByDataIndex(t),d=c.getModel(),h=(l=v(c,d,l),e.tree.root),p=c.parentNode===h?c:c.parentNode||c,g=e.getItemGraphicEl(p.dataIndex),f=p.getLayout(),m=g?{x:g.position[0],y:g.position[1],rawX:g.__radialOldRawX,rawY:g.__radialOldRawY}:f,y=c.getLayout();u?(i=new o(e,t,l),i.attr("position",[m.x,m.y])):i.updateData(e,t,l),i.__radialOldRawX=i.__radialRawX,i.__radialOldRawY=i.__radialRawY,i.__radialRawX=y.rawX,i.__radialRawY=y.rawY,r.add(i),e.setItemGraphicEl(t,i),a.updateProps(i,{position:[y.x,y.y]},s);var x=i.getSymbolPath();if("radial"===l.layout){var b,w,S=h.children[0],M=S.getLayout(),I=S.children.length;if(y.x===M.x&&!0===c.isExpand){var A={};A.x=(S.children[0].getLayout().x+S.children[I-1].getLayout().x)/2,A.y=(S.children[0].getLayout().y+S.children[I-1].getLayout().y)/2,b=Math.atan2(A.y-M.y,A.x-M.x),b<0&&(b=2*Math.PI+b),w=A.x<M.x,w&&(b-=Math.PI)}else b=Math.atan2(y.y-M.y,y.x-M.x),b<0&&(b=2*Math.PI+b),0===c.children.length||0!==c.children.length&&!1===c.isExpand?(w=y.x<M.x,w&&(b-=Math.PI)):(w=y.x>M.x,w||(b-=Math.PI));var T=w?"left":"right";x.setStyle({textPosition:T,textRotation:-b,textOrigin:"center",verticalAlign:"middle"})}if(c.parentNode&&c.parentNode!==h){var D=i.__edge;D||(D=i.__edge=new a.BezierCurve({shape:_(l,m,m),style:n.defaults({opacity:0,strokeNoScale:!0},l.lineStyle)})),a.updateProps(D,{shape:_(l,f,y),style:{opacity:1}},s),r.add(D)}}function x(e,t,i,n,o,r){var s,l=e.tree.getNodeByDataIndex(t),u=e.tree.root,c=l.getModel(),d=(r=v(l,c,r),l.parentNode===u?l:l.parentNode||l);while(s=d.getLayout(),null==s)d=d.parentNode===u?d:d.parentNode||d;a.updateProps(i,{position:[s.x+1,s.y+1]},o,(function(){n.remove(i),e.setItemGraphicEl(t,null)})),i.fadeOut(null,{keepLabel:!0});var h=i.__edge;h&&a.updateProps(h,{shape:_(r,s,s),style:{opacity:0}},o,(function(){n.remove(h)}))}function _(e,t,i){var n,a,o,r,l,u,c,d,h=e.orient;if("radial"===e.layout){l=t.rawX,c=t.rawY,u=i.rawX,d=i.rawY;var p=s(l,c),g=s(l,c+(d-c)*e.curvature),f=s(u,d+(c-d)*e.curvature),m=s(u,d);return{x1:p.x,y1:p.y,x2:m.x,y2:m.y,cpx1:g.x,cpy1:g.y,cpx2:f.x,cpy2:f.y}}return l=t.x,c=t.y,u=i.x,d=i.y,"LR"!==h&&"RL"!==h||(n=l+(u-l)*e.curvature,a=c,o=u+(l-u)*e.curvature,r=d),"TB"!==h&&"BT"!==h||(n=l,a=c+(d-c)*e.curvature,o=u,r=d+(c-d)*e.curvature),{x1:l,y1:c,x2:u,y2:d,cpx1:n,cpy1:a,cpx2:o,cpy2:r}}e.exports=f},"6d9a":function(e,t){function i(e,t,i){var n,a=[e],o=[];while(n=a.pop())if(o.push(n),n.isExpand){var r=n.children;if(r.length)for(var s=0;s<r.length;s++)a.push(r[s])}while(n=o.pop())t(n,i)}function n(e,t){var i,n=[e];while(i=n.pop())if(t(i),i.isExpand){var a=i.children;if(a.length)for(var o=a.length-1;o>=0;o--)n.push(a[o])}}t.eachAfter=i,t.eachBefore=n},"6fda":function(e,t,i){var n=i("6d8b"),a=n.each,o="\0_ec_hist_store";function r(e,t){var i=c(e);a(t,(function(t,n){for(var a=i.length-1;a>=0;a--){var o=i[a];if(o[n])break}if(a<0){var r=e.queryComponents({mainType:"dataZoom",subType:"select",id:n})[0];if(r){var s=r.getPercentRange();i[0][n]={dataZoomId:n,start:s[0],end:s[1]}}}})),i.push(t)}function s(e){var t=c(e),i=t[t.length-1];t.length>1&&t.pop();var n={};return a(i,(function(e,i){for(var a=t.length-1;a>=0;a--){e=t[a][i];if(e){n[i]=e;break}}})),n}function l(e){e[o]=null}function u(e){return c(e).length}function c(e){var t=e[o];return t||(t=e[o]=[{}]),t}t.push=r,t.pop=s,t.clear=l,t.count=u},7023:function(e,t,i){var n=i("6d8b"),a={updateSelectedMap:function(e){this._targetList=n.isArray(e)?e.slice():[],this._selectTargetMap=n.reduce(e||[],(function(e,t){return e.set(t.name,t),e}),n.createHashMap())},select:function(e,t){var i=null!=t?this._targetList[t]:this._selectTargetMap.get(e),n=this.get("selectedMode");"single"===n&&this._selectTargetMap.each((function(e){e.selected=!1})),i&&(i.selected=!0)},unSelect:function(e,t){var i=null!=t?this._targetList[t]:this._selectTargetMap.get(e);i&&(i.selected=!1)},toggleSelected:function(e,t){var i=null!=t?this._targetList[t]:this._selectTargetMap.get(e);if(null!=i)return this[i.selected?"unSelect":"select"](e,t),i.selected},isSelected:function(e,t){var i=null!=t?this._targetList[t]:this._selectTargetMap.get(e);return i&&i.selected}};e.exports=a},"71b2":function(e,t,i){var n=i("6d8b"),a=n.createHashMap;function o(e){e.eachSeriesByType("themeRiver",(function(e){var t=e.getData(),i=e.getRawData(),n=e.get("color"),o=a();t.each((function(e){o.set(t.getRawIndex(e),e)})),i.each((function(a){var r=i.getName(a),s=n[(e.nameMap.get(r)-1)%n.length];i.setItemVisual(a,"color",s);var l=o.get(a);null!=l&&t.setItemVisual(l,"color",s)}))}))}e.exports=o},7293:function(e,t,i){var n=i("4e08"),a=(n.__DEV__,i("4f85")),o=i("6179"),r=i("6d8b"),s=r.concatArray,l=r.mergeAll,u=r.map,c=i("eda2"),d=c.encodeHTML,h=(i("2039"),"undefined"===typeof Uint32Array?Array:Uint32Array),p="undefined"===typeof Float64Array?Array:Float64Array;function g(e){var t=e.data;t&&t[0]&&t[0][0]&&t[0][0].coord&&(e.data=u(t,(function(e){var t=[e[0].coord,e[1].coord],i={coords:t};return e[0].name&&(i.fromName=e[0].name),e[1].name&&(i.toName=e[1].name),l([i,e[0],e[1]])})))}var f=a.extend({type:"series.lines",dependencies:["grid","polar"],visualColorAccessPath:"lineStyle.color",init:function(e){e.data=e.data||[],g(e);var t=this._processFlatCoordsArray(e.data);this._flatCoords=t.flatCoords,this._flatCoordsOffset=t.flatCoordsOffset,t.flatCoords&&(e.data=new Float32Array(t.count)),f.superApply(this,"init",arguments)},mergeOption:function(e){if(e.data=e.data||[],g(e),e.data){var t=this._processFlatCoordsArray(e.data);this._flatCoords=t.flatCoords,this._flatCoordsOffset=t.flatCoordsOffset,t.flatCoords&&(e.data=new Float32Array(t.count))}f.superApply(this,"mergeOption",arguments)},appendData:function(e){var t=this._processFlatCoordsArray(e.data);t.flatCoords&&(this._flatCoords?(this._flatCoords=s(this._flatCoords,t.flatCoords),this._flatCoordsOffset=s(this._flatCoordsOffset,t.flatCoordsOffset)):(this._flatCoords=t.flatCoords,this._flatCoordsOffset=t.flatCoordsOffset),e.data=new Float32Array(t.count)),this.getRawData().appendData(e.data)},_getCoordsFromItemModel:function(e){var t=this.getData().getItemModel(e),i=t.option instanceof Array?t.option:t.getShallow("coords");return i},getLineCoordsCount:function(e){return this._flatCoordsOffset?this._flatCoordsOffset[2*e+1]:this._getCoordsFromItemModel(e).length},getLineCoords:function(e,t){if(this._flatCoordsOffset){for(var i=this._flatCoordsOffset[2*e],n=this._flatCoordsOffset[2*e+1],a=0;a<n;a++)t[a]=t[a]||[],t[a][0]=this._flatCoords[i+2*a],t[a][1]=this._flatCoords[i+2*a+1];return n}var o=this._getCoordsFromItemModel(e);for(a=0;a<o.length;a++)t[a]=t[a]||[],t[a][0]=o[a][0],t[a][1]=o[a][1];return o.length},_processFlatCoordsArray:function(e){var t=0;if(this._flatCoords&&(t=this._flatCoords.length),"number"===typeof e[0]){for(var i=e.length,n=new h(i),a=new p(i),o=0,r=0,s=0,l=0;l<i;){s++;var u=e[l++];n[r++]=o+t,n[r++]=u;for(var c=0;c<u;c++){var d=e[l++],g=e[l++];a[o++]=d,a[o++]=g}}return{flatCoordsOffset:new Uint32Array(n.buffer,0,r),flatCoords:a,count:s}}return{flatCoordsOffset:null,flatCoords:null,count:e.length}},getInitialData:function(e,t){var i=new o(["value"],this);return i.hasItemOption=!1,i.initData(e.data,[],(function(e,t,n,a){if(e instanceof Array)return NaN;i.hasItemOption=!0;var o=e.value;return null!=o?o instanceof Array?o[a]:o:void 0})),i},formatTooltip:function(e){var t=this.getData(),i=t.getItemModel(e),n=i.get("name");if(n)return n;var a=i.get("fromName"),o=i.get("toName"),r=[];return null!=a&&r.push(a),null!=o&&r.push(o),d(r.join(" > "))},preventIncremental:function(){return!!this.get("effect.show")},getProgressive:function(){var e=this.option.progressive;return null==e?this.option.large?1e4:this.get("progressive"):e},getProgressiveThreshold:function(){var e=this.option.progressiveThreshold;return null==e?this.option.large?2e4:this.get("progressiveThreshold"):e},defaultOption:{coordinateSystem:"geo",zlevel:0,z:2,legendHoverLink:!0,hoverAnimation:!0,xAxisIndex:0,yAxisIndex:0,symbol:["none","none"],symbolSize:[10,10],geoIndex:0,effect:{show:!1,period:4,constantSpeed:0,symbol:"circle",symbolSize:3,loop:!0,trailLength:.2},large:!1,largeThreshold:2e3,polyline:!1,label:{show:!1,position:"end"},lineStyle:{opacity:.5}}}),m=f;e.exports=m},"72b6":function(e,t,i){var n=i("3eba"),a=i("6d8b"),o=i("2306"),r=i("eda2"),s=i("f934"),l=i("5f14"),u=n.extendComponentView({type:"visualMap",autoPositionValues:{left:1,right:1,top:1,bottom:1},init:function(e,t){this.ecModel=e,this.api=t,this.visualMapModel},render:function(e,t,i,n){this.visualMapModel=e,!1!==e.get("show")?this.doRender.apply(this,arguments):this.group.removeAll()},renderBackground:function(e){var t=this.visualMapModel,i=r.normalizeCssArray(t.get("padding")||0),n=e.getBoundingRect();e.add(new o.Rect({z2:-1,silent:!0,shape:{x:n.x-i[3],y:n.y-i[0],width:n.width+i[3]+i[1],height:n.height+i[0]+i[2]},style:{fill:t.get("backgroundColor"),stroke:t.get("borderColor"),lineWidth:t.get("borderWidth")}}))},getControllerVisual:function(e,t,i){i=i||{};var n=i.forceState,o=this.visualMapModel,r={};if("symbol"===t&&(r.symbol=o.get("itemSymbol")),"color"===t){var s=o.get("contentColor");r.color=s}function u(e){return r[e]}function c(e,t){r[e]=t}var d=o.controllerVisuals[n||o.getValueState(e)],h=l.prepareVisualTypes(d);return a.each(h,(function(n){var a=d[n];i.convertOpacityToAlpha&&"opacity"===n&&(n="colorAlpha",a=d.__alphaForOpacity),l.dependsOn(n,t)&&a&&a.applyVisual(e,u,c)})),r[t]},positionGroup:function(e){var t=this.visualMapModel,i=this.api;s.positionElement(e,t.getBoxLayoutParams(),{width:i.getWidth(),height:i.getHeight()})},doRender:a.noop});e.exports=u},7368:function(e,t,i){var n=i("4e08"),a=(n.__DEV__,i("6d8b")),o=i("625e"),r=o.enableClassCheck;function s(e){return"_EC_"+e}var l=function(e){this._directed=e||!1,this.nodes=[],this.edges=[],this._nodesMap={},this._edgesMap={},this.data,this.edgeData},u=l.prototype;function c(e,t){this.id=null==e?"":e,this.inEdges=[],this.outEdges=[],this.edges=[],this.hostGraph,this.dataIndex=null==t?-1:t}function d(e,t,i){this.node1=e,this.node2=t,this.dataIndex=null==i?-1:i}u.type="graph",u.isDirected=function(){return this._directed},u.addNode=function(e,t){e=e||""+t;var i=this._nodesMap;if(!i[s(e)]){var n=new c(e,t);return n.hostGraph=this,this.nodes.push(n),i[s(e)]=n,n}},u.getNodeByIndex=function(e){var t=this.data.getRawIndex(e);return this.nodes[t]},u.getNodeById=function(e){return this._nodesMap[s(e)]},u.addEdge=function(e,t,i){var n=this._nodesMap,a=this._edgesMap;if("number"===typeof e&&(e=this.nodes[e]),"number"===typeof t&&(t=this.nodes[t]),c.isInstance(e)||(e=n[s(e)]),c.isInstance(t)||(t=n[s(t)]),e&&t){var o=e.id+"-"+t.id;if(!a[o]){var r=new d(e,t,i);return r.hostGraph=this,this._directed&&(e.outEdges.push(r),t.inEdges.push(r)),e.edges.push(r),e!==t&&t.edges.push(r),this.edges.push(r),a[o]=r,r}}},u.getEdgeByIndex=function(e){var t=this.edgeData.getRawIndex(e);return this.edges[t]},u.getEdge=function(e,t){c.isInstance(e)&&(e=e.id),c.isInstance(t)&&(t=t.id);var i=this._edgesMap;return this._directed?i[e+"-"+t]:i[e+"-"+t]||i[t+"-"+e]},u.eachNode=function(e,t){for(var i=this.nodes,n=i.length,a=0;a<n;a++)i[a].dataIndex>=0&&e.call(t,i[a],a)},u.eachEdge=function(e,t){for(var i=this.edges,n=i.length,a=0;a<n;a++)i[a].dataIndex>=0&&i[a].node1.dataIndex>=0&&i[a].node2.dataIndex>=0&&e.call(t,i[a],a)},u.breadthFirstTraverse=function(e,t,i,n){if(c.isInstance(t)||(t=this._nodesMap[s(t)]),t){for(var a="out"===i?"outEdges":"in"===i?"inEdges":"edges",o=0;o<this.nodes.length;o++)this.nodes[o].__visited=!1;if(!e.call(n,t,null)){var r=[t];while(r.length){var l=r.shift(),u=l[a];for(o=0;o<u.length;o++){var d=u[o],h=d.node1===l?d.node2:d.node1;if(!h.__visited){if(e.call(n,h,l))return;r.push(h),h.__visited=!0}}}}}},u.update=function(){for(var e=this.data,t=this.edgeData,i=this.nodes,n=this.edges,a=0,o=i.length;a<o;a++)i[a].dataIndex=-1;for(a=0,o=e.count();a<o;a++)i[e.getRawIndex(a)].dataIndex=a;t.filterSelf((function(e){var i=n[t.getRawIndex(e)];return i.node1.dataIndex>=0&&i.node2.dataIndex>=0}));for(a=0,o=n.length;a<o;a++)n[a].dataIndex=-1;for(a=0,o=t.count();a<o;a++)n[t.getRawIndex(a)].dataIndex=a},u.clone=function(){for(var e=new l(this._directed),t=this.nodes,i=this.edges,n=0;n<t.length;n++)e.addNode(t[n].id,t[n].dataIndex);for(n=0;n<i.length;n++){var a=i[n];e.addEdge(a.node1.id,a.node2.id,a.dataIndex)}return e},c.prototype={constructor:c,degree:function(){return this.edges.length},inDegree:function(){return this.inEdges.length},outDegree:function(){return this.outEdges.length},getModel:function(e){if(!(this.dataIndex<0)){var t=this.hostGraph,i=t.data.getItemModel(this.dataIndex);return i.getModel(e)}}},d.prototype.getModel=function(e){if(!(this.dataIndex<0)){var t=this.hostGraph,i=t.edgeData.getItemModel(this.dataIndex);return i.getModel(e)}};var h=function(e,t){return{getValue:function(i){var n=this[e][t];return n.get(n.getDimension(i||"value"),this.dataIndex)},setVisual:function(i,n){this.dataIndex>=0&&this[e][t].setItemVisual(this.dataIndex,i,n)},getVisual:function(i,n){return this[e][t].getItemVisual(this.dataIndex,i,n)},setLayout:function(i,n){this.dataIndex>=0&&this[e][t].setItemLayout(this.dataIndex,i,n)},getLayout:function(){return this[e][t].getItemLayout(this.dataIndex)},getGraphicEl:function(){return this[e][t].getItemGraphicEl(this.dataIndex)},getRawIndex:function(){return this[e][t].getRawIndex(this.dataIndex)}}};a.mixin(c,h("hostGraph","data")),a.mixin(d,h("hostGraph","edgeData")),l.Node=c,l.Edge=d,r(c),r(d);var p=l;e.exports=p},"73ca":function(e,t,i){var n=i("2306"),a=i("7e5b");function o(e){this._ctor=e||a,this.group=new n.Group}var r=o.prototype;function s(e,t,i,n){var a=t.getItemLayout(i);if(d(a)){var o=new e._ctor(t,i,n);t.setItemGraphicEl(i,o),e.group.add(o)}}function l(e,t,i,n,a,o){var r=t.getItemGraphicEl(n);d(i.getItemLayout(a))?(r?r.updateData(i,a,o):r=new e._ctor(i,a,o),i.setItemGraphicEl(a,r),e.group.add(r)):e.group.remove(r)}function u(e){var t=e.hostModel;return{lineStyle:t.getModel("lineStyle").getLineStyle(),hoverLineStyle:t.getModel("emphasis.lineStyle").getLineStyle(),labelModel:t.getModel("label"),hoverLabelModel:t.getModel("emphasis.label")}}function c(e){return isNaN(e[0])||isNaN(e[1])}function d(e){return!c(e[0])&&!c(e[1])}r.isPersistent=function(){return!0},r.updateData=function(e){var t=this,i=t.group,n=t._lineData;t._lineData=e,n||i.removeAll();var a=u(e);e.diff(n).add((function(i){s(t,e,i,a)})).update((function(i,o){l(t,n,e,o,i,a)})).remove((function(e){i.remove(n.getItemGraphicEl(e))})).execute()},r.updateLayout=function(){var e=this._lineData;e&&e.eachItemGraphicEl((function(t,i){t.updateLayout(e,i)}),this)},r.incrementalPrepareUpdate=function(e){this._seriesScope=u(e),this._lineData=null,this.group.removeAll()},r.incrementalUpdate=function(e,t){function i(e){e.isGroup||(e.incremental=e.useHoverLayer=!0)}for(var n=e.start;n<e.end;n++){var a=t.getItemLayout(n);if(d(a)){var o=new this._ctor(t,n,this._seriesScope);o.traverse(i),this.group.add(o),t.setItemGraphicEl(n,o)}}},r.remove=function(){this._clearIncremental(),this._incremental=null,this.group.removeAll()},r._clearIncremental=function(){var e=this._incremental;e&&e.clearDisplaybles()};var h=o;e.exports=h},7419:function(e,t,i){var n=i("3eba"),a=i("b336");i("bc5f"),i("ab05"),i("307a"),i("3cd6"),i("d6ef"),n.registerPreprocessor(a)},7661:function(e,t,i){var n=i("0c41"),a=i("3eba"),o=a.extendComponentView({type:"geo",init:function(e,t){var i=new n(t,!0);this._mapDraw=i,this.group.add(i.group)},render:function(e,t,i,n){if(!n||"geoToggleSelect"!==n.type||n.from!==this.uid){var a=this._mapDraw;e.get("show")?a.draw(e,t,i,this,n):this._mapDraw.group.removeAll(),this.group.silent=e.get("silent")}},dispose:function(){this._mapDraw&&this._mapDraw.remove()}});e.exports=o},"767c":function(e,t,i){var n=i("3eba"),a=i("6d8b"),o=i("607d"),r=i("29a8"),s=i("2145"),l=r.toolbox.dataView,u=new Array(60).join("-"),c="\t";function d(e){var t={},i=[],n=[];return e.eachRawSeries((function(e){var a=e.coordinateSystem;if(!a||"cartesian2d"!==a.type&&"polar"!==a.type)i.push(e);else{var o=a.getBaseAxis();if("category"===o.type){var r=o.dim+"_"+o.index;t[r]||(t[r]={categoryAxis:o,valueAxis:a.getOtherAxis(o),series:[]},n.push({axisDim:o.dim,axisIndex:o.index})),t[r].series.push(e)}else i.push(e)}})),{seriesGroupByCategoryAxis:t,other:i,meta:n}}function h(e){var t=[];return a.each(e,(function(e,i){var n=e.categoryAxis,o=e.valueAxis,r=o.dim,s=[" "].concat(a.map(e.series,(function(e){return e.name}))),l=[n.model.getCategories()];a.each(e.series,(function(e){l.push(e.getRawData().mapArray(r,(function(e){return e})))}));for(var u=[s.join(c)],d=0;d<l[0].length;d++){for(var h=[],p=0;p<l.length;p++)h.push(l[p][d]);u.push(h.join(c))}t.push(u.join("\n"))})),t.join("\n\n"+u+"\n\n")}function p(e){return a.map(e,(function(e){var t=e.getRawData(),i=[e.name],n=[];return t.each(t.dimensions,(function(){for(var e=arguments.length,a=arguments[e-1],o=t.getName(a),r=0;r<e-1;r++)n[r]=arguments[r];i.push((o?o+c:"")+n.join(c))})),i.join("\n")})).join("\n\n"+u+"\n\n")}function g(e){var t=d(e);return{value:a.filter([h(t.seriesGroupByCategoryAxis),p(t.other)],(function(e){return e.replace(/[\n\t\s]/g,"")})).join("\n\n"+u+"\n\n"),meta:t.meta}}function f(e){return e.replace(/^\s\s*/,"").replace(/\s\s*$/,"")}function m(e){var t=e.slice(0,e.indexOf("\n"));if(t.indexOf(c)>=0)return!0}var v=new RegExp("["+c+"]+","g");function y(e){for(var t=e.split(/\n+/g),i=f(t.shift()).split(v),n=[],o=a.map(i,(function(e){return{name:e,data:[]}})),r=0;r<t.length;r++){var s=f(t[r]).split(v);n.push(s.shift());for(var l=0;l<s.length;l++)o[l]&&(o[l].data[r]=s[l])}return{series:o,categories:n}}function x(e){for(var t=e.split(/\n+/g),i=f(t.shift()),n=[],a=0;a<t.length;a++){var o,r=f(t[a]).split(v),s="",l=!1;isNaN(r[0])?(l=!0,s=r[0],r=r.slice(1),n[a]={name:s,value:[]},o=n[a].value):o=n[a]=[];for(var u=0;u<r.length;u++)o.push(+r[u]);1===o.length&&(l?n[a].value=o[0]:n[a]=o[0])}return{name:i,data:n}}function _(e,t){var i=e.split(new RegExp("\n*"+u+"\n*","g")),n={series:[]};return a.each(i,(function(e,i){if(m(e)){var a=y(e),o=t[i],r=o.axisDim+"Axis";o&&(n[r]=n[r]||[],n[r][o.axisIndex]={data:a.categories},n.series=n.series.concat(a.series))}else{a=x(e);n.series.push(a)}})),n}function b(e){this._dom=null,this.model=e}function w(e,t){return a.map(e,(function(e,i){var n=t&&t[i];return a.isObject(n)&&!a.isArray(n)?(a.isObject(e)&&!a.isArray(e)&&(e=e.value),a.defaults({value:e},n)):e}))}b.defaultOption={show:!0,readOnly:!1,optionToContent:null,contentToOption:null,icon:"M17.5,17.3H33 M17.5,17.3H33 M45.4,29.5h-28 M11.5,2v56H51V14.8L38.4,2H11.5z M38.4,2.2v12.7H51 M45.4,41.7h-28",title:a.clone(l.title),lang:a.clone(l.lang),backgroundColor:"#fff",textColor:"#000",textareaColor:"#fff",textareaBorderColor:"#333",buttonColor:"#c23531",buttonTextColor:"#fff"},b.prototype.onclick=function(e,t){var i=t.getDom(),n=this.model;this._dom&&i.removeChild(this._dom);var r=document.createElement("div");r.style.cssText="position:absolute;left:5px;top:5px;bottom:5px;right:5px;",r.style.backgroundColor=n.get("backgroundColor")||"#fff";var s=document.createElement("h4"),l=n.get("lang")||[];s.innerHTML=l[0]||n.get("title"),s.style.cssText="margin: 10px 20px;",s.style.color=n.get("textColor");var u=document.createElement("div"),d=document.createElement("textarea");u.style.cssText="display:block;width:100%;overflow:auto;";var h=n.get("optionToContent"),p=n.get("contentToOption"),f=g(e);if("function"===typeof h){var m=h(t.getOption());"string"===typeof m?u.innerHTML=m:a.isDom(m)&&u.appendChild(m)}else u.appendChild(d),d.readOnly=n.get("readOnly"),d.style.cssText="width:100%;height:100%;font-family:monospace;font-size:14px;line-height:1.6rem;",d.style.color=n.get("textColor"),d.style.borderColor=n.get("textareaBorderColor"),d.style.backgroundColor=n.get("textareaColor"),d.value=f.value;var v=f.meta,y=document.createElement("div");y.style.cssText="position:absolute;bottom:0;left:0;right:0;";var x="float:right;margin-right:20px;border:none;cursor:pointer;padding:2px 5px;font-size:12px;border-radius:3px",b=document.createElement("div"),w=document.createElement("div");x+=";background-color:"+n.get("buttonColor"),x+=";color:"+n.get("buttonTextColor");var S=this;function M(){i.removeChild(r),S._dom=null}o.addEventListener(b,"click",M),o.addEventListener(w,"click",(function(){var e;try{e="function"===typeof p?p(u,t.getOption()):_(d.value,v)}catch(i){throw M(),new Error("Data view format error "+i)}e&&t.dispatchAction({type:"changeDataView",newOption:e}),M()})),b.innerHTML=l[1],w.innerHTML=l[2],w.style.cssText=x,b.style.cssText=x,!n.get("readOnly")&&y.appendChild(w),y.appendChild(b),o.addEventListener(d,"keydown",(function(e){if(9===(e.keyCode||e.which)){var t=this.value,i=this.selectionStart,n=this.selectionEnd;this.value=t.substring(0,i)+c+t.substring(n),this.selectionStart=this.selectionEnd=i+1,o.stop(e)}})),r.appendChild(s),r.appendChild(u),r.appendChild(y),u.style.height=i.clientHeight-80+"px",i.appendChild(r),this._dom=r},b.prototype.remove=function(e,t){this._dom&&t.getDom().removeChild(this._dom)},b.prototype.dispose=function(e,t){this.remove(e,t)},s.register("dataView",b),n.registerAction({type:"changeDataView",event:"dataViewChanged",update:"prepareAndUpdate"},(function(e,t){var i=[];a.each(e.newOption.series,(function(e){var n=t.getSeriesByName(e.name)[0];if(n){var o=n.get("data");i.push({name:e.name,data:w(e.data,o)})}else i.push(a.extend({type:"scatter"},e))})),t.mergeOption(a.defaults({series:i},e.newOption))}));var S=b;e.exports=S},7782:function(e,t,i){var n=i("3eba"),a=i("6d8b");function o(e,t){a.each(t,(function(t){t.update="updateView",n.registerAction(t,(function(i,n){var a={};return n.eachComponent({mainType:"series",subType:e,query:i},(function(e){e[t.method]&&e[t.method](i.name,i.dataIndex);var n=e.getData();n.each((function(t){var i=n.getName(t);a[i]=e.isSelected(i)||!1}))})),{name:i.name,selected:a}}))}))}e.exports=o},7887:function(e,t,i){var n=i("6d8b"),a=i("84ce");function o(e,t,i){a.call(this,e,t,i),this.type="value",this.angle=0,this.name="",this.model}n.inherits(o,a);var r=o;e.exports=r},7891:function(e,t,i){var n=i("6d8b");function a(e){var t=e.polar;if(t){n.isArray(t)||(t=[t]);var i=[];n.each(t,(function(t,a){t.indicator?(t.type&&!t.shape&&(t.shape=t.type),e.radar=e.radar||[],n.isArray(e.radar)||(e.radar=[e.radar]),e.radar.push(t)):i.push(t)})),e.polar=i}n.each(e.series,(function(e){e&&"radar"===e.type&&e.polarIndex&&(e.radarIndex=e.polarIndex)}))}e.exports=a},7919:function(e,t,i){var n=i("f934"),a=n.getLayoutRect,o=n.box,r=n.positionElement,s=i("eda2"),l=i("2306");function u(e,t,i){var n=t.getBoxLayoutParams(),s=t.get("padding"),l={width:i.getWidth(),height:i.getHeight()},u=a(n,l,s);o(t.get("orient"),e,t.get("itemGap"),u.width,u.height),r(e,n,l,s)}function c(e,t){var i=s.normalizeCssArray(t.get("padding")),n=t.getItemStyle(["color","opacity"]);n.fill=t.get("backgroundColor");e=new l.Rect({shape:{x:e.x-i[3],y:e.y-i[0],width:e.width+i[1]+i[3],height:e.height+i[0]+i[2],r:t.get("borderRadius")},style:n,silent:!0,z2:-1});return e}t.layout=u,t.makeBackground=c},"7b0c":function(e,t,i){var n=i("6cc5"),a=i("f934"),o=a.getLayoutRect,r=i("e263");function s(e,t,i){var n=e.getBoxLayoutParams();return n.aspect=i,o(n,{width:t.getWidth(),height:t.getHeight()})}function l(e,t){var i=[];return e.eachSeriesByType("graph",(function(e){var a=e.get("coordinateSystem");if(!a||"view"===a){var o=e.getData(),l=o.mapArray((function(e){var t=o.getItemModel(e);return[+t.get("x"),+t.get("y")]})),u=[],c=[];r.fromPoints(l,u,c),c[0]-u[0]===0&&(c[0]+=1,u[0]-=1),c[1]-u[1]===0&&(c[1]+=1,u[1]-=1);var d=(c[0]-u[0])/(c[1]-u[1]),h=s(e,t,d);isNaN(d)&&(u=[h.x,h.y],c=[h.x+h.width,h.y+h.height]);var p=c[0]-u[0],g=c[1]-u[1],f=h.width,m=h.height,v=e.coordinateSystem=new n;v.zoomLimit=e.get("scaleLimit"),v.setBoundingRect(u[0],u[1],p,g),v.setViewRect(h.x,h.y,f,m),v.setCenter(e.get("center")),v.setZoom(e.get("zoom")),i.push(v)}})),i}e.exports=l},"7c4d":function(e,t,i){var n=i("3eba"),a=i("6d8b"),o=i("fc82"),r=i("bd9e"),s=i("6fda"),l=i("ef6a"),u=i("29a8"),c=i("2145");i("dd39");var d=u.toolbox.dataZoom,h=a.each,p="\0_ec_\0toolbox-dataZoom_";function g(e,t,i){(this._brushController=new o(i.getZr())).on("brush",a.bind(this._onBrush,this)).mount(),this._isZoomActive}g.defaultOption={show:!0,icon:{zoom:"M0,13.5h26.9 M13.5,26.9V0 M32.1,13.5H58V58H13.5 V32.1",back:"M22,1.4L9.9,13.5l12.3,12.3 M10.3,13.5H54.9v44.6 H10.3v-26"},title:a.clone(d.title)};var f=g.prototype;f.render=function(e,t,i,n){this.model=e,this.ecModel=t,this.api=i,x(e,t,this,n,i),y(e,t)},f.onclick=function(e,t,i){m[i].call(this)},f.remove=function(e,t){this._brushController.unmount()},f.dispose=function(e,t){this._brushController.dispose()};var m={zoom:function(){var e=!this._isZoomActive;this.api.dispatchAction({type:"takeGlobalCursor",key:"dataZoomSelect",dataZoomSelectActive:e})},back:function(){this._dispatchZoomAction(s.pop(this.ecModel))}};function v(e){var t={};return a.each(["xAxisIndex","yAxisIndex"],(function(i){t[i]=e[i],null==t[i]&&(t[i]="all"),(!1===t[i]||"none"===t[i])&&(t[i]=[])})),t}function y(e,t){e.setIconStatus("back",s.count(t)>1?"emphasis":"normal")}function x(e,t,i,n,a){var o=i._isZoomActive;n&&"takeGlobalCursor"===n.type&&(o="dataZoomSelect"===n.key&&n.dataZoomSelectActive),i._isZoomActive=o,e.setIconStatus("zoom",o?"emphasis":"normal");var s=new r(v(e.option),t,{include:["grid"]});i._brushController.setPanels(s.makePanelOpts(a,(function(e){return e.xAxisDeclared&&!e.yAxisDeclared?"lineX":!e.xAxisDeclared&&e.yAxisDeclared?"lineY":"rect"}))).enableBrush(!!o&&{brushType:"auto",brushStyle:{lineWidth:0,fill:"rgba(0,0,0,0.2)"}})}f._onBrush=function(e,t){if(t.isEnd&&e.length){var i={},n=this.ecModel;this._brushController.updateCovers([]);var a=new r(v(this.model.option),n,{include:["grid"]});a.matchOutputRanges(e,n,(function(e,t,i){if("cartesian2d"===i.type){var n=e.brushType;"rect"===n?(o("x",i,t[0]),o("y",i,t[1])):o({lineX:"x",lineY:"y"}[n],i,t)}})),s.push(n,i),this._dispatchZoomAction(i)}function o(e,t,a){var o=t.getAxis(e),r=o.model,s=u(e,r,n),c=s.findRepresentativeAxisProxy(r).getMinMaxSpan();null==c.minValueSpan&&null==c.maxValueSpan||(a=l(0,a.slice(),o.scale.getExtent(),0,c.minValueSpan,c.maxValueSpan)),s&&(i[s.id]={dataZoomId:s.id,startValue:a[0],endValue:a[1]})}function u(e,t,i){var n;return i.eachComponent({mainType:"dataZoom",subType:"select"},(function(i){var a=i.getAxisModel(e,t.componentIndex);a&&(n=i)})),n}},f._dispatchZoomAction=function(e){var t=[];h(e,(function(e,i){t.push(a.clone(e))})),t.length&&this.api.dispatchAction({type:"dataZoom",from:this.uid,batch:t})},c.register("dataZoom",g),n.registerPreprocessor((function(e){if(e){var t=e.dataZoom||(e.dataZoom=[]);a.isArray(t)||(e.dataZoom=t=[t]);var i=e.toolbox;if(i&&(a.isArray(i)&&(i=i[0]),i&&i.feature)){var n=i.feature.dataZoom;o("xAxis",n),o("yAxis",n)}}function o(e,i){if(i){var n=e+"Index",o=i[n];null==o||"all"===o||a.isArray(o)||(o=!1===o||"none"===o?[]:[o]),r(e,(function(i,r){if(null==o||"all"===o||-1!==a.indexOf(o,r)){var s={type:"select",$fromToolbox:!0,id:p+e+r};s[n]=r,t.push(s)}}))}}function r(t,i){var n=e[t];a.isArray(n)||(n=n?[n]:[]),h(n,i)}}));var _=g;e.exports=_},"7dcf":function(e,t,i){var n=i("b12f"),a=n.extend({type:"dataZoom",render:function(e,t,i,n){this.dataZoomModel=e,this.ecModel=t,this.api=i},getTargetCoordInfo:function(){var e=this.dataZoomModel,t=this.ecModel,i={};function n(e,t,i,n){for(var a,o=0;o<i.length;o++)if(i[o].model===e){a=i[o];break}a||i.push(a={model:e,axisModels:[],coordIndex:n}),a.axisModels.push(t)}return e.eachTargetAxis((function(e,a){var o=t.getComponent(e.axis,a);if(o){var r=o.getCoordSysModel();r&&n(r,o,i[r.mainType]||(i[r.mainType]=[]),r.componentIndex)}}),this),i}});e.exports=a},"7e32":function(e,t,i){var n=i("3eba");i("43b8"),i("8e77"),n.registerPreprocessor((function(e){e.markArea=e.markArea||{}}))},"7e5b":function(e,t,i){var n=i("6d8b"),a=i("401b"),o=i("a15a"),r=i("7f91"),s=i("2306"),l=i("3842"),u=l.round,c=["fromSymbol","toSymbol"];function d(e){return"_"+e+"Type"}function h(e,t,i){var a=t.getItemVisual(i,"color"),r=t.getItemVisual(i,e),s=t.getItemVisual(i,e+"Size");if(r&&"none"!==r){n.isArray(s)||(s=[s,s]);var l=o.createSymbol(r,-s[0]/2,-s[1]/2,s[0],s[1],a);return l.name=e,l}}function p(e){var t=new r({name:"line"});return g(t.shape,e),t}function g(e,t){var i=t[0],n=t[1],a=t[2];e.x1=i[0],e.y1=i[1],e.x2=n[0],e.y2=n[1],e.percent=1,a?(e.cpx1=a[0],e.cpy1=a[1]):(e.cpx1=NaN,e.cpy1=NaN)}function f(){var e=this,t=e.childOfName("fromSymbol"),i=e.childOfName("toSymbol"),n=e.childOfName("label");if(t||i||!n.ignore){var o=1,r=this.parent;while(r)r.scale&&(o/=r.scale[0]),r=r.parent;var s=e.childOfName("line");if(this.__dirty||s.__dirty){var l=s.shape.percent,u=s.pointAt(0),c=s.pointAt(l),d=a.sub([],c,u);if(a.normalize(d,d),t){t.attr("position",u);var h=s.tangentAt(0);t.attr("rotation",Math.PI/2-Math.atan2(h[1],h[0])),t.attr("scale",[o*l,o*l])}if(i){i.attr("position",c);h=s.tangentAt(1);i.attr("rotation",-Math.PI/2-Math.atan2(h[1],h[0])),i.attr("scale",[o*l,o*l])}if(!n.ignore){var p,g,f;n.attr("position",c);var m=5*o;if("end"===n.__position)p=[d[0]*m+c[0],d[1]*m+c[1]],g=d[0]>.8?"left":d[0]<-.8?"right":"center",f=d[1]>.8?"top":d[1]<-.8?"bottom":"middle";else if("middle"===n.__position){var v=l/2,y=(h=s.tangentAt(v),[h[1],-h[0]]),x=s.pointAt(v);y[1]>0&&(y[0]=-y[0],y[1]=-y[1]),p=[x[0]+y[0]*m,x[1]+y[1]*m],g="center",f="bottom";var _=-Math.atan2(h[1],h[0]);c[0]<u[0]&&(_=Math.PI+_),n.attr("rotation",_)}else p=[-d[0]*m+u[0],-d[1]*m+u[1]],g=d[0]>.8?"right":d[0]<-.8?"left":"center",f=d[1]>.8?"bottom":d[1]<-.8?"top":"middle";n.attr({style:{textVerticalAlign:n.__verticalAlign||f,textAlign:n.__textAlign||g},position:p,scale:[o,o]})}}}}function m(e,t,i){s.Group.call(this),this._createLine(e,t,i)}var v=m.prototype;v.beforeUpdate=f,v._createLine=function(e,t,i){var a=e.hostModel,o=e.getItemLayout(t),r=p(o);r.shape.percent=0,s.initProps(r,{shape:{percent:1}},a,t),this.add(r);var l=new s.Text({name:"label",lineLabelOriginalOpacity:1});this.add(l),n.each(c,(function(i){var n=h(i,e,t);this.add(n),this[d(i)]=e.getItemVisual(t,i)}),this),this._updateCommonStl(e,t,i)},v.updateData=function(e,t,i){var a=e.hostModel,o=this.childOfName("line"),r=e.getItemLayout(t),l={shape:{}};g(l.shape,r),s.updateProps(o,l,a,t),n.each(c,(function(i){var n=e.getItemVisual(t,i),a=d(i);if(this[a]!==n){this.remove(this.childOfName(i));var o=h(i,e,t);this.add(o)}this[a]=n}),this),this._updateCommonStl(e,t,i)},v._updateCommonStl=function(e,t,i){var a=e.hostModel,o=this.childOfName("line"),r=i&&i.lineStyle,l=i&&i.hoverLineStyle,d=i&&i.labelModel,h=i&&i.hoverLabelModel;if(!i||e.hasItemOption){var p=e.getItemModel(t);r=p.getModel("lineStyle").getLineStyle(),l=p.getModel("emphasis.lineStyle").getLineStyle(),d=p.getModel("label"),h=p.getModel("emphasis.label")}var g=e.getItemVisual(t,"color"),f=n.retrieve3(e.getItemVisual(t,"opacity"),r.opacity,1);o.useStyle(n.defaults({strokeNoScale:!0,fill:"none",stroke:g,opacity:f},r)),o.hoverStyle=l,n.each(c,(function(e){var t=this.childOfName(e);t&&(t.setColor(g),t.setStyle({opacity:f}))}),this);var m,v,y=d.getShallow("show"),x=h.getShallow("show"),_=this.childOfName("label");if((y||x)&&(m=g||"#000",v=a.getFormattedLabel(t,"normal",e.dataType),null==v)){var b=a.getRawValue(t);v=null==b?e.getName(t):isFinite(b)?u(b):b}var w=y?v:null,S=x?n.retrieve2(a.getFormattedLabel(t,"emphasis",e.dataType),v):null,M=_.style;null==w&&null==S||(s.setTextStyle(_.style,d,{text:w},{autoColor:m}),_.__textAlign=M.textAlign,_.__verticalAlign=M.textVerticalAlign,_.__position=d.get("position")||"middle"),_.hoverStyle=null!=S?{text:S,textFill:h.getTextColor(!0),fontStyle:h.getShallow("fontStyle"),fontWeight:h.getShallow("fontWeight"),fontSize:h.getShallow("fontSize"),fontFamily:h.getShallow("fontFamily")}:{text:null},_.ignore=!y&&!x,s.setHoverStyle(this)},v.highlight=function(){this.trigger("emphasis")},v.downplay=function(){this.trigger("normal")},v.updateLayout=function(e,t){this.setLinePoints(e.getItemLayout(t))},v.setLinePoints=function(e){var t=this.childOfName("line");g(t.shape,e),t.dirty()},n.inherits(m,s.Group);var y=m;e.exports=y},"7f59":function(e,t,i){var n=i("4e08"),a=(n.__DEV__,i("3eba")),o=i("6d8b"),r=i("e0d3"),s=i("2306"),l=i("f934");a.registerPreprocessor((function(e){var t=e.graphic;o.isArray(t)?t[0]&&t[0].elements?e.graphic=[e.graphic[0]]:e.graphic=[{elements:t}]:t&&!t.elements&&(e.graphic=[{elements:[t]}])}));var u=a.extendComponentModel({type:"graphic",defaultOption:{elements:[],parentId:null},_elOptionsToUpdate:null,mergeOption:function(e){var t=this.option.elements;this.option.elements=null,u.superApply(this,"mergeOption",arguments),this.option.elements=t},optionUpdated:function(e,t){var i=this.option,n=(t?i:e).elements,a=i.elements=t?[]:i.elements,s=[];this._flatten(n,s);var l=r.mappingToExists(a,s);r.makeIdAndName(l);var u=this._elOptionsToUpdate=[];o.each(l,(function(e,t){var i=e.option;i&&(u.push(i),g(e,i),f(a,t,i),m(a[t],i))}),this);for(var c=a.length-1;c>=0;c--)null==a[c]?a.splice(c,1):delete a[c].$action},_flatten:function(e,t,i){o.each(e,(function(e){if(e){i&&(e.parentOption=i),t.push(e);var n=e.children;"group"===e.type&&n&&this._flatten(n,t,e),delete e.children}}),this)},useElOptionsToUpdate:function(){var e=this._elOptionsToUpdate;return this._elOptionsToUpdate=null,e}});function c(e,t,i,n){var a=i.type,o=s[a.charAt(0).toUpperCase()+a.slice(1)],r=new o(i);t.add(r),n.set(e,r),r.__ecGraphicId=e}function d(e,t){var i=e&&e.parent;i&&("group"===e.type&&e.traverse((function(e){d(e,t)})),t.removeKey(e.__ecGraphicId),i.remove(e))}function h(e){return e=o.extend({},e),o.each(["id","parentId","$action","hv","bounding"].concat(l.LOCATION_PARAMS),(function(t){delete e[t]})),e}function p(e,t){var i;return o.each(t,(function(t){null!=e[t]&&"auto"!==e[t]&&(i=!0)})),i}function g(e,t){var i=e.exist;if(t.id=e.keyInfo.id,!t.type&&i&&(t.type=i.type),null==t.parentId){var n=t.parentOption;n?t.parentId=n.id:i&&(t.parentId=i.parentId)}t.parentOption=null}function f(e,t,i){var n=o.extend({},i),a=e[t],r=i.$action||"merge";"merge"===r?a?(o.merge(a,n,!0),l.mergeLayoutParam(a,n,{ignoreSize:!0}),l.copyLayoutParams(i,a)):e[t]=n:"replace"===r?e[t]=n:"remove"===r&&a&&(e[t]=null)}function m(e,t){e&&(e.hv=t.hv=[p(t,["left","right"]),p(t,["top","bottom"])],"group"===e.type&&(null==e.width&&(e.width=t.width=0),null==e.height&&(e.height=t.height=0)))}function v(e,t,i){var n=e.eventData;e.silent||e.ignore||n||(n=e.eventData={componentType:"graphic",componentIndex:t.componentIndex,name:e.name}),n&&(n.info=e.info)}a.extendComponentView({type:"graphic",init:function(e,t){this._elMap=o.createHashMap(),this._lastGraphicModel},render:function(e,t,i){e!==this._lastGraphicModel&&this._clear(),this._lastGraphicModel=e,this._updateElements(e),this._relocate(e,i)},_updateElements:function(e){var t=e.useElOptionsToUpdate();if(t){var i=this._elMap,n=this.group;o.each(t,(function(t){var a=t.$action,o=t.id,r=i.get(o),s=t.parentId,l=null!=s?i.get(s):n,u=t.style;"text"===t.type&&u&&(t.hv&&t.hv[1]&&(u.textVerticalAlign=u.textBaseline=null),!u.hasOwnProperty("textFill")&&u.fill&&(u.textFill=u.fill),!u.hasOwnProperty("textStroke")&&u.stroke&&(u.textStroke=u.stroke));var p=h(t);a&&"merge"!==a?"replace"===a?(d(r,i),c(o,l,p,i)):"remove"===a&&d(r,i):r?r.attr(p):c(o,l,p,i);var g=i.get(o);g&&(g.__ecGraphicWidth=t.width,g.__ecGraphicHeight=t.height,v(g,e,t))}))}},_relocate:function(e,t){for(var i=e.option.elements,n=this.group,a=this._elMap,o=i.length-1;o>=0;o--){var r=i[o],s=a.get(r.id);if(s){var u=s.parent,c=u===n?{width:t.getWidth(),height:t.getHeight()}:{width:u.__ecGraphicWidth||0,height:u.__ecGraphicHeight||0};l.positionElement(s,r,c,null,{hv:r.hv,boundingMode:r.bounding})}}},_clear:function(){var e=this._elMap;e.each((function(t){d(t,e)})),this._elMap=o.createHashMap()},dispose:function(){this._clear()}})},"7f91":function(e,t,i){var n=i("2306"),a=i("401b"),o=n.Line.prototype,r=n.BezierCurve.prototype;function s(e){return isNaN(+e.cpx1)||isNaN(+e.cpy1)}var l=n.extendShape({type:"ec-line",style:{stroke:"#000",fill:null},shape:{x1:0,y1:0,x2:0,y2:0,percent:1,cpx1:null,cpy1:null},buildPath:function(e,t){(s(t)?o:r).buildPath(e,t)},pointAt:function(e){return s(this.shape)?o.pointAt.call(this,e):r.pointAt.call(this,e)},tangentAt:function(e){var t=this.shape,i=s(t)?[t.x2-t.x1,t.y2-t.y1]:r.tangentAt.call(this,e);return a.normalize(i,i)}});e.exports=l},"81ac":function(e,t,i){var n=i("f934"),a=i("6d8b"),o=i("e0d3"),r=o.groupData,s=i("4e08");s.__DEV__;function l(e,t,i){e.eachSeriesByType("sankey",(function(e){var i=e.get("nodeWidth"),n=e.get("nodeGap"),o=u(e,t);e.layoutInfo=o;var r=o.width,s=o.height,l=e.getGraph(),h=l.nodes,p=l.edges;d(h);var g=a.filter(h,(function(e){return 0===e.getLayout().value})),f=0!==g.length?0:e.get("layoutIterations"),m=e.get("orient");c(h,p,i,n,r,s,f,m)}))}function u(e,t){return n.getLayoutRect(e.getBoxLayoutParams(),{width:t.getWidth(),height:t.getHeight()})}function c(e,t,i,n,a,o,r,s){h(e,t,i,a,o,s),f(e,t,o,a,n,r,s),A(e,s)}function d(e){a.each(e,(function(e){var t=M(e.outEdges,S),i=M(e.inEdges,S),n=Math.max(t,i);e.setLayout({value:n},!0)}))}function h(e,t,i,n,a,o){for(var r=[],s=[],l=[],u=[],c=0,d=0,h=0;h<t.length;h++)r[h]=1;for(h=0;h<e.length;h++)s[h]=e[h].inEdges.length,0===s[h]&&l.push(e[h]);while(l.length){for(var f=0;f<l.length;f++){var m=l[f];"vertical"===o?(m.setLayout({y:c},!0),m.setLayout({dy:i},!0)):(m.setLayout({x:c},!0),m.setLayout({dx:i},!0));for(var v=0;v<m.outEdges.length;v++){var y=m.outEdges[v],x=t.indexOf(y);r[x]=0;var _=y.node2,b=e.indexOf(_);0===--s[b]&&u.push(_)}}++c,l=u,u=[]}for(h=0;h<r.length;h++);p(e,c,o),d="vertical"===o?(a-i)/(c-1):(n-i)/(c-1),g(e,d,o)}function p(e,t,i){a.each(e,(function(e){e.outEdges.length||("vertical"===i?e.setLayout({y:t-1},!0):e.setLayout({x:t-1},!0))}))}function g(e,t,i){a.each(e,(function(e){if("vertical"===i){var n=e.getLayout().y*t;e.setLayout({y:n},!0)}else{var a=e.getLayout().x*t;e.setLayout({x:a},!0)}}))}function f(e,t,i,n,a,o,r){var s=m(e,r);v(e,s,t,i,n,a,r),y(s,a,i,n,r);for(var l=1;o>0;o--)l*=.99,x(s,l,r),y(s,a,i,n,r),I(s,l,r),y(s,a,i,n,r)}function m(e,t){var i=[],n="vertical"===t?"y":"x",o=r(e,(function(e){return e.getLayout()[n]}));return o.keys.sort((function(e,t){return e-t})),a.each(o.keys,(function(e){i.push(o.buckets.get(e))})),i}function v(e,t,i,n,o,r,s){var l=[];a.each(t,(function(e){var t=e.length,i=0,u=0;a.each(e,(function(e){i+=e.getLayout().value})),u="vertical"===s?(o-(t-1)*r)/i:(n-(t-1)*r)/i,l.push(u)})),l.sort((function(e,t){return e-t}));var u=l[0];a.each(t,(function(e){a.each(e,(function(e,t){var i=e.getLayout().value*u;"vertical"===s?(e.setLayout({x:t},!0),e.setLayout({dx:i},!0)):(e.setLayout({y:t},!0),e.setLayout({dy:i},!0))}))})),a.each(i,(function(e){var t=+e.getValue()*u;e.setLayout({dy:t},!0)}))}function y(e,t,i,n,o){a.each(e,(function(e){var a,r,s,l=0,u=e.length;if("vertical"===o){var c;for(e.sort((function(e,t){return e.getLayout().x-t.getLayout().x})),s=0;s<u;s++)a=e[s],r=l-a.getLayout().x,r>0&&(c=a.getLayout().x+r,a.setLayout({x:c},!0)),l=a.getLayout().x+a.getLayout().dx+t;if(r=l-t-n,r>0)for(c=a.getLayout().x-r,a.setLayout({x:c},!0),l=c,s=u-2;s>=0;--s)a=e[s],r=a.getLayout().x+a.getLayout().dx+t-l,r>0&&(c=a.getLayout().x-r,a.setLayout({x:c},!0)),l=a.getLayout().x}else{var d;for(e.sort((function(e,t){return e.getLayout().y-t.getLayout().y})),s=0;s<u;s++)a=e[s],r=l-a.getLayout().y,r>0&&(d=a.getLayout().y+r,a.setLayout({y:d},!0)),l=a.getLayout().y+a.getLayout().dy+t;if(r=l-t-i,r>0)for(d=a.getLayout().y-r,a.setLayout({y:d},!0),l=d,s=u-2;s>=0;--s)a=e[s],r=a.getLayout().y+a.getLayout().dy+t-l,r>0&&(d=a.getLayout().y-r,a.setLayout({y:d},!0)),l=a.getLayout().y}}))}function x(e,t,i){a.each(e.slice().reverse(),(function(e){a.each(e,(function(e){if(e.outEdges.length){var n=M(e.outEdges,_,i)/M(e.outEdges,S,i);if("vertical"===i){var a=e.getLayout().x+(n-w(e,i))*t;e.setLayout({x:a},!0)}else{var o=e.getLayout().y+(n-w(e,i))*t;e.setLayout({y:o},!0)}}}))}))}function _(e,t){return w(e.node2,t)*e.getValue()}function b(e,t){return w(e.node1,t)*e.getValue()}function w(e,t){return"vertical"===t?e.getLayout().x+e.getLayout().dx/2:e.getLayout().y+e.getLayout().dy/2}function S(e){return e.getValue()}function M(e,t,i){var n=0,a=e.length,o=-1;while(++o<a){var r=+t.call(e,e[o],i);isNaN(r)||(n+=r)}return n}function I(e,t,i){a.each(e,(function(e){a.each(e,(function(e){if(e.inEdges.length){var n=M(e.inEdges,b,i)/M(e.inEdges,S,i);if("vertical"===i){var a=e.getLayout().x+(n-w(e,i))*t;e.setLayout({x:a},!0)}else{var o=e.getLayout().y+(n-w(e,i))*t;e.setLayout({y:o},!0)}}}))}))}function A(e,t){a.each(e,(function(e){"vertical"===t?(e.outEdges.sort((function(e,t){return e.node2.getLayout().x-t.node2.getLayout().x})),e.inEdges.sort((function(e,t){return e.node1.getLayout().x-t.node1.getLayout().x}))):(e.outEdges.sort((function(e,t){return e.node2.getLayout().y-t.node2.getLayout().y})),e.inEdges.sort((function(e,t){return e.node1.getLayout().y-t.node1.getLayout().y})))})),a.each(e,(function(e){var t=0,i=0;a.each(e.outEdges,(function(e){e.setLayout({sy:t},!0),t+=e.getLayout().dy})),a.each(e.inEdges,(function(e){e.setLayout({ty:i},!0),i+=e.getLayout().dy}))}))}e.exports=l},"82f9":function(e,t,i){var n=i("6d8b"),a=i("76a5");function o(e){this._zr=e.getZr(),this._show=!1,this._hideTimeout}o.prototype={constructor:o,_enterable:!0,update:function(){},show:function(e){this._hideTimeout&&clearTimeout(this._hideTimeout),this.el.attr("show",!0),this._show=!0},setContent:function(e,t,i){this.el&&this._zr.remove(this.el);var n={},o=e,r="{marker",s="|}",l=o.indexOf(r);while(l>=0){var u=o.indexOf(s),c=o.substr(l+r.length,u-l-r.length);c.indexOf("sub")>-1?n["marker"+c]={textWidth:4,textHeight:4,textBorderRadius:2,textBackgroundColor:t[c],textOffset:[3,0]}:n["marker"+c]={textWidth:10,textHeight:10,textBorderRadius:5,textBackgroundColor:t[c]},o=o.substr(u+1),l=o.indexOf("{marker")}this.el=new a({style:{rich:n,text:e,textLineHeight:20,textBackgroundColor:i.get("backgroundColor"),textBorderRadius:i.get("borderRadius"),textFill:i.get("textStyle.color"),textPadding:i.get("padding")},z:i.get("z")}),this._zr.add(this.el);var d=this;this.el.on("mouseover",(function(){d._enterable&&(clearTimeout(d._hideTimeout),d._show=!0),d._inContent=!0})),this.el.on("mouseout",(function(){d._enterable&&d._show&&d.hideLater(d._hideDelay),d._inContent=!1}))},setEnterable:function(e){this._enterable=e},getSize:function(){var e=this.el.getBoundingRect();return[e.width,e.height]},moveTo:function(e,t){this.el&&this.el.attr("position",[e,t])},hide:function(){this.el.hide(),this._show=!1},hideLater:function(e){!this._show||this._inContent&&this._enterable||(e?(this._hideDelay=e,this._show=!1,this._hideTimeout=setTimeout(n.bind(this.hide,this),e)):this.hide())},isShow:function(){return this._show},getOuterSize:function(){return this.getSize()}};var r=o;e.exports=r},8344:function(e,t,i){var n=i("6d8b"),a=i("f706"),o=i("3842"),r=i("6179"),s=i("923d"),l=i("88f0");function u(e,t,i){var n=t.coordinateSystem;e.each((function(a){var r,s=e.getItemModel(a),l=o.parsePercent(s.get("x"),i.getWidth()),u=o.parsePercent(s.get("y"),i.getHeight());if(isNaN(l)||isNaN(u)){if(t.getMarkerPosition)r=t.getMarkerPosition(e.getValues(e.dimensions,a));else if(n){var c=e.get(n.dimensions[0],a),d=e.get(n.dimensions[1],a);r=n.dataToPoint([c,d])}}else r=[l,u];isNaN(l)||(r[0]=l),isNaN(u)||(r[1]=u),e.setItemLayout(a,r)}))}var c=l.extend({type:"markPoint",updateTransform:function(e,t,i){t.eachSeries((function(e){var t=e.markPointModel;t&&(u(t.getData(),e,i),this.markerGroupMap.get(e.id).updateLayout(t))}),this)},renderSeries:function(e,t,i,n){var o=e.coordinateSystem,r=e.id,s=e.getData(),l=this.markerGroupMap,c=l.get(r)||l.set(r,new a),h=d(o,e,t);t.setData(h),u(t.getData(),e,n),h.each((function(e){var i=h.getItemModel(e),n=i.getShallow("symbolSize");"function"===typeof n&&(n=n(t.getRawValue(e),t.getDataParams(e))),h.setItemVisual(e,{symbolSize:n,color:i.get("itemStyle.color")||s.getVisual("color"),symbol:i.getShallow("symbol")})})),c.updateData(h),this.group.add(c.group),h.eachItemGraphicEl((function(e){e.traverse((function(e){e.dataModel=t}))})),c.__keep=!0,c.group.silent=t.get("silent")||e.get("silent")}});function d(e,t,i){var a;a=e?n.map(e&&e.dimensions,(function(e){var i=t.getData().getDimensionInfo(t.getData().mapDimension(e))||{};return n.defaults({name:e},i)})):[{name:"value",type:"float"}];var o=new r(a,i),l=n.map(i.get("data"),n.curry(s.dataTransform,t));return e&&(l=n.filter(l,n.curry(s.dataFilter,e))),o.initData(l,null,e?s.dimValueGetter:function(e){return e.value}),o}e.exports=c},"83ba":function(e,t,i){var n=i("6d8b"),a=i("6cb7"),o=i("f934"),r=o.getLayoutParams,s=o.sizeCalculable,l=o.mergeLayoutParam,u=a.extend({type:"calendar",coordinateSystem:null,defaultOption:{zlevel:0,z:2,left:80,top:60,cellSize:20,orient:"horizontal",splitLine:{show:!0,lineStyle:{color:"#000",width:1,type:"solid"}},itemStyle:{color:"#fff",borderWidth:1,borderColor:"#ccc"},dayLabel:{show:!0,firstDay:0,position:"start",margin:"50%",nameMap:"en",color:"#000"},monthLabel:{show:!0,position:"start",margin:5,align:"center",nameMap:"en",formatter:null,color:"#000"},yearLabel:{show:!0,position:null,margin:30,formatter:null,color:"#ccc",fontFamily:"sans-serif",fontWeight:"bolder",fontSize:20}},init:function(e,t,i,n){var a=r(e);u.superApply(this,"init",arguments),c(e,a)},mergeOption:function(e,t){u.superApply(this,"mergeOption",arguments),c(this.option,e)}});function c(e,t){var i=e.cellSize;n.isArray(i)?1===i.length&&(i[1]=i[0]):i=e.cellSize=[i,i];var a=n.map([0,1],(function(e){return s(t,e)&&(i[e]="auto"),null!=i[e]&&"auto"!==i[e]}));l(e,t,{type:"box",ignoreSize:a})}var d=u;e.exports=d},8459:function(e,t,i){var n=i("3eba"),a={type:"axisAreaSelect",event:"axisAreaSelected"};n.registerAction(a,(function(e,t){t.eachComponent({mainType:"parallelAxis",query:e},(function(t){t.axis.model.setActiveIntervals(e.intervals)}))})),n.registerAction("parallelAxisExpand",(function(e,t){t.eachComponent({mainType:"parallel",query:e},(function(t){t.setAxisExpand(e)}))}))},"849b":function(e,t,i){var n=i("d9d0"),a=i("2039");function o(e,t){var i=[];return e.eachComponent("parallel",(function(a,o){var r=new n(a,e,t);r.name="parallel_"+o,r.resize(a,t),a.coordinateSystem=r,r.model=a,i.push(r)})),e.eachSeries((function(t){if("parallel"===t.get("coordinateSystem")){var i=e.queryComponents({mainType:"parallel",index:t.get("parallelIndex"),id:t.get("parallelId")})[0];t.coordinateSystem=i.coordinateSystem}})),i}a.register("parallel",{create:o})},"84d5":function(e,t,i){var n=i("3eba"),a=i("6d8b"),o=i("4319"),r=i("e0d3"),s=r.isNameSpecified,l=n.extendComponentModel({type:"legend.plain",dependencies:["series"],layoutMode:{type:"box",ignoreSize:!0},init:function(e,t,i){this.mergeDefaultAndTheme(e,i),e.selected=e.selected||{}},mergeOption:function(e){l.superCall(this,"mergeOption",e)},optionUpdated:function(){this._updateData(this.ecModel);var e=this._data;if(e[0]&&"single"===this.get("selectedMode")){for(var t=!1,i=0;i<e.length;i++){var n=e[i].get("name");if(this.isSelected(n)){this.select(n),t=!0;break}}!t&&this.select(e[0].get("name"))}},_updateData:function(e){var t=[],i=[];e.eachRawSeries((function(n){var a,o=n.name;if(i.push(o),n.legendDataProvider){var r=n.legendDataProvider(),l=r.mapArray(r.getName);e.isSeriesFiltered(n)||(i=i.concat(l)),l.length?t=t.concat(l):a=!0}else a=!0;a&&s(n)&&t.push(n.name)})),this._availableNames=i;var n=this.get("data")||t,r=a.map(n,(function(e){return"string"!==typeof e&&"number"!==typeof e||(e={name:e}),new o(e,this,this.ecModel)}),this);this._data=r},getData:function(){return this._data},select:function(e){var t=this.option.selected,i=this.get("selectedMode");if("single"===i){var n=this._data;a.each(n,(function(e){t[e.get("name")]=!1}))}t[e]=!0},unSelect:function(e){"single"!==this.get("selectedMode")&&(this.option.selected[e]=!1)},toggleSelected:function(e){var t=this.option.selected;t.hasOwnProperty(e)||(t[e]=!0),this[t[e]?"unSelect":"select"](e)},isSelected:function(e){var t=this.option.selected;return!(t.hasOwnProperty(e)&&!t[e])&&a.indexOf(this._availableNames,e)>=0},defaultOption:{zlevel:0,z:4,show:!0,orient:"horizontal",left:"center",top:0,align:"auto",backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderRadius:0,borderWidth:0,padding:5,itemGap:10,itemWidth:25,itemHeight:14,inactiveColor:"#ccc",textStyle:{color:"#333"},selectedMode:!0,tooltip:{show:!1}}}),u=l;e.exports=u},"870e":function(e,t){function i(e){e.eachSeriesByType("radar",(function(e){var t=e.getData(),i=[],n=e.coordinateSystem;if(n){for(var a=n.getIndicatorAxes(),o=0;o<a.length;o++)t.each(t.mapDimension(a[o].dim),r);t.each((function(e){i[e][0]&&i[e].push(i[e][0].slice()),t.setItemLayout(e,i[e])}))}function r(e,t){i[t]=i[t]||[],i[t][o]=n.dataToPoint(e,o)}}))}e.exports=i},8727:function(e,t){var i="http://www.w3.org/2000/svg";function n(e){return document.createElementNS(i,e)}t.createElement=n},"879e":function(e,t,i){var n=i("3eba"),a=i("6179"),o=i("6d8b"),r=i("e0d3"),s=r.defaultEmphasis,l=i("4319"),u=i("eda2"),c=u.encodeHTML,d=i("237f"),h=n.extendSeriesModel({type:"series.graph",init:function(e){h.superApply(this,"init",arguments),this.legendDataProvider=function(){return this._categoriesData},this.fillDataTextStyle(e.edges||e.links),this._updateCategoriesData()},mergeOption:function(e){h.superApply(this,"mergeOption",arguments),this.fillDataTextStyle(e.edges||e.links),this._updateCategoriesData()},mergeDefaultAndTheme:function(e){h.superApply(this,"mergeDefaultAndTheme",arguments),s(e,["edgeLabel"],["show"])},getInitialData:function(e,t){var i=e.edges||e.links||[],n=e.data||e.nodes||[],a=this;if(n&&i)return d(n,i,this,!0,o).data;function o(e,i){e.wrapMethod("getItemModel",(function(e){var t=a._categoriesModels,i=e.getShallow("category"),n=t[i];return n&&(n.parentModel=e.parentModel,e.parentModel=n),e}));var n=a.getModel("edgeLabel"),o=new l({label:n.option},n.parentModel,t),r=a.getModel("emphasis.edgeLabel"),s=new l({emphasis:{label:r.option}},r.parentModel,t);function u(e){return e=this.parsePath(e),e&&"label"===e[0]?o:e&&"emphasis"===e[0]&&"label"===e[1]?s:this.parentModel}i.wrapMethod("getItemModel",(function(e){return e.customizeGetParent(u),e}))}},getGraph:function(){return this.getData().graph},getEdgeData:function(){return this.getGraph().edgeData},getCategoriesData:function(){return this._categoriesData},formatTooltip:function(e,t,i){if("edge"===i){var n=this.getData(),a=this.getDataParams(e,i),o=n.graph.getEdgeByIndex(e),r=n.getName(o.node1.dataIndex),s=n.getName(o.node2.dataIndex),l=[];return null!=r&&l.push(r),null!=s&&l.push(s),l=c(l.join(" > ")),a.value&&(l+=" : "+c(a.value)),l}return h.superApply(this,"formatTooltip",arguments)},_updateCategoriesData:function(){var e=o.map(this.option.categories||[],(function(e){return null!=e.value?e:o.extend({value:0},e)})),t=new a(["value"],this);t.initData(e),this._categoriesData=t,this._categoriesModels=t.mapArray((function(e){return t.getItemModel(e,!0)}))},setZoom:function(e){this.option.zoom=e},setCenter:function(e){this.option.center=e},isAnimationEnabled:function(){return h.superCall(this,"isAnimationEnabled")&&!("force"===this.get("layout")&&this.get("force.layoutAnimation"))},defaultOption:{zlevel:0,z:2,coordinateSystem:"view",legendHoverLink:!0,hoverAnimation:!0,layout:null,focusNodeAdjacency:!1,circular:{rotateLabel:!1},force:{initLayout:null,repulsion:[0,50],gravity:.1,edgeLength:30,layoutAnimation:!0},left:"center",top:"center",symbol:"circle",symbolSize:10,edgeSymbol:["none","none"],edgeSymbolSize:10,edgeLabel:{position:"middle"},draggable:!1,roam:!1,center:null,zoom:1,nodeScaleRatio:.6,label:{show:!1,formatter:"{b}"},itemStyle:{},lineStyle:{color:"#aaa",width:1,curveness:0,opacity:.5},emphasis:{label:{show:!0}}}}),p=h;e.exports=p},"88f0":function(e,t,i){var n=i("3eba"),a=i("6d8b"),o=n.extendComponentView({type:"marker",init:function(){this.markerGroupMap=a.createHashMap()},render:function(e,t,i){var n=this.markerGroupMap;n.each((function(e){e.__keep=!1}));var a=this.type+"Model";t.eachSeries((function(e){var n=e[a];n&&this.renderSeries(e,n,t,i)}),this),n.each((function(e){!e.__keep&&this.group.remove(e.group)}),this)},renderSeries:function(){}});e.exports=o},"8deb":function(e,t,i){var n=i("3eba");i("5522"),i("a016"),i("1466");var a=i("98e7"),o=i("7f96"),r=i("870e"),s=i("d3f47"),l=i("7891");n.registerVisual(a("radar")),n.registerVisual(o("radar","circle")),n.registerLayout(r),n.registerProcessor(s("radar")),n.registerPreprocessor(l)},"8e77":function(e,t,i){var n=i("6d8b"),a=i("41ef"),o=i("6179"),r=i("3842"),s=i("2306"),l=i("923d"),u=i("88f0"),c=function(e,t,i,a){var o=l.dataTransform(e,a[0]),r=l.dataTransform(e,a[1]),s=n.retrieve,u=o.coord,c=r.coord;u[0]=s(u[0],-1/0),u[1]=s(u[1],-1/0),c[0]=s(c[0],1/0),c[1]=s(c[1],1/0);var d=n.mergeAll([{},o,r]);return d.coord=[o.coord,r.coord],d.x0=o.x,d.y0=o.y,d.x1=r.x,d.y1=r.y,d};function d(e){return!isNaN(e)&&!isFinite(e)}function h(e,t,i,n){var a=1-e;return d(t[a])&&d(i[a])}function p(e,t){var i=t.coord[0],n=t.coord[1];return!("cartesian2d"!==e.type||!i||!n||!h(1,i,n,e)&&!h(0,i,n,e))||(l.dataFilter(e,{coord:i,x:t.x0,y:t.y0})||l.dataFilter(e,{coord:n,x:t.x1,y:t.y1}))}function g(e,t,i,n,a){var o,s=n.coordinateSystem,l=e.getItemModel(t),u=r.parsePercent(l.get(i[0]),a.getWidth()),c=r.parsePercent(l.get(i[1]),a.getHeight());if(isNaN(u)||isNaN(c)){if(n.getMarkerPosition)o=n.getMarkerPosition(e.getValues(i,t));else{var h=e.get(i[0],t),p=e.get(i[1],t),g=[h,p];s.clampData&&s.clampData(g,g),o=s.dataToPoint(g,!0)}if("cartesian2d"===s.type){var f=s.getAxis("x"),m=s.getAxis("y");h=e.get(i[0],t),p=e.get(i[1],t);d(h)?o[0]=f.toGlobalCoord(f.getExtent()["x0"===i[0]?0:1]):d(p)&&(o[1]=m.toGlobalCoord(m.getExtent()["y0"===i[1]?0:1]))}isNaN(u)||(o[0]=u),isNaN(c)||(o[1]=c)}else o=[u,c];return o}var f=[["x0","y0"],["x1","y0"],["x1","y1"],["x0","y1"]];function m(e,t,i){var a,r,s=["x0","y0","x1","y1"];e?(a=n.map(e&&e.dimensions,(function(e){var i=t.getData(),a=i.getDimensionInfo(i.mapDimension(e))||{};return n.defaults({name:e},a)})),r=new o(n.map(s,(function(e,t){return{name:e,type:a[t%2].type}})),i)):(a=[{name:"value",type:"float"}],r=new o(a,i));var l=n.map(i.get("data"),n.curry(c,t,e,i));e&&(l=n.filter(l,n.curry(p,e)));var u=e?function(e,t,i,n){return e.coord[Math.floor(n/2)][n%2]}:function(e){return e.value};return r.initData(l,null,u),r.hasItemOption=!0,r}u.extend({type:"markArea",updateTransform:function(e,t,i){t.eachSeries((function(e){var t=e.markAreaModel;if(t){var a=t.getData();a.each((function(t){var o=n.map(f,(function(n){return g(a,t,n,e,i)}));a.setItemLayout(t,o);var r=a.getItemGraphicEl(t);r.setShape("points",o)}))}}),this)},renderSeries:function(e,t,i,o){var r=e.coordinateSystem,l=e.id,u=e.getData(),c=this.markerGroupMap,d=c.get(l)||c.set(l,{group:new s.Group});this.group.add(d.group),d.__keep=!0;var h=m(r,e,t);t.setData(h),h.each((function(t){h.setItemLayout(t,n.map(f,(function(i){return g(h,t,i,e,o)}))),h.setItemVisual(t,{color:u.getVisual("color")})})),h.diff(d.__data).add((function(e){var t=new s.Polygon({shape:{points:h.getItemLayout(e)}});h.setItemGraphicEl(e,t),d.group.add(t)})).update((function(e,i){var n=d.__data.getItemGraphicEl(i);s.updateProps(n,{shape:{points:h.getItemLayout(e)}},t,e),d.group.add(n),h.setItemGraphicEl(e,n)})).remove((function(e){var t=d.__data.getItemGraphicEl(e);d.group.remove(t)})).execute(),h.eachItemGraphicEl((function(e,i){var o=h.getItemModel(i),r=o.getModel("label"),l=o.getModel("emphasis.label"),u=h.getItemVisual(i,"color");e.useStyle(n.defaults(o.getModel("itemStyle").getItemStyle(),{fill:a.modifyAlpha(u,.4),stroke:u})),e.hoverStyle=o.getModel("emphasis.itemStyle").getItemStyle(),s.setLabelStyle(e.style,e.hoverStyle,r,l,{labelFetcher:t,labelDataIndex:i,defaultText:h.getName(i)||"",isRectText:!0,autoColor:u}),s.setHoverStyle(e,{}),e.dataModel=t})),d.__data=h,d.group.silent=t.get("silent")||e.get("silent")}})},"8ec5":function(e,t,i){var n=i("3eba"),a=i("6d8b"),o=i("2145"),r=n.extendComponentModel({type:"toolbox",layoutMode:{type:"box",ignoreSize:!0},optionUpdated:function(){r.superApply(this,"optionUpdated",arguments),a.each(this.option.feature,(function(e,t){var i=o.get(t);i&&a.merge(e,i.defaultOption)}))},defaultOption:{show:!0,z:6,zlevel:0,orient:"horizontal",left:"right",top:"top",backgroundColor:"transparent",borderColor:"#ccc",borderRadius:0,borderWidth:0,padding:5,itemSize:15,itemGap:8,showTitle:!0,iconStyle:{borderColor:"#666",color:"none"},emphasis:{iconStyle:{borderColor:"#3E98C5"}}}}),s=r;e.exports=s},"8ee0":function(e,t,i){i("3f8e");var n=i("697e7"),a=n.registerPainter,o=i("dc20");a("svg",o)},"903c":function(e,t){function i(e){var t=e.findComponents({mainType:"legend"});t&&t.length&&e.filterSeries((function(e){for(var i=0;i<t.length;i++)if(!t[i].isSelected(e.name))return!1;return!0}))}e.exports=i},"90c2":function(e,t,i){var n=i("5f14"),a=i("41ef"),o=i("6d8b"),r=o.isArray,s="itemStyle",l={seriesType:"treemap",reset:function(e,t,i,n){var a=e.getData().tree,r=a.root,l=e.getModel(s);if(!r.isRemoved()){var c=o.map(a.levelModels,(function(e){return e?e.get(s):null}));u(r,{},c,l,e.getViewRoot().getAncestors(),e)}}};function u(e,t,i,n,a,r){var l=e.getModel(),p=e.getLayout();if(p&&!p.invisible&&p.isInView){var f,v=e.getModel(s),y=i[e.depth],x=c(v,t,y,n),_=v.get("borderColor"),b=v.get("borderColorSaturation");null!=b&&(f=d(x,e),_=h(b,f)),e.setVisual("borderColor",_);var w=e.viewChildren;if(w&&w.length){var S=g(e,l,p,v,x,w);o.each(w,(function(e,t){if(e.depth>=a.length||e===a[e.depth]){var o=m(l,x,e,t,S,r);u(e,o,i,n,a,r)}}))}else f=d(x,e),e.setVisual("color",f)}}function c(e,t,i,n){var a=o.extend({},t);return o.each(["color","colorAlpha","colorSaturation"],(function(o){var r=e.get(o,!0);null==r&&i&&(r=i[o]),null==r&&(r=t[o]),null==r&&(r=n.get(o)),null!=r&&(a[o]=r)})),a}function d(e){var t=p(e,"color");if(t){var i=p(e,"colorAlpha"),n=p(e,"colorSaturation");return n&&(t=a.modifyHSL(t,null,null,n)),i&&(t=a.modifyAlpha(t,i)),t}}function h(e,t){return null!=t?a.modifyHSL(t,null,null,e):null}function p(e,t){var i=e[t];if(null!=i&&"none"!==i)return i}function g(e,t,i,a,o,r){if(r&&r.length){var s=f(t,"color")||null!=o.color&&"none"!==o.color&&(f(t,"colorAlpha")||f(t,"colorSaturation"));if(s){var l=t.get("visualMin"),u=t.get("visualMax"),c=i.dataExtent.slice();null!=l&&l<c[0]&&(c[0]=l),null!=u&&u>c[1]&&(c[1]=u);var d=t.get("colorMappingBy"),h={type:s.name,dataExtent:c,visual:s.range};"color"!==h.type||"index"!==d&&"id"!==d?h.mappingMethod="linear":(h.mappingMethod="category",h.loop=!0);var p=new n(h);return p.__drColorMappingBy=d,p}}}function f(e,t){var i=e.get(t);return r(i)&&i.length?{name:t,range:i}:null}function m(e,t,i,n,a,r){var s=o.extend({},t);if(a){var l=a.type,u="color"===l&&a.__drColorMappingBy,c="index"===u?n:"id"===u?r.mapIdToIndex(i.getId()):i.getValue(e.get("visualDimension"));s[l]=a.mapValueToVisual(c)}return s}e.exports=l},"923d":function(e,t,i){var n=i("6d8b"),a=i("3842"),o=i("ee1a"),r=o.isDimensionStacked,s=n.indexOf;function l(e){return!(isNaN(parseFloat(e.x))&&isNaN(parseFloat(e.y)))}function u(e){return!isNaN(parseFloat(e.x))&&!isNaN(parseFloat(e.y))}function c(e,t,i,n,o,s){var l=[],u=r(t,n),c=u?t.getCalculationInfo("stackResultDimension"):n,d=y(t,c,e),h=t.indicesOfNearest(c,d)[0];l[o]=t.get(i,h),l[s]=t.get(n,h);var p=a.getPrecision(t.get(n,h));return p=Math.min(p,20),p>=0&&(l[s]=+l[s].toFixed(p)),l}var d=n.curry,h={min:d(c,"min"),max:d(c,"max"),average:d(c,"average")};function p(e,t){var i=e.getData(),a=e.coordinateSystem;if(t&&!u(t)&&!n.isArray(t.coord)&&a){var o=a.dimensions,r=g(t,i,a,e);if(t=n.clone(t),t.type&&h[t.type]&&r.baseAxis&&r.valueAxis){var l=s(o,r.baseAxis.dim),c=s(o,r.valueAxis.dim);t.coord=h[t.type](i,r.baseDataDim,r.valueDataDim,l,c),t.value=t.coord[c]}else{for(var d=[null!=t.xAxis?t.xAxis:t.radiusAxis,null!=t.yAxis?t.yAxis:t.angleAxis],p=0;p<2;p++)h[d[p]]&&(d[p]=y(i,i.mapDimension(o[p]),d[p]));t.coord=d}}return t}function g(e,t,i,n){var a={};return null!=e.valueIndex||null!=e.valueDim?(a.valueDataDim=null!=e.valueIndex?t.getDimension(e.valueIndex):e.valueDim,a.valueAxis=i.getAxis(f(n,a.valueDataDim)),a.baseAxis=i.getOtherAxis(a.valueAxis),a.baseDataDim=t.mapDimension(a.baseAxis.dim)):(a.baseAxis=n.getBaseAxis(),a.valueAxis=i.getOtherAxis(a.baseAxis),a.baseDataDim=t.mapDimension(a.baseAxis.dim),a.valueDataDim=t.mapDimension(a.valueAxis.dim)),a}function f(e,t){var i=e.getData(),n=i.dimensions;t=i.getDimension(t);for(var a=0;a<n.length;a++){var o=i.getDimensionInfo(n[a]);if(o.name===t)return o.coordDim}}function m(e,t){return!(e&&e.containData&&t.coord&&!l(t))||e.containData(t.coord)}function v(e,t,i,n){return n<2?e.coord&&e.coord[n]:e.value}function y(e,t,i){if("average"===i){var n=0,a=0;return e.each(t,(function(e,t){isNaN(e)||(n+=e,a++)})),n/a}return"median"===i?e.getMedian(t):e.getDataExtent(t,!0)["max"===i?1:0]}t.dataTransform=p,t.getAxisInfo=g,t.dataFilter=m,t.dimValueGetter=v,t.numCalculate=y},"928d":function(e,t,i){var n=i("6d8b"),a=i("4f85"),o=i("06c7"),r=i("4319"),s=i("eda2"),l=s.encodeHTML,u=s.addCommas,c=i("55ac"),d=c.wrapTreePathInfo,h=a.extend({type:"series.treemap",layoutMode:"box",dependencies:["grid","polar"],_viewRoot:null,defaultOption:{progressive:0,hoverLayerThreshold:1/0,left:"center",top:"middle",right:null,bottom:null,width:"80%",height:"80%",sort:!0,clipWindow:"origin",squareRatio:.5*(1+Math.sqrt(5)),leafDepth:null,drillDownIcon:"▶",zoomToNodeRatio:.1024,roam:!0,nodeClick:"zoomToNode",animation:!0,animationDurationUpdate:900,animationEasing:"quinticInOut",breadcrumb:{show:!0,height:22,left:"center",top:"bottom",emptyItemWidth:25,itemStyle:{color:"rgba(0,0,0,0.7)",borderColor:"rgba(255,255,255,0.7)",borderWidth:1,shadowColor:"rgba(150,150,150,1)",shadowBlur:3,shadowOffsetX:0,shadowOffsetY:0,textStyle:{color:"#fff"}},emphasis:{textStyle:{}}},label:{show:!0,distance:0,padding:5,position:"inside",color:"#fff",ellipsis:!0},upperLabel:{show:!1,position:[0,"50%"],height:20,color:"#fff",ellipsis:!0,verticalAlign:"middle"},itemStyle:{color:null,colorAlpha:null,colorSaturation:null,borderWidth:0,gapWidth:0,borderColor:"#fff",borderColorSaturation:null},emphasis:{upperLabel:{show:!0,position:[0,"50%"],color:"#fff",ellipsis:!0,verticalAlign:"middle"}},visualDimension:0,visualMin:null,visualMax:null,color:[],colorAlpha:null,colorSaturation:null,colorMappingBy:"index",visibleMin:10,childrenVisibleMin:null,levels:[]},getInitialData:function(e,t){var i={name:e.name,children:e.data};p(i);var n=e.levels||[];n=e.levels=g(n,t);var a={};return a.levels=n,o.createTree(i,this,a).data},optionUpdated:function(){this.resetViewRoot()},formatTooltip:function(e){var t=this.getData(),i=this.getRawValue(e),a=n.isArray(i)?u(i[0]):u(i),o=t.getName(e);return l(o+": "+a)},getDataParams:function(e){var t=a.prototype.getDataParams.apply(this,arguments),i=this.getData().tree.getNodeByDataIndex(e);return t.treePathInfo=d(i,this),t},setLayoutInfo:function(e){this.layoutInfo=this.layoutInfo||{},n.extend(this.layoutInfo,e)},mapIdToIndex:function(e){var t=this._idIndexMap;t||(t=this._idIndexMap=n.createHashMap(),this._idIndexMapCount=0);var i=t.get(e);return null==i&&t.set(e,i=this._idIndexMapCount++),i},getViewRoot:function(){return this._viewRoot},resetViewRoot:function(e){e?this._viewRoot=e:e=this._viewRoot;var t=this.getRawData().tree.root;e&&(e===t||t.contains(e))||(this._viewRoot=t)}});function p(e){var t=0;n.each(e.children,(function(e){p(e);var i=e.value;n.isArray(i)&&(i=i[0]),t+=i}));var i=e.value;n.isArray(i)&&(i=i[0]),(null==i||isNaN(i))&&(i=t),i<0&&(i=0),n.isArray(e.value)?e.value[0]=i:e.value=i}function g(e,t){var i=t.get("color");if(i){var a;if(e=e||[],n.each(e,(function(e){var t=new r(e),i=t.get("color");(t.get("itemStyle.color")||i&&"none"!==i)&&(a=!0)})),!a){var o=e[0]||(e[0]={});o.color=i.slice()}return e}}e.exports=h},"933b":function(e,t,i){var n=i("b12f"),a=n.extend({type:"timeline"});e.exports=a},9390:function(e,t,i){i("d0900"),i("83ba"),i("ee66")},9442:function(e,t,i){var n=i("6d8b");function a(e){var t=[];n.each(e.series,(function(e){e&&"map"===e.type&&(t.push(e),e.map=e.map||e.mapType,n.defaults(e,e.mapLocation))}))}e.exports=a},"94b1":function(e,t,i){var n=i("3eba"),a=i("6d8b"),o=i("9d57"),r=o.layout,s=o.largeLayout;i("5aa9"),i("17b8"),i("67cc"),i("01ed"),n.registerLayout(a.curry(r,"bar")),n.registerLayout(s),n.registerVisual({seriesType:"bar",reset:function(e){e.getData().setVisual("legendSymbol","roundRect")}})},"94e4":function(e,t,i){var n=i("401b");function a(e){var t=e.coordinateSystem;if(!t||"view"===t.type){var i=t.getBoundingRect(),a=e.getData(),o=a.graph,r=0,s=a.getSum("value"),l=2*Math.PI/(s||a.count()),u=i.width/2+i.x,c=i.height/2+i.y,d=Math.min(i.width,i.height)/2;o.eachNode((function(e){var t=e.getValue("value");r+=l*(s?t:1)/2,e.setLayout([d*Math.cos(r)+u,d*Math.sin(r)+c]),r+=l*(s?t:1)/2})),a.setLayout({cx:u,cy:c}),o.eachEdge((function(e){var t,i=e.getModel().get("lineStyle.curveness")||0,a=n.clone(e.node1.getLayout()),o=n.clone(e.node2.getLayout()),r=(a[0]+o[0])/2,s=(a[1]+o[1])/2;+i&&(i*=3,t=[u*i+r*(1-i),c*i+s*(1-i)]),e.setLayout([a,o,t])}))}}t.circularLayout=a},"95a8":function(e,t,i){var n=i("3eba");i("1953"),i("307d"),n.registerPreprocessor((function(e){e.markLine=e.markLine||{}}))},9704:function(e,t,i){var n=i("3eba"),a=i("6d8b"),o=i("f706"),r=i("73ca"),s=i("4a01"),l=i("01ef"),u=i("c526"),c=u.onIrrelevantElement,d=i("2306"),h=i("480f"),p="__focusNodeAdjacency",g="__unfocusNodeAdjacency",f=["itemStyle","opacity"],m=["lineStyle","opacity"];function v(e,t){return e.getVisual("opacity")||e.getModel().get(t)}function y(e,t,i){var n=e.getGraphicEl(),a=v(e,t);null!=i&&(null==a&&(a=1),a*=i),n.downplay&&n.downplay(),n.traverse((function(e){if("group"!==e.type){var t=e.lineLabelOriginalOpacity;null!=t&&null==i||(t=a),e.setStyle("opacity",t)}}))}function x(e,t){var i=v(e,t),n=e.getGraphicEl();n.highlight&&n.highlight(),n.traverse((function(e){"group"!==e.type&&e.setStyle("opacity",i)}))}var _=n.extendChartView({type:"graph",init:function(e,t){var i=new o,n=new r,a=this.group;this._controller=new s(t.getZr()),this._controllerHost={target:a},a.add(i.group),a.add(n.group),this._symbolDraw=i,this._lineDraw=n,this._firstRender=!0},render:function(e,t,i){var n=e.coordinateSystem;this._model=e,this._nodeScaleRatio=e.get("nodeScaleRatio");var a=this._symbolDraw,o=this._lineDraw,r=this.group;if("view"===n.type){var s={position:n.position,scale:n.scale};this._firstRender?r.attr(s):d.updateProps(r,s,e)}h(e.getGraph(),this._getNodeGlobalScale(e));var l=e.getData();a.updateData(l);var u=e.getEdgeData();o.updateData(u),this._updateNodeAndLinkScale(),this._updateController(e,t,i),clearTimeout(this._layoutTimeout);var c=e.forceLayout,f=e.get("force.layoutAnimation");c&&this._startForceLayoutIteration(c,f),l.eachItemGraphicEl((function(t,n){var a=l.getItemModel(n);t.off("drag").off("dragend");var o=a.get("draggable");o&&t.on("drag",(function(){c&&(c.warmUp(),!this._layouting&&this._startForceLayoutIteration(c,f),c.setFixed(n),l.setItemLayout(n,t.position))}),this).on("dragend",(function(){c&&c.setUnfixed(n)}),this),t.setDraggable(o&&c),t[p]&&t.off("mouseover",t[p]),t[g]&&t.off("mouseout",t[g]),a.get("focusNodeAdjacency")&&(t.on("mouseover",t[p]=function(){i.dispatchAction({type:"focusNodeAdjacency",seriesId:e.id,dataIndex:t.dataIndex})}),t.on("mouseout",t[g]=function(){i.dispatchAction({type:"unfocusNodeAdjacency",seriesId:e.id})}))}),this),l.graph.eachEdge((function(t){var n=t.getGraphicEl();n[p]&&n.off("mouseover",n[p]),n[g]&&n.off("mouseout",n[g]),t.getModel().get("focusNodeAdjacency")&&(n.on("mouseover",n[p]=function(){i.dispatchAction({type:"focusNodeAdjacency",seriesId:e.id,edgeDataIndex:t.dataIndex})}),n.on("mouseout",n[g]=function(){i.dispatchAction({type:"unfocusNodeAdjacency",seriesId:e.id})}))}));var m="circular"===e.get("layout")&&e.get("circular.rotateLabel"),v=l.getLayout("cx"),y=l.getLayout("cy");l.eachItemGraphicEl((function(e,t){var i=e.getSymbolPath();if(m){var n=l.getItemLayout(t),a=Math.atan2(n[1]-y,n[0]-v);a<0&&(a=2*Math.PI+a);var o=n[0]<v;o&&(a-=Math.PI);var r=o?"left":"right";i.setStyle({textRotation:-a,textPosition:r,textOrigin:"center"}),i.hoverStyle&&(i.hoverStyle.textPosition=r)}else i.setStyle({textRotation:0})})),this._firstRender=!1},dispose:function(){this._controller&&this._controller.dispose(),this._controllerHost={}},focusNodeAdjacency:function(e,t,i,n){var o=this._model.getData(),r=o.graph,s=n.dataIndex,l=n.edgeDataIndex,u=r.getNodeByIndex(s),c=r.getEdgeByIndex(l);(u||c)&&(r.eachNode((function(e){y(e,f,.1)})),r.eachEdge((function(e){y(e,m,.1)})),u&&(x(u,f),a.each(u.edges,(function(e){e.dataIndex<0||(x(e,m),x(e.node1,f),x(e.node2,f))}))),c&&(x(c,m),x(c.node1,f),x(c.node2,f)))},unfocusNodeAdjacency:function(e,t,i,n){var a=this._model.getData().graph;a.eachNode((function(e){y(e,f)})),a.eachEdge((function(e){y(e,m)}))},_startForceLayoutIteration:function(e,t){var i=this;(function n(){e.step((function(e){i.updateLayout(i._model),(i._layouting=!e)&&(t?i._layoutTimeout=setTimeout(n,16):n())}))})()},_updateController:function(e,t,i){var n=this._controller,a=this._controllerHost,o=this.group;n.setPointerChecker((function(t,n,a){var r=o.getBoundingRect();return r.applyTransform(o.transform),r.contain(n,a)&&!c(t,i,e)})),"view"===e.coordinateSystem.type?(n.enable(e.get("roam")),a.zoomLimit=e.get("scaleLimit"),a.zoom=e.coordinateSystem.getZoom(),n.off("pan").off("zoom").on("pan",(function(t){l.updateViewOnPan(a,t.dx,t.dy),i.dispatchAction({seriesId:e.id,type:"graphRoam",dx:t.dx,dy:t.dy})})).on("zoom",(function(t){l.updateViewOnZoom(a,t.scale,t.originX,t.originY),i.dispatchAction({seriesId:e.id,type:"graphRoam",zoom:t.scale,originX:t.originX,originY:t.originY}),this._updateNodeAndLinkScale(),h(e.getGraph(),this._getNodeGlobalScale(e)),this._lineDraw.updateLayout()}),this)):n.disable()},_updateNodeAndLinkScale:function(){var e=this._model,t=e.getData(),i=this._getNodeGlobalScale(e),n=[i,i];t.eachItemGraphicEl((function(e,t){e.attr("scale",n)}))},_getNodeGlobalScale:function(e){var t=e.coordinateSystem;if("view"!==t.type)return 1;var i=this._nodeScaleRatio,n=t.scale,a=n&&n[0]||1,o=t.getZoom(),r=(o-1)*i+1;return r/a},updateLayout:function(e){h(e.getGraph(),this._getNodeGlobalScale(e)),this._symbolDraw.updateLayout(),this._lineDraw.updateLayout()},remove:function(e,t){this._symbolDraw&&this._symbolDraw.remove(),this._lineDraw&&this._lineDraw.remove()}});e.exports=_},"98e7":function(e,t,i){var n=i("6d8b"),a=n.createHashMap;function o(e){return{getTargetSeries:function(t){var i={},n=a();return t.eachSeriesByType(e,(function(e){e.__paletteScope=i,n.set(e.uid,e)})),n},reset:function(e,t){var i=e.getRawData(),n={},a=e.getData();a.each((function(e){var t=a.getRawIndex(e);n[t]=e})),i.each((function(t){var o=n[t],r=null!=o&&a.getItemVisual(o,"color",!0);if(r)i.setItemVisual(t,"color",r);else{var s=i.getItemModel(t),l=s.get("itemStyle.color")||e.getColorFromPalette(i.getName(t)||t+"",e.__paletteScope,i.count());i.setItemVisual(t,"color",l),null!=o&&a.setItemVisual(o,"color",l)}}))}}}e.exports=o},"9ca8":function(e,t,i){var n=i("6d8b"),a=i("9850"),o=i("3842"),r=o.parsePercent,s=o.MAX_SAFE_INTEGER,l=i("f934"),u=i("55ac"),c=Math.max,d=Math.min,h=n.retrieve,p=n.each,g=["itemStyle","borderWidth"],f=["itemStyle","gapWidth"],m=["upperLabel","show"],v=["upperLabel","height"],y={seriesType:"treemap",reset:function(e,t,i,o){var s=i.getWidth(),c=i.getHeight(),d=e.option,g=l.getLayoutRect(e.getBoxLayoutParams(),{width:i.getWidth(),height:i.getHeight()}),f=d.size||[],m=r(h(g.width,f[0]),s),v=r(h(g.height,f[1]),c),y=o&&o.type,_=["treemapZoomToNode","treemapRootToNode"],b=u.retrieveTargetInfo(o,_,e),w="treemapRender"===y||"treemapMove"===y?o.rootRect:null,S=e.getViewRoot(),M=u.getPathToRoot(S);if("treemapMove"!==y){var I="treemapZoomToNode"===y?A(e,b,S,m,v):w?[w.width,w.height]:[m,v],L=d.sort;L&&"asc"!==L&&"desc"!==L&&(L="desc");var C={squareRatio:d.squareRatio,sort:L,leafDepth:d.leafDepth};S.hostTree.clearLayouts();var P={x:0,y:0,width:I[0],height:I[1],area:I[0]*I[1]};S.setLayout(P),x(S,C,!1,0);P=S.getLayout();p(M,(function(e,t){var i=(M[t+1]||S).getValue();e.setLayout(n.extend({dataExtent:[i,i],borderWidth:0,upperHeight:0},P))}))}var N=e.getData().tree.root;N.setLayout(T(g,w,b),!0),e.setLayoutInfo(g),D(N,new a(-g.x,-g.y,s,c),M,S,0)}};function x(e,t,i,n){var a,o;if(!e.isRemoved()){var r=e.getLayout();a=r.width,o=r.height;var s=e.getModel(),l=s.get(g),u=s.get(f)/2,h=L(s),p=Math.max(l,h),m=l-u,v=p-u;s=e.getModel();e.setLayout({borderWidth:l,upperHeight:p,upperLabelHeight:h},!0),a=c(a-2*m,0),o=c(o-m-v,0);var y=a*o,b=_(e,s,y,t,i,n);if(b.length){var w={x:m,y:v,width:a,height:o},S=d(a,o),A=1/0,T=[];T.area=0;for(var D=0,C=b.length;D<C;){var P=b[D];T.push(P),T.area+=P.getLayout().area;var N=M(T,S,t.squareRatio);N<=A?(D++,A=N):(T.area-=T.pop().getLayout().area,I(T,S,w,u,!1),S=d(w.width,w.height),T.length=T.area=0,A=1/0)}if(T.length&&I(T,S,w,u,!0),!i){var R=s.get("childrenVisibleMin");null!=R&&y<R&&(i=!0)}for(D=0,C=b.length;D<C;D++)x(b[D],t,i,n+1)}}}function _(e,t,i,a,o,r){var s=e.children||[],l=a.sort;"asc"!==l&&"desc"!==l&&(l=null);var u=null!=a.leafDepth&&a.leafDepth<=r;if(o&&!u)return e.viewChildren=[];s=n.filter(s,(function(e){return!e.isRemoved()})),w(s,l);var c=S(t,s,l);if(0===c.sum)return e.viewChildren=[];if(c.sum=b(t,i,c.sum,l,s),0===c.sum)return e.viewChildren=[];for(var d=0,h=s.length;d<h;d++){var p=s[d].getValue()/c.sum*i;s[d].setLayout({area:p})}return u&&(s.length&&e.setLayout({isLeafRoot:!0},!0),s.length=0),e.viewChildren=s,e.setLayout({dataExtent:c.dataExtent},!0),s}function b(e,t,i,n,a){if(!n)return i;for(var o=e.get("visibleMin"),r=a.length,s=r,l=r-1;l>=0;l--){var u=a["asc"===n?r-l-1:l].getValue();u/i*t<o&&(s=l,i-=u)}return"asc"===n?a.splice(0,r-s):a.splice(s,r-s),i}function w(e,t){return t&&e.sort((function(e,i){var n="asc"===t?e.getValue()-i.getValue():i.getValue()-e.getValue();return 0===n?"asc"===t?e.dataIndex-i.dataIndex:i.dataIndex-e.dataIndex:n})),e}function S(e,t,i){for(var n=0,a=0,o=t.length;a<o;a++)n+=t[a].getValue();var r=e.get("visualDimension");if(t&&t.length)if("value"===r&&i)s=[t[t.length-1].getValue(),t[0].getValue()],"asc"===i&&s.reverse();else{var s=[1/0,-1/0];p(t,(function(e){var t=e.getValue(r);t<s[0]&&(s[0]=t),t>s[1]&&(s[1]=t)}))}else s=[NaN,NaN];return{sum:n,dataExtent:s}}function M(e,t,i){for(var n,a=0,o=1/0,r=0,s=e.length;r<s;r++)n=e[r].getLayout().area,n&&(n<o&&(o=n),n>a&&(a=n));var l=e.area*e.area,u=t*t*i;return l?c(u*a/l,l/(u*o)):1/0}function I(e,t,i,n,a){var o=t===i.width?0:1,r=1-o,s=["x","y"],l=["width","height"],u=i[s[o]],h=t?e.area/t:0;(a||h>i[l[r]])&&(h=i[l[r]]);for(var p=0,g=e.length;p<g;p++){var f=e[p],m={},v=h?f.getLayout().area/h:0,y=m[l[r]]=c(h-2*n,0),x=i[s[o]]+i[l[o]]-u,_=p===g-1||x<v?x:v,b=m[l[o]]=c(_-2*n,0);m[s[r]]=i[s[r]]+d(n,y/2),m[s[o]]=u+d(n,b/2),u+=_,f.setLayout(m,!0)}i[s[r]]+=h,i[l[r]]-=h}function A(e,t,i,n,a){var o,r=(t||{}).node,l=[n,a];if(!r||r===i)return l;var u=n*a,c=u*e.option.zoomToNodeRatio;while(o=r.parentNode){for(var d=0,h=o.children,p=0,f=h.length;p<f;p++)d+=h[p].getValue();var m=r.getValue();if(0===m)return l;c*=d/m;var v=o.getModel(),y=v.get(g),x=Math.max(y,L(v,y));c+=4*y*y+(3*y+x)*Math.pow(c,.5),c>s&&(c=s),r=o}c<u&&(c=u);var _=Math.pow(c/u,.5);return[n*_,a*_]}function T(e,t,i){if(t)return{x:t.x,y:t.y};var n={x:0,y:0};if(!i)return n;var a=i.node,o=a.getLayout();if(!o)return n;var r=[o.width/2,o.height/2],s=a;while(s){var l=s.getLayout();r[0]+=l.x,r[1]+=l.y,s=s.parentNode}return{x:e.width/2-r[0],y:e.height/2-r[1]}}function D(e,t,i,n,o){var r=e.getLayout(),s=i[o],l=s&&s===e;if(!(s&&!l||o===i.length&&e!==n)){e.setLayout({isInView:!0,invisible:!l&&!t.intersect(r),isAboveViewRoot:l},!0);var u=new a(t.x-r.x,t.y-r.y,t.width,t.height);p(e.viewChildren||[],(function(e){D(e,u,i,n,o+1)}))}}function L(e){return e.get(m)?e.get(v):0}e.exports=y},"9e87":function(e,t,i){var n=i("3eba"),a=i("6d8b"),o=i("50e5");n.registerAction("dataZoom",(function(e,t){var i=o.createLinkedNodesFinder(a.bind(t.eachComponent,t,"dataZoom"),o.eachAxisDim,(function(e,t){return e.get(t.axisIndex)})),n=[];t.eachComponent({mainType:"dataZoom",query:e},(function(e,t){n.push.apply(n,i(e).nodes)})),a.each(n,(function(t,i){t.setRawRange({start:e.start,end:e.end,startValue:e.startValue,endValue:e.endValue})}))}))},"9fa3":function(e,t,i){var n=i("4ab1"),a=i("6d8b"),o=i("1687");function r(e,t){n.call(this,e,t,"clipPath","__clippath_in_use__")}a.inherits(r,n),r.prototype.update=function(e){var t=this.getSvgElement(e);t&&this.updateDom(t,e.__clipPaths,!1);var i=this.getTextSvgElement(e);i&&this.updateDom(i,e.__clipPaths,!0),this.markUsed(e)},r.prototype.updateDom=function(e,t,i){if(t&&t.length>0){var n,a,r=this.getDefs(!0),s=t[0],l=i?"_textDom":"_dom";s[l]?(a=s[l].getAttribute("id"),n=s[l],r.contains(n)||r.appendChild(n)):(a="zr"+this._zrId+"-clip-"+this.nextId,++this.nextId,n=this.createElement("clipPath"),n.setAttribute("id",a),r.appendChild(n),s[l]=n);var u=this.getSvgProxy(s);if(s.transform&&s.parent.invTransform&&!i){var c=Array.prototype.slice.call(s.transform);o.mul(s.transform,s.parent.invTransform,s.transform),u.brush(s),s.transform=c}else u.brush(s);var d=this.getSvgElement(s);n.innerHTML="",n.appendChild(d.cloneNode()),e.setAttribute("clip-path","url(#"+a+")"),t.length>1&&this.updateDom(n,t.slice(1),i)}else e&&e.setAttribute("clip-path","none")},r.prototype.markUsed=function(e){var t=this;e.__clipPaths&&e.__clipPaths.length>0&&a.each(e.__clipPaths,(function(e){e._dom&&n.prototype.markUsed.call(t,e._dom),e._textDom&&n.prototype.markUsed.call(t,e._textDom)}))};var s=r;e.exports=s},a016:function(e,t,i){var n=i("4f85"),a=i("e46b"),o=i("6d8b"),r=i("eda2"),s=r.encodeHTML,l=n.extend({type:"series.radar",dependencies:["radar"],init:function(e){l.superApply(this,"init",arguments),this.legendDataProvider=function(){return this.getRawData()}},getInitialData:function(e,t){return a(this,{generateCoord:"indicator_",generateCoordCount:1/0})},formatTooltip:function(e){var t=this.getData(),i=this.coordinateSystem,n=i.getIndicatorAxes(),a=this.getData().getName(e);return s(""===a?this.name:a)+"<br/>"+o.map(n,(function(i,n){var a=t.get(t.mapDimension(i.dim),e);return s(i.name+" : "+a)})).join("<br />")},defaultOption:{zlevel:0,z:2,coordinateSystem:"radar",legendHoverLink:!0,radarIndex:0,lineStyle:{width:2,type:"solid"},label:{position:"top"},symbol:"emptyCircle",symbolSize:4}}),u=l;e.exports=u},a04e:function(e,t,i){var n=i("6cb7");n.registerSubTypeDefaulter("timeline",(function(){return"slider"}))},a18f:function(e,t,i){var n=i("3a56"),a=n.extend({type:"dataZoom.inside",defaultOption:{disabled:!1,zoomLock:!1,zoomOnMouseWheel:!0,moveOnMouseMove:!0,moveOnMouseWheel:!1,preventDefaultMouseMove:!0}});e.exports=a},a38d:function(e,t,i){var n=i("2306"),a=i("392f"),o=i("9680"),r=i("68ab"),s=n.extendShape({shape:{polyline:!1,curveness:0,segs:[]},buildPath:function(e,t){var i=t.segs,n=t.curveness;if(t.polyline)for(var a=0;a<i.length;){var o=i[a++];if(o>0){e.moveTo(i[a++],i[a++]);for(var r=1;r<o;r++)e.lineTo(i[a++],i[a++])}}else for(a=0;a<i.length;){var s=i[a++],l=i[a++],u=i[a++],c=i[a++];if(e.moveTo(s,l),n>0){var d=(s+u)/2-(l-c)*n,h=(l+c)/2-(u-s)*n;e.quadraticCurveTo(d,h,u,c)}else e.lineTo(u,c)}},findDataIndex:function(e,t){var i=this.shape,n=i.segs,a=i.curveness;if(i.polyline)for(var s=0,l=0;l<n.length;){var u=n[l++];if(u>0)for(var c=n[l++],d=n[l++],h=1;h<u;h++){var p=n[l++],g=n[l++];if(o.containStroke(c,d,p,g))return s}s++}else for(s=0,l=0;l<n.length;){c=n[l++],d=n[l++],p=n[l++],g=n[l++];if(a>0){var f=(c+p)/2-(d-g)*a,m=(d+g)/2-(p-c)*a;if(r.containStroke(c,d,f,m,p,g))return s}else if(o.containStroke(c,d,p,g))return s;s++}return-1}});function l(){this.group=new n.Group}var u=l.prototype;u.isPersistent=function(){return!this._incremental},u.updateData=function(e){this.group.removeAll();var t=new s({rectHover:!0,cursor:"default"});t.setShape({segs:e.getLayout("linesPoints")}),this._setCommon(t,e),this.group.add(t),this._incremental=null},u.incrementalPrepareUpdate=function(e){this.group.removeAll(),this._clearIncremental(),e.count()>5e5?(this._incremental||(this._incremental=new a({silent:!0})),this.group.add(this._incremental)):this._incremental=null},u.incrementalUpdate=function(e,t){var i=new s;i.setShape({segs:t.getLayout("linesPoints")}),this._setCommon(i,t,!!this._incremental),this._incremental?this._incremental.addDisplayable(i,!0):(i.rectHover=!0,i.cursor="default",i.__startIndex=e.start,this.group.add(i))},u.remove=function(){this._clearIncremental(),this._incremental=null,this.group.removeAll()},u._setCommon=function(e,t,i){var n=t.hostModel;e.setShape({polyline:n.get("polyline"),curveness:n.get("lineStyle.curveness")}),e.useStyle(n.getModel("lineStyle").getLineStyle()),e.style.strokeNoScale=!0;var a=t.getVisual("color");a&&e.setStyle("stroke",a),e.setStyle("fill"),i||(e.seriesIndex=n.seriesIndex,e.on("mousemove",(function(t){e.dataIndex=null;var i=e.findDataIndex(t.offsetX,t.offsetY);i>0&&(e.dataIndex=i+e.__startIndex)})))},u._clearIncremental=function(){var e=this._incremental;e&&e.clearDisplaybles()};var c=l;e.exports=c},a4b1:function(e,t,i){var n=i("3eba");i("00ba"),i("4d62");var a=i("98e7"),o=i("24b9"),r=i("d3f47");n.registerVisual(a("funnel")),n.registerLayout(o),n.registerProcessor(r("funnel"))},a4fe:function(e,t,i){var n=i("3eba"),a="\0_ec_interaction_mutex";function o(e,t,i){var n=l(e);n[t]=i}function r(e,t,i){var n=l(e),a=n[t];a===i&&(n[t]=null)}function s(e,t){return!!l(e)[t]}function l(e){return e[a]||(e[a]={})}n.registerAction({type:"takeGlobalCursor",event:"globalCursorTaken",update:"update"},(function(){})),t.take=o,t.release=r,t.isTaken=s},a666:function(e,t,i){var n=i("3eba"),a=i("6d8b"),o=i("2306"),r=i("0c41"),s="__seriesMapHighDown",l="__seriesMapCallKey",u=n.extendChartView({type:"map",render:function(e,t,i,n){if(!n||"mapToggleSelect"!==n.type||n.from!==this.uid){var a=this.group;if(a.removeAll(),!e.getHostGeoModel()){if(n&&"geoRoam"===n.type&&"series"===n.componentType&&n.seriesId===e.id){o=this._mapDraw;o&&a.add(o.group)}else if(e.needsDrawMap){var o=this._mapDraw||new r(i,!0);a.add(o.group),o.draw(e,t,i,this,n),this._mapDraw=o}else this._mapDraw&&this._mapDraw.remove(),this._mapDraw=null;e.get("showLegendSymbol")&&t.getComponent("legend")&&this._renderSymbols(e,t,i)}}},remove:function(){this._mapDraw&&this._mapDraw.remove(),this._mapDraw=null,this.group.removeAll()},dispose:function(){this._mapDraw&&this._mapDraw.remove(),this._mapDraw=null},_renderSymbols:function(e,t,i){var n=e.originalData,r=this.group;n.each(n.mapDimension("value"),(function(t,i){if(!isNaN(t)){var u=n.getItemLayout(i);if(u&&u.point){var h=u.point,p=u.offset,g=new o.Circle({style:{fill:e.getData().getVisual("color")},shape:{cx:h[0]+9*p,cy:h[1],r:3},silent:!0,z2:8+(p?0:o.Z2_EMPHASIS_LIFT+1)});if(!p){var f=e.mainSeries.getData(),m=n.getName(i),v=f.indexOfName(m),y=n.getItemModel(i),x=y.getModel("label"),_=y.getModel("emphasis.label"),b=f.getItemGraphicEl(v),w=a.retrieve2(e.getFormattedLabel(v,"normal"),m),S=a.retrieve2(e.getFormattedLabel(v,"emphasis"),w),M=b[s],I=Math.random();if(!M){M=b[s]={};var A=a.curry(c,!0),T=a.curry(c,!1);b.on("mouseover",A).on("mouseout",T).on("emphasis",A).on("normal",T)}b[l]=I,a.extend(M,{recordVersion:I,circle:g,labelModel:x,hoverLabelModel:_,emphasisText:S,normalText:w}),d(M,!1)}r.add(g)}}}))}});function c(e){var t=this[s];t&&t.recordVersion===this[l]&&d(t,e)}function d(e,t){var i=e.circle,n=e.labelModel,a=e.hoverLabelModel,r=e.emphasisText,s=e.normalText;t?(i.style.extendFrom(o.setTextStyle({},a,{text:a.get("show")?r:null},{isRectText:!0,useInsideStyle:!1},!0)),i.__mapOriginalZ2=i.z2,i.z2+=o.Z2_EMPHASIS_LIFT):(o.setTextStyle(i.style,n,{text:n.get("show")?s:null,textPosition:n.getShallow("position")||"bottom"},{isRectText:!0,useInsideStyle:!1}),i.dirty(!1),null!=i.__mapOriginalZ2&&(i.z2=i.__mapOriginalZ2,i.__mapOriginalZ2=null))}e.exports=u},a753:function(e,t,i){var n=i("1f0e"),a=i("2306"),o=i("e887"),r=i("3842"),s=r.parsePercent,l=r.round,u=r.linearMap;function c(e,t){var i=e.get("center"),n=t.getWidth(),a=t.getHeight(),o=Math.min(n,a),r=s(i[0],t.getWidth()),l=s(i[1],t.getHeight()),u=s(e.get("radius"),o/2);return{cx:r,cy:l,r:u}}function d(e,t){return t&&("string"===typeof t?e=t.replace("{value}",null!=e?e:""):"function"===typeof t&&(e=t(e))),e}var h=2*Math.PI,p=o.extend({type:"gauge",render:function(e,t,i){this.group.removeAll();var n=e.get("axisLine.lineStyle.color"),a=c(e,i);this._renderMain(e,t,i,n,a)},dispose:function(){},_renderMain:function(e,t,i,n,o){for(var r=this.group,s=e.getModel("axisLine"),l=s.getModel("lineStyle"),u=e.get("clockwise"),c=-e.get("startAngle")/180*Math.PI,d=-e.get("endAngle")/180*Math.PI,p=(d-c)%h,g=c,f=l.get("width"),m=0;m<n.length;m++){var v=Math.min(Math.max(n[m][0],0),1),y=(d=c+p*v,new a.Sector({shape:{startAngle:g,endAngle:d,cx:o.cx,cy:o.cy,clockwise:u,r0:o.r-f,r:o.r},silent:!0}));y.setStyle({fill:n[m][1]}),y.setStyle(l.getLineStyle(["color","borderWidth","borderColor"])),r.add(y),g=d}var x=function(e){if(e<=0)return n[0][1];for(var t=0;t<n.length;t++)if(n[t][0]>=e&&(0===t?0:n[t-1][0])<e)return n[t][1];return n[t-1][1]};if(!u){var _=c;c=d,d=_}this._renderTicks(e,t,i,x,o,c,d,u),this._renderPointer(e,t,i,x,o,c,d,u),this._renderTitle(e,t,i,x,o),this._renderDetail(e,t,i,x,o)},_renderTicks:function(e,t,i,n,o,r,u,c){for(var h=this.group,p=o.cx,g=o.cy,f=o.r,m=+e.get("min"),v=+e.get("max"),y=e.getModel("splitLine"),x=e.getModel("axisTick"),_=e.getModel("axisLabel"),b=e.get("splitNumber"),w=x.get("splitNumber"),S=s(y.get("length"),f),M=s(x.get("length"),f),I=r,A=(u-r)/b,T=A/w,D=y.getModel("lineStyle").getLineStyle(),L=x.getModel("lineStyle").getLineStyle(),C=0;C<=b;C++){var P=Math.cos(I),N=Math.sin(I);if(y.get("show")){var R=new a.Line({shape:{x1:P*f+p,y1:N*f+g,x2:P*(f-S)+p,y2:N*(f-S)+g},style:D,silent:!0});"auto"===D.stroke&&R.setStyle({stroke:n(C/b)}),h.add(R)}if(_.get("show")){var V=d(l(C/b*(v-m)+m),_.get("formatter")),k=_.get("distance"),E=n(C/b);h.add(new a.Text({style:a.setTextStyle({},_,{text:V,x:P*(f-S-k)+p,y:N*(f-S-k)+g,textVerticalAlign:N<-.4?"top":N>.4?"bottom":"middle",textAlign:P<-.4?"left":P>.4?"right":"center"},{autoColor:E}),silent:!0}))}if(x.get("show")&&C!==b){for(var z=0;z<=w;z++){P=Math.cos(I),N=Math.sin(I);var O=new a.Line({shape:{x1:P*f+p,y1:N*f+g,x2:P*(f-M)+p,y2:N*(f-M)+g},silent:!0,style:L});"auto"===L.stroke&&O.setStyle({stroke:n((C+z/w)/b)}),h.add(O),I+=T}I-=T}else I+=A}},_renderPointer:function(e,t,i,o,r,l,c,d){var h=this.group,p=this._data;if(e.get("pointer.show")){var g=[+e.get("min"),+e.get("max")],f=[l,c],m=e.getData(),v=m.mapDimension("value");m.diff(p).add((function(t){var i=new n({shape:{angle:l}});a.initProps(i,{shape:{angle:u(m.get(v,t),g,f,!0)}},e),h.add(i),m.setItemGraphicEl(t,i)})).update((function(t,i){var n=p.getItemGraphicEl(i);a.updateProps(n,{shape:{angle:u(m.get(v,t),g,f,!0)}},e),h.add(n),m.setItemGraphicEl(t,n)})).remove((function(e){var t=p.getItemGraphicEl(e);h.remove(t)})).execute(),m.eachItemGraphicEl((function(e,t){var i=m.getItemModel(t),n=i.getModel("pointer");e.setShape({x:r.cx,y:r.cy,width:s(n.get("width"),r.r),r:s(n.get("length"),r.r)}),e.useStyle(i.getModel("itemStyle").getItemStyle()),"auto"===e.style.fill&&e.setStyle("fill",o(u(m.get(v,t),g,[0,1],!0))),a.setHoverStyle(e,i.getModel("emphasis.itemStyle").getItemStyle())})),this._data=m}else p&&p.eachItemGraphicEl((function(e){h.remove(e)}))},_renderTitle:function(e,t,i,n,o){var r=e.getData(),l=r.mapDimension("value"),c=e.getModel("title");if(c.get("show")){var d=c.get("offsetCenter"),h=o.cx+s(d[0],o.r),p=o.cy+s(d[1],o.r),g=+e.get("min"),f=+e.get("max"),m=e.getData().get(l,0),v=n(u(m,[g,f],[0,1],!0));this.group.add(new a.Text({silent:!0,style:a.setTextStyle({},c,{x:h,y:p,text:r.getName(0),textAlign:"center",textVerticalAlign:"middle"},{autoColor:v,forceRich:!0})}))}},_renderDetail:function(e,t,i,n,o){var r=e.getModel("detail"),l=+e.get("min"),c=+e.get("max");if(r.get("show")){var h=r.get("offsetCenter"),p=o.cx+s(h[0],o.r),g=o.cy+s(h[1],o.r),f=s(r.get("width"),o.r),m=s(r.get("height"),o.r),v=e.getData(),y=v.get(v.mapDimension("value"),0),x=n(u(y,[l,c],[0,1],!0));this.group.add(new a.Text({silent:!0,style:a.setTextStyle({},r,{x:p,y:g,text:d(y,r.get("formatter")),textWidth:isNaN(f)?null:f,textHeight:isNaN(m)?null:m,textAlign:"center",textVerticalAlign:"middle"},{autoColor:x,forceRich:!0})}))}}}),g=p;e.exports=g},a7e2:function(e,t,i){var n=i("3eba");i("7293"),i("ae46");var a=i("6582"),o=i("ee98");n.registerLayout(a),n.registerVisual(o)},a7f2:function(e,t){var i=[[[123.45165252685547,25.73527164402261],[123.49731445312499,25.73527164402261],[123.49731445312499,25.750734064600884],[123.45165252685547,25.750734064600884],[123.45165252685547,25.73527164402261]]];function n(e,t){"china"===e&&"台湾"===t.name&&t.geometries.push({type:"polygon",exterior:i[0]})}e.exports=n},a87d:function(e,t,i){var n=i("22d1"),a=i("401b"),o=a.applyTransform,r=i("9850"),s=i("41ef"),l=i("e86a"),u=i("a73c"),c=i("9e2e"),d=i("19eb"),h=i("0da8"),p=i("76a5"),g=i("cbe5"),f=i("20c8"),m=i("42e5"),v=i("d3a4"),y=f.CMD,x=Math.round,_=Math.sqrt,b=Math.abs,w=Math.cos,S=Math.sin,M=Math.max;if(!n.canvasSupported){var I=",",A="progid:DXImageTransform.Microsoft",T=21600,D=T/2,L=1e5,C=1e3,P=function(e){e.style.cssText="position:absolute;left:0;top:0;width:1px;height:1px;",e.coordsize=T+","+T,e.coordorigin="0,0"},N=function(e){return String(e).replace(/&/g,"&amp;").replace(/"/g,"&quot;")},R=function(e,t,i){return"rgb("+[e,t,i].join(",")+")"},V=function(e,t){t&&e&&t.parentNode!==e&&e.appendChild(t)},k=function(e,t){t&&e&&t.parentNode===e&&e.removeChild(t)},E=function(e,t,i){return(parseFloat(e)||0)*L+(parseFloat(t)||0)*C+i},z=function(e,t){return"string"===typeof e?e.lastIndexOf("%")>=0?parseFloat(e)/100*t:parseFloat(e):e},O=function(e,t,i){var n=s.parse(t);i=+i,isNaN(i)&&(i=1),n&&(e.color=R(n[0],n[1],n[2]),e.opacity=i*n[3])},B=function(e){var t=s.parse(e);return[R(t[0],t[1],t[2]),t[3]]},G=function(e,t,i){var n=t.fill;if(null!=n)if(n instanceof m){var a,r=0,s=[0,0],l=0,u=1,c=i.getBoundingRect(),d=c.width,h=c.height;if("linear"===n.type){a="gradient";var p=i.transform,g=[n.x*d,n.y*h],f=[n.x2*d,n.y2*h];p&&(o(g,g,p),o(f,f,p));var v=f[0]-g[0],y=f[1]-g[1];r=180*Math.atan2(v,y)/Math.PI,r<0&&(r+=360),r<1e-6&&(r=0)}else{a="gradientradial";g=[n.x*d,n.y*h],p=i.transform;var x=i.scale,_=d,b=h;s=[(g[0]-c.x)/_,(g[1]-c.y)/b],p&&o(g,g,p),_/=x[0]*T,b/=x[1]*T;var w=M(_,b);l=0/w,u=2*n.r/w-l}var S=n.colorStops.slice();S.sort((function(e,t){return e.offset-t.offset}));for(var I=S.length,A=[],D=[],L=0;L<I;L++){var C=S[L],P=B(C.color);D.push(C.offset*u+l+" "+P[0]),0!==L&&L!==I-1||A.push(P)}if(I>=2){var N=A[0][0],R=A[1][0],V=A[0][1]*t.opacity,k=A[1][1]*t.opacity;e.type=a,e.method="none",e.focus="100%",e.angle=r,e.color=N,e.color2=R,e.colors=D.join(","),e.opacity=k,e.opacity2=V}"radial"===a&&(e.focusposition=s.join(","))}else O(e,n,t.opacity)},H=function(e,t){null!=t.lineDash&&(e.dashstyle=t.lineDash.join(" ")),null==t.stroke||t.stroke instanceof m||O(e,t.stroke,t.opacity)},W=function(e,t,i,n){var a="fill"===t,o=e.getElementsByTagName(t)[0];null!=i[t]&&"none"!==i[t]&&(a||!a&&i.lineWidth)?(e[a?"filled":"stroked"]="true",i[t]instanceof m&&k(e,o),o||(o=v.createNode(t)),a?G(o,i,n):H(o,i),V(e,o)):(e[a?"filled":"stroked"]="false",k(e,o))},F=[[],[],[]],Z=function(e,t){var i,n,a,r,s,l,u=y.M,c=y.C,d=y.L,h=y.A,p=y.Q,g=[],f=e.data,m=e.len();for(r=0;r<m;){switch(a=f[r++],n="",i=0,a){case u:n=" m ",i=1,s=f[r++],l=f[r++],F[0][0]=s,F[0][1]=l;break;case d:n=" l ",i=1,s=f[r++],l=f[r++],F[0][0]=s,F[0][1]=l;break;case p:case c:n=" c ",i=3;var v,b,M=f[r++],A=f[r++],L=f[r++],C=f[r++];a===p?(v=L,b=C,L=(L+2*M)/3,C=(C+2*A)/3,M=(s+2*M)/3,A=(l+2*A)/3):(v=f[r++],b=f[r++]),F[0][0]=M,F[0][1]=A,F[1][0]=L,F[1][1]=C,F[2][0]=v,F[2][1]=b,s=v,l=b;break;case h:var P=0,N=0,R=1,V=1,k=0;t&&(P=t[4],N=t[5],R=_(t[0]*t[0]+t[1]*t[1]),V=_(t[2]*t[2]+t[3]*t[3]),k=Math.atan2(-t[1]/V,t[0]/R));var E=f[r++],z=f[r++],O=f[r++],B=f[r++],G=f[r++]+k,H=f[r++]+G+k;r++;var W=f[r++],Z=E+w(G)*O,U=z+S(G)*B,j=(M=E+w(H)*O,A=z+S(H)*B,W?" wa ":" at ");Math.abs(Z-M)<1e-4&&(Math.abs(H-G)>.01?W&&(Z+=270/T):Math.abs(U-z)<1e-4?W&&Z<E||!W&&Z>E?A-=270/T:A+=270/T:W&&U<z||!W&&U>z?M+=270/T:M-=270/T),g.push(j,x(((E-O)*R+P)*T-D),I,x(((z-B)*V+N)*T-D),I,x(((E+O)*R+P)*T-D),I,x(((z+B)*V+N)*T-D),I,x((Z*R+P)*T-D),I,x((U*V+N)*T-D),I,x((M*R+P)*T-D),I,x((A*V+N)*T-D)),s=M,l=A;break;case y.R:var X=F[0],Y=F[1];X[0]=f[r++],X[1]=f[r++],Y[0]=X[0]+f[r++],Y[1]=X[1]+f[r++],t&&(o(X,X,t),o(Y,Y,t)),X[0]=x(X[0]*T-D),Y[0]=x(Y[0]*T-D),X[1]=x(X[1]*T-D),Y[1]=x(Y[1]*T-D),g.push(" m ",X[0],I,X[1]," l ",Y[0],I,X[1]," l ",Y[0],I,Y[1]," l ",X[0],I,Y[1]);break;case y.Z:g.push(" x ")}if(i>0){g.push(n);for(var q=0;q<i;q++){var K=F[q];t&&o(K,K,t),g.push(x(K[0]*T-D),I,x(K[1]*T-D),q<i-1?I:"")}}}return g.join("")};g.prototype.brushVML=function(e){var t=this.style,i=this._vmlEl;i||(i=v.createNode("shape"),P(i),this._vmlEl=i),W(i,"fill",t,this),W(i,"stroke",t,this);var n=this.transform,a=null!=n,o=i.getElementsByTagName("stroke")[0];if(o){var r=t.lineWidth;if(a&&!t.strokeNoScale){var s=n[0]*n[3]-n[1]*n[2];r*=_(b(s))}o.weight=r+"px"}var l=this.path||(this.path=new f);this.__dirtyPath&&(l.beginPath(),l.subPixelOptimize=!1,this.buildPath(l,this.shape),l.toStatic(),this.__dirtyPath=!1),i.path=Z(l,this.transform),i.style.zIndex=E(this.zlevel,this.z,this.z2),V(e,i),null!=t.text?this.drawRectText(e,this.getBoundingRect()):this.removeRectText(e)},g.prototype.onRemove=function(e){k(e,this._vmlEl),this.removeRectText(e)},g.prototype.onAdd=function(e){V(e,this._vmlEl),this.appendRectText(e)};var U=function(e){return"object"===typeof e&&e.tagName&&"IMG"===e.tagName.toUpperCase()};h.prototype.brushVML=function(e){var t,i,n=this.style,a=n.image;if(U(a)){var r=a.src;if(r===this._imageSrc)t=this._imageWidth,i=this._imageHeight;else{var s=a.runtimeStyle,l=s.width,u=s.height;s.width="auto",s.height="auto",t=a.width,i=a.height,s.width=l,s.height=u,this._imageSrc=r,this._imageWidth=t,this._imageHeight=i}a=r}else a===this._imageSrc&&(t=this._imageWidth,i=this._imageHeight);if(a){var c=n.x||0,d=n.y||0,h=n.width,p=n.height,g=n.sWidth,f=n.sHeight,m=n.sx||0,y=n.sy||0,b=g&&f,w=this._vmlEl;w||(w=v.doc.createElement("div"),P(w),this._vmlEl=w);var S,T=w.style,D=!1,L=1,C=1;if(this.transform&&(S=this.transform,L=_(S[0]*S[0]+S[1]*S[1]),C=_(S[2]*S[2]+S[3]*S[3]),D=S[1]||S[2]),D){var N=[c,d],R=[c+h,d],k=[c,d+p],z=[c+h,d+p];o(N,N,S),o(R,R,S),o(k,k,S),o(z,z,S);var O=M(N[0],R[0],k[0],z[0]),B=M(N[1],R[1],k[1],z[1]),G=[];G.push("M11=",S[0]/L,I,"M12=",S[2]/C,I,"M21=",S[1]/L,I,"M22=",S[3]/C,I,"Dx=",x(c*L+S[4]),I,"Dy=",x(d*C+S[5])),T.padding="0 "+x(O)+"px "+x(B)+"px 0",T.filter=A+".Matrix("+G.join("")+", SizingMethod=clip)"}else S&&(c=c*L+S[4],d=d*C+S[5]),T.filter="",T.left=x(c)+"px",T.top=x(d)+"px";var H=this._imageEl,W=this._cropEl;H||(H=v.doc.createElement("div"),this._imageEl=H);var F=H.style;if(b){if(t&&i)F.width=x(L*t*h/g)+"px",F.height=x(C*i*p/f)+"px";else{var Z=new Image,j=this;Z.onload=function(){Z.onload=null,t=Z.width,i=Z.height,F.width=x(L*t*h/g)+"px",F.height=x(C*i*p/f)+"px",j._imageWidth=t,j._imageHeight=i,j._imageSrc=a},Z.src=a}W||(W=v.doc.createElement("div"),W.style.overflow="hidden",this._cropEl=W);var X=W.style;X.width=x((h+m*h/g)*L),X.height=x((p+y*p/f)*C),X.filter=A+".Matrix(Dx="+-m*h/g*L+",Dy="+-y*p/f*C+")",W.parentNode||w.appendChild(W),H.parentNode!==W&&W.appendChild(H)}else F.width=x(L*h)+"px",F.height=x(C*p)+"px",w.appendChild(H),W&&W.parentNode&&(w.removeChild(W),this._cropEl=null);var Y="",q=n.opacity;q<1&&(Y+=".Alpha(opacity="+x(100*q)+") "),Y+=A+".AlphaImageLoader(src="+a+", SizingMethod=scale)",F.filter=Y,w.style.zIndex=E(this.zlevel,this.z,this.z2),V(e,w),null!=n.text&&this.drawRectText(e,this.getBoundingRect())}},h.prototype.onRemove=function(e){k(e,this._vmlEl),this._vmlEl=null,this._cropEl=null,this._imageEl=null,this.removeRectText(e)},h.prototype.onAdd=function(e){V(e,this._vmlEl),this.appendRectText(e)};var j,X="normal",Y={},q=0,K=100,$=document.createElement("div"),J=function(e){var t=Y[e];if(!t){q>K&&(q=0,Y={});var i,n=$.style;try{n.font=e,i=n.fontFamily.split(",")[0]}catch(a){}t={style:n.fontStyle||X,variant:n.fontVariant||X,weight:n.fontWeight||X,size:0|parseFloat(n.fontSize||12),family:i||"Microsoft YaHei"},Y[e]=t,q++}return t};l.$override("measureText",(function(e,t){var i=v.doc;j||(j=i.createElement("div"),j.style.cssText="position:absolute;top:-20000px;left:0;padding:0;margin:0;border:none;white-space:pre;",v.doc.body.appendChild(j));try{j.style.font=t}catch(n){}return j.innerHTML="",j.appendChild(i.createTextNode(e)),{width:j.offsetWidth}}));for(var Q=new r,ee=function(e,t,i,n){var a=this.style;this.__dirty&&u.normalizeTextStyle(a,!0);var r=a.text;if(null!=r&&(r+=""),r){if(a.rich){var s=l.parseRichText(r,a);r=[];for(var c=0;c<s.lines.length;c++){for(var d=s.lines[c].tokens,h=[],p=0;p<d.length;p++)h.push(d[p].text);r.push(h.join(""))}r=r.join("\n")}var g,f,m=a.textAlign,y=a.textVerticalAlign,_=J(a.font),b=_.style+" "+_.variant+" "+_.weight+" "+_.size+'px "'+_.family+'"';i=i||l.getBoundingRect(r,b,m,y,a.textPadding,a.textLineHeight);var w=this.transform;if(w&&!n&&(Q.copy(t),Q.applyTransform(w),t=Q),n)g=t.x,f=t.y;else{var S=a.textPosition,M=a.textDistance;if(S instanceof Array)g=t.x+z(S[0],t.width),f=t.y+z(S[1],t.height),m=m||"left";else{var A=l.adjustTextPositionOnRect(S,t,M);g=A.x,f=A.y,m=m||A.textAlign,y=y||A.textVerticalAlign}}g=l.adjustTextX(g,i.width,m),f=l.adjustTextY(f,i.height,y),f+=i.height/2;var T,D,L,C=v.createNode,R=this._textVmlEl;R?(L=R.firstChild,T=L.nextSibling,D=T.nextSibling):(R=C("line"),T=C("path"),D=C("textpath"),L=C("skew"),D.style["v-text-align"]="left",P(R),T.textpathok=!0,D.on=!0,R.from="0 0",R.to="1000 0.05",V(R,L),V(R,T),V(R,D),this._textVmlEl=R);var k=[g,f],O=R.style;w&&n?(o(k,k,w),L.on=!0,L.matrix=w[0].toFixed(3)+I+w[2].toFixed(3)+I+w[1].toFixed(3)+I+w[3].toFixed(3)+",0,0",L.offset=(x(k[0])||0)+","+(x(k[1])||0),L.origin="0 0",O.left="0px",O.top="0px"):(L.on=!1,O.left=x(g)+"px",O.top=x(f)+"px"),D.string=N(r);try{D.style.font=b}catch(B){}W(R,"fill",{fill:a.textFill,opacity:a.opacity},this),W(R,"stroke",{stroke:a.textStroke,opacity:a.opacity,lineDash:a.lineDash},this),R.style.zIndex=E(this.zlevel,this.z,this.z2),V(e,R)}},te=function(e){k(e,this._textVmlEl),this._textVmlEl=null},ie=function(e){V(e,this._textVmlEl)},ne=[c,d,h,g,p],ae=0;ae<ne.length;ae++){var oe=ne[ae].prototype;oe.drawRectText=ee,oe.removeRectText=te,oe.appendRectText=ie}p.prototype.brushVML=function(e){var t=this.style;null!=t.text?this.drawRectText(e,{x:t.x||0,y:t.y||0,width:0,height:0},this.getBoundingRect(),!0):this.removeRectText(e)},p.prototype.onRemove=function(e){this.removeRectText(e)},p.prototype.onAdd=function(e){this.appendRectText(e)}}},a890:function(e,t,i){var n=i("0655"),a=i("9850"),o={lineX:r(0),lineY:r(1),rect:{point:function(e,t,i){return e&&i.boundingRect.contain(e[0],e[1])},rect:function(e,t,i){return e&&i.boundingRect.intersect(e)}},polygon:{point:function(e,t,i){return e&&i.boundingRect.contain(e[0],e[1])&&n.contain(i.range,e[0],e[1])},rect:function(e,t,i){var o=i.range;if(!e||o.length<=1)return!1;var r=e.x,s=e.y,u=e.width,c=e.height,d=o[0];return!!(n.contain(o,r,s)||n.contain(o,r+u,s)||n.contain(o,r,s+c)||n.contain(o,r+u,s+c)||a.create(e).contain(d[0],d[1])||l(r,s,r+u,s,o)||l(r,s,r,s+c,o)||l(r+u,s,r+u,s+c,o)||l(r,s+c,r+u,s+c,o))||void 0}}};function r(e){var t=["x","y"],i=["width","height"];return{point:function(t,i,n){if(t){var a=n.range,o=t[e];return s(o,a)}},rect:function(n,a,o){if(n){var r=o.range,l=[n[t[e]],n[t[e]]+n[i[e]]];return l[1]<l[0]&&l.reverse(),s(l[0],r)||s(l[1],r)||s(r[0],l)||s(r[1],l)}}}}function s(e,t){return t[0]<=e&&e<=t[1]}function l(e,t,i,n,a){for(var o=0,r=a[a.length-1];o<a.length;o++){var s=a[o];if(u(e,t,i,n,s[0],s[1],r[0],r[1]))return!0;r=s}}function u(e,t,i,n,a,o,r,s){var l=d(i-e,a-r,n-t,o-s);if(c(l))return!1;var u=d(a-e,a-r,o-t,o-s)/l;if(u<0||u>1)return!1;var h=d(i-e,a-e,n-t,o-t)/l;return!(h<0||h>1)}function c(e){return e<=1e-6&&e>=-1e-6}function d(e,t,i,n){return e*n-t*i}var h=o;e.exports=h},a8c6:function(e,t,i){var n=i("2449"),a=n.extend({type:"markPoint",defaultOption:{zlevel:0,z:5,symbol:"pin",symbolSize:50,tooltip:{trigger:"item"},label:{show:!0,position:"inside"},itemStyle:{borderWidth:2},emphasis:{label:{show:!0}}}});e.exports=a},a96b:function(e,t,i){var n=i("3eba"),a=n.extendComponentModel({type:"tooltip",dependencies:["axisPointer"],defaultOption:{zlevel:0,z:60,show:!0,showContent:!0,trigger:"item",triggerOn:"mousemove|click",alwaysShowContent:!1,displayMode:"single",renderMode:"auto",confine:!1,showDelay:0,hideDelay:100,transitionDuration:.4,enterable:!1,backgroundColor:"rgba(50,50,50,0.7)",borderColor:"#333",borderRadius:4,borderWidth:0,padding:5,extraCssText:"",axisPointer:{type:"line",axis:"auto",animation:"auto",animationDurationUpdate:200,animationEasingUpdate:"exponentialOut",crossStyle:{color:"#999",width:1,type:"dashed",textStyle:{}}},textStyle:{color:"#fff",fontSize:14}}});e.exports=a},aa01:function(e,t,i){var n=i("6d8b"),a=i("4f85"),o=i("06c7"),r=i("55ac"),s=r.wrapTreePathInfo,l=a.extend({type:"series.sunburst",_viewRoot:null,getInitialData:function(e,t){var i={name:e.name,children:e.data};u(i);var n=e.levels||[],a={};return a.levels=n,o.createTree(i,this,a).data},optionUpdated:function(){this.resetViewRoot()},getDataParams:function(e){var t=a.prototype.getDataParams.apply(this,arguments),i=this.getData().tree.getNodeByDataIndex(e);return t.treePathInfo=s(i,this),t},defaultOption:{zlevel:0,z:2,center:["50%","50%"],radius:[0,"75%"],clockwise:!0,startAngle:90,minAngle:0,percentPrecision:2,stillShowZeroSum:!0,highlightPolicy:"descendant",nodeClick:"rootToNode",renderLabelForZeroData:!1,label:{rotate:"radial",show:!0,opacity:1,align:"center",position:"inside",distance:5,silent:!0,emphasis:{}},itemStyle:{borderWidth:1,borderColor:"white",borderType:"solid",shadowBlur:0,shadowColor:"rgba(0, 0, 0, 0.2)",shadowOffsetX:0,shadowOffsetY:0,opacity:1,emphasis:{},highlight:{opacity:1},downplay:{opacity:.9}},animationType:"expansion",animationDuration:1e3,animationDurationUpdate:500,animationEasing:"cubicOut",data:[],levels:[],sort:"desc"},getViewRoot:function(){return this._viewRoot},resetViewRoot:function(e){e?this._viewRoot=e:e=this._viewRoot;var t=this.getRawData().tree.root;e&&(e===t||t.contains(e))||(this._viewRoot=t)}});function u(e){var t=0;n.each(e.children,(function(e){u(e);var i=e.value;n.isArray(i)&&(i=i[0]),t+=i}));var i=e.value;n.isArray(i)&&(i=i[0]),(null==i||isNaN(i))&&(i=t),i<0&&(i=0),n.isArray(e.value)?e.value[0]=i:e.value=i}e.exports=l},aa3e:function(e,t,i){var n=i("6d8b");function a(e,t){return t=t||[0,0],n.map(["x","y"],(function(i,n){var a=this.getAxis(i),o=t[n],r=e[n]/2;return"category"===a.type?a.getBandWidth():Math.abs(a.dataToCoord(o-r)-a.dataToCoord(o+r))}),this)}function o(e){var t=e.grid.getRect();return{coordSys:{type:"cartesian2d",x:t.x,y:t.y,width:t.width,height:t.height},api:{coord:function(t){return e.dataToPoint(t)},size:n.bind(a,e)}}}e.exports=o},aadf:function(e,t,i){var n=i("3eba"),a=i("6d8b");i("5aa9"),i("d6d9"),i("3329");var o=i("9d57"),r=o.layout,s=i("7f96");i("01ed"),n.registerLayout(a.curry(r,"pictorialBar")),n.registerVisual(s("pictorialBar","roundRect"))},ab05:function(e,t,i){var n=i("3eba"),a=i("6d8b"),o=i("2b8c"),r=i("5f14"),s=n.PRIORITY.VISUAL.COMPONENT;function l(e,t,i,n){for(var a=t.targetVisuals[n],o=r.prepareVisualTypes(a),s={color:e.getData().getVisual("color")},l=0,u=o.length;l<u;l++){var c=o[l],d=a["opacity"===c?"__alphaForOpacity":c];d&&d.applyVisual(i,h,p)}return s.color;function h(e){return s[e]}function p(e,t){s[e]=t}}n.registerVisual(s,{createOnAllSeries:!0,reset:function(e,t){var i=[];return t.eachComponent("visualMap",(function(t){var n=e.pipelineContext;!t.isTargetSeries(e)||n&&n.large||i.push(o.incrementalApplyVisual(t.stateList,t.targetVisuals,a.bind(t.getValueState,t),t.getDataDimension(e.getData())))})),i}}),n.registerVisual(s,{createOnAllSeries:!0,reset:function(e,t){var i=e.getData(),n=[];t.eachComponent("visualMap",(function(t){if(t.isTargetSeries(e)){var o=t.getVisualMeta(a.bind(l,null,e,t))||{stops:[],outerColors:[]},r=t.getDataDimension(i),s=i.getDimensionInfo(r);null!=s&&(o.dimension=s.index,n.push(o))}})),e.getData().setVisual("visualMeta",n)}})},ab71:function(e,t){var i=["lineStyle","normal","opacity"],n={seriesType:"parallel",reset:function(e,t,n){var a=e.getModel("itemStyle"),o=e.getModel("lineStyle"),r=t.get("color"),s=o.get("color")||a.get("color")||r[e.seriesIndex%r.length],l=e.get("inactiveOpacity"),u=e.get("activeOpacity"),c=e.getModel("lineStyle").getLineStyle(),d=e.coordinateSystem,h=e.getData(),p={normal:c.opacity,active:u,inactive:l};function g(e,t){d.eachActiveState(t,(function(e,n){var a=p[e];if("normal"===e&&t.hasItemOption){var o=t.getItemModel(n).get(i,!0);null!=o&&(a=o)}t.setItemVisual(n,"opacity",a)}),e.start,e.end)}return h.setVisual("color",s),{progress:g}}};e.exports=n},abff:function(e,t,i){var n=i("3eba"),a=i("f706"),o=i("c965"),r=i("87c3");n.extendChartView({type:"scatter",render:function(e,t,i){var n=e.getData(),a=this._updateSymbolDraw(n,e);a.updateData(n),this._finished=!0},incrementalPrepareRender:function(e,t,i){var n=e.getData(),a=this._updateSymbolDraw(n,e);a.incrementalPrepareUpdate(n),this._finished=!1},incrementalRender:function(e,t,i){this._symbolDraw.incrementalUpdate(e,t.getData()),this._finished=e.end===t.getData().count()},updateTransform:function(e,t,i){var n=e.getData();if(this.group.dirty(),!this._finished||n.count()>1e4||!this._symbolDraw.isPersistent())return{update:!0};var a=r().reset(e);a.progress&&a.progress({start:0,end:n.count()},n),this._symbolDraw.updateLayout(n)},_updateSymbolDraw:function(e,t){var i=this._symbolDraw,n=t.pipelineContext,r=n.large;return i&&r===this._isLargeDraw||(i&&i.remove(),i=this._symbolDraw=r?new o:new a,this._isLargeDraw=r,this.group.removeAll()),this.group.add(i.group),i},remove:function(e,t){this._symbolDraw&&this._symbolDraw.remove(!0),this._symbolDraw=null},dispose:function(){}})},adda:function(e,t,i){var n=i("94e4"),a=n.circularLayout;function o(e){e.eachSeriesByType("graph",(function(e){"circular"===e.get("layout")&&a(e)}))}e.exports=o},adf4:function(e,t,i){var n=i("4f85"),a=i("b1d4"),o=i("2f45"),r=o.getDimensionTypeByAxis,s=i("6179"),l=i("6d8b"),u=i("e0d3"),c=u.groupData,d=i("eda2"),h=d.encodeHTML,p=2,g=n.extend({type:"series.themeRiver",dependencies:["singleAxis"],nameMap:null,init:function(e){g.superApply(this,"init",arguments),this.legendDataProvider=function(){return this.getRawData()}},fixData:function(e){var t=e.length,i=c(e,(function(e){return e[2]})),n=[];i.buckets.each((function(e,t){n.push({name:t,dataList:e})}));for(var a=n.length,o=-1,r=-1,s=0;s<a;++s){var l=n[s].dataList.length;l>o&&(o=l,r=s)}for(var u=0;u<a;++u)if(u!==r)for(var d=n[u].name,h=0;h<o;++h){for(var p=n[r].dataList[h][0],g=n[u].dataList.length,f=-1,m=0;m<g;++m){var v=n[u].dataList[m][0];if(v===p){f=m;break}}-1===f&&(e[t]=[],e[t][0]=p,e[t][1]=0,e[t][2]=d,t++)}return e},getInitialData:function(e,t){for(var i=t.queryComponents({mainType:"singleAxis",index:this.get("singleAxisIndex"),id:this.get("singleAxisId")})[0],n=i.get("type"),o=l.filter(e.data,(function(e){return void 0!==e[2]})),u=this.fixData(o||[]),c=[],d=this.nameMap=l.createHashMap(),h=0,g=0;g<u.length;++g)c.push(u[g][p]),d.get(u[g][p])||(d.set(u[g][p],h),h++);var f=a(u,{coordDimensions:["single"],dimensionsDefine:[{name:"time",type:r(n)},{name:"value",type:"float"},{name:"name",type:"ordinal"}],encodeDefine:{single:0,value:1,itemName:2}}),m=new s(f,this);return m.initData(u),m},getLayerSeries:function(){for(var e=this.getData(),t=e.count(),i=[],n=0;n<t;++n)i[n]=n;var a=e.mapDimension("single"),o=c(i,(function(t){return e.get("name",t)})),r=[];return o.buckets.each((function(t,i){t.sort((function(t,i){return e.get(a,t)-e.get(a,i)})),r.push({name:i,indices:t})})),r},getAxisTooltipData:function(e,t,i){l.isArray(e)||(e=e?[e]:[]);for(var n,a=this.getData(),o=this.getLayerSeries(),r=[],s=o.length,u=0;u<s;++u){for(var c=Number.MAX_VALUE,d=-1,h=o[u].indices.length,p=0;p<h;++p){var g=a.get(e[0],o[u].indices[p]),f=Math.abs(g-t);f<=c&&(n=g,c=f,d=o[u].indices[p])}r.push(d)}return{dataIndices:r,nestestValue:n}},formatTooltip:function(e){var t=this.getData(),i=t.getName(e),n=t.get(t.mapDimension("value"),e);return(isNaN(n)||null==n)&&(n="-"),h(i+" : "+n)},defaultOption:{zlevel:0,z:2,coordinateSystem:"singleAxis",boundaryGap:["10%","10%"],singleAxisIndex:0,animationEasing:"linear",label:{margin:4,show:!0,position:"left",color:"#000",fontSize:11},emphasis:{label:{show:!0}}}}),f=g;e.exports=f},ae46:function(e,t,i){var n=i("4e08"),a=(n.__DEV__,i("3eba")),o=i("73ca"),r=i("0fd3"),s=i("7e5b"),l=i("4527"),u=i("6a4c"),c=i("a38d"),d=i("6582"),h=a.extendChartView({type:"lines",init:function(){},render:function(e,t,i){var n=e.getData(),a=this._updateLineDraw(n,e),o=e.get("zlevel"),r=e.get("effect.trailLength"),s=i.getZr(),l="svg"===s.painter.getType();l||s.painter.getLayer(o).clear(!0),null==this._lastZlevel||l||s.configLayer(this._lastZlevel,{motionBlur:!1}),this._showEffect(e)&&r&&(l||s.configLayer(o,{motionBlur:!0,lastFrameAlpha:Math.max(Math.min(r/10+.9,1),0)})),a.updateData(n),this._lastZlevel=o,this._finished=!0},incrementalPrepareRender:function(e,t,i){var n=e.getData(),a=this._updateLineDraw(n,e);a.incrementalPrepareUpdate(n),this._clearLayer(i),this._finished=!1},incrementalRender:function(e,t,i){this._lineDraw.incrementalUpdate(e,t.getData()),this._finished=e.end===t.getData().count()},updateTransform:function(e,t,i){var n=e.getData(),a=e.pipelineContext;if(!this._finished||a.large||a.progressiveRender)return{update:!0};var o=d.reset(e);o.progress&&o.progress({start:0,end:n.count()},n),this._lineDraw.updateLayout(),this._clearLayer(i)},_updateLineDraw:function(e,t){var i=this._lineDraw,n=this._showEffect(t),a=!!t.get("polyline"),d=t.pipelineContext,h=d.large;return i&&n===this._hasEffet&&a===this._isPolyline&&h===this._isLargeDraw||(i&&i.remove(),i=this._lineDraw=h?new c:new o(a?n?u:l:n?r:s),this._hasEffet=n,this._isPolyline=a,this._isLargeDraw=h,this.group.removeAll()),this.group.add(i.group),i},_showEffect:function(e){return!!e.get("effect.show")},_clearLayer:function(e){var t=e.getZr(),i="svg"===t.painter.getType();i||null==this._lastZlevel||t.painter.getLayer(this._lastZlevel).clear(!0)},remove:function(e,t){this._lineDraw&&this._lineDraw.remove(),this._lineDraw=null,this._clearLayer(t)},dispose:function(){}});e.exports=h},ae75:function(e,t,i){var n=i("6d8b"),a=["rect","polygon","keep","clear"];function o(e,t){var i=e&&e.brush;if(n.isArray(i)||(i=i?[i]:[]),i.length){var o=[];n.each(i,(function(e){var t=e.hasOwnProperty("toolbox")?e.toolbox:[];t instanceof Array&&(o=o.concat(t))}));var s=e&&e.toolbox;n.isArray(s)&&(s=s[0]),s||(s={feature:{}},e.toolbox=[s]);var l=s.feature||(s.feature={}),u=l.brush||(l.brush={}),c=u.type||(u.type=[]);c.push.apply(c,o),r(c),t&&!c.length&&c.push.apply(c,a)}}function r(e){var t={};n.each(e,(function(e){t[e]=1})),e.length=0,n.each(t,(function(t,i){e.push(i)}))}e.exports=o},b006:function(e,t,i){var n=i("3eba"),a=i("6d8b"),o=i("fab22"),r=i("fc82"),s=i("f4a2"),l=i("2306"),u=["axisLine","axisTickLabel","axisName"],c=n.extendComponentView({type:"parallelAxis",init:function(e,t){c.superApply(this,"init",arguments),(this._brushController=new r(t.getZr())).on("brush",a.bind(this._onBrush,this))},render:function(e,t,i,n){if(!d(e,t,n)){this.axisModel=e,this.api=i,this.group.removeAll();var r=this._axisGroup;if(this._axisGroup=new l.Group,this.group.add(this._axisGroup),e.get("show")){var s=p(e,t),c=s.coordinateSystem,h=e.getAreaSelectStyle(),g=h.width,f=e.axis.dim,m=c.getAxisLayout(f),v=a.extend({strokeContainThreshold:g},m),y=new o(e,v);a.each(u,y.add,y),this._axisGroup.add(y.getGroup()),this._refreshBrushController(v,h,e,s,g,i);var x=n&&!1===n.animation?null:e;l.groupTransition(r,this._axisGroup,x)}}},_refreshBrushController:function(e,t,i,n,a,o){var r=i.axis.getExtent(),u=r[1]-r[0],c=Math.min(30,.1*Math.abs(u)),d=l.BoundingRect.create({x:r[0],y:-a/2,width:u,height:a});d.x-=c,d.width+=2*c,this._brushController.mount({enableGlobalPan:!0,rotation:e.rotation,position:e.position}).setPanels([{panelId:"pl",clipPath:s.makeRectPanelClipPath(d),isTargetByCursor:s.makeRectIsTargetByCursor(d,o,n),getLinearBrushOtherExtent:s.makeLinearBrushOtherExtent(d,0)}]).enableBrush({brushType:"lineX",brushStyle:t,removeOnClick:!0}).updateCovers(h(i))},_onBrush:function(e,t){var i=this.axisModel,n=i.axis,o=a.map(e,(function(e){return[n.coordToData(e.range[0],!0),n.coordToData(e.range[1],!0)]}));(!i.option.realtime===t.isEnd||t.removeOnClick)&&this.api.dispatchAction({type:"axisAreaSelect",parallelAxisId:i.id,intervals:o})},dispose:function(){this._brushController.dispose()}});function d(e,t,i){return i&&"axisAreaSelect"===i.type&&t.findComponents({mainType:"parallelAxis",query:i})[0]===e}function h(e){var t=e.axis;return a.map(e.activeIntervals,(function(e){return{brushType:"lineX",panelId:"pl",range:[t.dataToCoord(e[0],!0),t.dataToCoord(e[1],!0)]}}))}function p(e,t){return t.getComponent("parallel",e.get("parallelIndex"))}var g=c;e.exports=g},b11c:function(e,t,i){i("8ec5"),i("db9e"),i("4e9f"),i("d3a0"),i("767c"),i("7c4d"),i("df70")},b16f:function(e,t,i){var n=i("4ab1"),a=i("6d8b"),o=i("4942"),r=i("41ef");function s(e,t){n.call(this,e,t,["linearGradient","radialGradient"],"__gradient_in_use__")}a.inherits(s,n),s.prototype.addWithoutUpdate=function(e,t){if(t&&t.style){var i=this;a.each(["fill","stroke"],(function(n){if(t.style[n]&&("linear"===t.style[n].type||"radial"===t.style[n].type)){var a,o=t.style[n],r=i.getDefs(!0);o._dom?(a=o._dom,r.contains(o._dom)||i.addDom(a)):a=i.add(o),i.markUsed(t);var s=a.getAttribute("id");e.setAttribute(n,"url(#"+s+")")}}))}},s.prototype.add=function(e){var t;if("linear"===e.type)t=this.createElement("linearGradient");else{if("radial"!==e.type)return o("Illegal gradient type."),null;t=this.createElement("radialGradient")}return e.id=e.id||this.nextId++,t.setAttribute("id","zr"+this._zrId+"-gradient-"+e.id),this.updateDom(e,t),this.addDom(t),t},s.prototype.update=function(e){var t=this;n.prototype.update.call(this,e,(function(){var i=e.type,n=e._dom.tagName;"linear"===i&&"linearGradient"===n||"radial"===i&&"radialGradient"===n?t.updateDom(e,e._dom):(t.removeDom(e),t.add(e))}))},s.prototype.updateDom=function(e,t){if("linear"===e.type)t.setAttribute("x1",e.x),t.setAttribute("y1",e.y),t.setAttribute("x2",e.x2),t.setAttribute("y2",e.y2);else{if("radial"!==e.type)return void o("Illegal gradient type.");t.setAttribute("cx",e.x),t.setAttribute("cy",e.y),t.setAttribute("r",e.r)}e.global?t.setAttribute("gradientUnits","userSpaceOnUse"):t.setAttribute("gradientUnits","objectBoundingBox"),t.innerHTML="";for(var i=e.colorStops,n=0,a=i.length;n<a;++n){var s=this.createElement("stop");s.setAttribute("offset",100*i[n].offset+"%");var l=i[n].color;if(l.indexOf(!1)){var u=r.parse(l)[3],c=r.toHex(l);s.setAttribute("stop-color","#"+c),s.setAttribute("stop-opacity",u)}else s.setAttribute("stop-color",i[n].color);t.appendChild(s)}e._dom=t},s.prototype.markUsed=function(e){if(e.style){var t=e.style.fill;t&&t._dom&&n.prototype.markUsed.call(this,t._dom),t=e.style.stroke,t&&t._dom&&n.prototype.markUsed.call(this,t._dom)}};var l=s;e.exports=l},b336:function(e,t,i){var n=i("6d8b"),a=n.each;function o(e){var t=e&&e.visualMap;n.isArray(t)||(t=t?[t]:[]),a(t,(function(e){if(e){r(e,"splitList")&&!r(e,"pieces")&&(e.pieces=e.splitList,delete e.splitList);var t=e.pieces;t&&n.isArray(t)&&a(t,(function(e){n.isObject(e)&&(r(e,"start")&&!r(e,"min")&&(e.min=e.start),r(e,"end")&&!r(e,"max")&&(e.max=e.end))}))}}))}function r(e,t){return e&&e.hasOwnProperty&&e.hasOwnProperty(t)}e.exports=o},b369:function(e,t,i){var n=i("3eba"),a=i("6d8b"),o=i("2306"),r=i("80f0"),s=i("55ac"),l=i("f610"),u=i("4a01"),c=i("9850"),d=i("1687"),h=i("e6cd"),p=i("282b"),g=a.bind,f=o.Group,m=o.Rect,v=a.each,y=3,x=["label"],_=["emphasis","label"],b=["upperLabel"],w=["emphasis","upperLabel"],S=10,M=1,I=2,A=p([["fill","color"],["stroke","strokeColor"],["lineWidth","strokeWidth"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"]]),T=function(e){var t=A(e);return t.stroke=t.fill=t.lineWidth=null,t},D=n.extendChartView({type:"treemap",init:function(e,t){this._containerGroup,this._storage=L(),this._oldTree,this._breadcrumb,this._controller,this._state="ready"},render:function(e,t,i,n){var o=t.findComponents({mainType:"series",subType:"treemap",query:n});if(!(a.indexOf(o,e)<0)){this.seriesModel=e,this.api=i,this.ecModel=t;var r=["treemapZoomToNode","treemapRootToNode"],l=s.retrieveTargetInfo(n,r,e),u=n&&n.type,c=e.layoutInfo,d=!this._oldTree,h=this._storage,p="treemapRootToNode"===u&&l&&h?{rootNodeGroup:h.nodeGroup[l.node.getRawIndex()],direction:n.direction}:null,g=this._giveContainerGroup(c),f=this._doRender(g,e,p);d||u&&"treemapZoomToNode"!==u&&"treemapRootToNode"!==u?f.renderFinally():this._doAnimation(g,f,e,p),this._resetController(i),this._renderBreadcrumb(e,i,l)}},_giveContainerGroup:function(e){var t=this._containerGroup;return t||(t=this._containerGroup=new f,this._initEvents(t),this.group.add(t)),t.attr("position",[e.x,e.y]),t},_doRender:function(e,t,i){var n=t.getData().tree,o=this._oldTree,s=L(),l=L(),u=this._storage,c=[],d=a.curry(C,t,l,u,i,s,c);p(n.root?[n.root]:[],o&&o.root?[o.root]:[],e,n===o||!o,0);var h=g(u);return this._oldTree=n,this._storage=l,{lastsForAnimation:s,willDeleteEls:h,renderFinally:f};function p(e,t,i,n,o){function s(e){return e.getId()}function l(a,r){var s=null!=a?e[a]:null,l=null!=r?t[r]:null,u=d(s,l,i,o);u&&p(s&&s.viewChildren||[],l&&l.viewChildren||[],u,n,o+1)}n?(t=e,v(e,(function(e,t){!e.isRemoved()&&l(t,t)}))):new r(t,e,s,s).add(l).update(l).remove(a.curry(l,null)).execute()}function g(e){var t=L();return e&&v(e,(function(e,i){var n=t[i];v(e,(function(e){e&&(n.push(e),e.__tmWillDelete=1)}))})),t}function f(){v(h,(function(e){v(e,(function(e){e.parent&&e.parent.remove(e)}))})),v(c,(function(e){e.invisible=!0,e.dirty()}))}},_doAnimation:function(e,t,i,n){if(i.get("animation")){var o=i.get("animationDurationUpdate"),r=i.get("animationEasing"),s=h.createWrap();v(t.willDeleteEls,(function(e,t){v(e,(function(e,i){if(!e.invisible){var a,l=e.parent;if(n&&"drillDown"===n.direction)a=l===n.rootNodeGroup?{shape:{x:0,y:0,width:l.__tmNodeWidth,height:l.__tmNodeHeight},style:{opacity:0}}:{style:{opacity:0}};else{var u=0,c=0;l.__tmWillDelete||(u=l.__tmNodeWidth/2,c=l.__tmNodeHeight/2),a="nodeGroup"===t?{position:[u,c],style:{opacity:0}}:{shape:{x:u,y:c,width:0,height:0},style:{opacity:0}}}a&&s.add(e,a,o,r)}}))})),v(this._storage,(function(e,i){v(e,(function(e,n){var l=t.lastsForAnimation[i][n],u={};l&&("nodeGroup"===i?l.old&&(u.position=e.position.slice(),e.attr("position",l.old)):(l.old&&(u.shape=a.extend({},e.shape),e.setShape(l.old)),l.fadein?(e.setStyle("opacity",0),u.style={opacity:1}):1!==e.style.opacity&&(u.style={opacity:1})),s.add(e,u,o,r))}))}),this),this._state="animating",s.done(g((function(){this._state="ready",t.renderFinally()}),this)).start()}},_resetController:function(e){var t=this._controller;t||(t=this._controller=new u(e.getZr()),t.enable(this.seriesModel.get("roam")),t.on("pan",g(this._onPan,this)),t.on("zoom",g(this._onZoom,this)));var i=new c(0,0,e.getWidth(),e.getHeight());t.setPointerChecker((function(e,t,n){return i.contain(t,n)}))},_clearController:function(){var e=this._controller;e&&(e.dispose(),e=null)},_onPan:function(e){if("animating"!==this._state&&(Math.abs(e.dx)>y||Math.abs(e.dy)>y)){var t=this.seriesModel.getData().tree.root;if(!t)return;var i=t.getLayout();if(!i)return;this.api.dispatchAction({type:"treemapMove",from:this.uid,seriesId:this.seriesModel.id,rootRect:{x:i.x+e.dx,y:i.y+e.dy,width:i.width,height:i.height}})}},_onZoom:function(e){var t=e.originX,i=e.originY;if("animating"!==this._state){var n=this.seriesModel.getData().tree.root;if(!n)return;var a=n.getLayout();if(!a)return;var o=new c(a.x,a.y,a.width,a.height),r=this.seriesModel.layoutInfo;t-=r.x,i-=r.y;var s=d.create();d.translate(s,s,[-t,-i]),d.scale(s,s,[e.scale,e.scale]),d.translate(s,s,[t,i]),o.applyTransform(s),this.api.dispatchAction({type:"treemapRender",from:this.uid,seriesId:this.seriesModel.id,rootRect:{x:o.x,y:o.y,width:o.width,height:o.height}})}},_initEvents:function(e){e.on("click",(function(e){if("ready"===this._state){var t=this.seriesModel.get("nodeClick",!0);if(t){var i=this.findTarget(e.offsetX,e.offsetY);if(i){var n=i.node;if(n.getLayout().isLeafRoot)this._rootToNode(i);else if("zoomToNode"===t)this._zoomToNode(i);else if("link"===t){var a=n.hostTree.data.getItemModel(n.dataIndex),o=a.get("link",!0),r=a.get("target",!0)||"blank";o&&window.open(o,r)}}}}}),this)},_renderBreadcrumb:function(e,t,i){function n(t){"animating"!==this._state&&(s.aboveViewRoot(e.getViewRoot(),t)?this._rootToNode({node:t}):this._zoomToNode({node:t}))}i||(i=null!=e.get("leafDepth",!0)?{node:e.getViewRoot()}:this.findTarget(t.getWidth()/2,t.getHeight()/2),i||(i={node:e.getData().tree.root})),(this._breadcrumb||(this._breadcrumb=new l(this.group))).render(e,t,i.node,g(n,this))},remove:function(){this._clearController(),this._containerGroup&&this._containerGroup.removeAll(),this._storage=L(),this._state="ready",this._breadcrumb&&this._breadcrumb.remove()},dispose:function(){this._clearController()},_zoomToNode:function(e){this.api.dispatchAction({type:"treemapZoomToNode",from:this.uid,seriesId:this.seriesModel.id,targetNode:e.node})},_rootToNode:function(e){this.api.dispatchAction({type:"treemapRootToNode",from:this.uid,seriesId:this.seriesModel.id,targetNode:e.node})},findTarget:function(e,t){var i,n=this.seriesModel.getViewRoot();return n.eachNode({attr:"viewChildren",order:"preorder"},(function(n){var a=this._storage.background[n.getRawIndex()];if(a){var o=a.transformCoordToLocal(e,t),r=a.shape;if(!(r.x<=o[0]&&o[0]<=r.x+r.width&&r.y<=o[1]&&o[1]<=r.y+r.height))return!1;i={node:n,offsetX:o[0],offsetY:o[1]}}}),this),i}});function L(){return{nodeGroup:[],background:[],content:[]}}function C(e,t,i,n,r,s,l,u,c,d){if(l){var h=l.getLayout();if(h&&h.isInView){var p=h.width,g=h.height,v=h.borderWidth,y=h.invisible,S=l.getRawIndex(),D=u&&u.getRawIndex(),L=l.viewChildren,C=h.upperHeight,N=L&&L.length,R=l.getModel("itemStyle"),V=l.getModel("emphasis.itemStyle"),k=W("nodeGroup",f);if(k){if(c.add(k),k.attr("position",[h.x||0,h.y||0]),k.__tmNodeWidth=p,k.__tmNodeHeight=g,h.isAboveViewRoot)return k;var E=W("background",m,d,M);if(E&&O(k,E,N&&h.upperHeight),!N){var z=W("content",m,d,I);z&&B(k,z)}return k}}}function O(t,i,n){i.dataIndex=l.dataIndex,i.seriesIndex=e.seriesIndex,i.setShape({x:0,y:0,width:p,height:g});var a=l.getVisual("borderColor",!0),r=V.get("borderColor");G(i,(function(){var e=T(R);e.fill=a;var t=A(V);if(t.fill=r,n){var s=p-2*v;H(e,t,a,s,C,{x:v,y:0,width:s,height:C})}else e.text=t.text=null;i.setStyle(e),o.setHoverStyle(i,t)})),t.add(i)}function B(t,i){i.dataIndex=l.dataIndex,i.seriesIndex=e.seriesIndex;var n=Math.max(p-2*v,0),a=Math.max(g-2*v,0);i.culling=!0,i.setShape({x:v,y:v,width:n,height:a});var r=l.getVisual("color",!0);G(i,(function(){var e=T(R);e.fill=r;var t=A(V);H(e,t,r,n,a),i.setStyle(e),o.setHoverStyle(i,t)})),t.add(i)}function G(e,t){y?!e.invisible&&s.push(e):(t(),e.__tmWillVisible||(e.invisible=!1))}function H(t,i,n,r,s,u){var c=l.getModel(),d=a.retrieve(e.getFormattedLabel(l.dataIndex,"normal",null,null,u?"upperLabel":"label"),c.get("name"));if(!u&&h.isLeafRoot){var p=e.get("drillDownIcon",!0);d=p?p+" "+d:d}var g=c.getModel(u?b:x),f=c.getModel(u?w:_),m=g.getShallow("show");o.setLabelStyle(t,i,g,f,{defaultText:m?d:null,autoColor:n,isRectText:!0}),u&&(t.textRect=a.clone(u)),t.truncate=m&&g.get("ellipsis")?{outerWidth:r,outerHeight:s,minChar:2}:null}function W(e,n,a,o){var s=null!=D&&i[e][D],l=r[e];return s?(i[e][D]=null,F(l,s,e)):y||(s=new n({z:P(a,o)}),s.__tmDepth=a,s.__tmStorageName=e,Z(l,s,e)),t[e][S]=s}function F(e,t,i){var n=e[S]={};n.old="nodeGroup"===i?t.position.slice():a.extend({},t.shape)}function Z(e,t,i){var a=e[S]={},o=l.parentNode;if(o&&(!n||"drillDown"===n.direction)){var s=0,u=0,c=r.background[o.getRawIndex()];!n&&c&&c.old&&(s=c.old.width,u=c.old.height),a.old="nodeGroup"===i?[0,u]:{x:s,y:u,width:0,height:0}}a.fadein="nodeGroup"!==i}}function P(e,t){var i=e*S+t;return(i-1)/i}e.exports=D},b5c7:function(e,t,i){var n=i("282b"),a=n([["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["stroke","barBorderColor"],["lineWidth","barBorderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"]]),o={getBarItemStyle:function(e){var t=a(this,e);if(this.getBorderLineDash){var i=this.getBorderLineDash();i&&(t.lineDash=i)}return t}};e.exports=o},b8ec:function(e,t,i){var n=i("3eba");n.registerAction({type:"brush",event:"brush"},(function(e,t){t.eachComponent({mainType:"brush",query:e},(function(t){t.setAreas(e.areas)}))})),n.registerAction({type:"brushSelect",event:"brushSelected",update:"none"},(function(){}))},bb70:function(e,t,i){var n=i("e86a");function a(e,t,i,n,a,o,r){function s(t,i,n,a){for(var o=t;o<i;o++)if(e[o].y+=n,o>t&&o+1<i&&e[o+1].y>e[o].y+e[o].height)return void l(o,n/2);l(i-1,n/2)}function l(t,i){for(var n=t;n>=0;n--)if(e[n].y-=i,n>0&&e[n].y>e[n-1].y+e[n-1].height)break}function u(e,t,i,n,a,o){for(var r=t?Number.MAX_VALUE:0,s=0,l=e.length;s<l;s++){var u=Math.abs(e[s].y-n),c=e[s].len,d=e[s].len2,h=u<a+c?Math.sqrt((a+c+d)*(a+c+d)-u*u):Math.abs(e[s].x-i);t&&h>=r&&(h=r-10),!t&&h<=r&&(h=r+10),e[s].x=i+h*o,r=h}}e.sort((function(e,t){return e.y-t.y}));for(var c,d=0,h=e.length,p=[],g=[],f=0;f<h;f++)c=e[f].y-d,c<0&&s(f,h,-c,a),d=e[f].y+e[f].height;r-d<0&&l(h-1,d-r);for(f=0;f<h;f++)e[f].y>=i?g.push(e[f]):p.push(e[f]);u(p,!1,t,i,n,a),u(g,!0,t,i,n,a)}function o(e,t,i,n,o,s){for(var l=[],u=[],c=0;c<e.length;c++)r(e[c])||(e[c].x<t?l.push(e[c]):u.push(e[c]));a(u,t,i,n,1,o,s),a(l,t,i,n,-1,o,s);for(c=0;c<e.length;c++)if(!r(e[c])){var d=e[c].linePoints;if(d){var h=d[1][0]-d[2][0];e[c].x<t?d[2][0]=e[c].x+3:d[2][0]=e[c].x-3,d[1][1]=d[2][1]=e[c].y,d[1][0]=d[2][0]+h}}}function r(e){return"center"===e.position}function s(e,t,i,a){var r,s,l=e.getData(),u=[],c=!1;l.each((function(i){var a,o,d,h,p=l.getItemLayout(i),g=l.getItemModel(i),f=g.getModel("label"),m=f.get("position")||g.get("emphasis.label.position"),v=g.getModel("labelLine"),y=v.get("length"),x=v.get("length2"),_=(p.startAngle+p.endAngle)/2,b=Math.cos(_),w=Math.sin(_);r=p.cx,s=p.cy;var S="inside"===m||"inner"===m;if("center"===m)a=p.cx,o=p.cy,h="center";else{var M=(S?(p.r+p.r0)/2*b:p.r*b)+r,I=(S?(p.r+p.r0)/2*w:p.r*w)+s;if(a=M+3*b,o=I+3*w,!S){var A=M+b*(y+t-p.r),T=I+w*(y+t-p.r),D=A+(b<0?-1:1)*x,L=T;a=D+(b<0?-5:5),o=L,d=[[M,I],[A,T],[D,L]]}h=S?"center":b>0?"left":"right"}var C=f.getFont(),P=f.get("rotate")?b<0?-_+Math.PI:-_:0,N=e.getFormattedLabel(i,"normal")||l.getName(i),R=n.getBoundingRect(N,C,h,"top");c=!!P,p.label={x:a,y:o,position:m,height:R.height,len:y,len2:x,linePoints:d,textAlign:h,verticalAlign:"middle",rotation:P,inside:S},S||u.push(p.label)})),!c&&e.get("avoidLabelOverlap")&&o(u,r,s,t,i,a)}e.exports=s},bc5f:function(e,t,i){var n=i("6cb7");n.registerSubTypeDefaulter("visualMap",(function(e){return e.categories||(e.pieces?e.pieces.length>0:e.splitNumber>0)&&!e.calculable?"piecewise":"continuous"}))},bcaa1:function(e,t,i){var n=i("4ab1"),a=i("6d8b");function o(e,t){n.call(this,e,t,["filter"],"__filter_in_use__","_shadowDom")}function r(e){return e&&(e.shadowBlur||e.shadowOffsetX||e.shadowOffsetY||e.textShadowBlur||e.textShadowOffsetX||e.textShadowOffsetY)}a.inherits(o,n),o.prototype.addWithoutUpdate=function(e,t){if(t&&r(t.style)){var i,n=t.style;if(n._shadowDom){i=n._shadowDom;var a=this.getDefs(!0);a.contains(n._shadowDom)||this.addDom(i)}else i=this.add(t);this.markUsed(t);var o=i.getAttribute("id");e.style.filter="url(#"+o+")"}},o.prototype.add=function(e){var t=this.createElement("filter"),i=e.style;return i._shadowDomId=i._shadowDomId||this.nextId++,t.setAttribute("id","zr"+this._zrId+"-shadow-"+i._shadowDomId),this.updateDom(e,t),this.addDom(t),t},o.prototype.update=function(e,t){var i=t.style;if(r(i)){var a=this;n.prototype.update.call(this,t,(function(e){a.updateDom(t,e._shadowDom)}))}else this.remove(e,i)},o.prototype.remove=function(e,t){null!=t._shadowDomId&&(this.removeDom(t),e.style.filter="")},o.prototype.updateDom=function(e,t){var i=t.getElementsByTagName("feDropShadow");i=0===i.length?this.createElement("feDropShadow"):i[0];var n,a,o,r,s=e.style,l=e.scale&&e.scale[0]||1,u=e.scale&&e.scale[1]||1;if(s.shadowBlur||s.shadowOffsetX||s.shadowOffsetY)n=s.shadowOffsetX||0,a=s.shadowOffsetY||0,o=s.shadowBlur,r=s.shadowColor;else{if(!s.textShadowBlur)return void this.removeDom(t,s);n=s.textShadowOffsetX||0,a=s.textShadowOffsetY||0,o=s.textShadowBlur,r=s.textShadowColor}i.setAttribute("dx",n/l),i.setAttribute("dy",a/u),i.setAttribute("flood-color",r);var c=o/2/l,d=o/2/u,h=c+" "+d;i.setAttribute("stdDeviation",h),t.setAttribute("x","-100%"),t.setAttribute("y","-100%"),t.setAttribute("width",Math.ceil(o/2*200)+"%"),t.setAttribute("height",Math.ceil(o/2*200)+"%"),t.appendChild(i),s._shadowDom=t},o.prototype.markUsed=function(e){var t=e.style;t&&t._shadowDom&&n.prototype.markUsed.call(this,t._shadowDom)};var s=o;e.exports=s},bcbe:function(e,t,i){var n=i("6d8b"),a=i("fab22"),o=i("2306"),r=i("edb9"),s=i("6679"),l=["axisLine","axisTickLabel","axisName"],u="splitLine",c=s.extend({type:"singleAxis",axisPointerClass:"SingleAxisPointer",render:function(e,t,i,o){var s=this.group;s.removeAll();var d=r.layout(e),h=new a(e,d);n.each(l,h.add,h),s.add(h.getGroup()),e.get(u+".show")&&this["_"+u](e),c.superCall(this,"render",e,t,i,o)},_splitLine:function(e){var t=e.axis;if(!t.scale.isBlank()){var i=e.getModel("splitLine"),n=i.getModel("lineStyle"),a=n.get("width"),r=n.get("color");r=r instanceof Array?r:[r];for(var s=e.coordinateSystem.getRect(),l=t.isHorizontal(),u=[],c=0,d=t.getTicksCoords({tickModel:i}),h=[],p=[],g=0;g<d.length;++g){var f=t.toGlobalCoord(d[g].coord);l?(h[0]=f,h[1]=s.y,p[0]=f,p[1]=s.y+s.height):(h[0]=s.x,h[1]=f,p[0]=s.x+s.width,p[1]=f);var m=c++%r.length;u[m]=u[m]||[],u[m].push(new o.Line(o.subPixelOptimizeLine({shape:{x1:h[0],y1:h[1],x2:p[0],y2:p[1]},style:{lineWidth:a},silent:!0})))}for(g=0;g<u.length;++g)this.group.add(o.mergePath(u[g],{style:{stroke:r[g%r.length],lineDash:n.getLineDash(a),lineWidth:a},silent:!0}))}}}),d=c;e.exports=d},bd92:function(e,t,i){var n=i("6d8b"),a=i("4f85"),o=i("e468"),r=o.seriesModelMixin,s=a.extend({type:"series.candlestick",dependencies:["xAxis","yAxis","grid"],defaultValueDimensions:[{name:"open",defaultTooltip:!0},{name:"close",defaultTooltip:!0},{name:"lowest",defaultTooltip:!0},{name:"highest",defaultTooltip:!0}],dimensions:null,defaultOption:{zlevel:0,z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,hoverAnimation:!0,layout:null,itemStyle:{color:"#c23531",color0:"#314656",borderWidth:1,borderColor:"#c23531",borderColor0:"#314656"},emphasis:{itemStyle:{borderWidth:2}},barMaxWidth:null,barMinWidth:null,barWidth:null,large:!0,largeThreshold:600,progressive:3e3,progressiveThreshold:1e4,progressiveChunkMode:"mod",animationUpdate:!1,animationEasing:"linear",animationDuration:300},getShadowDim:function(){return"open"},brushSelector:function(e,t,i){var n=t.getItemLayout(e);return n&&i.rect(n.brushRect)}});n.mixin(s,r,!0);var l=s;e.exports=l},bd9e:function(e,t,i){var n=i("4e08"),a=(n.__DEV__,i("6d8b")),o=i("2306"),r=i("e0d3"),s=i("f4a2"),l=a.each,u=a.indexOf,c=a.curry,d=["dataToPoint","pointToData"],h=["grid","xAxis","yAxis","geo","graph","polar","radiusAxis","angleAxis","bmap"];function p(e,t,i){var n=this._targetInfoList=[],a={},o=m(t,e);l(v,(function(e,t){(!i||!i.include||u(i.include,t)>=0)&&e(o,n,a)}))}var g=p.prototype;function f(e){return e[0]>e[1]&&e.reverse(),e}function m(e,t){return r.parseFinder(e,t,{includeMainTypes:h})}g.setOutputRanges=function(e,t){this.matchOutputRanges(e,t,(function(e,t,i){if((e.coordRanges||(e.coordRanges=[])).push(t),!e.coordRange){e.coordRange=t;var n=_[e.brushType](0,i,t);e.__rangeOffset={offset:w[e.brushType](n.values,e.range,[1,1]),xyMinMax:n.xyMinMax}}}))},g.matchOutputRanges=function(e,t,i){l(e,(function(e){var n=this.findTargetInfo(e,t);n&&!0!==n&&a.each(n.coordSyses,(function(n){var a=_[e.brushType](1,n,e.range);i(e,a.values,n,t)}))}),this)},g.setInputRanges=function(e,t){l(e,(function(e){var i=this.findTargetInfo(e,t);if(e.range=e.range||[],i&&!0!==i){e.panelId=i.panelId;var n=_[e.brushType](0,i.coordSys,e.coordRange),a=e.__rangeOffset;e.range=a?w[e.brushType](n.values,a.offset,M(n.xyMinMax,a.xyMinMax)):n.values}}),this)},g.makePanelOpts=function(e,t){return a.map(this._targetInfoList,(function(i){var n=i.getPanelRect();return{panelId:i.panelId,defaultBrushType:t&&t(i),clipPath:s.makeRectPanelClipPath(n),isTargetByCursor:s.makeRectIsTargetByCursor(n,e,i.coordSysModel),getLinearBrushOtherExtent:s.makeLinearBrushOtherExtent(n)}}))},g.controlSeries=function(e,t,i){var n=this.findTargetInfo(e,i);return!0===n||n&&u(n.coordSyses,t.coordinateSystem)>=0},g.findTargetInfo=function(e,t){for(var i=this._targetInfoList,n=m(t,e),a=0;a<i.length;a++){var o=i[a],r=e.panelId;if(r){if(o.panelId===r)return o}else for(a=0;a<y.length;a++)if(y[a](n,o))return o}return!0};var v={grid:function(e,t){var i=e.xAxisModels,n=e.yAxisModels,o=e.gridModels,r=a.createHashMap(),s={},c={};(i||n||o)&&(l(i,(function(e){var t=e.axis.grid.model;r.set(t.id,t),s[t.id]=!0})),l(n,(function(e){var t=e.axis.grid.model;r.set(t.id,t),c[t.id]=!0})),l(o,(function(e){r.set(e.id,e),s[e.id]=!0,c[e.id]=!0})),r.each((function(e){var a=e.coordinateSystem,o=[];l(a.getCartesians(),(function(e,t){(u(i,e.getAxis("x").model)>=0||u(n,e.getAxis("y").model)>=0)&&o.push(e)})),t.push({panelId:"grid--"+e.id,gridModel:e,coordSysModel:e,coordSys:o[0],coordSyses:o,getPanelRect:x.grid,xAxisDeclared:s[e.id],yAxisDeclared:c[e.id]})})))},geo:function(e,t){l(e.geoModels,(function(e){var i=e.coordinateSystem;t.push({panelId:"geo--"+e.id,geoModel:e,coordSysModel:e,coordSys:i,coordSyses:[i],getPanelRect:x.geo})}))}},y=[function(e,t){var i=e.xAxisModel,n=e.yAxisModel,a=e.gridModel;return!a&&i&&(a=i.axis.grid.model),!a&&n&&(a=n.axis.grid.model),a&&a===t.gridModel},function(e,t){var i=e.geoModel;return i&&i===t.geoModel}],x={grid:function(){return this.coordSys.grid.getRect().clone()},geo:function(){var e=this.coordSys,t=e.getBoundingRect().clone();return t.applyTransform(o.getTransform(e)),t}},_={lineX:c(b,0),lineY:c(b,1),rect:function(e,t,i){var n=t[d[e]]([i[0][0],i[1][0]]),a=t[d[e]]([i[0][1],i[1][1]]),o=[f([n[0],a[0]]),f([n[1],a[1]])];return{values:o,xyMinMax:o}},polygon:function(e,t,i){var n=[[1/0,-1/0],[1/0,-1/0]],o=a.map(i,(function(i){var a=t[d[e]](i);return n[0][0]=Math.min(n[0][0],a[0]),n[1][0]=Math.min(n[1][0],a[1]),n[0][1]=Math.max(n[0][1],a[0]),n[1][1]=Math.max(n[1][1],a[1]),a}));return{values:o,xyMinMax:n}}};function b(e,t,i,n){var o=i.getAxis(["x","y"][e]),r=f(a.map([0,1],(function(e){return t?o.coordToData(o.toLocalCoord(n[e])):o.toGlobalCoord(o.dataToCoord(n[e]))}))),s=[];return s[e]=r,s[1-e]=[NaN,NaN],{values:r,xyMinMax:s}}var w={lineX:c(S,0),lineY:c(S,1),rect:function(e,t,i){return[[e[0][0]-i[0]*t[0][0],e[0][1]-i[0]*t[0][1]],[e[1][0]-i[1]*t[1][0],e[1][1]-i[1]*t[1][1]]]},polygon:function(e,t,i){return a.map(e,(function(e,n){return[e[0]-i[0]*t[n][0],e[1]-i[1]*t[n][1]]}))}};function S(e,t,i,n){return[t[0]-n[e]*i[0],t[1]-n[e]*i[1]]}function M(e,t){var i=I(e),n=I(t),a=[i[0]/n[0],i[1]/n[1]];return isNaN(a[0])&&(a[0]=1),isNaN(a[1])&&(a[1]=1),a}function I(e){return e?[e[0][1]-e[0][0],e[1][1]-e[1][0]]:[NaN,NaN]}var A=p;e.exports=A},bdc0:function(e,t,i){var n=i("3eba");i("d2a5"),n.registerAction({type:"dragNode",event:"dragNode",update:"update"},(function(e,t){t.eachComponent({mainType:"series",subType:"sankey",query:e},(function(t){t.setNodePosition(e.dataIndex,[e.localX,e.localY])}))}))},bf9b:function(e,t,i){var n=i("3eba"),a=i("d81e"),o=a.updateCenterAndZoom;n.registerAction({type:"treeExpandAndCollapse",event:"treeExpandAndCollapse",update:"update"},(function(e,t){t.eachComponent({mainType:"series",subType:"tree",query:e},(function(t){var i=e.dataIndex,n=t.getData().tree,a=n.getNodeByDataIndex(i);a.isExpand=!a.isExpand}))})),n.registerAction({type:"treeRoam",event:"treeRoam",update:"none"},(function(e,t){t.eachComponent({mainType:"series",subType:"tree",query:e},(function(t){var i=t.coordinateSystem,n=o(i,e);t.setCenter&&t.setCenter(n.center),t.setZoom&&t.setZoom(n.zoom)}))}))},c037:function(e,t,i){var n=i("3eba"),a=i("6d8b");i("f7c6"),i("1ab3");var o=i("7782"),r=i("98e7"),s=i("292e"),l=i("d3f47");o("pie",[{type:"pieToggleSelect",event:"pieselectchanged",method:"toggleSelected"},{type:"pieSelect",event:"pieselected",method:"select"},{type:"pieUnSelect",event:"pieunselected",method:"unSelect"}]),n.registerVisual(r("pie")),n.registerLayout(a.curry(s,"pie")),n.registerProcessor(l("pie"))},c2dd:function(e,t,i){var n=i("3eba"),a=i("6d8b"),o=i("fc82"),r=n.extendComponentView({type:"brush",init:function(e,t){this.ecModel=e,this.api=t,this.model,(this._brushController=new o(t.getZr())).on("brush",a.bind(this._onBrush,this)).mount()},render:function(e){return this.model=e,s.apply(this,arguments)},updateTransform:s,updateView:s,dispose:function(){this._brushController.dispose()},_onBrush:function(e,t){var i=this.model.id;this.model.brushTargetManager.setOutputRanges(e,this.ecModel),(!t.isEnd||t.removeOnClick)&&this.api.dispatchAction({type:"brush",brushId:i,areas:a.clone(e),$from:i})}});function s(e,t,i,n){(!n||n.$from!==e.id)&&this._brushController.setPanels(e.brushTargetManager.makePanelOpts(i)).enableBrush(e.brushOption).updateCovers(e.areas.slice())}e.exports=r},c515:function(e,t,i){i("849b"),i("8459"),i("b006")},c526:function(e,t){var i={axisPointer:1,tooltip:1,brush:1};function n(e,t,n){var a=t.getComponentByElement(e.topTarget),o=a&&a.coordinateSystem;return a&&a!==n&&!i[a.mainType]&&o&&o.model!==n}t.onIrrelevantElement=n},c62c:function(e,t,i){var n=i("6d8b"),a=i("6cb7"),o=i("9e47"),r=i("2023"),s=a.extend({type:"singleAxis",layoutMode:"box",axis:null,coordinateSystem:null,getCoordSysModel:function(){return this}}),l={left:"5%",top:"5%",right:"5%",bottom:"5%",type:"value",position:"bottom",orient:"horizontal",axisLine:{show:!0,lineStyle:{width:2,type:"solid"}},tooltip:{show:!0},axisTick:{show:!0,length:6,lineStyle:{width:2}},axisLabel:{show:!0,interval:"auto"},splitLine:{show:!0,lineStyle:{type:"dashed",opacity:.2}}};function u(e,t){return t.type||(t.data?"category":"value")}n.merge(s.prototype,r),o("single",s,u,l);var c=s;e.exports=c},c8ef:function(e,t,i){var n=i("6d8b"),a=i("a15a"),o=a.createSymbol,r=i("2306"),s=r.Group,l=i("3842"),u=l.parsePercent,c=i("1418"),d=3;function h(e){return n.isArray(e)||(e=[+e,+e]),e}function p(e,t){e.eachChild((function(e){e.attr({z:t.z,zlevel:t.zlevel,style:{stroke:"stroke"===t.brushType?t.color:null,fill:"fill"===t.brushType?t.color:null}})}))}function g(e,t){s.call(this);var i=new c(e,t),n=new s;this.add(i),this.add(n),n.beforeUpdate=function(){this.attr(i.getScale())},this.updateData(e,t)}var f=g.prototype;f.stopEffectAnimation=function(){this.childAt(1).removeAll()},f.startEffectAnimation=function(e){for(var t=e.symbolType,i=e.color,n=this.childAt(1),a=0;a<d;a++){var r=o(t,-1,-1,2,2,i);r.attr({style:{strokeNoScale:!0},z2:99,silent:!0,scale:[.5,.5]});var s=-a/d*e.period+e.effectOffset;r.animate("",!0).when(e.period,{scale:[e.rippleScale/2,e.rippleScale/2]}).delay(s).start(),r.animateStyle(!0).when(e.period,{opacity:0}).delay(s).start(),n.add(r)}p(n,e)},f.updateEffectAnimation=function(e){for(var t=this._effectCfg,i=this.childAt(1),n=["symbolType","period","rippleScale"],a=0;a<n.length;a++){var o=n[a];if(t[o]!==e[o])return this.stopEffectAnimation(),void this.startEffectAnimation(e)}p(i,e)},f.highlight=function(){this.trigger("emphasis")},f.downplay=function(){this.trigger("normal")},f.updateData=function(e,t){var i=e.hostModel;this.childAt(0).updateData(e,t);var n=this.childAt(1),a=e.getItemModel(t),o=e.getItemVisual(t,"symbol"),r=h(e.getItemVisual(t,"symbolSize")),s=e.getItemVisual(t,"color");n.attr("scale",r),n.traverse((function(e){e.attr({fill:s})}));var l=a.getShallow("symbolOffset");if(l){var c=n.position;c[0]=u(l[0],r[0]),c[1]=u(l[1],r[1])}n.rotation=(a.getShallow("symbolRotate")||0)*Math.PI/180||0;var d={};if(d.showEffectOn=i.get("showEffectOn"),d.rippleScale=a.get("rippleEffect.scale"),d.brushType=a.get("rippleEffect.brushType"),d.period=1e3*a.get("rippleEffect.period"),d.effectOffset=t/e.count(),d.z=a.getShallow("z")||0,d.zlevel=a.getShallow("zlevel")||0,d.symbolType=o,d.color=s,this.off("mouseover").off("mouseout").off("emphasis").off("normal"),"render"===d.showEffectOn)this._effectCfg?this.updateEffectAnimation(d):this.startEffectAnimation(d),this._effectCfg=d;else{this._effectCfg=null,this.stopEffectAnimation();var p=this.childAt(0),g=function(){p.highlight(),"render"!==d.showEffectOn&&this.startEffectAnimation(d)},f=function(){p.downplay(),"render"!==d.showEffectOn&&this.stopEffectAnimation()};this.on("mouseover",g,this).on("mouseout",f,this).on("emphasis",g,this).on("normal",f,this)}this._effectCfg=d},f.fadeOut=function(e){this.off("mouseover").off("mouseout").off("emphasis").off("normal"),e&&e()},n.inherits(g,s);var m=g;e.exports=m},c92f:function(e,t,i){var n=i("3041"),a=n.parseSVG,o=n.makeViewBoxTransform,r=i("e1fc"),s=i("c7a2"),l=i("6d8b"),u=l.assert,c=l.createHashMap,d=i("9850"),h=i("e0d3"),p=h.makeInner,g=p(),f={load:function(e,t){var i=g(t).originRoot;if(i)return{root:i,boundingRect:g(t).boundingRect};var n=m(t);return g(t).originRoot=n.root,g(t).boundingRect=n.boundingRect,n},makeGraphic:function(e,t,i){var n=g(t),a=n.rootMap||(n.rootMap=c()),o=a.get(i);if(o)return o;var r=n.originRoot,s=n.boundingRect;return n.originRootHostKey?o=m(t,s).root:(n.originRootHostKey=i,o=r),a.set(i,o)},removeGraphic:function(e,t,i){var n=g(t),a=n.rootMap;a&&a.removeKey(i),i===n.originRootHostKey&&(n.originRootHostKey=null)}};function m(e,t){var i,n,l=e.svgXML;try{i=l&&a(l,{ignoreViewBox:!0,ignoreRootClip:!0})||{},n=i.root,u(null!=n)}catch(m){throw new Error("Invalid svg format\n"+m.message)}var c=i.width,h=i.height,p=i.viewBoxRect;if(t||(t=null==c||null==h?n.getBoundingRect():new d(0,0,0,0),null!=c&&(t.width=c),null!=h&&(t.height=h)),p){var g=o(p,t.width,t.height),f=n;n=new r,n.add(f),f.scale=g.scale,f.position=g.position}return n.setClipPath(new s({shape:t.plain()})),{root:n,boundingRect:t}}e.exports=f},c965:function(e,t,i){var n=i("2306"),a=i("a15a"),o=a.createSymbol,r=i("392f"),s=4,l=n.extendShape({shape:{points:null},symbolProxy:null,buildPath:function(e,t){var i=t.points,n=t.size,a=this.symbolProxy,o=a.shape,r=e.getContext?e.getContext():e,l=r&&n[0]<s;if(!l)for(var u=0;u<i.length;){var c=i[u++],d=i[u++];isNaN(c)||isNaN(d)||(o.x=c-n[0]/2,o.y=d-n[1]/2,o.width=n[0],o.height=n[1],a.buildPath(e,o,!0))}},afterBrush:function(e){var t=this.shape,i=t.points,n=t.size,a=n[0]<s;if(a){this.setTransform(e);for(var o=0;o<i.length;){var r=i[o++],l=i[o++];isNaN(r)||isNaN(l)||e.fillRect(r-n[0]/2,l-n[1]/2,n[0],n[1])}this.restoreTransform(e)}},findDataIndex:function(e,t){for(var i=this.shape,n=i.points,a=i.size,o=Math.max(a[0],4),r=Math.max(a[1],4),s=n.length/2-1;s>=0;s--){var l=2*s,u=n[l]-o/2,c=n[l+1]-r/2;if(e>=u&&t>=c&&e<=u+o&&t<=c+r)return s}return-1}});function u(){this.group=new n.Group}var c=u.prototype;c.isPersistent=function(){return!this._incremental},c.updateData=function(e){this.group.removeAll();var t=new l({rectHover:!0,cursor:"default"});t.setShape({points:e.getLayout("symbolPoints")}),this._setCommon(t,e),this.group.add(t),this._incremental=null},c.updateLayout=function(e){if(!this._incremental){var t=e.getLayout("symbolPoints");this.group.eachChild((function(e){if(null!=e.startIndex){var i=2*(e.endIndex-e.startIndex),n=4*e.startIndex*2;t=new Float32Array(t.buffer,n,i)}e.setShape("points",t)}))}},c.incrementalPrepareUpdate=function(e){this.group.removeAll(),this._clearIncremental(),e.count()>2e6?(this._incremental||(this._incremental=new r({silent:!0})),this.group.add(this._incremental)):this._incremental=null},c.incrementalUpdate=function(e,t){var i;this._incremental?(i=new l,this._incremental.addDisplayable(i,!0)):(i=new l({rectHover:!0,cursor:"default",startIndex:e.start,endIndex:e.end}),i.incremental=!0,this.group.add(i)),i.setShape({points:t.getLayout("symbolPoints")}),this._setCommon(i,t,!!this._incremental)},c._setCommon=function(e,t,i){var n=t.hostModel,a=t.getVisual("symbolSize");e.setShape("size",a instanceof Array?a:[a,a]),e.symbolProxy=o(t.getVisual("symbol"),0,0,0,0),e.setColor=e.symbolProxy.setColor;var r=e.shape.size[0]<s;e.useStyle(n.getModel("itemStyle").getItemStyle(r?["color","shadowBlur","shadowColor"]:["color"]));var l=t.getVisual("color");l&&e.setColor(l),i||(e.seriesIndex=n.seriesIndex,e.on("mousemove",(function(t){e.dataIndex=null;var i=e.findDataIndex(t.offsetX,t.offsetY);i>=0&&(e.dataIndex=i+(e.startIndex||0))})))},c.remove=function(){this._clearIncremental(),this._incremental=null,this.group.removeAll()},c._clearIncremental=function(){var e=this._incremental;e&&e.clearDisplaybles()};var d=u;e.exports=d},ca29:function(e,t,i){var n=i("6d9a"),a=n.eachAfter,o=n.eachBefore,r=i("22da"),s=r.init,l=r.firstWalk,u=r.secondWalk,c=r.separation,d=r.radialCoordinate,h=r.getViewRect;function p(e,t){e.eachSeriesByType("tree",(function(e){g(e,t)}))}function g(e,t){var i=h(e,t);e.layoutInfo=i;var n=e.get("layout"),r=0,p=0,g=null;"radial"===n?(r=2*Math.PI,p=Math.min(i.height,i.width)/2,g=c((function(e,t){return(e.parentNode===t.parentNode?1:2)/e.depth}))):(r=i.width,p=i.height,g=c());var f=e.getData().tree.root,m=f.children[0];if(m){s(f),a(m,l,g),f.hierNode.modifier=-m.hierNode.prelim,o(m,u);var v=m,y=m,x=m;o(m,(function(e){var t=e.getLayout().x;t<v.getLayout().x&&(v=e),t>y.getLayout().x&&(y=e),e.depth>x.depth&&(x=e)}));var _=v===y?1:g(v,y)/2,b=_-v.getLayout().x,w=0,S=0,M=0,I=0;if("radial"===n)w=r/(y.getLayout().x+_+b),S=p/(x.depth-1||1),o(m,(function(e){M=(e.getLayout().x+b)*w,I=(e.depth-1)*S;var t=d(M,I);e.setLayout({x:t.x,y:t.y,rawX:M,rawY:I},!0)}));else{var A=e.getOrient();"RL"===A||"LR"===A?(S=p/(y.getLayout().x+_+b),w=r/(x.depth-1||1),o(m,(function(e){I=(e.getLayout().x+b)*S,M="LR"===A?(e.depth-1)*w:r-(e.depth-1)*w,e.setLayout({x:M,y:I},!0)}))):"TB"!==A&&"BT"!==A||(w=r/(y.getLayout().x+_+b),S=p/(x.depth-1||1),o(m,(function(e){M=(e.getLayout().x+b)*w,I="TB"===A?(e.depth-1)*S:p-(e.depth-1)*S,e.setLayout({x:M,y:I},!0)})))}}}e.exports=p},cb69:function(e,t,i){var n=i("3301"),a=i("4f85"),o=a.extend({type:"series.scatter",dependencies:["grid","polar","geo","singleAxis","calendar"],getInitialData:function(e,t){return n(this.getSource(),this)},brushSelector:"point",getProgressive:function(){var e=this.option.progressive;return null==e?this.option.large?5e3:this.get("progressive"):e},getProgressiveThreshold:function(){var e=this.option.progressiveThreshold;return null==e?this.option.large?1e4:this.get("progressiveThreshold"):e},defaultOption:{coordinateSystem:"cartesian2d",zlevel:0,z:2,legendHoverLink:!0,hoverAnimation:!0,symbolSize:10,large:!1,largeThreshold:2e3,itemStyle:{opacity:.8}}});e.exports=o},cb73:function(e,t,i){var n=i("3842"),a=n.parsePercent,o=i("6d8b"),r=(Math.PI,Math.PI/180);function s(e,t,i,n){t.eachSeriesByType(e,(function(e){var t=e.get("center"),n=e.get("radius");o.isArray(n)||(n=[0,n]),o.isArray(t)||(t=[t,t]);var s=i.getWidth(),u=i.getHeight(),c=Math.min(s,u),d=a(t[0],s),h=a(t[1],u),p=a(n[0],c/2),g=a(n[1],c/2),f=-e.get("startAngle")*r,m=e.get("minAngle")*r,v=e.getData().tree.root,y=e.getViewRoot(),x=y.depth,_=e.get("sort");null!=_&&l(y,_);var b=0;o.each(y.children,(function(e){!isNaN(e.getValue())&&b++}));var w=y.getValue(),S=Math.PI/(w||b)*2,M=y.depth>0,I=y.height-(M?-1:1),A=(g-p)/(I||1),T=e.get("clockwise"),D=e.get("stillShowZeroSum"),L=T?1:-1,C=function(e,t){if(e){var i=t;if(e!==v){var n=e.getValue(),r=0===w&&D?S:n*S;r<m?(r=m,m):n,i=t+L*r;var s=e.depth-x-(M?-1:1),l=p+A*s,u=p+A*(s+1),g=e.getModel();null!=g.get("r0")&&(l=a(g.get("r0"),c/2)),null!=g.get("r")&&(u=a(g.get("r"),c/2)),e.setLayout({angle:r,startAngle:t,endAngle:i,clockwise:T,cx:d,cy:h,r0:l,r:u})}if(e.children&&e.children.length){var f=0;o.each(e.children,(function(e){f+=C(e,t+f)}))}return i-t}};if(M){var P=p,N=p+A,R=2*Math.PI;v.setLayout({angle:R,startAngle:f,endAngle:f+R,clockwise:T,cx:d,cy:h,r0:P,r:N})}C(y,f)}))}function l(e,t){var i=e.children||[];e.children=u(i,t),i.length&&o.each(e.children,(function(e){l(e,t)}))}function u(e,t){if("function"===typeof t)return e.sort(t);var i="asc"===t;return e.sort((function(e,t){var n=(e.getValue()-t.getValue())*(i?1:-1);return 0===n?(e.dataIndex-t.dataIndex)*(i?-1:1):n}))}e.exports=s},cbb0:function(e,t,i){var n=i("6d8b"),a=i("f934"),o=a.getLayoutRect;function r(e,t,i){var n=e.option,a=n.align;if(null!=a&&"auto"!==a)return a;for(var r={width:t.getWidth(),height:t.getHeight()},s="horizontal"===n.orient?1:0,l=[["left","right","width"],["top","bottom","height"]],u=l[s],c=[0,null,10],d={},h=0;h<3;h++)d[l[1-s][h]]=c[h],d[u[h]]=2===h?i[0]:n[u[h]];var p=[["x","width",3],["y","height",0]][s],g=o(d,r,n.padding);return u[(g.margin[p[2]]||0)+g[p[0]]+.5*g[p[1]]<.5*r[p[1]]?0:1]}function s(e){return n.each(e||[],(function(t){null!=e.dataIndex&&(e.dataIndexInside=e.dataIndex,e.dataIndex=null)})),e}t.getItemAlign=r,t.convertDataIndex=s},cc39:function(e,t,i){var n=i("6d8b"),a=i("3842"),o=i("50e5"),r=n.each,s=a.asc,l=function(e,t,i,n){this._dimName=e,this._axisIndex=t,this._valueWindow,this._percentWindow,this._dataExtent,this._minMaxSpan,this.ecModel=n,this._dataZoomModel=i};function u(e,t,i){var n=[1/0,-1/0];return r(i,(function(e){var i=e.getData();i&&r(i.mapDimension(t,!0),(function(e){var t=i.getApproximateExtent(e);t[0]<n[0]&&(n[0]=t[0]),t[1]>n[1]&&(n[1]=t[1])}))})),n[1]<n[0]&&(n=[NaN,NaN]),c(e,n),n}function c(e,t){var i=e.getAxisModel(),n=i.getMin(!0),a="category"===i.get("type"),o=a&&i.getCategories().length;null!=n&&"dataMin"!==n&&"function"!==typeof n?t[0]=n:a&&(t[0]=o>0?0:NaN);var r=i.getMax(!0);return null!=r&&"dataMax"!==r&&"function"!==typeof r?t[1]=r:a&&(t[1]=o>0?o-1:NaN),i.get("scale",!0)||(t[0]>0&&(t[0]=0),t[1]<0&&(t[1]=0)),t}function d(e,t){var i=e.getAxisModel(),n=e._percentWindow,o=e._valueWindow;if(n){var r=a.getPixelPrecision(o,[0,500]);r=Math.min(r,20);var s=t||0===n[0]&&100===n[1];i.setRange(s?null:+o[0].toFixed(r),s?null:+o[1].toFixed(r))}}function h(e){var t=e._minMaxSpan={},i=e._dataZoomModel;r(["min","max"],(function(n){t[n+"Span"]=i.get(n+"Span");var o=i.get(n+"ValueSpan");if(null!=o&&(t[n+"ValueSpan"]=o,o=e.getAxisModel().axis.scale.parse(o),null!=o)){var r=e._dataExtent;t[n+"Span"]=a.linearMap(r[0]+o,r,[0,100],!0)}}))}l.prototype={constructor:l,hostedBy:function(e){return this._dataZoomModel===e},getDataValueWindow:function(){return this._valueWindow.slice()},getDataPercentWindow:function(){return this._percentWindow.slice()},getTargetSeriesModels:function(){var e=[],t=this.ecModel;return t.eachSeries((function(i){if(o.isCoordSupported(i.get("coordinateSystem"))){var n=this._dimName,a=t.queryComponents({mainType:n+"Axis",index:i.get(n+"AxisIndex"),id:i.get(n+"AxisId")})[0];this._axisIndex===(a&&a.componentIndex)&&e.push(i)}}),this),e},getAxisModel:function(){return this.ecModel.getComponent(this._dimName+"Axis",this._axisIndex)},getOtherAxisModel:function(){var e,t,i,n=this._dimName,a=this.ecModel,o=this.getAxisModel(),r="x"===n||"y"===n;return r?(t="gridIndex",e="x"===n?"y":"x"):(t="polarIndex",e="angle"===n?"radius":"angle"),a.eachComponent(e+"Axis",(function(e){(e.get(t)||0)===(o.get(t)||0)&&(i=e)})),i},getMinMaxSpan:function(){return n.clone(this._minMaxSpan)},calculateDataWindow:function(e){var t=this._dataExtent,i=this.getAxisModel(),n=i.axis.scale,o=this._dataZoomModel.getRangePropMode(),l=[0,100],u=[e.start,e.end],c=[];return r(["startValue","endValue"],(function(t){c.push(null!=e[t]?n.parse(e[t]):null)})),r([0,1],(function(e){var i=c[e],r=u[e];"percent"===o[e]?(null==r&&(r=l[e]),i=n.parse(a.linearMap(r,l,t,!0))):r=a.linearMap(i,t,l,!0),c[e]=i,u[e]=r})),{valueWindow:s(c),percentWindow:s(u)}},reset:function(e){if(e===this._dataZoomModel){var t=this.getTargetSeriesModels();this._dataExtent=u(this,this._dimName,t);var i=this.calculateDataWindow(e.option);this._valueWindow=i.valueWindow,this._percentWindow=i.percentWindow,h(this),d(this)}},restore:function(e){e===this._dataZoomModel&&(this._valueWindow=this._percentWindow=null,d(this,!0))},filterData:function(e,t){if(e===this._dataZoomModel){var i=this._dimName,n=this.getTargetSeriesModels(),a=e.get("filterMode"),o=this._valueWindow;"none"!==a&&r(n,(function(e){var t=e.getData(),n=t.mapDimension(i,!0);n.length&&("weakFilter"===a?t.filterSelf((function(e){for(var i,a,r,s=0;s<n.length;s++){var l=t.get(n[s],e),u=!isNaN(l),c=l<o[0],d=l>o[1];if(u&&!c&&!d)return!0;u&&(r=!0),c&&(i=!0),d&&(a=!0)}return r&&i&&a})):r(n,(function(i){if("empty"===a)e.setData(t.map(i,(function(e){return s(e)?e:NaN})));else{var n={};n[i]=o,t.selectRange(n)}})),r(n,(function(e){t.setApproximateExtent(o,e)})))}))}function s(e){return e>=o[0]&&e<=o[1]}}};var p=l;e.exports=p},cd12:function(e,t,i){i("01ed"),i("4a9d"),i("cb8f")},cd84:function(e,t,i){var n=i("6d8b"),a=256;function o(){var e=n.createCanvas();this.canvas=e,this.blurSize=30,this.pointSize=20,this.maxOpacity=1,this.minOpacity=0,this._gradientPixels={}}o.prototype={update:function(e,t,i,n,o,r){var s=this._getBrush(),l=this._getGradient(e,o,"inRange"),u=this._getGradient(e,o,"outOfRange"),c=this.pointSize+this.blurSize,d=this.canvas,h=d.getContext("2d"),p=e.length;d.width=t,d.height=i;for(var g=0;g<p;++g){var f=e[g],m=f[0],v=f[1],y=f[2],x=n(y);h.globalAlpha=x,h.drawImage(s,m-c,v-c)}if(!d.width||!d.height)return d;var _=h.getImageData(0,0,d.width,d.height),b=_.data,w=0,S=b.length,M=this.minOpacity,I=this.maxOpacity,A=I-M;while(w<S){x=b[w+3]/256;var T=4*Math.floor(x*(a-1));if(x>0){var D=r(x)?l:u;x>0&&(x=x*A+M),b[w++]=D[T],b[w++]=D[T+1],b[w++]=D[T+2],b[w++]=D[T+3]*x*256}else w+=4}return h.putImageData(_,0,0),d},_getBrush:function(){var e=this._brushCanvas||(this._brushCanvas=n.createCanvas()),t=this.pointSize+this.blurSize,i=2*t;e.width=i,e.height=i;var a=e.getContext("2d");return a.clearRect(0,0,i,i),a.shadowOffsetX=i,a.shadowBlur=this.blurSize,a.shadowColor="#000",a.beginPath(),a.arc(-t,t,this.pointSize,0,2*Math.PI,!0),a.closePath(),a.fill(),e},_getGradient:function(e,t,i){for(var n=this._gradientPixels,a=n[i]||(n[i]=new Uint8ClampedArray(1024)),o=[0,0,0,0],r=0,s=0;s<256;s++)t[i](s/255,!0,o),a[r++]=o[0],a[r++]=o[1],a[r++]=o[2],a[r++]=o[3];return a}};var r=o;e.exports=r},cee1:function(e,t,i){var n=i("6d8b");function a(e){var t={};e.eachSeriesByType("map",(function(i){var a=i.getMapType();if(!i.getHostGeoModel()&&!t[a]){var o={};n.each(i.seriesGroup,(function(t){var i=t.coordinateSystem,n=t.originalData;t.get("showLegendSymbol")&&e.getComponent("legend")&&n.each(n.mapDimension("value"),(function(e,t){var a=n.getName(t),r=i.getRegion(a);if(r&&!isNaN(e)){var s=o[a]||0,l=i.dataToPoint(r.center);o[a]=s+1,n.setItemLayout(t,{point:l,offset:s})}}))}));var r=i.getData();r.each((function(e){var t=r.getName(e),i=r.getItemLayout(e)||{};i.showLabel=!o[t],r.setItemLayout(e,i)})),t[a]=!0}}))}e.exports=a},d01c:function(e,t,i){var n=i("4f85"),a=i("237f"),o=i("eda2"),r=o.encodeHTML,s=n.extend({type:"series.sankey",layoutInfo:null,getInitialData:function(e){var t=e.edges||e.links,i=e.data||e.nodes;if(i&&t){var n=a(i,t,this,!0);return n.data}},setNodePosition:function(e,t){var i=this.option.data[e];i.localX=t[0],i.localY=t[1]},getGraph:function(){return this.getData().graph},getEdgeData:function(){return this.getGraph().edgeData},formatTooltip:function(e,t,i){if("edge"===i){var n=this.getDataParams(e,i),a=n.data,o=a.source+" -- "+a.target;return n.value&&(o+=" : "+n.value),r(o)}return s.superCall(this,"formatTooltip",e,t)},optionUpdated:function(){var e=this.option;!0===e.focusNodeAdjacency&&(e.focusNodeAdjacency="allEdges")},defaultOption:{zlevel:0,z:2,coordinateSystem:"view",layout:null,left:"5%",top:"5%",right:"20%",bottom:"5%",orient:"horizontal",nodeWidth:20,nodeGap:8,draggable:!0,focusNodeAdjacency:!1,layoutIterations:32,label:{show:!0,position:"right",color:"#000",fontSize:12},itemStyle:{borderWidth:1,borderColor:"#333"},lineStyle:{color:"#314656",opacity:.2,curveness:.5},emphasis:{label:{show:!0},lineStyle:{opacity:.6}},animationEasing:"linear",animationDuration:1e3}}),l=s;e.exports=l},d070:function(e,t,i){var n=i("3eba"),a=i("6d8b");function o(e,t){t.update="updateView",n.registerAction(t,(function(t,i){var n={};return i.eachComponent({mainType:"geo",query:t},(function(i){i[e](t.name);var o=i.coordinateSystem;a.each(o.regions,(function(e){n[e.name]=i.isSelected(e.name)||!1}))})),{selected:n,name:t.name}}))}i("1f1a"),i("eeea"),i("7661"),i("49e8"),o("toggleSelected",{type:"geoToggleSelect",event:"geoselectchanged"}),o("select",{type:"geoSelect",event:"geoselected"}),o("unSelect",{type:"geoUnSelect",event:"geounselected"})},d0900:function(e,t,i){var n=i("6d8b"),a=i("f934"),o=i("3842"),r=i("2039"),s=864e5;function l(e,t,i){this._model=e}function u(e,t,i,n){var a=i.calendarModel,o=i.seriesModel,r=a?a.coordinateSystem:o?o.coordinateSystem:null;return r===this?r[e](n):null}l.prototype={constructor:l,type:"calendar",dimensions:["time","value"],getDimensionsInfo:function(){return[{name:"time",type:"time"},"value"]},getRangeInfo:function(){return this._rangeInfo},getModel:function(){return this._model},getRect:function(){return this._rect},getCellWidth:function(){return this._sw},getCellHeight:function(){return this._sh},getOrient:function(){return this._orient},getFirstDayOfWeek:function(){return this._firstDayOfWeek},getDateInfo:function(e){e=o.parseDate(e);var t=e.getFullYear(),i=e.getMonth()+1;i=i<10?"0"+i:i;var n=e.getDate();n=n<10?"0"+n:n;var a=e.getDay();return a=Math.abs((a+7-this.getFirstDayOfWeek())%7),{y:t,m:i,d:n,day:a,time:e.getTime(),formatedDate:t+"-"+i+"-"+n,date:e}},getNextNDay:function(e,t){return t=t||0,0===t||(e=new Date(this.getDateInfo(e).time),e.setDate(e.getDate()+t)),this.getDateInfo(e)},update:function(e,t){this._firstDayOfWeek=+this._model.getModel("dayLabel").get("firstDay"),this._orient=this._model.get("orient"),this._lineWidth=this._model.getModel("itemStyle").getItemStyle().lineWidth||0,this._rangeInfo=this._getRangeInfo(this._initRangeOption());var i=this._rangeInfo.weeks||1,o=["width","height"],r=this._model.get("cellSize").slice(),s=this._model.getBoxLayoutParams(),l="horizontal"===this._orient?[i,7]:[7,i];n.each([0,1],(function(e){d(r,e)&&(s[o[e]]=r[e]*l[e])}));var u={width:t.getWidth(),height:t.getHeight()},c=this._rect=a.getLayoutRect(s,u);function d(e,t){return null!=e[t]&&"auto"!==e[t]}n.each([0,1],(function(e){d(r,e)||(r[e]=c[o[e]]/l[e])})),this._sw=r[0],this._sh=r[1]},dataToPoint:function(e,t){n.isArray(e)&&(e=e[0]),null==t&&(t=!0);var i=this.getDateInfo(e),a=this._rangeInfo,o=i.formatedDate;if(t&&!(i.time>=a.start.time&&i.time<a.end.time+s))return[NaN,NaN];var r=i.day,l=this._getRangeInfo([a.start.time,o]).nthWeek;return"vertical"===this._orient?[this._rect.x+r*this._sw+this._sw/2,this._rect.y+l*this._sh+this._sh/2]:[this._rect.x+l*this._sw+this._sw/2,this._rect.y+r*this._sh+this._sh/2]},pointToData:function(e){var t=this.pointToDate(e);return t&&t.time},dataToRect:function(e,t){var i=this.dataToPoint(e,t);return{contentShape:{x:i[0]-(this._sw-this._lineWidth)/2,y:i[1]-(this._sh-this._lineWidth)/2,width:this._sw-this._lineWidth,height:this._sh-this._lineWidth},center:i,tl:[i[0]-this._sw/2,i[1]-this._sh/2],tr:[i[0]+this._sw/2,i[1]-this._sh/2],br:[i[0]+this._sw/2,i[1]+this._sh/2],bl:[i[0]-this._sw/2,i[1]+this._sh/2]}},pointToDate:function(e){var t=Math.floor((e[0]-this._rect.x)/this._sw)+1,i=Math.floor((e[1]-this._rect.y)/this._sh)+1,n=this._rangeInfo.range;return"vertical"===this._orient?this._getDateByWeeksAndDay(i,t-1,n):this._getDateByWeeksAndDay(t,i-1,n)},convertToPixel:n.curry(u,"dataToPoint"),convertFromPixel:n.curry(u,"pointToData"),_initRangeOption:function(){var e=this._model.get("range"),t=e;if(n.isArray(t)&&1===t.length&&(t=t[0]),/^\d{4}$/.test(t)&&(e=[t+"-01-01",t+"-12-31"]),/^\d{4}[\/|-]\d{1,2}$/.test(t)){var i=this.getDateInfo(t),a=i.date;a.setMonth(a.getMonth()+1);var o=this.getNextNDay(a,-1);e=[i.formatedDate,o.formatedDate]}/^\d{4}[\/|-]\d{1,2}[\/|-]\d{1,2}$/.test(t)&&(e=[t,t]);var r=this._getRangeInfo(e);return r.start.time>r.end.time&&e.reverse(),e},_getRangeInfo:function(e){var t;e=[this.getDateInfo(e[0]),this.getDateInfo(e[1])],e[0].time>e[1].time&&(t=!0,e.reverse());var i=Math.floor(e[1].time/s)-Math.floor(e[0].time/s)+1,n=new Date(e[0].time),a=n.getDate(),o=e[1].date.getDate();if(n.setDate(a+i-1),n.getDate()!==o){var r=n.getTime()-e[1].time>0?1:-1;while(n.getDate()!==o&&(n.getTime()-e[1].time)*r>0)i-=r,n.setDate(a+i-1)}var l=Math.floor((i+e[0].day+6)/7),u=t?1-l:l-1;return t&&e.reverse(),{range:[e[0].formatedDate,e[1].formatedDate],start:e[0],end:e[1],allDay:i,weeks:l,nthWeek:u,fweek:e[0].day,lweek:e[1].day}},_getDateByWeeksAndDay:function(e,t,i){var n=this._getRangeInfo(i);if(e>n.weeks||0===e&&t<n.fweek||e===n.weeks&&t>n.lweek)return!1;var a=7*(e-1)-n.fweek+t,o=new Date(n.start.time);return o.setDate(n.start.d+a),this.getDateInfo(o)}},l.dimensions=l.prototype.dimensions,l.getDimensionsInfo=l.prototype.getDimensionsInfo,l.create=function(e,t){var i=[];return e.eachComponent("calendar",(function(n){var a=new l(n,e,t);i.push(a),n.coordinateSystem=a})),e.eachSeries((function(e){"calendar"===e.get("coordinateSystem")&&(e.coordinateSystem=i[e.get("calendarIndex")||0])})),i},r.register("calendar",l);var c=l;e.exports=c},d28f:function(e,t,i){var n=i("3eba");i("84d5"),i("4650"),i("5e97");var a=i("903c"),o=i("6cb7");n.registerProcessor(a),o.registerSubTypeDefaulter("legend",(function(){return"plain"}))},d2a5:function(e,t,i){var n=i("3eba");n.registerAction({type:"focusNodeAdjacency",event:"focusNodeAdjacency",update:"series:focusNodeAdjacency"},(function(){})),n.registerAction({type:"unfocusNodeAdjacency",event:"unfocusNodeAdjacency",update:"series:unfocusNodeAdjacency"},(function(){}))},d357:function(e,t,i){var n=i("6d8b"),a=n.each,o=i("1c5f"),r=o.simpleLayout,s=o.simpleLayoutEdge;function l(e,t){e.eachSeriesByType("graph",(function(e){var t=e.get("layout"),i=e.coordinateSystem;if(i&&"view"!==i.type){var n=e.getData(),o=[];a(i.dimensions,(function(e){o=o.concat(n.mapDimension(e,!0))}));for(var l=0;l<n.count();l++){for(var u=[],c=!1,d=0;d<o.length;d++){var h=n.get(o[d],l);isNaN(h)||(c=!0),u.push(h)}c?n.setItemLayout(l,i.dataToPoint(u)):n.setItemLayout(l,[NaN,NaN])}s(n.graph)}else t&&"none"!==t||r(e)}))}e.exports=l},d3a0:function(e,t,i){var n=i("3eba"),a=i("6d8b"),o=i("29a8"),r=i("2145"),s=o.toolbox.magicType;function l(e){this.model=e}l.defaultOption={show:!0,type:[],icon:{line:"M4.1,28.9h7.1l9.3-22l7.4,38l9.7-19.7l3,12.8h14.9M4.1,58h51.4",bar:"M6.7,22.9h10V48h-10V22.9zM24.9,13h10v35h-10V13zM43.2,2h10v46h-10V2zM3.1,58h53.7",stack:"M8.2,38.4l-8.4,4.1l30.6,15.3L60,42.5l-8.1-4.1l-21.5,11L8.2,38.4z M51.9,30l-8.1,4.2l-13.4,6.9l-13.9-6.9L8.2,30l-8.4,4.2l8.4,4.2l22.2,11l21.5-11l8.1-4.2L51.9,30z M51.9,21.7l-8.1,4.2L35.7,30l-5.3,2.8L24.9,30l-8.4-4.1l-8.3-4.2l-8.4,4.2L8.2,30l8.3,4.2l13.9,6.9l13.4-6.9l8.1-4.2l8.1-4.1L51.9,21.7zM30.4,2.2L-0.2,17.5l8.4,4.1l8.3,4.2l8.4,4.2l5.5,2.7l5.3-2.7l8.1-4.2l8.1-4.2l8.1-4.1L30.4,2.2z",tiled:"M2.3,2.2h22.8V25H2.3V2.2z M35,2.2h22.8V25H35V2.2zM2.3,35h22.8v22.8H2.3V35z M35,35h22.8v22.8H35V35z"},title:a.clone(s.title),option:{},seriesIndex:{}};var u=l.prototype;u.getIcons=function(){var e=this.model,t=e.get("icon"),i={};return a.each(e.get("type"),(function(e){t[e]&&(i[e]=t[e])})),i};var c={line:function(e,t,i,n){if("bar"===e)return a.merge({id:t,type:"line",data:i.get("data"),stack:i.get("stack"),markPoint:i.get("markPoint"),markLine:i.get("markLine")},n.get("option.line")||{},!0)},bar:function(e,t,i,n){if("line"===e)return a.merge({id:t,type:"bar",data:i.get("data"),stack:i.get("stack"),markPoint:i.get("markPoint"),markLine:i.get("markLine")},n.get("option.bar")||{},!0)},stack:function(e,t,i,n){if("line"===e||"bar"===e)return a.merge({id:t,stack:"__ec_magicType_stack__"},n.get("option.stack")||{},!0)},tiled:function(e,t,i,n){if("line"===e||"bar"===e)return a.merge({id:t,stack:""},n.get("option.tiled")||{},!0)}},d=[["line","bar"],["stack","tiled"]];u.onclick=function(e,t,i){var n=this.model,o=n.get("seriesIndex."+i);if(c[i]){var r={series:[]},s=function(t){var o=t.subType,s=t.id,l=c[i](o,s,t,n);l&&(a.defaults(l,t.option),r.series.push(l));var u=t.coordinateSystem;if(u&&"cartesian2d"===u.type&&("line"===i||"bar"===i)){var d=u.getAxesByScale("ordinal")[0];if(d){var h=d.dim,p=h+"Axis",g=e.queryComponents({mainType:p,index:t.get(name+"Index"),id:t.get(name+"Id")})[0],f=g.componentIndex;r[p]=r[p]||[];for(var m=0;m<=f;m++)r[p][f]=r[p][f]||{};r[p][f].boundaryGap="bar"===i}}};a.each(d,(function(e){a.indexOf(e,i)>=0&&a.each(e,(function(e){n.setIconStatus(e,"normal")}))})),n.setIconStatus(i,"emphasis"),e.eachComponent({mainType:"series",query:null==o?null:{seriesIndex:o}},s),t.dispatchAction({type:"changeMagicType",currentType:i,newOption:r})}},n.registerAction({type:"changeMagicType",event:"magicTypeChanged",update:"prepareAndUpdate"},(function(e,t){t.mergeOption(e.newOption)})),r.register("magicType",l);var h=l;e.exports=h},d3a4:function(e,t,i){var n,a=i("22d1"),o="urn:schemas-microsoft-com:vml",r="undefined"===typeof window?null:window,s=!1,l=r&&r.document;function u(e){return n(e)}if(l&&!a.canvasSupported)try{!l.namespaces.zrvml&&l.namespaces.add("zrvml",o),n=function(e){return l.createElement("<zrvml:"+e+' class="zrvml">')}}catch(d){n=function(e){return l.createElement("<"+e+' xmlns="'+o+'" class="zrvml">')}}function c(){if(!s&&l){s=!0;var e=l.styleSheets;e.length<31?l.createStyleSheet().addRule(".zrvml","behavior:url(#default#VML)"):e[0].addRule(".zrvml","behavior:url(#default#VML)")}}t.doc=l,t.createNode=u,t.initVML=c},d3f47:function(e,t){function i(e){return{seriesType:e,reset:function(e,t){var i=t.findComponents({mainType:"legend"});if(i&&i.length){var n=e.getData();n.filterSelf((function(e){for(var t=n.getName(e),a=0;a<i.length;a++)if(!i[a].isSelected(t))return!1;return!0}))}}}}e.exports=i},d6d9:function(e,t,i){var n=i("3014"),a=n.extend({type:"series.pictorialBar",dependencies:["grid"],defaultOption:{symbol:"circle",symbolSize:null,symbolRotate:null,symbolPosition:null,symbolOffset:null,symbolMargin:null,symbolRepeat:!1,symbolRepeatDirection:"end",symbolClip:!1,symbolBoundingData:null,symbolPatternSize:400,barGap:"-100%",progressive:0,hoverAnimation:!1},getInitialData:function(e){return e.stack=null,a.superApply(this,"getInitialData",arguments)}}),o=a;e.exports=o},d6ef:function(e,t,i){var n=i("3eba"),a={type:"selectDataRange",event:"dataRangeSelected",update:"update"};n.registerAction(a,(function(e,t){t.eachComponent({mainType:"visualMap",query:e},(function(t){t.setSelected(e.selected)}))}))},d716:function(e,t,i){var n=i("3eba"),a=i("6d8b");i("aa01"),i("340d"),i("44fb");var o=i("98e7"),r=i("cb73"),s=i("d3f47");n.registerVisual(a.curry(o,"sunburst")),n.registerLayout(a.curry(r,"sunburst")),n.registerProcessor(a.curry(s,"sunburst"))},d747:function(e,t,i){var n=i("3eba"),a=i("d81e"),o=a.updateCenterAndZoom;i("d2a5");var r={type:"graphRoam",event:"graphRoam",update:"none"};n.registerAction(r,(function(e,t){t.eachComponent({mainType:"series",query:e},(function(t){var i=t.coordinateSystem,n=o(i,e);t.setCenter&&t.setCenter(n.center),t.setZoom&&t.setZoom(n.zoom)}))}))},d81e:function(e,t){function i(e,t,i){var n=e.getZoom(),a=e.getCenter(),o=t.zoom,r=e.dataToPoint(a);if(null!=t.dx&&null!=t.dy){r[0]-=t.dx,r[1]-=t.dy;a=e.pointToData(r);e.setCenter(a)}if(null!=o){if(i){var s=i.min||0,l=i.max||1/0;o=Math.max(Math.min(n*o,l),s)/n}e.scale[0]*=o,e.scale[1]*=o;var u=e.position,c=(t.originX-u[0])*(o-1),d=(t.originY-u[1])*(o-1);u[0]-=c,u[1]-=d,e.updateTransform();a=e.pointToData(r);e.setCenter(a),e.setZoom(o*n)}return{center:e.getCenter(),zoom:e.getZoom()}}t.updateCenterAndZoom=i},d9d0:function(e,t,i){var n=i("6d8b"),a=i("1687"),o=i("f934"),r=i("697e"),s=i("0f55"),l=i("2306"),u=i("3842"),c=i("ef6a"),d=n.each,h=Math.min,p=Math.max,g=Math.floor,f=Math.ceil,m=u.round,v=Math.PI;function y(e,t,i){this._axesMap=n.createHashMap(),this._axesLayout={},this.dimensions=e.dimensions,this._rect,this._model=e,this._init(e,t,i)}function x(e,t){return h(p(e,t[0]),t[1])}function _(e,t){var i=t.layoutLength/(t.axisCount-1);return{position:i*e,axisNameAvailableWidth:i,axisLabelShow:!0}}function b(e,t){var i,n,a=t.layoutLength,o=t.axisExpandWidth,r=t.axisCount,s=t.axisCollapseWidth,l=t.winInnerIndices,u=s,c=!1;return e<l[0]?(i=e*s,n=s):e<=l[1]?(i=t.axisExpandWindow0Pos+e*o-t.axisExpandWindow[0],u=o,c=!0):(i=a-(r-1-e)*s,n=s),{position:i,axisNameAvailableWidth:u,axisLabelShow:c,nameTruncateMaxWidth:n}}y.prototype={type:"parallel",constructor:y,_init:function(e,t,i){var n=e.dimensions,a=e.parallelAxisIndex;d(n,(function(e,i){var n=a[i],o=t.getComponent("parallelAxis",n),l=this._axesMap.set(e,new s(e,r.createScaleByModel(o),[0,0],o.get("type"),n)),u="category"===l.type;l.onBand=u&&o.get("boundaryGap"),l.inverse=o.get("inverse"),o.axis=l,l.model=o,l.coordinateSystem=o.coordinateSystem=this}),this)},update:function(e,t){this._updateAxesFromSeries(this._model,e)},containPoint:function(e){var t=this._makeLayoutInfo(),i=t.axisBase,n=t.layoutBase,a=t.pixelDimIndex,o=e[1-a],r=e[a];return o>=i&&o<=i+t.axisLength&&r>=n&&r<=n+t.layoutLength},getModel:function(){return this._model},_updateAxesFromSeries:function(e,t){t.eachSeries((function(i){if(e.contains(i,t)){var n=i.getData();d(this.dimensions,(function(e){var t=this._axesMap.get(e);t.scale.unionExtentFromData(n,n.mapDimension(e)),r.niceScaleExtent(t.scale,t.model)}),this)}}),this)},resize:function(e,t){this._rect=o.getLayoutRect(e.getBoxLayoutParams(),{width:t.getWidth(),height:t.getHeight()}),this._layoutAxes()},getRect:function(){return this._rect},_makeLayoutInfo:function(){var e,t=this._model,i=this._rect,n=["x","y"],a=["width","height"],o=t.get("layout"),r="horizontal"===o?0:1,s=i[a[r]],l=[0,s],u=this.dimensions.length,c=x(t.get("axisExpandWidth"),l),d=x(t.get("axisExpandCount")||0,[0,u]),h=t.get("axisExpandable")&&u>3&&u>d&&d>1&&c>0&&s>0,p=t.get("axisExpandWindow");if(p)e=x(p[1]-p[0],l),p[1]=p[0]+e;else{e=x(c*(d-1),l);var v=t.get("axisExpandCenter")||g(u/2);p=[c*v-e/2],p[1]=p[0]+e}var y=(s-e)/(u-d);y<3&&(y=0);var _=[g(m(p[0]/c,1))+1,f(m(p[1]/c,1))-1],b=y/c*p[0];return{layout:o,pixelDimIndex:r,layoutBase:i[n[r]],layoutLength:s,axisBase:i[n[1-r]],axisLength:i[a[1-r]],axisExpandable:h,axisExpandWidth:c,axisCollapseWidth:y,axisExpandWindow:p,axisCount:u,winInnerIndices:_,axisExpandWindow0Pos:b}},_layoutAxes:function(){var e=this._rect,t=this._axesMap,i=this.dimensions,n=this._makeLayoutInfo(),o=n.layout;t.each((function(e){var t=[0,n.axisLength],i=e.inverse?1:0;e.setExtent(t[i],t[1-i])})),d(i,(function(t,i){var r=(n.axisExpandable?b:_)(i,n),s={horizontal:{x:r.position,y:n.axisLength},vertical:{x:0,y:r.position}},l={horizontal:v/2,vertical:0},u=[s[o].x+e.x,s[o].y+e.y],c=l[o],d=a.create();a.rotate(d,d,c),a.translate(d,d,u),this._axesLayout[t]={position:u,rotation:c,transform:d,axisNameAvailableWidth:r.axisNameAvailableWidth,axisLabelShow:r.axisLabelShow,nameTruncateMaxWidth:r.nameTruncateMaxWidth,tickDirection:1,labelDirection:1}}),this)},getAxis:function(e){return this._axesMap.get(e)},dataToPoint:function(e,t){return this.axisCoordToPoint(this._axesMap.get(t).dataToCoord(e),t)},eachActiveState:function(e,t,i,a){null==i&&(i=0),null==a&&(a=e.count());var o=this._axesMap,r=this.dimensions,s=[],l=[];n.each(r,(function(t){s.push(e.mapDimension(t)),l.push(o.get(t).model)}));for(var u=this.hasAxisBrushed(),c=i;c<a;c++){var d;if(u){d="active";for(var h=e.getValues(s,c),p=0,g=r.length;p<g;p++){var f=l[p].getActiveState(h[p]);if("inactive"===f){d="inactive";break}}}else d="normal";t(d,c)}},hasAxisBrushed:function(){for(var e=this.dimensions,t=this._axesMap,i=!1,n=0,a=e.length;n<a;n++)"normal"!==t.get(e[n]).model.getActiveState()&&(i=!0);return i},axisCoordToPoint:function(e,t){var i=this._axesLayout[t];return l.applyTransform([e,0],i.transform)},getAxisLayout:function(e){return n.clone(this._axesLayout[e])},getSlidedAxisExpandWindow:function(e){var t=this._makeLayoutInfo(),i=t.pixelDimIndex,n=t.axisExpandWindow.slice(),a=n[1]-n[0],o=[0,t.axisExpandWidth*(t.axisCount-1)];if(!this.containPoint(e))return{behavior:"none",axisExpandWindow:n};var r,s=e[i]-t.layoutBase-t.axisExpandWindow0Pos,l="slide",u=t.axisCollapseWidth,d=this._model.get("axisExpandSlideTriggerArea"),g=null!=d[0];if(u)g&&u&&s<a*d[0]?(l="jump",r=s-a*d[2]):g&&u&&s>a*(1-d[0])?(l="jump",r=s-a*(1-d[2])):(r=s-a*d[1])>=0&&(r=s-a*(1-d[1]))<=0&&(r=0),r*=t.axisExpandWidth/u,r?c(r,n,o,"all"):l="none";else{a=n[1]-n[0];var f=o[1]*s/a;n=[p(0,f-a/2)],n[1]=h(o[1],n[0]+a),n[0]=n[1]-a}return{axisExpandWindow:n,behavior:l}}};var w=y;e.exports=w},dae1:function(e,t,i){var n=i("3eba");i("928d"),i("b369"),i("4411");var a=i("90c2"),o=i("9ca8");n.registerVisual(a),n.registerLayout(o)},db0e:function(e,t,i){var n=i("3eba");i("a8c6"),i("8344"),n.registerPreprocessor((function(e){e.markPoint=e.markPoint||{}}))},db9e:function(e,t,i){var n=i("3eba"),a=i("6d8b"),o=i("e86a"),r=i("2145"),s=i("2306"),l=i("4319"),u=i("80f0"),c=i("7919"),d=n.extendComponentView({type:"toolbox",render:function(e,t,i,n){var d=this.group;if(d.removeAll(),e.get("show")){var p=+e.get("itemSize"),g=e.get("feature")||{},f=this._features||(this._features={}),m=[];a.each(g,(function(e,t){m.push(t)})),new u(this._featureNames||[],m).add(v).update(v).remove(a.curry(v,null)).execute(),this._featureNames=m,c.layout(d,e,i),d.add(c.makeBackground(d.getBoundingRect(),e)),d.eachChild((function(e){var t=e.__title,n=e.hoverStyle;if(n&&t){var a=o.getBoundingRect(t,o.makeFont(n)),r=e.position[0]+d.position[0],s=e.position[1]+d.position[1]+p,l=!1;s+a.height>i.getHeight()&&(n.textPosition="top",l=!0);var u=l?-5-a.height:p+8;r+a.width/2>i.getWidth()?(n.textPosition=["100%",u],n.textAlign="right"):r-a.width/2<0&&(n.textPosition=[0,u],n.textAlign="left")}}))}function v(a,o){var s,u=m[a],c=m[o],d=g[u],p=new l(d,e,e.ecModel);if(u&&!c){if(h(u))s={model:p,onclick:p.option.onclick,featureName:u};else{var v=r.get(u);if(!v)return;s=new v(p,t,i)}f[u]=s}else{if(s=f[c],!s)return;s.model=p,s.ecModel=t,s.api=i}u||!c?p.get("show")&&!s.unusable?(y(p,s,u),p.setIconStatus=function(e,t){var i=this.option,n=this.iconPaths;i.iconStatus=i.iconStatus||{},i.iconStatus[e]=t,n[e]&&n[e].trigger(t)},s.render&&s.render(p,t,i,n)):s.remove&&s.remove(t,i):s.dispose&&s.dispose(t,i)}function y(n,o,r){var l=n.getModel("iconStyle"),u=n.getModel("emphasis.iconStyle"),c=o.getIcons?o.getIcons():n.get("icon"),h=n.get("title")||{};if("string"===typeof c){var g=c,f=h;c={},h={},c[r]=g,h[r]=f}var m=n.iconPaths={};a.each(c,(function(r,c){var g=s.createIcon(r,{},{x:-p/2,y:-p/2,width:p,height:p});g.setStyle(l.getItemStyle()),g.hoverStyle=u.getItemStyle(),s.setHoverStyle(g),e.get("showTitle")&&(g.__title=h[c],g.on("mouseover",(function(){var e=u.getItemStyle();g.setStyle({text:h[c],textPosition:e.textPosition||"bottom",textFill:e.fill||e.stroke||"#000",textAlign:e.textAlign||"center"})})).on("mouseout",(function(){g.setStyle({textFill:null})}))),g.trigger(n.get("iconStatus."+c)||"normal"),d.add(g),g.on("click",a.bind(o.onclick,o,t,i,c)),m[c]=g}))}},updateView:function(e,t,i,n){a.each(this._features,(function(e){e.updateView&&e.updateView(e.model,t,i,n)}))},remove:function(e,t){a.each(this._features,(function(i){i.remove&&i.remove(e,t)})),this.group.removeAll()},dispose:function(e,t){a.each(this._features,(function(i){i.dispose&&i.dispose(e,t)}))}});function h(e){return 0===e.indexOf("my")}e.exports=d},dc20:function(e,t,i){var n=i("8727"),a=n.createElement,o=i("6d8b"),r=i("4942"),s=i("cbe5"),l=i("0da8"),u=i("76a5"),c=i("0c12"),d=i("b16f"),h=i("9fa3"),p=i("bcaa1"),g=i("3f8e"),f=g.path,m=g.image,v=g.text;function y(e){return parseInt(e,10)}function x(e){return e instanceof s?f:e instanceof l?m:e instanceof u?v:f}function _(e,t){return t&&e&&t.parentNode!==e}function b(e,t,i){if(_(e,t)&&i){var n=i.nextSibling;n?e.insertBefore(t,n):e.appendChild(t)}}function w(e,t){if(_(e,t)){var i=e.firstChild;i?e.insertBefore(t,i):e.appendChild(t)}}function S(e,t){t&&e&&t.parentNode===e&&e.removeChild(t)}function M(e){return e.__textSvgEl}function I(e){return e.__svgEl}var A=function(e,t,i,n){this.root=e,this.storage=t,this._opts=i=o.extend({},i||{});var r=a("svg");r.setAttribute("xmlns","http://www.w3.org/2000/svg"),r.setAttribute("version","1.1"),r.setAttribute("baseProfile","full"),r.style.cssText="user-select:none;position:absolute;left:0;top:0;",this.gradientManager=new d(n,r),this.clipPathManager=new h(n,r),this.shadowManager=new p(n,r);var s=document.createElement("div");s.style.cssText="overflow:hidden;position:relative",this._svgRoot=r,this._viewport=s,e.appendChild(s),s.appendChild(r),this.resize(i.width,i.height),this._visibleList=[]};function T(e){return function(){r('In SVG mode painter not support method "'+e+'"')}}A.prototype={constructor:A,getType:function(){return"svg"},getViewportRoot:function(){return this._viewport},getViewportRootOffset:function(){var e=this.getViewportRoot();if(e)return{offsetLeft:e.offsetLeft||0,offsetTop:e.offsetTop||0}},refresh:function(){var e=this.storage.getDisplayList(!0);this._paintList(e)},setBackgroundColor:function(e){this._viewport.style.background=e},_paintList:function(e){this.gradientManager.markAllUnused(),this.clipPathManager.markAllUnused(),this.shadowManager.markAllUnused();var t,i=this._svgRoot,n=this._visibleList,a=e.length,o=[];for(t=0;t<a;t++){var r=e[t],s=x(r),l=I(r)||M(r);r.invisible||(r.__dirty&&(s&&s.brush(r),this.clipPathManager.update(r),r.style&&(this.gradientManager.update(r.style.fill),this.gradientManager.update(r.style.stroke),this.shadowManager.update(l,r)),r.__dirty=!1),o.push(r))}var u,d=c(n,o);for(t=0;t<d.length;t++){var h=d[t];if(h.removed)for(var p=0;p<h.count;p++){r=n[h.indices[p]],l=I(r);var g=M(r);S(i,l),S(i,g)}}for(t=0;t<d.length;t++){h=d[t];if(h.added)for(p=0;p<h.count;p++){r=o[h.indices[p]],l=I(r),g=M(r);u?b(i,l,u):w(i,l),l?b(i,g,l):u?b(i,g,u):w(i,g),b(i,g,l),u=g||l||u,this.gradientManager.addWithoutUpdate(l,r),this.shadowManager.addWithoutUpdate(u,r),this.clipPathManager.markUsed(r)}else if(!h.removed)for(p=0;p<h.count;p++){r=o[h.indices[p]];u=l=M(r)||I(r)||u,this.gradientManager.markUsed(r),this.gradientManager.addWithoutUpdate(l,r),this.shadowManager.markUsed(r),this.shadowManager.addWithoutUpdate(l,r),this.clipPathManager.markUsed(r)}}this.gradientManager.removeUnused(),this.clipPathManager.removeUnused(),this.shadowManager.removeUnused(),this._visibleList=o},_getDefs:function(e){var t=this._svgRoot,i=this._svgRoot.getElementsByTagName("defs");if(0===i.length){if(e){i=t.insertBefore(a("defs"),t.firstChild);return i.contains||(i.contains=function(e){var t=i.children;if(!t)return!1;for(var n=t.length-1;n>=0;--n)if(t[n]===e)return!0;return!1}),i}return null}return i[0]},resize:function(e,t){var i=this._viewport;i.style.display="none";var n=this._opts;if(null!=e&&(n.width=e),null!=t&&(n.height=t),e=this._getSize(0),t=this._getSize(1),i.style.display="",this._width!==e||this._height!==t){this._width=e,this._height=t;var a=i.style;a.width=e+"px",a.height=t+"px";var o=this._svgRoot;o.setAttribute("width",e),o.setAttribute("height",t)}},getWidth:function(){return this._width},getHeight:function(){return this._height},_getSize:function(e){var t=this._opts,i=["width","height"][e],n=["clientWidth","clientHeight"][e],a=["paddingLeft","paddingTop"][e],o=["paddingRight","paddingBottom"][e];if(null!=t[i]&&"auto"!==t[i])return parseFloat(t[i]);var r=this.root,s=document.defaultView.getComputedStyle(r);return(r[n]||y(s[i])||y(r.style[i]))-(y(s[a])||0)-(y(s[o])||0)|0},dispose:function(){this.root.innerHTML="",this._svgRoot=this._viewport=this.storage=null},clear:function(){this._viewport&&this.root.removeChild(this._viewport)},pathToDataUrl:function(){this.refresh();var e=this._svgRoot.outerHTML;return"data:image/svg+xml;charset=UTF-8,"+e}},o.each(["getLayer","insertLayer","eachLayer","eachBuiltinLayer","eachOtherLayer","getLayers","modLayer","delLayer","clearLayer","toDataURL","pathToImage"],(function(e){A.prototype[e]=T(e)}));var D=A;e.exports=D},dcea:function(e,t,i){var n=i("6d8b"),a=i("e887"),o=i("2306"),r=i("cbe5"),s=["itemStyle"],l=["emphasis","itemStyle"],u=a.extend({type:"boxplot",render:function(e,t,i){var n=e.getData(),a=this.group,o=this._data;this._data||a.removeAll();var r="horizontal"===e.get("layout")?1:0;n.diff(o).add((function(e){if(n.hasValue(e)){var t=n.getItemLayout(e),i=d(t,n,e,r,!0);n.setItemGraphicEl(e,i),a.add(i)}})).update((function(e,t){var i=o.getItemGraphicEl(t);if(n.hasValue(e)){var s=n.getItemLayout(e);i?h(s,i,n,e):i=d(s,n,e,r),a.add(i),n.setItemGraphicEl(e,i)}else a.remove(i)})).remove((function(e){var t=o.getItemGraphicEl(e);t&&a.remove(t)})).execute(),this._data=n},remove:function(e){var t=this.group,i=this._data;this._data=null,i&&i.eachItemGraphicEl((function(e){e&&t.remove(e)}))},dispose:n.noop}),c=r.extend({type:"boxplotBoxPath",shape:{},buildPath:function(e,t){var i=t.points,n=0;for(e.moveTo(i[n][0],i[n][1]),n++;n<4;n++)e.lineTo(i[n][0],i[n][1]);for(e.closePath();n<i.length;n++)e.moveTo(i[n][0],i[n][1]),n++,e.lineTo(i[n][0],i[n][1])}});function d(e,t,i,n,a){var o=e.ends,r=new c({shape:{points:a?p(o,n,e):o}});return h(e,r,t,i,a),r}function h(e,t,i,n,a){var r=i.hostModel,u=o[a?"initProps":"updateProps"];u(t,{shape:{points:e.ends}},r,n);var c=i.getItemModel(n),d=c.getModel(s),h=i.getItemVisual(n,"color"),p=d.getItemStyle(["borderColor"]);p.stroke=h,p.strokeNoScale=!0,t.useStyle(p),t.z2=100;var g=c.getModel(l).getItemStyle();o.setHoverStyle(t,g)}function p(e,t,i){return n.map(e,(function(e){return e=e.slice(),e[t]=i.initBaseline,e}))}var g=u;e.exports=g},dd39:function(e,t,i){i("6932"),i("3a56"),i("7dcf"),i("414c"),i("4b08"),i("2c17"),i("9e87")},dd7e:function(e,t,i){var n=i("6d8b"),a=i("edaf"),o=i("38a2"),r=a.extend({type:"timeline.slider",defaultOption:{backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderWidth:0,orient:"horizontal",inverse:!1,tooltip:{trigger:"item"},symbol:"emptyCircle",symbolSize:10,lineStyle:{show:!0,width:2,color:"#304654"},label:{position:"auto",show:!0,interval:"auto",rotate:0,color:"#304654"},itemStyle:{color:"#304654",borderWidth:1},checkpointStyle:{symbol:"circle",symbolSize:13,color:"#c23531",borderWidth:5,borderColor:"rgba(194,53,49, 0.5)",animation:!0,animationDuration:300,animationEasing:"quinticInOut"},controlStyle:{show:!0,showPlayBtn:!0,showPrevBtn:!0,showNextBtn:!0,itemSize:22,itemGap:12,position:"left",playIcon:"path://M31.6,53C17.5,53,6,41.5,6,27.4S17.5,1.8,31.6,1.8C45.7,1.8,57.2,13.3,57.2,27.4S45.7,53,31.6,53z M31.6,3.3 C18.4,3.3,7.5,14.1,7.5,27.4c0,13.3,10.8,24.1,24.1,24.1C44.9,51.5,55.7,40.7,55.7,27.4C55.7,14.1,44.9,3.3,31.6,3.3z M24.9,21.3 c0-2.2,1.6-3.1,3.5-2l10.5,6.1c1.899,1.1,1.899,2.9,0,4l-10.5,6.1c-1.9,1.1-3.5,0.2-3.5-2V21.3z",stopIcon:"path://M30.9,53.2C16.8,53.2,5.3,41.7,5.3,27.6S16.8,2,30.9,2C45,2,56.4,13.5,56.4,27.6S45,53.2,30.9,53.2z M30.9,3.5C17.6,3.5,6.8,14.4,6.8,27.6c0,13.3,10.8,24.1,24.101,24.1C44.2,51.7,55,40.9,55,27.6C54.9,14.4,44.1,3.5,30.9,3.5z M36.9,35.8c0,0.601-0.4,1-0.9,1h-1.3c-0.5,0-0.9-0.399-0.9-1V19.5c0-0.6,0.4-1,0.9-1H36c0.5,0,0.9,0.4,0.9,1V35.8z M27.8,35.8 c0,0.601-0.4,1-0.9,1h-1.3c-0.5,0-0.9-0.399-0.9-1V19.5c0-0.6,0.4-1,0.9-1H27c0.5,0,0.9,0.4,0.9,1L27.8,35.8L27.8,35.8z",nextIcon:"path://M18.6,50.8l22.5-22.5c0.2-0.2,0.3-0.4,0.3-0.7c0-0.3-0.1-0.5-0.3-0.7L18.7,4.4c-0.1-0.1-0.2-0.3-0.2-0.5 c0-0.4,0.3-0.8,0.8-0.8c0.2,0,0.5,0.1,0.6,0.3l23.5,23.5l0,0c0.2,0.2,0.3,0.4,0.3,0.7c0,0.3-0.1,0.5-0.3,0.7l-0.1,0.1L19.7,52 c-0.1,0.1-0.3,0.2-0.5,0.2c-0.4,0-0.8-0.3-0.8-0.8C18.4,51.2,18.5,51,18.6,50.8z",prevIcon:"path://M43,52.8L20.4,30.3c-0.2-0.2-0.3-0.4-0.3-0.7c0-0.3,0.1-0.5,0.3-0.7L42.9,6.4c0.1-0.1,0.2-0.3,0.2-0.5 c0-0.4-0.3-0.8-0.8-0.8c-0.2,0-0.5,0.1-0.6,0.3L18.3,28.8l0,0c-0.2,0.2-0.3,0.4-0.3,0.7c0,0.3,0.1,0.5,0.3,0.7l0.1,0.1L41.9,54 c0.1,0.1,0.3,0.2,0.5,0.2c0.4,0,0.8-0.3,0.8-0.8C43.2,53.2,43.1,53,43,52.8z",color:"#304654",borderColor:"#304654",borderWidth:1},emphasis:{label:{show:!0,color:"#c23531"},itemStyle:{color:"#c23531"},controlStyle:{color:"#c23531",borderColor:"#c23531",borderWidth:2}},data:[]}});n.mixin(r,o);var s=r;e.exports=s},de6e:function(e,t){function i(e){return e instanceof Array||(e=[e,e]),e}function n(e){e.eachSeriesByType("graph",(function(e){var t=e.getGraph(),n=e.getEdgeData(),a=i(e.get("edgeSymbol")),o=i(e.get("edgeSymbolSize")),r="lineStyle.color".split("."),s="lineStyle.opacity".split(".");n.setVisual("fromSymbol",a&&a[0]),n.setVisual("toSymbol",a&&a[1]),n.setVisual("fromSymbolSize",o&&o[0]),n.setVisual("toSymbolSize",o&&o[1]),n.setVisual("color",e.get(r)),n.setVisual("opacity",e.get(s)),n.each((function(e){var a=n.getItemModel(e),o=t.getEdgeByIndex(e),l=i(a.getShallow("symbol",!0)),u=i(a.getShallow("symbolSize",!0)),c=a.get(r),d=a.get(s);switch(c){case"source":c=o.node1.getVisual("color");break;case"target":c=o.node2.getVisual("color");break}l[0]&&o.setVisual("fromSymbol",l[0]),l[1]&&o.setVisual("toSymbol",l[1]),u[0]&&o.setVisual("fromSymbolSize",u[0]),u[1]&&o.setVisual("toSymbolSize",u[1]),o.setVisual("color",c),o.setVisual("opacity",d)}))}))}e.exports=n},df3a:function(e,t,i){var n=i("6d8b"),a=i("6cb7"),o=i("282b"),r=i("9e47"),s=i("3842"),l=i("2023"),u=a.extend({type:"baseParallelAxis",axis:null,activeIntervals:[],getAreaSelectStyle:function(){return o([["fill","color"],["lineWidth","borderWidth"],["stroke","borderColor"],["width","width"],["opacity","opacity"]])(this.getModel("areaSelectStyle"))},setActiveIntervals:function(e){var t=this.activeIntervals=n.clone(e);if(t)for(var i=t.length-1;i>=0;i--)s.asc(t[i])},getActiveState:function(e){var t=this.activeIntervals;if(!t.length)return"normal";if(null==e||isNaN(e))return"inactive";if(1===t.length){var i=t[0];if(i[0]<=e&&e<=i[1])return"active"}else for(var n=0,a=t.length;n<a;n++)if(t[n][0]<=e&&e<=t[n][1])return"active";return"inactive"}}),c={type:"value",dim:null,areaSelectStyle:{width:20,borderWidth:1,borderColor:"rgba(160,197,232)",color:"rgba(160,197,232)",opacity:.3},realtime:!0,z:10};function d(e,t){return t.type||(t.data?"category":"value")}n.merge(u.prototype,l),r("parallel",u,d,c);var h=u;e.exports=h},df70:function(e,t,i){var n=i("3eba"),a=i("6fda"),o=i("29a8"),r=i("2145"),s=o.toolbox.restore;function l(e){this.model=e}l.defaultOption={show:!0,icon:"M3.8,33.4 M47,18.9h9.8V8.7 M56.3,20.1 C52.1,9,40.5,0.6,26.8,2.1C12.6,3.7,1.6,16.2,2.1,30.6 M13,41.1H3.1v10.2 M3.7,39.9c4.2,11.1,15.8,19.5,29.5,18 c14.2-1.6,25.2-14.1,24.7-28.5",title:s.title};var u=l.prototype;u.onclick=function(e,t,i){a.clear(e),t.dispatchAction({type:"restore",from:this.uid})},r.register("restore",l),n.registerAction({type:"restore",event:"restore",update:"prepareAndUpdate"},(function(e,t){t.resetOption("recreate")}));var c=l;e.exports=c},e057:function(e,t,i){var n=i("4e08"),a=(n.__DEV__,i("6d8b")),o=i("2306"),r=i("c775"),s=r.getDefaultLabel,l=i("3301"),u=i("9d57"),c=u.getLayoutOnAxis,d=i("80f0"),h=i("4f85"),p=i("e887"),g=i("aa3e"),f=i("00d8"),m=i("307b"),v=i("eaeb"),y=i("471e"),x=["itemStyle"],_=["emphasis","itemStyle"],b=["label"],w=["emphasis","label"],S="e\0\0",M={cartesian2d:g,geo:f,singleAxis:m,polar:v,calendar:y};function I(e){var t,i=e.type;if("path"===i){var n=e.shape,a=null!=n.width&&null!=n.height?{x:n.x||0,y:n.y||0,width:n.width,height:n.height}:null,r=z(n);t=o.makePath(r,null,a,n.layout||"center"),t.__customPathData=r}else if("image"===i)t=new o.Image({}),t.__customImagePath=e.style.image;else if("text"===i)t=new o.Text({}),t.__customText=e.style.text;else{var s=o[i.charAt(0).toUpperCase()+i.slice(1)];t=new s}return t.__customGraphicType=i,t.name=e.name,t}function A(e,t,i,n,r,s,l){var u={},c=i.style||{};if(i.shape&&(u.shape=a.clone(i.shape)),i.position&&(u.position=i.position.slice()),i.scale&&(u.scale=i.scale.slice()),i.origin&&(u.origin=i.origin.slice()),i.rotation&&(u.rotation=i.rotation),"image"===e.type&&i.style){var d=u.style={};a.each(["x","y","width","height"],(function(t){T(t,d,c,e.style,s)}))}if("text"===e.type&&i.style){d=u.style={};a.each(["x","y"],(function(t){T(t,d,c,e.style,s)})),!c.hasOwnProperty("textFill")&&c.fill&&(c.textFill=c.fill),!c.hasOwnProperty("textStroke")&&c.stroke&&(c.textStroke=c.stroke)}if("group"!==e.type&&(e.useStyle(c),s)){e.style.opacity=0;var h=c.opacity;null==h&&(h=1),o.initProps(e,{style:{opacity:h}},n,t)}s?e.attr(u):o.updateProps(e,u,n,t),i.hasOwnProperty("z2")&&e.attr("z2",i.z2||0),i.hasOwnProperty("silent")&&e.attr("silent",i.silent),i.hasOwnProperty("invisible")&&e.attr("invisible",i.invisible),i.hasOwnProperty("ignore")&&e.attr("ignore",i.ignore),i.hasOwnProperty("info")&&e.attr("info",i.info);var p=i.styleEmphasis,g=!1===p;e.__cusHasEmphStl&&null==p||!e.__cusHasEmphStl&&g||(o.setElementHoverStyle(e,p),e.__cusHasEmphStl=!g),l&&o.setAsHoverStyleTrigger(e,!g)}function T(e,t,i,n,a){null==i[e]||a||(t[e]=i[e],i[e]=n[e])}function D(e,t,i,n){var r=e.get("renderItem"),l=e.coordinateSystem,u={};l&&(u=l.prepareCustoms?l.prepareCustoms():M[l.type](l));var d,h,p,g,f,m=a.defaults({getWidth:n.getWidth,getHeight:n.getHeight,getZr:n.getZr,getDevicePixelRatio:n.getDevicePixelRatio,value:I,style:A,styleEmphasis:T,visual:D,barLayout:C,currentSeriesIndices:P,font:N},u.api||{}),v={context:{},seriesId:e.id,seriesName:e.name,seriesIndex:e.seriesIndex,coordSys:u.coordSys,dataInsideLength:t.count(),encode:L(e.getData())},y=!0;return function(e,i){return d=e,y=!0,r&&r(a.defaults({dataIndexInside:e,dataIndex:t.getRawIndex(e),actionType:i?i.type:null},v),m)};function S(e){null==e&&(e=d),y&&(h=t.getItemModel(e),p=h.getModel(b),g=h.getModel(w),f=t.getItemVisual(e,"color"),y=!1)}function I(e,i){return null==i&&(i=d),t.get(t.getDimension(e||0),i)}function A(i,n){null==n&&(n=d),S(n);var r=h.getModel(x).getItemStyle();null!=f&&(r.fill=f);var l=t.getItemVisual(n,"opacity");return null!=l&&(r.opacity=l),o.setTextStyle(r,p,null,{autoColor:f,isRectText:!0}),r.text=p.getShallow("show")?a.retrieve2(e.getFormattedLabel(n,"normal"),s(t,n)):null,i&&a.extend(r,i),r}function T(i,n){null==n&&(n=d),S(n);var r=h.getModel(_).getItemStyle();return o.setTextStyle(r,g,null,{isRectText:!0},!0),r.text=g.getShallow("show")?a.retrieve3(e.getFormattedLabel(n,"emphasis"),e.getFormattedLabel(n,"normal"),s(t,n)):null,i&&a.extend(r,i),r}function D(e,i){return null==i&&(i=d),t.getItemVisual(i,e)}function C(e){if(l.getBaseAxis){var t=l.getBaseAxis();return c(a.defaults({axis:t},e),n)}}function P(){return i.getCurrentSeriesIndices()}function N(e){return o.getFont(e,i)}}function L(e){var t={};return a.each(e.dimensions,(function(i,n){var a=e.getDimensionInfo(i);if(!a.isExtraCoord){var o=a.coordDim,r=t[o]=t[o]||[];r[a.coordDimIndex]=n}})),t}function C(e,t,i,n,a,o){return e=P(e,t,i,n,a,o,!0),e&&o.setItemGraphicEl(t,e),e}function P(e,t,i,n,a,o,r){var s=!i;i=i||{};var l=i.type,u=i.shape,c=i.style;if(e&&(s||null!=l&&l!==e.__customGraphicType||"path"===l&&O(u)&&z(u)!==e.__customPathData||"image"===l&&B(c,"image")&&c.image!==e.__customImagePath||"text"===l&&B(u,"text")&&c.text!==e.__customText)&&(a.remove(e),e=null),!s){var d=!e;return!e&&(e=I(i)),A(e,t,i,n,o,d,r),"group"===l&&N(e,t,i,n,o),a.add(e),e}}function N(e,t,i,n,a){var o=i.children,r=o?o.length:0,s=i.$mergeChildren,l="byName"===s||i.diffChildrenByName,u=!1===s;if(r||l||u)if(l)R({oldChildren:e.children()||[],newChildren:o||[],dataIndex:t,animatableModel:n,group:e,data:a});else{u&&e.removeAll();for(var c=0;c<r;c++)o[c]&&P(e.childAt(c),t,o[c],n,e,a)}}function R(e){new d(e.oldChildren,e.newChildren,V,V,e).add(k).update(k).remove(E).execute()}function V(e,t){var i=e&&e.name;return null!=i?i:S+t}function k(e,t){var i=this.context,n=null!=e?i.newChildren[e]:null,a=null!=t?i.oldChildren[t]:null;P(a,i.dataIndex,n,i.animatableModel,i.group,i.data)}function E(e){var t=this.context,i=t.oldChildren[e];i&&t.group.remove(i)}function z(e){return e&&(e.pathData||e.d)}function O(e){return e&&(e.hasOwnProperty("pathData")||e.hasOwnProperty("d"))}function B(e,t){return e&&e.hasOwnProperty(t)}h.extend({type:"series.custom",dependencies:["grid","polar","geo","singleAxis","calendar"],defaultOption:{coordinateSystem:"cartesian2d",zlevel:0,z:2,legendHoverLink:!0,useTransform:!0},getInitialData:function(e,t){return l(this.getSource(),this)},getDataParams:function(e,t,i){var n=h.prototype.getDataParams.apply(this,arguments);return i&&(n.info=i.info),n}}),p.extend({type:"custom",_data:null,render:function(e,t,i,n){var a=this._data,o=e.getData(),r=this.group,s=D(e,o,t,i);o.diff(a).add((function(t){C(null,t,s(t,n),e,r,o)})).update((function(t,i){var l=a.getItemGraphicEl(i);C(l,t,s(t,n),e,r,o)})).remove((function(e){var t=a.getItemGraphicEl(e);t&&r.remove(t)})).execute(),this._data=o},incrementalPrepareRender:function(e,t,i){this.group.removeAll(),this._data=null},incrementalRender:function(e,t,i,n,a){var o=t.getData(),r=D(t,o,i,n);function s(e){e.isGroup||(e.incremental=!0,e.useHoverLayer=!0)}for(var l=e.start;l<e.end;l++){var u=C(null,l,r(l,a),t,this.group,o);u.traverse(s)}},dispose:a.noop,filterForExposedEvent:function(e,t,i,n){var a=t.element;if(null==a||i.name===a)return!0;while((i=i.parent)&&i!==this.group)if(i.name===a)return!0;return!1}})},e468:function(e,t,i){var n=i("e46b"),a=i("6d8b"),o=i("2f45"),r=o.getDimensionTypeByAxis,s={_baseAxisDim:null,getInitialData:function(e,t){var i,o,s=t.getComponent("xAxis",this.get("xAxisIndex")),l=t.getComponent("yAxis",this.get("yAxisIndex")),u=s.get("type"),c=l.get("type");"category"===u?(e.layout="horizontal",i=s.getOrdinalMeta(),o=!0):"category"===c?(e.layout="vertical",i=l.getOrdinalMeta(),o=!0):e.layout=e.layout||"horizontal";var d=["x","y"],h="horizontal"===e.layout?0:1,p=this._baseAxisDim=d[h],g=d[1-h],f=[s,l],m=f[h].get("type"),v=f[1-h].get("type"),y=e.data;if(y&&o){var x=[];a.each(y,(function(e,t){var i;e.value&&a.isArray(e.value)?(i=e.value.slice(),e.value.unshift(t)):a.isArray(e)?(i=e.slice(),e.unshift(t)):i=e,x.push(i)})),e.data=x}var _=this.defaultValueDimensions;return n(this,{coordDimensions:[{name:p,type:r(m),ordinalMeta:i,otherDims:{tooltip:!1,itemName:0},dimsDef:["base"]},{name:g,type:r(v),dimsDef:_.slice()}],dimensionsCount:_.length+1})},getBaseAxis:function(){var e=this._baseAxisDim;return this.ecModel.getComponent(e+"Axis",this.get(e+"AxisIndex")).axis}};t.seriesModelMixin=s},e46b:function(e,t,i){var n=i("b1d4"),a=i("6179"),o=i("6d8b"),r=o.extend,s=o.isArray;function l(e,t,i){t=s(t)&&{coordDimensions:t}||r({},t);var o=e.getSource(),l=n(o,t),u=new a(l,e);return u.initData(o,i),u}e.exports=l},e6cd:function(e,t,i){var n=i("6d8b");function a(){var e,t=[],i={};return{add:function(e,a,o,r,s){return n.isString(r)&&(s=r,r=0),!i[e.id]&&(i[e.id]=1,t.push({el:e,target:a,time:o,delay:r,easing:s}),!0)},done:function(t){return e=t,this},start:function(){for(var n=t.length,a=0,o=t.length;a<o;a++){var r=t[a];r.el.animateTo(r.target,r.time,r.delay,r.easing,s)}return this;function s(){n--,n||(t.length=0,i={},e&&e())}}}}t.createWrap=a},e7aa:function(e,t,i){var n=i("2306"),a=i("c775"),o=a.getDefaultLabel;function r(e,t,i,a,r,l,u){var c=i.getModel("label"),d=i.getModel("emphasis.label");n.setLabelStyle(e,t,c,d,{labelFetcher:r,labelDataIndex:l,defaultText:o(r.getData(),l),isRectText:!0,autoColor:a}),s(e),s(t)}function s(e,t){"outside"===e.textPosition&&(e.textPosition=t)}t.setLabel=r},e9f9:function(e,t,i){var n=i("4942"),a=i("d3a4"),o=i("6d8b"),r=o.each;function s(e){return parseInt(e,10)}function l(e,t){a.initVML(),this.root=e,this.storage=t;var i=document.createElement("div"),n=document.createElement("div");i.style.cssText="display:inline-block;overflow:hidden;position:relative;width:300px;height:150px;",n.style.cssText="position:absolute;left:0;top:0;",e.appendChild(i),this._vmlRoot=n,this._vmlViewport=i,this.resize();var o=t.delFromStorage,r=t.addToStorage;t.delFromStorage=function(e){o.call(t,e),e&&e.onRemove&&e.onRemove(n)},t.addToStorage=function(e){e.onAdd&&e.onAdd(n),r.call(t,e)},this._firstPaint=!0}function u(e){return function(){n('In IE8.0 VML mode painter not support method "'+e+'"')}}l.prototype={constructor:l,getType:function(){return"vml"},getViewportRoot:function(){return this._vmlViewport},getViewportRootOffset:function(){var e=this.getViewportRoot();if(e)return{offsetLeft:e.offsetLeft||0,offsetTop:e.offsetTop||0}},refresh:function(){var e=this.storage.getDisplayList(!0,!0);this._paintList(e)},_paintList:function(e){for(var t=this._vmlRoot,i=0;i<e.length;i++){var n=e[i];n.invisible||n.ignore?(n.__alreadyNotVisible||n.onRemove(t),n.__alreadyNotVisible=!0):(n.__alreadyNotVisible&&n.onAdd(t),n.__alreadyNotVisible=!1,n.__dirty&&(n.beforeBrush&&n.beforeBrush(),(n.brushVML||n.brush).call(n,t),n.afterBrush&&n.afterBrush())),n.__dirty=!1}this._firstPaint&&(this._vmlViewport.appendChild(t),this._firstPaint=!1)},resize:function(e,t){e=null==e?this._getWidth():e,t=null==t?this._getHeight():t;if(this._width!==e||this._height!==t){this._width=e,this._height=t;var i=this._vmlViewport.style;i.width=e+"px",i.height=t+"px"}},dispose:function(){this.root.innerHTML="",this._vmlRoot=this._vmlViewport=this.storage=null},getWidth:function(){return this._width},getHeight:function(){return this._height},clear:function(){this._vmlViewport&&this.root.removeChild(this._vmlViewport)},_getWidth:function(){var e=this.root,t=e.currentStyle;return(e.clientWidth||s(t.width))-s(t.paddingLeft)-s(t.paddingRight)|0},_getHeight:function(){var e=this.root,t=e.currentStyle;return(e.clientHeight||s(t.height))-s(t.paddingTop)-s(t.paddingBottom)|0}},r(["getLayer","insertLayer","eachLayer","eachBuiltinLayer","eachOtherLayer","getLayers","modLayer","delLayer","clearLayer","toDataURL","pathToImage"],(function(e){l.prototype[e]=u(e)}));var c=l;e.exports=c},eabf:function(e,t,i){var n=i("6d8b");function a(e){e&&n.isArray(e.series)&&n.each(e.series,(function(e){n.isObject(e)&&"k"===e.type&&(e.type="candlestick")}))}e.exports=a},eaea:function(e,t,i){var n=i("3eba"),a=i("6d8b"),o=i("22d1"),r=i("60e3"),s=i("5f14"),l=i("2b8c"),u=i("e0d3"),c=i("3842"),d=s.mapVisual,h=s.eachVisual,p=a.isArray,g=a.each,f=c.asc,m=c.linearMap,v=a.noop,y=n.extendComponentModel({type:"visualMap",dependencies:["series"],stateList:["inRange","outOfRange"],replacableOptionKeys:["inRange","outOfRange","target","controller","color"],dataBound:[-1/0,1/0],layoutMode:{type:"box",ignoreSize:!0},defaultOption:{show:!0,zlevel:0,z:4,seriesIndex:"all",min:0,max:200,dimension:null,inRange:null,outOfRange:null,left:0,right:null,top:null,bottom:0,itemWidth:null,itemHeight:null,inverse:!1,orient:"vertical",backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",contentColor:"#5793f3",inactiveColor:"#aaa",borderWidth:0,padding:5,textGap:10,precision:0,color:null,formatter:null,text:null,textStyle:{color:"#333"}},init:function(e,t,i){this._dataExtent,this.targetVisuals={},this.controllerVisuals={},this.textStyleModel,this.itemSize,this.mergeDefaultAndTheme(e,i)},optionUpdated:function(e,t){var i=this.option;o.canvasSupported||(i.realtime=!1),!t&&l.replaceVisualOption(i,e,this.replacableOptionKeys),this.textStyleModel=this.getModel("textStyle"),this.resetItemSize(),this.completeVisualOption()},resetVisual:function(e){var t=this.stateList;e=a.bind(e,this),this.controllerVisuals=l.createVisualMappings(this.option.controller,t,e),this.targetVisuals=l.createVisualMappings(this.option.target,t,e)},getTargetSeriesIndices:function(){var e=this.option.seriesIndex,t=[];return null==e||"all"===e?this.ecModel.eachSeries((function(e,i){t.push(i)})):t=u.normalizeToArray(e),t},eachTargetSeries:function(e,t){a.each(this.getTargetSeriesIndices(),(function(i){e.call(t,this.ecModel.getSeriesByIndex(i))}),this)},isTargetSeries:function(e){var t=!1;return this.eachTargetSeries((function(i){i===e&&(t=!0)})),t},formatValueText:function(e,t,i){var n,o,r=this.option,s=r.precision,l=this.dataBound,u=r.formatter;return i=i||["<",">"],a.isArray(e)&&(e=e.slice(),n=!0),o=t?e:n?[c(e[0]),c(e[1])]:c(e),a.isString(u)?u.replace("{value}",n?o[0]:o).replace("{value2}",n?o[1]:o):a.isFunction(u)?n?u(e[0],e[1]):u(e):n?e[0]===l[0]?i[0]+" "+o[1]:e[1]===l[1]?i[1]+" "+o[0]:o[0]+" - "+o[1]:o;function c(e){return e===l[0]?"min":e===l[1]?"max":(+e).toFixed(Math.min(s,20))}},resetExtent:function(){var e=this.option,t=f([e.min,e.max]);this._dataExtent=t},getDataDimension:function(e){var t=this.option.dimension,i=e.dimensions;if(null!=t||i.length){if(null!=t)return e.getDimension(t);for(var n=e.dimensions,a=n.length-1;a>=0;a--){var o=n[a],r=e.getDimensionInfo(o);if(!r.isCalculationCoord)return o}}},getExtent:function(){return this._dataExtent.slice()},completeVisualOption:function(){var e=this.ecModel,t=this.option,i={inRange:t.inRange,outOfRange:t.outOfRange},n=t.target||(t.target={}),o=t.controller||(t.controller={});a.merge(n,i),a.merge(o,i);var l=this.isCategory();function u(i){p(t.color)&&!i.inRange&&(i.inRange={color:t.color.slice().reverse()}),i.inRange=i.inRange||{color:e.get("gradientColor")},g(this.stateList,(function(e){var t=i[e];if(a.isString(t)){var n=r.get(t,"active",l);n?(i[e]={},i[e][t]=n):delete i[e]}}),this)}function c(e,t,i){var n=e[t],a=e[i];n&&!a&&(a=e[i]={},g(n,(function(e,t){if(s.isValidType(t)){var i=r.get(t,"inactive",l);null!=i&&(a[t]=i,"color"!==t||a.hasOwnProperty("opacity")||a.hasOwnProperty("colorAlpha")||(a.opacity=[0,0]))}})))}function f(e){var t=(e.inRange||{}).symbol||(e.outOfRange||{}).symbol,i=(e.inRange||{}).symbolSize||(e.outOfRange||{}).symbolSize,n=this.get("inactiveColor");g(this.stateList,(function(o){var r=this.itemSize,s=e[o];s||(s=e[o]={color:l?n:[n]}),null==s.symbol&&(s.symbol=t&&a.clone(t)||(l?"roundRect":["roundRect"])),null==s.symbolSize&&(s.symbolSize=i&&a.clone(i)||(l?r[0]:[r[0],r[0]])),s.symbol=d(s.symbol,(function(e){return"none"===e||"square"===e?"roundRect":e}));var u=s.symbolSize;if(null!=u){var c=-1/0;h(u,(function(e){e>c&&(c=e)})),s.symbolSize=d(u,(function(e){return m(e,[0,c],[0,r[0]],!0)}))}}),this)}u.call(this,n),u.call(this,o),c.call(this,n,"inRange","outOfRange"),f.call(this,o)},resetItemSize:function(){this.itemSize=[parseFloat(this.get("itemWidth")),parseFloat(this.get("itemHeight"))]},isCategory:function(){return!!this.option.categories},setSelected:v,getValueState:v,getVisualMeta:v}),x=y;e.exports=x},eaeb:function(e,t,i){var n=i("6d8b");function a(e,t){return n.map(["Radius","Angle"],(function(i,n){var a=this["get"+i+"Axis"](),o=t[n],r=e[n]/2,s="dataTo"+i,l="category"===a.type?a.getBandWidth():Math.abs(a[s](o-r)-a[s](o+r));return"Angle"===i&&(l=l*Math.PI/180),l}),this)}function o(e){var t=e.getRadiusAxis(),i=e.getAngleAxis(),o=t.getExtent();return o[0]>o[1]&&o.reverse(),{coordSys:{type:"polar",cx:e.cx,cy:e.cy,r:o[1],r0:o[0]},api:{coord:n.bind((function(n){var a=t.dataToRadius(n[0]),o=i.dataToAngle(n[1]),r=e.coordToPoint([a,o]);return r.push(a,o*Math.PI/180),r})),size:n.bind(a,e)}}}e.exports=o},ebf9:function(e,t,i){var n=i("3eba");n.registerAction("legendScroll","legendscroll",(function(e,t){var i=e.scrollDataIndex;null!=i&&t.eachComponent({mainType:"legend",subType:"scroll",query:e},(function(e){e.setScrollDataIndex(i)}))}))},ecf8:function(e,t,i){var n=i("6d8b"),a=i("3842"),o=a.parsePercent,r=n.each;function s(e){var t=l(e);r(t,(function(e){var t=e.seriesModels;t.length&&(u(e),r(t,(function(t,i){c(t,e.boxOffsetList[i],e.boxWidthList[i])})))}))}function l(e){var t=[],i=[];return e.eachSeriesByType("boxplot",(function(e){var a=e.getBaseAxis(),o=n.indexOf(i,a);o<0&&(o=i.length,i[o]=a,t[o]={axis:a,seriesModels:[]}),t[o].seriesModels.push(e)})),t}function u(e){var t,i,a=e.axis,s=e.seriesModels,l=s.length,u=e.boxWidthList=[],c=e.boxOffsetList=[],d=[];if("category"===a.type)i=a.getBandWidth();else{var h=0;r(s,(function(e){h=Math.max(h,e.getData().count())})),t=a.getExtent(),Math.abs(t[1]-t[0])}r(s,(function(e){var t=e.get("boxWidth");n.isArray(t)||(t=[t,t]),d.push([o(t[0],i)||0,o(t[1],i)||0])}));var p=.8*i-2,g=p/l*.3,f=(p-g*(l-1))/l,m=f/2-p/2;r(s,(function(e,t){c.push(m),m+=g+f,u.push(Math.min(Math.max(f,d[t][0]),d[t][1]))}))}function c(e,t,i){var n=e.coordinateSystem,a=e.getData(),o=i/2,r="horizontal"===e.get("layout")?0:1,s=1-r,l=["x","y"],u=a.mapDimension(l[r]),c=a.mapDimension(l[s],!0);if(!(null==u||c.length<5))for(var d=0;d<a.count();d++){var h=a.get(u,d),p=x(h,c[2],d),g=x(h,c[0],d),f=x(h,c[1],d),m=x(h,c[3],d),v=x(h,c[4],d),y=[];_(y,f,0),_(y,m,1),y.push(g,f,v,m),b(y,g),b(y,v),b(y,p),a.setItemLayout(d,{initBaseline:p[s],ends:y})}function x(e,i,o){var l,u=a.get(i,o),c=[];return c[r]=e,c[s]=u,isNaN(e)||isNaN(u)?l=[NaN,NaN]:(l=n.dataToPoint(c),l[r]+=t),l}function _(e,t,i){var n=t.slice(),a=t.slice();n[r]+=o,a[r]-=o,i?e.push(n,a):e.push(a,n)}function b(e,t){var i=t.slice(),n=t.slice();i[r]-=o,n[r]+=o,e.push(i,n)}}e.exports=s},edaf:function(e,t,i){var n=i("6d8b"),a=i("6cb7"),o=i("6179"),r=i("e0d3"),s=a.extend({type:"timeline",layoutMode:"box",defaultOption:{zlevel:0,z:4,show:!0,axisType:"time",realtime:!0,left:"20%",top:null,right:"20%",bottom:0,width:null,height:40,padding:5,controlPosition:"left",autoPlay:!1,rewind:!1,loop:!0,playInterval:2e3,currentIndex:0,itemStyle:{},label:{color:"#000"},data:[]},init:function(e,t,i){this._data,this._names,this.mergeDefaultAndTheme(e,i),this._initData()},mergeOption:function(e){s.superApply(this,"mergeOption",arguments),this._initData()},setCurrentIndex:function(e){null==e&&(e=this.option.currentIndex);var t=this._data.count();this.option.loop?e=(e%t+t)%t:(e>=t&&(e=t-1),e<0&&(e=0)),this.option.currentIndex=e},getCurrentIndex:function(){return this.option.currentIndex},isIndexMax:function(){return this.getCurrentIndex()>=this._data.count()-1},setPlayState:function(e){this.option.autoPlay=!!e},getPlayState:function(){return!!this.option.autoPlay},_initData:function(){var e=this.option,t=e.data||[],i=e.axisType,a=this._names=[];if("category"===i){var s=[];n.each(t,(function(e,t){var i,o=r.getDataItemValue(e);n.isObject(e)?(i=n.clone(e),i.value=t):i=t,s.push(i),n.isString(o)||null!=o&&!isNaN(o)||(o=""),a.push(o+"")})),t=s}var l={category:"ordinal",time:"time"}[i]||"number",u=this._data=new o([{name:"value",type:l}],this);u.initData(t,a)},getData:function(){return this._data},getCategories:function(){if("category"===this.get("axisType"))return this._names.slice()}}),l=s;e.exports=l},edb9:function(e,t,i){var n=i("6d8b");function a(e,t){t=t||{};var i=e.coordinateSystem,a=e.axis,o={},r=a.position,s=a.orient,l=i.getRect(),u=[l.x,l.x+l.width,l.y,l.y+l.height],c={horizontal:{top:u[2],bottom:u[3]},vertical:{left:u[0],right:u[1]}};o.position=["vertical"===s?c.vertical[r]:u[0],"horizontal"===s?c.horizontal[r]:u[3]];var d={horizontal:0,vertical:1};o.rotation=Math.PI/2*d[s];var h={top:-1,bottom:1,right:1,left:-1};o.labelDirection=o.tickDirection=o.nameDirection=h[r],e.get("axisTick.inside")&&(o.tickDirection=-o.tickDirection),n.retrieve(t.labelInside,e.get("axisLabel.inside"))&&(o.labelDirection=-o.labelDirection);var p=t.rotate;return null==p&&(p=e.get("axisLabel.rotate")),o.labelRotation="top"===r?-p:p,o.z2=1,o}t.layout=a},ee66:function(e,t,i){var n=i("3eba"),a=i("6d8b"),o=i("2306"),r=i("eda2"),s=i("3842"),l={EN:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],CN:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"]},u={EN:["S","M","T","W","T","F","S"],CN:["日","一","二","三","四","五","六"]},c=n.extendComponentView({type:"calendar",_tlpoints:null,_blpoints:null,_firstDayOfMonth:null,_firstDayPoints:null,render:function(e,t,i){var n=this.group;n.removeAll();var a=e.coordinateSystem,o=a.getRangeInfo(),r=a.getOrient();this._renderDayRect(e,o,n),this._renderLines(e,o,r,n),this._renderYearText(e,o,r,n),this._renderMonthText(e,r,n),this._renderWeekText(e,o,r,n)},_renderDayRect:function(e,t,i){for(var n=e.coordinateSystem,a=e.getModel("itemStyle").getItemStyle(),r=n.getCellWidth(),s=n.getCellHeight(),l=t.start.time;l<=t.end.time;l=n.getNextNDay(l,1).time){var u=n.dataToRect([l],!1).tl,c=new o.Rect({shape:{x:u[0],y:u[1],width:r,height:s},cursor:"default",style:a});i.add(c)}},_renderLines:function(e,t,i,n){var a=this,o=e.coordinateSystem,r=e.getModel("splitLine.lineStyle").getLineStyle(),s=e.get("splitLine.show"),l=r.lineWidth;this._tlpoints=[],this._blpoints=[],this._firstDayOfMonth=[],this._firstDayPoints=[];for(var u=t.start,c=0;u.time<=t.end.time;c++){h(u.formatedDate),0===c&&(u=o.getDateInfo(t.start.y+"-"+t.start.m));var d=u.date;d.setMonth(d.getMonth()+1),u=o.getDateInfo(d)}function h(t){a._firstDayOfMonth.push(o.getDateInfo(t)),a._firstDayPoints.push(o.dataToRect([t],!1).tl);var l=a._getLinePointsOfOneWeek(e,t,i);a._tlpoints.push(l[0]),a._blpoints.push(l[l.length-1]),s&&a._drawSplitline(l,r,n)}h(o.getNextNDay(t.end.time,1).formatedDate),s&&this._drawSplitline(a._getEdgesPoints(a._tlpoints,l,i),r,n),s&&this._drawSplitline(a._getEdgesPoints(a._blpoints,l,i),r,n)},_getEdgesPoints:function(e,t,i){var n=[e[0].slice(),e[e.length-1].slice()],a="horizontal"===i?0:1;return n[0][a]=n[0][a]-t/2,n[1][a]=n[1][a]+t/2,n},_drawSplitline:function(e,t,i){var n=new o.Polyline({z2:20,shape:{points:e},style:t});i.add(n)},_getLinePointsOfOneWeek:function(e,t,i){var n=e.coordinateSystem;t=n.getDateInfo(t);for(var a=[],o=0;o<7;o++){var r=n.getNextNDay(t.time,o),s=n.dataToRect([r.time],!1);a[2*r.day]=s.tl,a[2*r.day+1]=s["horizontal"===i?"bl":"tr"]}return a},_formatterLabel:function(e,t){return"string"===typeof e&&e?r.formatTplSimple(e,t):"function"===typeof e?e(t):t.nameMap},_yearTextPositionControl:function(e,t,i,n,a){t=t.slice();var o=["center","bottom"];"bottom"===n?(t[1]+=a,o=["center","top"]):"left"===n?t[0]-=a:"right"===n?(t[0]+=a,o=["center","top"]):t[1]-=a;var r=0;return"left"!==n&&"right"!==n||(r=Math.PI/2),{rotation:r,position:t,style:{textAlign:o[0],textVerticalAlign:o[1]}}},_renderYearText:function(e,t,i,n){var a=e.getModel("yearLabel");if(a.get("show")){var r=a.get("margin"),s=a.get("position");s||(s="horizontal"!==i?"top":"left");var l=[this._tlpoints[this._tlpoints.length-1],this._blpoints[0]],u=(l[0][0]+l[1][0])/2,c=(l[0][1]+l[1][1])/2,d="horizontal"===i?0:1,h={top:[u,l[d][1]],bottom:[u,l[1-d][1]],left:[l[1-d][0],c],right:[l[d][0],c]},p=t.start.y;+t.end.y>+t.start.y&&(p=p+"-"+t.end.y);var g=a.get("formatter"),f={start:t.start.y,end:t.end.y,nameMap:p},m=this._formatterLabel(g,f),v=new o.Text({z2:30});o.setTextStyle(v.style,a,{text:m}),v.attr(this._yearTextPositionControl(v,h[s],i,s,r)),n.add(v)}},_monthTextPositionControl:function(e,t,i,n,a){var o="left",r="top",s=e[0],l=e[1];return"horizontal"===i?(l+=a,t&&(o="center"),"start"===n&&(r="bottom")):(s+=a,t&&(r="middle"),"start"===n&&(o="right")),{x:s,y:l,textAlign:o,textVerticalAlign:r}},_renderMonthText:function(e,t,i){var n=e.getModel("monthLabel");if(n.get("show")){var r=n.get("nameMap"),s=n.get("margin"),u=n.get("position"),c=n.get("align"),d=[this._tlpoints,this._blpoints];a.isString(r)&&(r=l[r.toUpperCase()]||[]);var h="start"===u?0:1,p="horizontal"===t?0:1;s="start"===u?-s:s;for(var g="center"===c,f=0;f<d[h].length-1;f++){var m=d[h][f].slice(),v=this._firstDayOfMonth[f];if(g){var y=this._firstDayPoints[f];m[p]=(y[p]+d[0][f+1][p])/2}var x=n.get("formatter"),_=r[+v.m-1],b={yyyy:v.y,yy:(v.y+"").slice(2),MM:v.m,M:+v.m,nameMap:_},w=this._formatterLabel(x,b),S=new o.Text({z2:30});a.extend(o.setTextStyle(S.style,n,{text:w}),this._monthTextPositionControl(m,g,t,u,s)),i.add(S)}}},_weekTextPositionControl:function(e,t,i,n,a){var o="center",r="middle",s=e[0],l=e[1],u="start"===i;return"horizontal"===t?(s=s+n+(u?1:-1)*a[0]/2,o=u?"right":"left"):(l=l+n+(u?1:-1)*a[1]/2,r=u?"bottom":"top"),{x:s,y:l,textAlign:o,textVerticalAlign:r}},_renderWeekText:function(e,t,i,n){var r=e.getModel("dayLabel");if(r.get("show")){var l=e.coordinateSystem,c=r.get("position"),d=r.get("nameMap"),h=r.get("margin"),p=l.getFirstDayOfWeek();a.isString(d)&&(d=u[d.toUpperCase()]||[]);var g=l.getNextNDay(t.end.time,7-t.lweek).time,f=[l.getCellWidth(),l.getCellHeight()];h=s.parsePercent(h,f["horizontal"===i?0:1]),"start"===c&&(g=l.getNextNDay(t.start.time,-(7+t.fweek)).time,h=-h);for(var m=0;m<7;m++){var v=l.getNextNDay(g,m),y=l.dataToRect([v.time],!1).center,x=m;x=Math.abs((m+p)%7);var _=new o.Text({z2:30});a.extend(o.setTextStyle(_.style,r,{text:d[x]}),this._weekTextPositionControl(y,i,c,h,f)),n.add(_)}}}});e.exports=c},ee95:function(e,t,i){var n=i("3eba"),a=i("66a4");i("a04e"),i("3942"),i("dd7e"),i("347f"),n.registerPreprocessor(a)},ee98:function(e,t){function i(e){return e instanceof Array||(e=[e,e]),e}var n="lineStyle.opacity".split("."),a={seriesType:"lines",reset:function(e,t,a){var o=i(e.get("symbol")),r=i(e.get("symbolSize")),s=e.getData();function l(e,t){var a=e.getItemModel(t),o=i(a.getShallow("symbol",!0)),r=i(a.getShallow("symbolSize",!0)),s=a.get(n);o[0]&&e.setItemVisual(t,"fromSymbol",o[0]),o[1]&&e.setItemVisual(t,"toSymbol",o[1]),r[0]&&e.setItemVisual(t,"fromSymbolSize",r[0]),r[1]&&e.setItemVisual(t,"toSymbolSize",r[1]),e.setItemVisual(t,"opacity",s)}return s.setVisual("fromSymbol",o&&o[0]),s.setVisual("toSymbol",o&&o[1]),s.setVisual("fromSymbolSize",r&&r[0]),s.setVisual("toSymbolSize",r&&r[1]),s.setVisual("opacity",e.get(n)),{dataEach:s.hasItemOption?l:null}}};e.exports=a},eeea:function(e,t,i){var n=i("4e08"),a=(n.__DEV__,i("3eba")),o=i("6d8b"),r=i("0141"),s=i("f934"),l=i("3842"),u=i("5b87"),c=i("ec34");function d(e,t){var i=e.get("boundingCoords");if(null!=i){var n=i[0],a=i[1];isNaN(n[0])||isNaN(n[1])||isNaN(a[0])||isNaN(a[1])||this.setBoundingRect(n[0],n[1],a[0]-n[0],a[1]-n[1])}var o,r=this.getBoundingRect(),u=e.get("layoutCenter"),c=e.get("layoutSize"),d=t.getWidth(),h=t.getHeight(),p=r.width/r.height*this.aspectScale,g=!1;if(u&&c&&(u=[l.parsePercent(u[0],d),l.parsePercent(u[1],h)],c=l.parsePercent(c,Math.min(d,h)),isNaN(u[0])||isNaN(u[1])||isNaN(c)||(g=!0)),g){var f={};p>1?(f.width=c,f.height=c/p):(f.height=c,f.width=c*p),f.y=u[1]-f.height/2,f.x=u[0]-f.width/2}else o=e.getBoxLayoutParams(),o.aspect=p,f=s.getLayoutRect(o,{width:d,height:h});this.setViewRect(f.x,f.y,f.width,f.height),this.setCenter(e.get("center")),this.setZoom(e.get("zoom"))}function h(e,t){o.each(t.get("geoCoord"),(function(t,i){e.addGeoCoord(i,t)}))}var p={dimensions:r.prototype.dimensions,create:function(e,t){var i=[];e.eachComponent("geo",(function(e,n){var a=e.get("map"),o=e.get("aspectScale"),s=!0,l=c.retrieveMap(a);l&&l[0]&&"svg"===l[0].type?(null==o&&(o=1),s=!1):null==o&&(o=.75);var u=new r(a+n,a,e.get("nameMap"),s);u.aspectScale=o,u.zoomLimit=e.get("scaleLimit"),i.push(u),h(u,e),e.coordinateSystem=u,u.model=e,u.resize=d,u.resize(e,t)})),e.eachSeries((function(e){var t=e.get("coordinateSystem");if("geo"===t){var n=e.get("geoIndex")||0;e.coordinateSystem=i[n]}}));var n={};return e.eachSeriesByType("map",(function(e){if(!e.getHostGeoModel()){var t=e.getMapType();n[t]=n[t]||[],n[t].push(e)}})),o.each(n,(function(e,n){var a=o.map(e,(function(e){return e.get("nameMap")})),s=new r(n,n,o.mergeAll(a));s.zoomLimit=o.retrieve.apply(null,o.map(e,(function(e){return e.get("scaleLimit")}))),i.push(s),s.resize=d,s.aspectScale=e[0].get("aspectScale"),s.resize(e[0],t),o.each(e,(function(e){e.coordinateSystem=s,h(s,e)}))})),i},getFilledRegions:function(e,t,i){for(var n=(e||[]).slice(),a=o.createHashMap(),r=0;r<n.length;r++)a.set(n[r].name,n[r]);var s=u.load(t,i);return o.each(s.regions,(function(e){var t=e.name;!a.get(t)&&n.push({name:t})})),n}};a.registerCoordinateSystem("geo",p);var g=p;e.exports=g},ef2b:function(e,t,i){var n=i("401b"),a=n.scaleAndAdd;function o(e,t,i){for(var o=i.rect,r=o.width,s=o.height,l=[o.x+r/2,o.y+s/2],u=null==i.gravity?.1:i.gravity,c=0;c<e.length;c++){var d=e[c];d.p||(d.p=n.create(r*(Math.random()-.5)+l[0],s*(Math.random()-.5)+l[1])),d.pp=n.clone(d.p),d.edges=null}var h=.6;return{warmUp:function(){h=.5},setFixed:function(t){e[t].fixed=!0},setUnfixed:function(t){e[t].fixed=!1},step:function(i){for(var o=[],r=e.length,s=0;s<t.length;s++){var c=t[s],d=c.n1,p=c.n2;n.sub(o,p.p,d.p);var g=n.len(o)-c.d,f=p.w/(d.w+p.w);isNaN(f)&&(f=0),n.normalize(o,o),!d.fixed&&a(d.p,d.p,o,f*g*h),!p.fixed&&a(p.p,p.p,o,-(1-f)*g*h)}for(s=0;s<r;s++){var m=e[s];m.fixed||(n.sub(o,l,m.p),a(m.p,m.p,o,u*h))}for(s=0;s<r;s++){d=e[s];for(var v=s+1;v<r;v++){p=e[v];n.sub(o,p.p,d.p);g=n.len(o);0===g&&(n.set(o,Math.random()-.5,Math.random()-.5),g=1);var y=(d.rep+p.rep)/g/g;!d.fixed&&a(d.pp,d.pp,o,y),!p.fixed&&a(p.pp,p.pp,o,-y)}}var x=[];for(s=0;s<r;s++){m=e[s];m.fixed||(n.sub(x,m.p,m.pp),a(m.p,m.p,x,h),n.copy(m.pp,m.p))}h*=.992,i&&i(e,t,h<.01)}}}t.forceLayout=o},ef6a:function(e,t){function i(e,t,i,o,r,s){t[0]=a(t[0],i),t[1]=a(t[1],i),e=e||0;var l=i[1]-i[0];null!=r&&(r=a(r,[0,l])),null!=s&&(s=Math.max(s,null!=r?r:0)),"all"===o&&(r=s=Math.abs(t[1]-t[0]),o=0);var u=n(t,o);t[o]+=e;var c=r||0,d=i.slice();u.sign<0?d[0]+=c:d[1]-=c,t[o]=a(t[o],d);var h=n(t,o);null!=r&&(h.sign!==u.sign||h.span<r)&&(t[1-o]=t[o]+u.sign*r);h=n(t,o);return null!=s&&h.span>s&&(t[1-o]=t[o]+h.sign*s),t}function n(e,t){var i=e[t]-e[1-t];return{span:Math.abs(i),sign:i>0?-1:i<0?1:t?-1:1}}function a(e,t){return Math.min(t[1],Math.max(t[0],e))}e.exports=i},ef97a:function(e,t,i){var n=i("3eba");i("2163"),i("6cd8"),i("bf9b");var a=i("7f96"),o=i("ca29");n.registerVisual(a("tree","circle")),n.registerLayout(o)},f138:function(e,t,i){var n=i("2306"),a=i("dcb3"),o=i("ff2e"),r=i("edb9"),s=i("6679"),l=["x","y"],u=["width","height"],c=a.extend({makeElOption:function(e,t,i,n,a){var s=i.axis,l=s.coordinateSystem,u=p(l,1-h(s)),c=l.dataToPoint(t)[0],g=n.get("type");if(g&&"none"!==g){var f=o.buildElStyle(n),m=d[g](s,c,u,f);m.style=f,e.graphicKey=m.type,e.pointer=m}var v=r.layout(i);o.buildCartesianSingleLabelElOption(t,e,v,i,n,a)},getHandleTransform:function(e,t,i){var n=r.layout(t,{labelInside:!1});return n.labelMargin=i.get("handle.margin"),{position:o.getTransformedPosition(t.axis,e,n),rotation:n.rotation+(n.labelDirection<0?Math.PI:0)}},updateHandleTransform:function(e,t,i,n){var a=i.axis,o=a.coordinateSystem,r=h(a),s=p(o,r),l=e.position;l[r]+=t[r],l[r]=Math.min(s[1],l[r]),l[r]=Math.max(s[0],l[r]);var u=p(o,1-r),c=(u[1]+u[0])/2,d=[c,c];return d[r]=l[r],{position:l,rotation:e.rotation,cursorPoint:d,tooltipOption:{verticalAlign:"middle"}}}}),d={line:function(e,t,i,a){var r=o.makeLineShape([t,i[0]],[t,i[1]],h(e));return n.subPixelOptimizeLine({shape:r,style:a}),{type:"Line",shape:r}},shadow:function(e,t,i,n){var a=e.getBandWidth(),r=i[1]-i[0];return{type:"Rect",shape:o.makeRectShape([t-a/2,i[0]],[a,r],h(e))}}};function h(e){return e.isHorizontal()?0:1}function p(e,t){var i=e.getRect();return[i[l[t]],i[l[t]]+i[u[t]]]}s.registerAxisPointerClass("SingleAxisPointer",c);var g=c;e.exports=g},f14c:function(e,t,i){var n=i("84d5"),a=i("f934"),o=a.mergeLayoutParam,r=a.getLayoutParams,s=n.extend({type:"legend.scroll",setScrollDataIndex:function(e){this.option.scrollDataIndex=e},defaultOption:{scrollDataIndex:0,pageButtonItemGap:5,pageButtonGap:null,pageButtonPosition:"end",pageFormatter:"{current}/{total}",pageIcons:{horizontal:["M0,0L12,-10L12,10z","M0,0L-12,-10L-12,10z"],vertical:["M0,0L20,0L10,-20z","M0,0L20,0L10,20z"]},pageIconColor:"#2f4554",pageIconInactiveColor:"#aaa",pageIconSize:15,pageTextStyle:{color:"#333"},animationDurationUpdate:800},init:function(e,t,i,n){var a=r(e);s.superCall(this,"init",e,t,i,n),l(this,e,a)},mergeOption:function(e,t){s.superCall(this,"mergeOption",e,t),l(this,this.option,e)},getOrient:function(){return"vertical"===this.get("orient")?{index:1,name:"vertical"}:{index:0,name:"horizontal"}}});function l(e,t,i){var n=e.getOrient(),a=[1,1];a[n.index]=0,o(t,i,{type:"box",ignoreSize:a})}var u=s;e.exports=u},f170:function(e,t,i){i("a87d");var n=i("697e7"),a=n.registerPainter,o=i("e9f9");a("vml",o)},f306:function(e,t,i){var n=i("3eba"),a=i("6d8b"),o=i("88b3"),r=i("6569");i("849b"),i("217c"),i("c515");var s=5;n.extendComponentView({type:"parallel",render:function(e,t,i){this._model=e,this._api=i,this._handlers||(this._handlers={},a.each(l,(function(e,t){i.getZr().on(t,this._handlers[t]=a.bind(e,this))}),this)),o.createOrUpdate(this,"_throttledDispatchExpand",e.get("axisExpandRate"),"fixRate")},dispose:function(e,t){a.each(this._handlers,(function(e,i){t.getZr().off(i,e)})),this._handlers=null},_throttledDispatchExpand:function(e){this._dispatchExpand(e)},_dispatchExpand:function(e){e&&this._api.dispatchAction(a.extend({type:"parallelAxisExpand"},e))}});var l={mousedown:function(e){u(this,"click")&&(this._mouseDownPoint=[e.offsetX,e.offsetY])},mouseup:function(e){var t=this._mouseDownPoint;if(u(this,"click")&&t){var i=[e.offsetX,e.offsetY],n=Math.pow(t[0]-i[0],2)+Math.pow(t[1]-i[1],2);if(n>s)return;var a=this._model.coordinateSystem.getSlidedAxisExpandWindow([e.offsetX,e.offsetY]);"none"!==a.behavior&&this._dispatchExpand({axisExpandWindow:a.axisExpandWindow})}this._mouseDownPoint=null},mousemove:function(e){if(!this._mouseDownPoint&&u(this,"mousemove")){var t=this._model,i=t.coordinateSystem.getSlidedAxisExpandWindow([e.offsetX,e.offsetY]),n=i.behavior;"jump"===n&&this._throttledDispatchExpand.debounceNextCall(t.get("axisExpandDebounce")),this._throttledDispatchExpand("none"===n?null:{axisExpandWindow:i.axisExpandWindow,animation:"jump"===n&&null})}}};function u(e,t){var i=e._model;return i.get("axisExpandable")&&i.get("axisExpandTriggerOn")===t}n.registerPreprocessor(r)},f31f:function(e,t,i){var n=i("4e08"),a=(n.__DEV__,i("3eba")),o=i("6d8b"),r=i("2b8c"),s=i("4319"),l=["#ddd"],u=a.extendComponentModel({type:"brush",dependencies:["geo","grid","xAxis","yAxis","parallel","series"],defaultOption:{toolbox:null,brushLink:null,seriesIndex:"all",geoIndex:null,xAxisIndex:null,yAxisIndex:null,brushType:"rect",brushMode:"single",transformable:!0,brushStyle:{borderWidth:1,color:"rgba(120,140,180,0.3)",borderColor:"rgba(120,140,180,0.8)"},throttleType:"fixRate",throttleDelay:0,removeOnClick:!0,z:1e4},areas:[],brushType:null,brushOption:{},coordInfoList:[],optionUpdated:function(e,t){var i=this.option;!t&&r.replaceVisualOption(i,e,["inBrush","outOfBrush"]);var n=i.inBrush=i.inBrush||{};i.outOfBrush=i.outOfBrush||{color:l},n.hasOwnProperty("liftZ")||(n.liftZ=5)},setAreas:function(e){e&&(this.areas=o.map(e,(function(e){return c(this.option,e)}),this))},setBrushOption:function(e){this.brushOption=c(this.option,e),this.brushType=this.brushOption.brushType}});function c(e,t){return o.merge({brushType:e.brushType,brushMode:e.brushMode,transformable:e.transformable,brushStyle:new s(e.brushStyle).getItemStyle(),removeOnClick:e.removeOnClick,z:e.z},t,!0)}var d=u;e.exports=d},f4a2:function(e,t,i){var n=i("9850"),a=i("c526"),o=a.onIrrelevantElement,r=i("2306");function s(e){return e=c(e),function(t,i){return r.clipPointsByRect(t,e)}}function l(e,t){return e=c(e),function(i){var n=null!=t?t:i,a=n?e.width:e.height,o=n?e.x:e.y;return[o,o+(a||0)]}}function u(e,t,i){return e=c(e),function(n,a,r){return e.contain(a[0],a[1])&&!o(n,t,i)}}function c(e){return n.create(e)}t.makeRectPanelClipPath=s,t.makeLinearBrushOtherExtent=l,t.makeRectIsTargetByCursor=u},f610:function(e,t,i){var n=i("2306"),a=i("f934"),o=i("6d8b"),r=i("55ac"),s=r.wrapTreePathInfo,l=8,u=8,c=5;function d(e){this.group=new n.Group,e.add(this.group)}function h(e,t,i,n,a,o){var r=[[a?e:e-c,t],[e+i,t],[e+i,t+n],[a?e:e-c,t+n]];return!o&&r.splice(2,0,[e+i+c,t+n/2]),!a&&r.push([e,t+n/2]),r}function p(e,t,i){e.eventData={componentType:"series",componentSubType:"treemap",componentIndex:t.componentIndex,seriesIndex:t.componentIndex,seriesName:t.name,seriesType:"treemap",selfType:"breadcrumb",nodeData:{dataIndex:i&&i.dataIndex,name:i&&i.name},treePathInfo:i&&s(i,t)}}d.prototype={constructor:d,render:function(e,t,i,n){var o=e.getModel("breadcrumb"),r=this.group;if(r.removeAll(),o.get("show")&&i){var s=o.getModel("itemStyle"),l=s.getModel("textStyle"),u={pos:{left:o.get("left"),right:o.get("right"),top:o.get("top"),bottom:o.get("bottom")},box:{width:t.getWidth(),height:t.getHeight()},emptyItemWidth:o.get("emptyItemWidth"),totalWidth:0,renderList:[]};this._prepare(i,u,l),this._renderContent(e,u,s,l,n),a.positionElement(r,u.pos,u.box)}},_prepare:function(e,t,i){for(var n=e;n;n=n.parentNode){var a=n.getModel().get("name"),o=i.getTextRect(a),r=Math.max(o.width+2*l,t.emptyItemWidth);t.totalWidth+=r+u,t.renderList.push({node:n,text:a,width:r})}},_renderContent:function(e,t,i,r,s){for(var l=0,c=t.emptyItemWidth,d=e.get("breadcrumb.height"),g=a.getAvailableSize(t.pos,t.box),f=t.totalWidth,m=t.renderList,v=m.length-1;v>=0;v--){var y=m[v],x=y.node,_=y.width,b=y.text;f>g.width&&(f-=_-c,_=c,b=null);var w=new n.Polygon({shape:{points:h(l,0,_,d,v===m.length-1,0===v)},style:o.defaults(i.getItemStyle(),{lineJoin:"bevel",text:b,textFill:r.getTextColor(),textFont:r.getFont()}),z:10,onclick:o.curry(s,x)});this.group.add(w),p(w,e,x),l+=_+u}},remove:function(){this.group.removeAll()}};var g=d;e.exports=g},f6ed:function(e,t,i){var n=i("6d8b");function a(e,t){var i={};return n.each(e,(function(e){e.each(e.mapDimension("value"),(function(t,n){var a="ec-"+e.getName(n);i[a]=i[a]||[],isNaN(t)||i[a].push(t)}))})),e[0].map(e[0].mapDimension("value"),(function(n,a){for(var o,r="ec-"+e[0].getName(a),s=0,l=1/0,u=-1/0,c=i[r].length,d=0;d<c;d++)l=Math.min(l,i[r][d]),u=Math.max(u,i[r][d]),s+=i[r][d];return o="min"===t?l:"max"===t?u:"average"===t?s/c:s,0===c?NaN:o}))}function o(e){var t={};e.eachSeriesByType("map",(function(e){var i=e.getHostGeoModel(),n=i?"o"+i.id:"i"+e.getMapType();(t[n]=t[n]||[]).push(e)})),n.each(t,(function(e,t){for(var i=a(n.map(e,(function(e){return e.getData()})),e[0].get("mapValueCalculation")),o=0;o<e.length;o++)e[o].originalData=e[o].getData();for(o=0;o<e.length;o++)e[o].seriesGroup=e,e[o].needsDrawMap=0===o&&!e[o].getHostGeoModel(),e[o].setData(i.cloneShallow()),e[o].mainSeries=e[0]}))}e.exports=o},f7c6:function(e,t,i){var n=i("3eba"),a=i("e46b"),o=i("6d8b"),r=i("e0d3"),s=i("3842"),l=s.getPercentWithPrecision,u=i("7023"),c=i("2b17"),d=c.retrieveRawAttr,h=n.extendSeriesModel({type:"series.pie",init:function(e){h.superApply(this,"init",arguments),this.legendDataProvider=function(){return this.getRawData()},this.updateSelectedMap(this._createSelectableList()),this._defaultLabelLine(e)},mergeOption:function(e){h.superCall(this,"mergeOption",e),this.updateSelectedMap(this._createSelectableList())},getInitialData:function(e,t){return a(this,["value"])},_createSelectableList:function(){for(var e=this.getRawData(),t=e.mapDimension("value"),i=[],n=0,a=e.count();n<a;n++)i.push({name:e.getName(n),value:e.get(t,n),selected:d(e,n,"selected")});return i},getDataParams:function(e){var t=this.getData(),i=h.superCall(this,"getDataParams",e),n=[];return t.each(t.mapDimension("value"),(function(e){n.push(e)})),i.percent=l(n,e,t.hostModel.get("percentPrecision")),i.$vars.push("percent"),i},_defaultLabelLine:function(e){r.defaultEmphasis(e,"labelLine",["show"]);var t=e.labelLine,i=e.emphasis.labelLine;t.show=t.show&&e.label.show,i.show=i.show&&e.emphasis.label.show},defaultOption:{zlevel:0,z:2,legendHoverLink:!0,hoverAnimation:!0,center:["50%","50%"],radius:[0,"75%"],clockwise:!0,startAngle:90,minAngle:0,selectedOffset:10,hoverOffset:10,avoidLabelOverlap:!0,percentPrecision:2,stillShowZeroSum:!0,label:{rotate:!1,show:!0,position:"outer"},labelLine:{show:!0,length:15,length2:15,smooth:!1,lineStyle:{width:1,type:"solid"}},itemStyle:{borderWidth:1},animationType:"expansion",animationEasing:"cubicOut"}});o.mixin(h,u);var p=h;e.exports=p},fa52:function(e,t,i){var n=i("3eba");i("febc"),i("dcea");var a=i("2f91"),o=i("ecf8");n.registerVisual(a),n.registerLayout(o)},fc82:function(e,t,i){var n=i("4e08"),a=(n.__DEV__,i("6d8b")),o=i("1fab"),r=i("2306"),s=i("a4fe"),l=i("80f0"),u=a.curry,c=a.each,d=a.map,h=Math.min,p=Math.max,g=Math.pow,f=1e4,m=6,v=6,y="globalPan",x={w:[0,0],e:[0,1],n:[1,0],s:[1,1]},_={w:"ew",e:"ew",n:"ns",s:"ns",ne:"nesw",sw:"nesw",nw:"nwse",se:"nwse"},b={brushStyle:{lineWidth:2,stroke:"rgba(0,0,0,0.3)",fill:"rgba(0,0,0,0.1)"},transformable:!0,brushMode:"single",removeOnClick:!1},w=0;function S(e){o.call(this),this._zr=e,this.group=new r.Group,this._brushType,this._brushOption,this._panels,this._track=[],this._dragging,this._covers=[],this._creatingCover,this._creatingPanel,this._enableGlobalPan,this._uid="brushController_"+w++,this._handlers={},c(ie,(function(e,t){this._handlers[t]=a.bind(e,this)}),this)}function M(e,t){var i=e._zr;e._enableGlobalPan||s.take(i,y,e._uid),c(e._handlers,(function(e,t){i.on(t,e)})),e._brushType=t.brushType,e._brushOption=a.merge(a.clone(b),t,!0)}function I(e){var t=e._zr;s.release(t,y,e._uid),c(e._handlers,(function(e,i){t.off(i,e)})),e._brushType=e._brushOption=null}function A(e,t){var i=ae[t.brushType].createCover(e,t);return i.__brushOption=t,L(i,t),e.group.add(i),i}function T(e,t){var i=P(t);return i.endCreating&&(i.endCreating(e,t),L(t,t.__brushOption)),t}function D(e,t){var i=t.__brushOption;P(t).updateCoverShape(e,t,i.range,i)}function L(e,t){var i=t.z;null==i&&(i=f),e.traverse((function(e){e.z=i,e.z2=i}))}function C(e,t){P(t).updateCommon(e,t),D(e,t)}function P(e){return ae[e.__brushOption.brushType]}function N(e,t,i){var n,a=e._panels;if(!a)return!0;var o=e._transform;return c(a,(function(e){e.isTargetByCursor(t,i,o)&&(n=e)})),n}function R(e,t){var i=e._panels;if(!i)return!0;var n=t.__brushOption.panelId;return null==n||i[n]}function V(e){var t=e._covers,i=t.length;return c(t,(function(t){e.group.remove(t)}),e),t.length=0,!!i}function k(e,t){var i=d(e._covers,(function(e){var t=e.__brushOption,i=a.clone(t.range);return{brushType:t.brushType,panelId:t.panelId,range:i}}));e.trigger("brush",i,{isEnd:!!t.isEnd,removeOnClick:!!t.removeOnClick})}function E(e){var t=e._track;if(!t.length)return!1;var i=t[t.length-1],n=t[0],a=i[0]-n[0],o=i[1]-n[1],r=g(a*a+o*o,.5);return r>m}function z(e){var t=e.length-1;return t<0&&(t=0),[e[0],e[t]]}function O(e,t,i,n){var a=new r.Group;return a.add(new r.Rect({name:"main",style:W(i),silent:!0,draggable:!0,cursor:"move",drift:u(e,t,a,"nswe"),ondragend:u(k,t,{isEnd:!0})})),c(n,(function(i){a.add(new r.Rect({name:i,style:{opacity:0},draggable:!0,silent:!0,invisible:!0,drift:u(e,t,a,i),ondragend:u(k,t,{isEnd:!0})}))})),a}function B(e,t,i,n){var a=n.brushStyle.lineWidth||0,o=p(a,v),r=i[0][0],s=i[1][0],l=r-a/2,u=s-a/2,c=i[0][1],d=i[1][1],h=c-o+a/2,g=d-o+a/2,f=c-r,m=d-s,y=f+a,x=m+a;H(e,t,"main",r,s,f,m),n.transformable&&(H(e,t,"w",l,u,o,x),H(e,t,"e",h,u,o,x),H(e,t,"n",l,u,y,o),H(e,t,"s",l,g,y,o),H(e,t,"nw",l,u,o,o),H(e,t,"ne",h,u,o,o),H(e,t,"sw",l,g,o,o),H(e,t,"se",h,g,o,o))}function G(e,t){var i=t.__brushOption,n=i.transformable,a=t.childAt(0);a.useStyle(W(i)),a.attr({silent:!n,cursor:n?"move":"default"}),c(["w","e","n","s","se","sw","ne","nw"],(function(i){var a=t.childOfName(i),o=U(e,i);a&&a.attr({silent:!n,invisible:!n,cursor:n?_[o]+"-resize":null})}))}function H(e,t,i,n,a,o,r){var s=t.childOfName(i);s&&s.setShape(K(q(e,t,[[n,a],[n+o,a+r]])))}function W(e){return a.defaults({strokeNoScale:!0},e.brushStyle)}function F(e,t,i,n){var a=[h(e,i),h(t,n)],o=[p(e,i),p(t,n)];return[[a[0],o[0]],[a[1],o[1]]]}function Z(e){return r.getTransform(e.group)}function U(e,t){if(t.length>1){t=t.split("");var i=[U(e,t[0]),U(e,t[1])];return("e"===i[0]||"w"===i[0])&&i.reverse(),i.join("")}var n={w:"left",e:"right",n:"top",s:"bottom"},a={left:"w",right:"e",top:"n",bottom:"s"};i=r.transformDirection(n[t],Z(e));return a[i]}function j(e,t,i,n,a,o,r,s){var l=n.__brushOption,u=e(l.range),d=Y(i,o,r);c(a.split(""),(function(e){var t=x[e];u[t[0]][t[1]]+=d[t[0]]})),l.range=t(F(u[0][0],u[1][0],u[0][1],u[1][1])),C(i,n),k(i,{isEnd:!1})}function X(e,t,i,n,a){var o=t.__brushOption.range,r=Y(e,i,n);c(o,(function(e){e[0]+=r[0],e[1]+=r[1]})),C(e,t),k(e,{isEnd:!1})}function Y(e,t,i){var n=e.group,a=n.transformCoordToLocal(t,i),o=n.transformCoordToLocal(0,0);return[a[0]-o[0],a[1]-o[1]]}function q(e,t,i){var n=R(e,t);return n&&!0!==n?n.clipPath(i,e._transform):a.clone(i)}function K(e){var t=h(e[0][0],e[1][0]),i=h(e[0][1],e[1][1]),n=p(e[0][0],e[1][0]),a=p(e[0][1],e[1][1]);return{x:t,y:i,width:n-t,height:a-i}}function $(e,t,i){if(e._brushType){var n=e._zr,a=e._covers,o=N(e,t,i);if(!e._dragging)for(var r=0;r<a.length;r++){var s=a[r].__brushOption;if(o&&(!0===o||s.panelId===o.panelId)&&ae[s.brushType].contain(a[r],i[0],i[1]))return}o&&n.setCursorStyle("crosshair")}}function J(e){var t=e.event;t.preventDefault&&t.preventDefault()}function Q(e,t,i){return e.childOfName("main").contain(t,i)}function ee(e,t,i,n){var o,r=e._creatingCover,s=e._creatingPanel,l=e._brushOption;if(e._track.push(i.slice()),E(e)||r){if(s&&!r){"single"===l.brushMode&&V(e);var u=a.clone(l);u.brushType=te(u.brushType,s),u.panelId=!0===s?null:s.panelId,r=e._creatingCover=A(e,u),e._covers.push(r)}if(r){var c=ae[te(e._brushType,s)],d=r.__brushOption;d.range=c.getCreatingRange(q(e,r,e._track)),n&&(T(e,r),c.updateCommon(e,r)),D(e,r),o={isEnd:n}}}else n&&"single"===l.brushMode&&l.removeOnClick&&N(e,t,i)&&V(e)&&(o={isEnd:n,removeOnClick:!0});return o}function te(e,t){return"auto"===e?t.defaultBrushType:e}S.prototype={constructor:S,enableBrush:function(e){return this._brushType&&I(this),e.brushType&&M(this,e),this},setPanels:function(e){if(e&&e.length){var t=this._panels={};a.each(e,(function(e){t[e.panelId]=a.clone(e)}))}else this._panels=null;return this},mount:function(e){e=e||{},this._enableGlobalPan=e.enableGlobalPan;var t=this.group;return this._zr.add(t),t.attr({position:e.position||[0,0],rotation:e.rotation||0,scale:e.scale||[1,1]}),this._transform=t.getLocalTransform(),this},eachCover:function(e,t){c(this._covers,e,t)},updateCovers:function(e){e=a.map(e,(function(e){return a.merge(a.clone(b),e,!0)}));var t="\0-brush-index-",i=this._covers,n=this._covers=[],o=this,r=this._creatingCover;return new l(i,e,u,s).add(c).update(c).remove(d).execute(),this;function s(e,i){return(null!=e.id?e.id:t+i)+"-"+e.brushType}function u(e,t){return s(e.__brushOption,t)}function c(t,a){var s=e[t];if(null!=a&&i[a]===r)n[t]=i[a];else{var l=n[t]=null!=a?(i[a].__brushOption=s,i[a]):T(o,A(o,s));C(o,l)}}function d(e){i[e]!==r&&o.group.remove(i[e])}},unmount:function(){return this.enableBrush(!1),V(this),this._zr.remove(this.group),this},dispose:function(){this.unmount(),this.off()}},a.mixin(S,o);var ie={mousedown:function(e){if(this._dragging)ne.call(this,e);else if(!e.target||!e.target.draggable){J(e);var t=this.group.transformCoordToLocal(e.offsetX,e.offsetY);this._creatingCover=null;var i=this._creatingPanel=N(this,e,t);i&&(this._dragging=!0,this._track=[t.slice()])}},mousemove:function(e){var t=this.group.transformCoordToLocal(e.offsetX,e.offsetY);if($(this,e,t),this._dragging){J(e);var i=ee(this,e,t,!1);i&&k(this,i)}},mouseup:ne};function ne(e){if(this._dragging){J(e);var t=this.group.transformCoordToLocal(e.offsetX,e.offsetY),i=ee(this,e,t,!0);this._dragging=!1,this._track=[],this._creatingCover=null,i&&k(this,i)}}var ae={lineX:oe(0),lineY:oe(1),rect:{createCover:function(e,t){return O(u(j,(function(e){return e}),(function(e){return e})),e,t,["w","e","n","s","se","sw","ne","nw"])},getCreatingRange:function(e){var t=z(e);return F(t[1][0],t[1][1],t[0][0],t[0][1])},updateCoverShape:function(e,t,i,n){B(e,t,i,n)},updateCommon:G,contain:Q},polygon:{createCover:function(e,t){var i=new r.Group;return i.add(new r.Polyline({name:"main",style:W(t),silent:!0})),i},getCreatingRange:function(e){return e},endCreating:function(e,t){t.remove(t.childAt(0)),t.add(new r.Polygon({name:"main",draggable:!0,drift:u(X,e,t),ondragend:u(k,e,{isEnd:!0})}))},updateCoverShape:function(e,t,i,n){t.childAt(0).setShape({points:q(e,t,i)})},updateCommon:G,contain:Q}};function oe(e){return{createCover:function(t,i){return O(u(j,(function(t){var i=[t,[0,100]];return e&&i.reverse(),i}),(function(t){return t[e]})),t,i,[["w","e"],["n","s"]][e])},getCreatingRange:function(t){var i=z(t),n=h(i[0][e],i[1][e]),a=p(i[0][e],i[1][e]);return[n,a]},updateCoverShape:function(t,i,n,a){var o,r=R(t,i);if(!0!==r&&r.getLinearBrushOtherExtent)o=r.getLinearBrushOtherExtent(e,t._transform);else{var s=t._zr;o=[0,[s.getWidth(),s.getHeight()][1-e]]}var l=[n,o];e&&l.reverse(),B(t,i,l,a)},updateCommon:G,contain:Q}}var re=S;e.exports=re},febc:function(e,t,i){var n=i("6d8b"),a=i("4f85"),o=i("e468"),r=o.seriesModelMixin,s=a.extend({type:"series.boxplot",dependencies:["xAxis","yAxis","grid"],defaultValueDimensions:[{name:"min",defaultTooltip:!0},{name:"Q1",defaultTooltip:!0},{name:"median",defaultTooltip:!0},{name:"Q3",defaultTooltip:!0},{name:"max",defaultTooltip:!0}],dimensions:null,defaultOption:{zlevel:0,z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,hoverAnimation:!0,layout:null,boxWidth:[7,50],itemStyle:{color:"#fff",borderWidth:1},emphasis:{itemStyle:{borderWidth:2,shadowBlur:5,shadowOffsetX:2,shadowOffsetY:2,shadowColor:"rgba(0,0,0,0.4)"}},animationEasing:"elasticOut",animationDuration:800}});n.mixin(s,r,!0);var l=s;e.exports=l},fecb:function(e,t,i){var n=i("6d8b"),a=i("2145"),o=i("29a8"),r=o.toolbox.brush;function s(e,t,i){this.model=e,this.ecModel=t,this.api=i,this._brushType,this._brushMode}s.defaultOption={show:!0,type:["rect","polygon","lineX","lineY","keep","clear"],icon:{rect:"M7.3,34.7 M0.4,10V-0.2h9.8 M89.6,10V-0.2h-9.8 M0.4,60v10.2h9.8 M89.6,60v10.2h-9.8 M12.3,22.4V10.5h13.1 M33.6,10.5h7.8 M49.1,10.5h7.8 M77.5,22.4V10.5h-13 M12.3,31.1v8.2 M77.7,31.1v8.2 M12.3,47.6v11.9h13.1 M33.6,59.5h7.6 M49.1,59.5 h7.7 M77.5,47.6v11.9h-13",polygon:"M55.2,34.9c1.7,0,3.1,1.4,3.1,3.1s-1.4,3.1-3.1,3.1 s-3.1-1.4-3.1-3.1S53.5,34.9,55.2,34.9z M50.4,51c1.7,0,3.1,1.4,3.1,3.1c0,1.7-1.4,3.1-3.1,3.1c-1.7,0-3.1-1.4-3.1-3.1 C47.3,52.4,48.7,51,50.4,51z M55.6,37.1l1.5-7.8 M60.1,13.5l1.6-8.7l-7.8,4 M59,19l-1,5.3 M24,16.1l6.4,4.9l6.4-3.3 M48.5,11.6 l-5.9,3.1 M19.1,12.8L9.7,5.1l1.1,7.7 M13.4,29.8l1,7.3l6.6,1.6 M11.6,18.4l1,6.1 M32.8,41.9 M26.6,40.4 M27.3,40.2l6.1,1.6 M49.9,52.1l-5.6-7.6l-4.9-1.2",lineX:"M15.2,30 M19.7,15.6V1.9H29 M34.8,1.9H40.4 M55.3,15.6V1.9H45.9 M19.7,44.4V58.1H29 M34.8,58.1H40.4 M55.3,44.4 V58.1H45.9 M12.5,20.3l-9.4,9.6l9.6,9.8 M3.1,29.9h16.5 M62.5,20.3l9.4,9.6L62.3,39.7 M71.9,29.9H55.4",lineY:"M38.8,7.7 M52.7,12h13.2v9 M65.9,26.6V32 M52.7,46.3h13.2v-9 M24.9,12H11.8v9 M11.8,26.6V32 M24.9,46.3H11.8v-9 M48.2,5.1l-9.3-9l-9.4,9.2 M38.9-3.9V12 M48.2,53.3l-9.3,9l-9.4-9.2 M38.9,62.3V46.4",keep:"M4,10.5V1h10.3 M20.7,1h6.1 M33,1h6.1 M55.4,10.5V1H45.2 M4,17.3v6.6 M55.6,17.3v6.6 M4,30.5V40h10.3 M20.7,40 h6.1 M33,40h6.1 M55.4,30.5V40H45.2 M21,18.9h62.9v48.6H21V18.9z",clear:"M22,14.7l30.9,31 M52.9,14.7L22,45.7 M4.7,16.8V4.2h13.1 M26,4.2h7.8 M41.6,4.2h7.8 M70.3,16.8V4.2H57.2 M4.7,25.9v8.6 M70.3,25.9v8.6 M4.7,43.2v12.6h13.1 M26,55.8h7.8 M41.6,55.8h7.8 M70.3,43.2v12.6H57.2"},title:n.clone(r.title)};var l=s.prototype;l.render=l.updateView=function(e,t,i){var a,o,r;t.eachComponent({mainType:"brush"},(function(e){a=e.brushType,o=e.brushOption.brushMode||"single",r|=e.areas.length})),this._brushType=a,this._brushMode=o,n.each(e.get("type",!0),(function(t){e.setIconStatus(t,("keep"===t?"multiple"===o:"clear"===t?r:t===a)?"emphasis":"normal")}))},l.getIcons=function(){var e=this.model,t=e.get("icon",!0),i={};return n.each(e.get("type",!0),(function(e){t[e]&&(i[e]=t[e])})),i},l.onclick=function(e,t,i){var n=this._brushType,a=this._brushMode;"clear"===i?(t.dispatchAction({type:"axisAreaSelect",intervals:[]}),t.dispatchAction({type:"brush",command:"clear",areas:[]})):t.dispatchAction({type:"takeGlobalCursor",key:"brush",brushOption:{brushType:"keep"===i?n:n!==i&&i,brushMode:"keep"===i?"multiple"===a?"single":"multiple":a}})},a.register("brush",s);var u=s;e.exports=u}}]);