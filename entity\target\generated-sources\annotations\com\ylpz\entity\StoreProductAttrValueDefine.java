package com.ylpz.entity;

public interface StoreProductAttrValueDefine {
    String brokerage = "brokerage";
    String image = "image";
    String cost = "cost";
    String quantity = "quantity";
    String productId = "productId";
    String otPrice = "otPrice";
    String suk = "suk";
    String weight = "weight";
    String type = "type";
    String sales = "sales";
    String barCode = "barCode";
    String brokerageTwo = "brokerageTwo";
    String volume = "volume";
    String price = "price";
    String quota = "quota";
    String quotaShow = "quotaShow";
    String unique = "unique";
    String attrValue = "attrValue";
    String id = "id";
    String stock = "stock";
    String isDel = "isDel";
}
