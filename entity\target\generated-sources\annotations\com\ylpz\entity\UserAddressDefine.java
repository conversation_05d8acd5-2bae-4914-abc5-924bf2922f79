package com.ylpz.entity;

public interface UserAddressDefine {
    String city = "city";
    String latitude = "latitude";
    String updateTime = "updateTime";
    String cityId = "cityId";
    String realName = "realName";
    String uid = "uid";
    String isDefault = "isDefault";
    String province = "province";
    String createTime = "createTime";
    String phone = "phone";
    String district = "district";
    String postCode = "postCode";
    String detail = "detail";
    String id = "id";
    String isDel = "isDel";
    String longitude = "longitude";
}
