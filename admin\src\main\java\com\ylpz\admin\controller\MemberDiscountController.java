package com.ylpz.admin.controller;

import com.ylpz.core.common.page.CommonPage;
import com.ylpz.core.common.request.MemberDiscountRequest;
import com.ylpz.core.common.request.MemberDiscountSearchRequest;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.core.common.response.CommonResult;
import com.ylpz.core.service.MemberDiscountService;
import com.ylpz.core.service.SystemUserLevelService;
import com.ylpz.model.discount.MemberDiscount;
import com.ylpz.model.system.SystemUserLevel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 会员折扣控制器
 */
@Slf4j
@RestController
@RequestMapping("api/admin/marketing/member/discount")
@Api(tags = "营销 -- 会员折扣")
public class MemberDiscountController {

    @Autowired
    private MemberDiscountService memberDiscountService;

    @Autowired
    private SystemUserLevelService systemUserLevelService;

    /**
     * 分页显示会员折扣列表
     * @param request 搜索条件
     * @param pageParamRequest 分页参数
     */
    @ApiOperation(value = "分页列表")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public CommonResult<CommonPage<MemberDiscount>> getList(@Validated MemberDiscountSearchRequest request,
                                                           @Validated PageParamRequest pageParamRequest) {
        CommonPage<MemberDiscount> commonPage = CommonPage.restPage(
                memberDiscountService.getList(request.getKeywords(), request.getStatus(), pageParamRequest));
        return CommonResult.success(commonPage);
    }

    /**
     * 新增会员折扣
     * @param request 新增参数
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public CommonResult<String> save(@RequestBody @Validated MemberDiscountRequest request) {
        MemberDiscount memberDiscount = new MemberDiscount();
        BeanUtils.copyProperties(request, memberDiscount);
        
        if (memberDiscountService.create(memberDiscount)) {
            return CommonResult.success();
        } else {
            return CommonResult.failed();
        }
    }

    /**
     * 修改会员折扣
     * @param request 修改参数
     */
    @ApiOperation(value = "修改")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public CommonResult<String> update(@RequestBody @Validated MemberDiscountRequest request) {
        if (request.getId() == null) {
            return CommonResult.validateFailed("会员折扣ID不能为空");
        }
        
        MemberDiscount memberDiscount = new MemberDiscount();
        BeanUtils.copyProperties(request, memberDiscount);
        
        if (memberDiscountService.update(memberDiscount)) {
            return CommonResult.success();
        } else {
            return CommonResult.failed();
        }
    }

    /**
     * 修改会员折扣状态
     * @param id 折扣id
     * @param status 状态
     */
    @ApiOperation(value = "修改状态")
    @RequestMapping(value = "/update/status", method = RequestMethod.POST)
    public CommonResult<String> updateStatus(@RequestParam Integer id, @RequestParam Boolean status) {
        if (memberDiscountService.updateStatus(id, status)) {
            return CommonResult.success();
        } else {
            return CommonResult.failed();
        }
    }

    /**
     * 获取会员折扣详情
     * @param id 折扣id
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/info", method = RequestMethod.GET)
    @ApiImplicitParam(name = "id", value = "会员折扣ID", required = true)
    public CommonResult<MemberDiscount> info(@RequestParam Integer id) {
        return CommonResult.success(memberDiscountService.getInfo(id));
    }

    /**
     * 删除会员折扣
     * @param id 折扣id
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    @ApiImplicitParam(name = "id", value = "会员折扣ID", required = true)
    public CommonResult<String> delete(@RequestParam Integer id) {
        if (memberDiscountService.delete(id)) {
            return CommonResult.success();
        } else {
            return CommonResult.failed();
        }
    }

    /**
     * 复制会员折扣
     * @param id 折扣id
     */
    @ApiOperation(value = "复制")
    @RequestMapping(value = "/copy", method = RequestMethod.POST)
    @ApiImplicitParam(name = "id", value = "会员折扣ID", required = true)
    public CommonResult<String> copy(@RequestParam Integer id) {
        if (memberDiscountService.copy(id)) {
            return CommonResult.success();
        } else {
            return CommonResult.failed();
        }
    }

    /**
     * 获取可用会员等级列表
     */
    @ApiOperation(value = "可用会员等级列表")
    @RequestMapping(value = "/level/list", method = RequestMethod.GET)
    public CommonResult<List<SystemUserLevel>> levelList() {
        return CommonResult.success(systemUserLevelService.getUsableList());
    }

    /**
     * 获取数据统计
     */
    @ApiOperation(value = "数据统计")
    @RequestMapping(value = "/statistics", method = RequestMethod.GET)
    public CommonResult<Map<String, Object>> statistics() {
        return CommonResult.success(memberDiscountService.getStatistics());
    }
} 