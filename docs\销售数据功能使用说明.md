# 销售数据功能使用说明

## 功能概述

销售数据功能为管理员提供了完整的销售数据统计和分析能力，支持按会员等级、时间范围、手机号等多维度查询销售数据，帮助管理员了解各会员的销售表现。

## 功能特性

### 1. 多维度筛选
- **手机号筛选**：支持按手机号进行精确或模糊查询
- **时间范围筛选**：支持按开始时间和结束时间筛选数据
- **会员等级筛选**：支持按VIP会员、SVIP会员等级别筛选
- **昵称筛选**：支持按用户昵称进行模糊查询

### 2. 销售统计指标
- **销售金额**：该会员的总销售金额（已支付订单的实付金额）
- **订单数量**：该会员的总订单数量
- **待售数量**：待处理的订单数量（待支付、待发货）
- **已售数量**：已完成的订单数量
- **自购金额**：该会员自己购买的金额

### 3. 数据展示
- **列表展示**：以表格形式展示销售数据，支持分页
- **统计汇总**：显示筛选条件下的总体统计数据
- **明细查看**：支持查看每个会员的详细销售明细
- **数据导出**：支持将销售数据导出为Excel文件

## API接口说明

### 1. 获取销售数据列表

**接口地址**：`GET /api/admin/sales/data/list`

**请求参数**：
- `mobile`：手机号，可选，支持模糊查询
- `startTime`：开始时间，格式：yyyy-MM-dd HH:mm:ss
- `endTime`：结束时间，格式：yyyy-MM-dd HH:mm:ss
- `memberLevel`：会员等级，可选（1-VIP会员，2-SVIP会员）
- `nickname`：用户昵称，可选，支持模糊查询
- `page`：页码，默认值1
- `limit`：每页数量，默认值10

**返回示例**：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "list": [
      {
        "uid": 123,
        "nickname": "张三",
        "phone": "13800138000",
        "memberLevel": "SVIP会员",
        "salesAmount": 14829.34,
        "orderCount": 43,
        "pendingCount": 150,
        "completedCount": 1852,
        "selfPurchaseAmount": 4523.00,
        "createTime": "2023-12-01 10:30:00"
      }
    ],
    "total": 100,
    "pageNum": 1,
    "pageSize": 10
  }
}
```

### 2. 获取销售数据统计

**接口地址**：`GET /api/admin/sales/data/statistics`

**请求参数**：
- `mobile`：手机号，可选
- `startTime`：开始时间，可选
- `endTime`：结束时间，可选
- `memberLevel`：会员等级，可选

**返回示例**：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "totalSalesAmount": 1482934.00,
    "totalOrderCount": 4300,
    "totalPendingCount": 15000,
    "totalCompletedCount": 185200,
    "totalSelfPurchaseAmount": 452300.00,
    "totalMemberCount": 150
  }
}
```

### 3. 获取会员销售明细

**接口地址**：`GET /api/admin/sales/data/detail/{uid}`

**请求参数**：
- `uid`：用户ID，必填（路径参数）
- `startTime`：开始时间，可选
- `endTime`：结束时间，可选
- `page`：页码，默认值1
- `limit`：每页数量，默认值10

**返回示例**：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "list": [
      {
        "orderId": 1001,
        "orderNo": "ORDER20231201001",
        "orderTime": "2023-12-01 10:30:00",
        "orderAmount": 299.00,
        "payAmount": 279.00,
        "orderStatus": "已完成",
        "productName": "测试商品A",
        "quantity": 2,
        "productImage": "/images/product1.jpg",
        "payType": "weixin",
        "isSelfPurchase": 1
      }
    ],
    "total": 50,
    "pageNum": 1,
    "pageSize": 10
  }
}
```

### 4. 导出销售数据

**接口地址**：`GET /api/admin/sales/data/export`

**请求参数**：
- `mobile`：手机号，可选
- `startTime`：开始时间，可选
- `endTime`：结束时间，可选
- `memberLevel`：会员等级，可选

**返回**：Excel文件下载

## 权限配置

### 1. 菜单权限
- `admin:sales:data`：销售数据主菜单权限
- `admin:sales:data:list`：查看销售数据列表权限
- `admin:sales:data:statistics`：查看销售数据统计权限
- `admin:sales:data:detail`：查看销售明细权限
- `admin:sales:data:export`：导出销售数据权限

### 2. 角色配置
系统默认为超级管理员分配所有销售数据权限，其他角色需要手动分配相应权限。

## 数据库配置

### 1. 执行SQL脚本
在部署前需要执行以下SQL脚本：
- `sql/create_sales_data_view.sql`：创建销售数据视图和索引
- `sql/insert_sales_data_permissions.sql`：插入权限配置
- `sql/insert_test_sales_data.sql`：插入测试数据（可选）

### 2. 系统配置
- `sales_data_enabled`：是否启用销售数据功能（1-启用，0-禁用）
- `sales_data_cache_time`：销售数据缓存时间（秒）
- `sales_data_export_limit`：单次导出数据的最大条数

## 使用步骤

### 1. 访问销售数据管理
1. 登录管理后台
2. 进入"销售数据"菜单
3. 选择"销售数据管理"

### 2. 筛选销售数据
1. 在筛选条件中输入手机号进行查询
2. 选择时间范围查看特定时期的销售数据
3. 选择会员等级筛选特定等级的会员数据
4. 点击"筛选"按钮执行查询

### 3. 查看销售明细
1. 在销售数据列表中找到目标会员
2. 点击"查看明细"按钮
3. 查看该会员的详细订单信息
4. 支持按时间范围筛选明细数据

### 4. 导出数据
1. 设置筛选条件
2. 点击"导出"按钮
3. 系统将生成Excel文件并自动下载
4. 导出文件包含所有显示的字段信息

## 注意事项

### 1. 性能优化
- 大数据量查询时建议使用时间范围筛选
- 导出功能限制单次最大导出条数
- 系统对频繁查询的数据进行了缓存优化

### 2. 数据准确性
- 销售金额统计基于已支付订单
- 订单状态实时更新
- 统计数据每5分钟更新一次缓存

### 3. 权限管理
- 确保操作人员具有相应的权限
- 敏感数据访问需要审计日志记录
- 定期检查权限配置的合理性

## 故障排除

### 1. 数据不显示
- 检查权限配置是否正确
- 确认数据库连接正常
- 验证筛选条件是否合理

### 2. 导出失败
- 检查服务器磁盘空间
- 确认导出数据量未超过限制
- 验证Excel库依赖是否正常

### 3. 性能问题
- 检查数据库索引是否创建
- 优化查询条件减少数据量
- 考虑增加缓存时间

## 技术支持

如遇到技术问题，请联系开发团队或查看系统日志获取详细错误信息。
