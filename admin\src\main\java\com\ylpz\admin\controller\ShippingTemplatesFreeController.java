package com.ylpz.admin.controller;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ylpz.core.common.request.ShippingTemplatesFreeRequest;
import com.ylpz.core.common.response.CommonResult;
import com.ylpz.core.service.ShippingTemplatesFreeService;
import com.ylpz.model.express.ShippingTemplatesFree;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * 包邮模板控制器
 */
@Slf4j
@RestController
@RequestMapping("api/admin/shipping/free")
@Api(tags = "设置 -- 物流 -- 包邮")
public class ShippingTemplatesFreeController {

    @Autowired
    private ShippingTemplatesFreeService shippingTemplatesFreeService;

    /**
     * 保存包邮模板
     */
    @ApiOperation(value = "保存包邮模板")
    @PostMapping("/save")
    public CommonResult<String> save(@RequestBody @Validated List<ShippingTemplatesFreeRequest> request) {
        if (shippingTemplatesFreeService.save(request)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 获取包邮模板列表
     */
    @ApiOperation(value = "获取包邮模板列表")
    @GetMapping("/list")
    public CommonResult<List<ShippingTemplatesFree>> getList() {
        return CommonResult.success(shippingTemplatesFreeService.getShippingTemplatesFreeGroupByPrice());
    }

    /**
     * 按金额分组获取包邮模板数据
     */
    @ApiOperation(value = "按金额分组获取包邮模板数据")
    @GetMapping("/group")
    public CommonResult<Map<BigDecimal, List<ShippingTemplatesFree>>> getGroupData() {
        return CommonResult.success(shippingTemplatesFreeService.getGroupShippingTemplatesFreeData());
    }
}
