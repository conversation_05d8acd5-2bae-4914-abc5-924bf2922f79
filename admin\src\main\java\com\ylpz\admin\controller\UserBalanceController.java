package com.ylpz.admin.controller;

import com.ylpz.model.user.User;
import com.ylpz.model.user.UserBill;
import com.ylpz.core.common.page.CommonPage;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.core.common.request.FundsMonitorSearchRequest;
import com.ylpz.core.common.request.UserOperateIntegralMoneyRequest;
import com.ylpz.core.common.response.CommonResult;
import com.ylpz.core.common.response.UserBalanceResponse;
import com.ylpz.core.service.UserBillService;
import com.ylpz.core.service.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import cn.hutool.core.util.StrUtil;

/**
 * 会员余额管理控制器
 */
@Slf4j
@RestController
@RequestMapping("api/admin/user/balance")
@Api(tags = "会员管理 -- 余额")
public class UserBalanceController {

    @Autowired
    private UserService userService;

    @Autowired
    private UserBillService userBillService;

    /**
     * 获取会员余额统计
     */
    // @PreAuthorize("hasAuthority('admin:user:balance:statistics')")
    @ApiOperation(value = "会员余额统计")
    @RequestMapping(value = "/statistics", method = RequestMethod.GET)
    public CommonResult<Map<String, Object>> getStatistics() {
        Map<String, Object> map = new HashMap<>();

        // 会员余额总计
        BigDecimal totalBalance = userService.getTotalBalance();
        // VIP充值金额
        BigDecimal totalRecharge = userService.getTotalRecharge();
        // 累计返现和差额
        BigDecimal totalRefund = userService.getTotalRefund();
        // 累计消费金额
        BigDecimal totalConsume = userService.getTotalConsume();
        // 累计提现金额
        BigDecimal totalWithdrawal = userService.getTotalWithdrawal();

        map.put("totalBalance", totalBalance);
        map.put("totalRecharge", totalRecharge);
        map.put("totalRefund", totalRefund);
        map.put("totalConsume", totalConsume);
        map.put("totalWithdrawal", totalWithdrawal);

        return CommonResult.success(map);
    }

    /**
     * 获取会员余额列表
     */
    // @PreAuthorize("hasAuthority('admin:user:balance:list')")
    @ApiOperation(value = "会员余额列表")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public CommonResult<CommonPage<User>> getList(@ModelAttribute @Validated PageParamRequest pageParamRequest,
            @RequestParam(required = false) String keywords,
            @RequestParam(required = false) String level) {
        return CommonResult
                .success(CommonPage.restPage(userService.getUserBalanceList(keywords, level, pageParamRequest)));
    }

    /**
     * 获取指定用户余额信息
     */
    // @PreAuthorize("hasAuthority('admin:user:balance:info')")
    @ApiOperation(value = "获取指定用户余额信息")
    @RequestMapping(value = "/info/{id}", method = RequestMethod.GET)
    public CommonResult<UserBalanceResponse> getUserBalanceInfo(
            @PathVariable("id") @NotNull(message = "用户ID不能为空") Integer id) {
        return CommonResult.success(userService.getUserBalanceInfo(id));
    }

    /**
     * 会员余额操作
     */
    // @PreAuthorize("hasAuthority('admin:user:balance:update')")
    @ApiOperation(value = "会员余额操作")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public CommonResult<String> updateUserBalance(@RequestBody @Validated UserOperateIntegralMoneyRequest request) {
        if (userService.updateIntegralMoney(request)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 获取会员余额明细
     */
    // @PreAuthorize("hasAuthority('admin:user:balance:detail')")
    @ApiOperation(value = "会员余额明细")
    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    public CommonResult<CommonPage<UserBill>> getBalanceDetail(
            @RequestParam(required = false) Integer uid,
            @RequestParam(required = false) Integer pm,
            @RequestParam(required = false) String category,
            @RequestParam(required = false) String dateLimit,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime,
            @ModelAttribute @Validated PageParamRequest pageParamRequest) {
        FundsMonitorSearchRequest request = new FundsMonitorSearchRequest();
        request.setUid(uid);
        request.setPm(pm);
        request.setCategory(category);

        // 如果提供了具体的开始结束时间，优先使用这些参数
        if (StrUtil.isNotBlank(startTime) && StrUtil.isNotBlank(endTime)) {
            request.setStartTime(startTime);
            request.setEndTime(endTime);
        } else {
            // 否则使用dateLimit参数
            request.setDateLimit(dateLimit);
        }

        List<UserBill> list = userBillService.getList(request, pageParamRequest);
        return CommonResult.success(CommonPage.restPage(list));
    }
}