(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-01a14b0e"],{"0a52":function(t,e,r){},"21d2":function(t,e,r){"use strict";r.d(e,"a",(function(){return n})),r.d(e,"b",(function(){return a})),r.d(e,"c",(function(){return o})),r.d(e,"e",(function(){return l})),r.d(e,"f",(function(){return s})),r.d(e,"d",(function(){return u}));var i=r("b775");function n(){return Object(i["a"])({url:"/admin/store/retail/spread/manage/get",method:"get"})}function a(t){return Object(i["a"])({url:"/admin/store/retail/spread/manage/set",method:"post",data:t})}function o(t){return Object(i["a"])({url:"/admin/store/retail/list",method:"get",params:t})}function l(t,e){return Object(i["a"])({url:"/admin/store/retail/spread/userlist",method:"post",params:t,data:e})}function s(t,e){return Object(i["a"])({url:"/admin/store/retail/spread/orderlist",method:"post",params:t,data:e})}function u(t){return Object(i["a"])({url:"/admin/store/retail/spread/clean/".concat(t),method:"get"})}},"2f2c":function(t,e,r){"use strict";r.d(e,"b",(function(){return c})),r.d(e,"c",(function(){return d})),r.d(e,"r",(function(){return m})),r.d(e,"d",(function(){return p})),r.d(e,"a",(function(){return f})),r.d(e,"g",(function(){return h})),r.d(e,"h",(function(){return v})),r.d(e,"j",(function(){return b})),r.d(e,"i",(function(){return g})),r.d(e,"e",(function(){return y})),r.d(e,"o",(function(){return _})),r.d(e,"q",(function(){return w})),r.d(e,"l",(function(){return x})),r.d(e,"m",(function(){return k})),r.d(e,"n",(function(){return F})),r.d(e,"p",(function(){return L})),r.d(e,"k",(function(){return C})),r.d(e,"f",(function(){return O}));var i=r("b775");function n(t){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},n(t)}function a(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,i)}return r}function o(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?a(Object(r),!0).forEach((function(e){l(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):a(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function l(t,e,r){return(e=s(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function s(t){var e=u(t,"string");return"symbol"==n(e)?e:e+""}function u(t,e){if("object"!=n(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var i=r.call(t,e||"default");if("object"!=n(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function c(t){return Object(i["a"])({url:"/admin/system/city/list",method:"get",params:o({},t)})}function d(){return Object(i["a"])({url:"/admin/system/city/list/tree",method:"get"})}function m(t){return Object(i["a"])({url:"/admin/system/city/update/status",method:"post",params:o({},t)})}function p(t){return Object(i["a"])({url:"/admin/system/city/update",method:"post",params:o({},t)})}function f(t){return Object(i["a"])({url:"/admin/system/city/info",method:"get",params:o({},t)})}function h(t){return Object(i["a"])({url:"/admin/express/list",method:"get",params:o({},t)})}function v(){return Object(i["a"])({url:"/admin/express/sync/express",method:"post"})}function b(t){return Object(i["a"])({url:"/admin/express/update/show",method:"post",data:t})}function g(t){return Object(i["a"])({url:"/admin/express/update",method:"post",data:t})}function y(t){return Object(i["a"])({url:"/admin/express/delete",method:"GET",params:o({},t)})}function _(t){return Object(i["a"])({url:"/admin/express/shipping/templates/list",method:"get",params:o({},t)})}function w(t){return Object(i["a"])({url:"/admin/express/shipping/templates/info",method:"get",params:o({},t)})}function x(t){return Object(i["a"])({url:"/admin/express/shipping/free/list",method:"get",params:o({},t)})}function k(t){return Object(i["a"])({url:"admin/express/shipping/region/list",method:"get",params:o({},t)})}function F(t){return Object(i["a"])({url:"admin/express/shipping/templates/save",method:"post",data:t})}function L(t,e){return Object(i["a"])({url:"admin/express/shipping/templates/update",method:"post",data:t,params:o({},e)})}function C(t){return Object(i["a"])({url:"admin/express/shipping/templates/delete",method:"get",params:t})}function O(t){return Object(i["a"])({url:"admin/express/info",method:"get",params:o({},t)})}},"7c0c":function(t,e,r){},abfb:function(t,e,r){"use strict";r("7c0c")},b2bf:function(t,e,r){},b9c2:function(t,e,r){"use strict";r.r(e);var i=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"divBox relative"},[r("el-card",{staticClass:"box-card"},[r("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[r("el-tabs",{on:{"tab-click":function(e){return t.getList(1)}},model:{value:t.loginType,callback:function(e){t.loginType=e},expression:"loginType"}},t._l(t.headeNum,(function(t,e){return r("el-tab-pane",{key:e,attrs:{label:t.name,name:t.type.toString()}})})),1),t._v(" "),r("div",{staticClass:"container"},[r("el-form",{ref:"userFrom",attrs:{inline:"",size:"small",model:t.userFrom,"label-position":t.labelPosition,"label-width":"100px"}},[r("el-row",[r("el-col",{attrs:{xs:24,sm:24,md:24,lg:18,xl:18}},[r("el-col",t._b({},"el-col",t.grid,!1),[r("el-form-item",{attrs:{label:"用户搜索："}},[r("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入姓名或手机号",clearable:""},model:{value:t.userFrom.keywords,callback:function(e){t.$set(t.userFrom,"keywords",e)},expression:"userFrom.keywords"}})],1)],1)],1),t._v(" "),t.collapse?[r("el-col",{attrs:{xs:24,sm:24,md:24,lg:18,xl:18}},[r("el-col",t._b({},"el-col",t.grid,!1),[r("el-form-item",{attrs:{label:"用户等级："}},[r("el-select",{staticClass:"selWidth",attrs:{placeholder:"请选择",clearable:"",filterable:"",multiple:""},model:{value:t.levelData,callback:function(e){t.levelData=e},expression:"levelData"}},t._l(t.levelList,(function(t,e){return r("el-option",{key:e,attrs:{value:t.id,label:t.name}})})),1)],1)],1),t._v(" "),r("el-col",t._b({},"el-col",t.grid,!1),[r("el-form-item",{attrs:{label:"用户分组："}},[r("el-select",{staticClass:"selWidth",attrs:{placeholder:"请选择",clearable:"",filterable:"",multiple:""},model:{value:t.groupData,callback:function(e){t.groupData=e},expression:"groupData"}},t._l(t.groupList,(function(t,e){return r("el-option",{key:e,attrs:{value:t.id,label:t.groupName}})})),1)],1)],1),t._v(" "),r("el-col",t._b({},"el-col",t.grid,!1),[r("el-form-item",{attrs:{label:"用户标签："}},[r("el-select",{staticClass:"selWidth",attrs:{placeholder:"请选择",clearable:"",filterable:"",multiple:""},model:{value:t.labelData,callback:function(e){t.labelData=e},expression:"labelData"}},t._l(t.labelLists,(function(t,e){return r("el-option",{key:e,attrs:{value:t.id,label:t.name}})})),1)],1)],1)],1),t._v(" "),r("el-col",{attrs:{xs:24,sm:24,md:24,lg:18,xl:18}},[r("el-col",t._b({},"el-col",t.grid,!1),[r("el-form-item",{attrs:{label:"国家："}},[r("el-select",{staticClass:"selWidth",attrs:{placeholder:"请选择",clearable:""},on:{"on-change":t.changeCountry},model:{value:t.userFrom.country,callback:function(e){t.$set(t.userFrom,"country",e)},expression:"userFrom.country"}},[r("el-option",{attrs:{value:"",label:"全部"}}),t._v(" "),r("el-option",{attrs:{value:"CN",label:"中国"}}),t._v(" "),r("el-option",{attrs:{value:"OTHER",label:"国外"}})],1)],1)],1),t._v(" "),"CN"===t.userFrom.country?r("el-col",t._b({},"el-col",t.grid,!1),[r("el-form-item",{attrs:{label:"省份："}},[r("el-cascader",{staticClass:"selWidth",attrs:{options:t.addresData,props:t.propsCity,filterable:"",clearable:""},on:{change:t.handleChange},model:{value:t.address,callback:function(e){t.address=e},expression:"address"}})],1)],1):t._e(),t._v(" "),r("el-col",t._b({},"el-col",t.grid,!1),[r("el-form-item",{attrs:{label:"消费情况："}},[r("el-select",{staticClass:"selWidth",attrs:{placeholder:"请选择",clearable:""},model:{value:t.userFrom.payCount,callback:function(e){t.$set(t.userFrom,"payCount",e)},expression:"userFrom.payCount"}},[r("el-option",{attrs:{value:"",label:"全部"}}),t._v(" "),r("el-option",{attrs:{value:"0",label:"0"}}),t._v(" "),r("el-option",{attrs:{value:"1",label:"1+"}}),t._v(" "),r("el-option",{attrs:{value:"2",label:"2+"}}),t._v(" "),r("el-option",{attrs:{value:"3",label:"3+"}}),t._v(" "),r("el-option",{attrs:{value:"4",label:"4+"}}),t._v(" "),r("el-option",{attrs:{value:"5",label:"5+"}})],1)],1)],1),t._v(" "),r("el-col",t._b({},"el-col",t.grid,!1),[r("el-form-item",{staticClass:"timeBox",attrs:{label:"时间选择："}},[r("el-date-picker",{staticClass:"selWidth",attrs:{align:"right","unlink-panels":"","value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",size:"small",type:"daterange",placement:"bottom-end",placeholder:"自定义时间","picker-options":t.pickerOptions},on:{change:t.onchangeTime},model:{value:t.timeVal,callback:function(e){t.timeVal=e},expression:"timeVal"}})],1)],1)],1),t._v(" "),r("el-col",{attrs:{xs:24,sm:24,md:24,lg:18,xl:18}},[r("el-col",t._b({},"el-col",t.grid,!1),[r("el-form-item",{attrs:{label:"访问情况："}},[r("el-select",{staticClass:"selWidth",attrs:{placeholder:"请选择",clearable:""},model:{value:t.userFrom.accessType,callback:function(e){t.$set(t.userFrom,"accessType",e)},expression:"userFrom.accessType"}},[r("el-option",{attrs:{value:0,label:"全部"}}),t._v(" "),r("el-option",{attrs:{value:1,label:"首次访问"}}),t._v(" "),r("el-option",{attrs:{value:2,label:"时间段访问过"}}),t._v(" "),r("el-option",{attrs:{value:3,label:"时间段未访问"}})],1)],1)],1),t._v(" "),r("el-col",t._b({},"el-col",t.grid,!1),[r("el-form-item",{attrs:{label:"性别："}},[r("el-radio-group",{staticClass:"selWidth",attrs:{type:"button"},model:{value:t.userFrom.sex,callback:function(e){t.$set(t.userFrom,"sex",e)},expression:"userFrom.sex"}},[r("el-radio-button",{attrs:{label:""}},[r("span",[t._v("全部")])]),t._v(" "),r("el-radio-button",{attrs:{label:"0"}},[r("span",[t._v("未知")])]),t._v(" "),r("el-radio-button",{attrs:{label:"1"}},[r("span",[t._v("男")])]),t._v(" "),r("el-radio-button",{attrs:{label:"2"}},[r("span",[t._v("女")])]),t._v(" "),r("el-radio-button",{attrs:{label:"3"}},[r("span",[t._v("保密")])])],1)],1)],1),t._v(" "),r("el-col",t._b({},"el-col",t.grid,!1),[r("el-form-item",{attrs:{label:"身份："}},[r("el-radio-group",{staticClass:"selWidth",attrs:{type:"button"},model:{value:t.userFrom.isPromoter,callback:function(e){t.$set(t.userFrom,"isPromoter",e)},expression:"userFrom.isPromoter"}},[r("el-radio-button",{attrs:{label:""}},[r("span",[t._v("全部")])]),t._v(" "),r("el-radio-button",{attrs:{label:"1"}},[r("span",[t._v("推广员")])]),t._v(" "),r("el-radio-button",{attrs:{label:"0"}},[r("span",[t._v("普通用户")])])],1)],1)],1)],1)]:t._e(),t._v(" "),r("el-col",{staticClass:"text-right userFrom",attrs:{xs:24,sm:24,md:24,lg:6,xl:6}},[r("el-form-item",[r("el-button",{staticClass:"mr15",attrs:{type:"primary",icon:"ios-search",label:"default",size:"small"},on:{click:t.userSearchs}},[t._v("搜索")]),t._v(" "),r("el-button",{staticClass:"ResetSearch mr10",attrs:{size:"small"},on:{click:function(e){return t.reset("userFrom")}}},[t._v("重置")]),t._v(" "),r("a",{staticClass:"ivu-ml-8",on:{click:function(e){t.collapse=!t.collapse}}},[t.collapse?[t._v("\n                    收起 "),r("i",{staticClass:"el-icon-arrow-up"})]:[t._v("\n                    展开 "),r("i",{staticClass:"el-icon-arrow-down"})]],2)],1)],1)],2)],1)],1),t._v(" "),r("div",{staticClass:"btn_bt"},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:coupon:user:receive"],expression:"['admin:coupon:user:receive']"}],staticClass:"mr10",attrs:{size:"small",type:"primary"},on:{click:t.onSend}},[t._v("发送优惠券")]),t._v(" "),r("el-button",{staticClass:"mr10",attrs:{size:"small"},on:{click:function(e){return t.setBatch("group")}}},[t._v("批量设置分组")]),t._v(" "),r("el-button",{staticClass:"mr10",attrs:{size:"small"},on:{click:function(e){return t.setBatch("label")}}},[t._v("批量设置标签")])],1)],1),t._v(" "),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"table",staticStyle:{width:"100%"},attrs:{data:t.tableData.data,size:"mini","highlight-current-row":""},on:{"selection-change":t.onSelectTab}},[r("el-table-column",{attrs:{type:"expand"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("el-form",{staticClass:"demo-table-expand",attrs:{"label-position":"left",inline:""}},[r("el-form-item",{attrs:{label:"身份："}},[r("span",[t._v(t._s(t._f("filterIsPromoter")(e.row.isPromoter)))])]),t._v(" "),r("el-form-item",{attrs:{label:"首次访问："}},[r("span",[t._v(t._s(t._f("filterEmpty")(e.row.createTime)))])]),t._v(" "),r("el-form-item",{attrs:{label:"近次访问："}},[r("span",[t._v(t._s(t._f("filterEmpty")(e.row.lastLoginTime)))])]),t._v(" "),r("el-form-item",{attrs:{label:"手机号："}},[r("span",[t._v(t._s(t._f("filterEmpty")(e.row.phone)))])]),t._v(" "),r("el-form-item",{attrs:{label:"标签："}},[r("span",[t._v(t._s(t._f("filterEmpty")(e.row.tagName)))])]),t._v(" "),r("el-form-item",{attrs:{label:"地址："}},[r("span",[t._v(t._s(t._f("filterEmpty")(e.row.addres)))])]),t._v(" "),r("el-form-item",{staticStyle:{width:"100%",display:"flex","margin-right":"10px"},attrs:{label:"备注："}},[r("span",[t._v(t._s(t._f("filterEmpty")(e.row.mark)))])])],1)]}}])}),t._v(" "),r("el-table-column",{attrs:{type:"selection",width:"55"}}),t._v(" "),r("el-table-column",{attrs:{prop:"uid",label:"ID","min-width":"80"}}),t._v(" "),r("el-table-column",{attrs:{label:"头像","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(t){return[r("div",{staticClass:"demo-image__preview"},[r("el-image",{staticStyle:{width:"36px",height:"36px"},attrs:{src:t.row.avatar,"preview-src-list":[t.row.avatar]}})],1)]}}])}),t._v(" "),r("el-table-column",{attrs:{label:"姓名","min-width":"160"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("span",[t._v(t._s(t._f("filterEmpty")(e.row.nickname))+" | "+t._s(t._f("sexFilter")(e.row.sex)))])]}}])}),t._v(" "),r("el-table-column",{attrs:{label:"用户等级","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("span",[t._v(t._s(t._f("filterEmpty")(t._f("levelFilter")(e.row.level))))])]}}])}),t._v(" "),r("el-table-column",{attrs:{prop:"groupName",label:"分组","min-width":"100"}}),t._v(" "),r("el-table-column",{attrs:{prop:"spreadNickname",label:"推荐人","min-width":"130"}}),t._v(" "),r("el-table-column",{attrs:{label:"手机号","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("span",[t._v(t._s(t._f("filterEmpty")(e.row.phone)))])]}}])}),t._v(" "),r("el-table-column",{attrs:{prop:"nowMoney",label:"余额","min-width":"100"}}),t._v(" "),r("el-table-column",{attrs:{prop:"integral",label:"积分","min-width":"100"}}),t._v(" "),r("el-table-column",{attrs:{label:"操作","min-width":"130",fixed:"right",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:user:infobycondition"],expression:"['admin:user:infobycondition']"}],attrs:{type:"text",size:"small"},on:{click:function(r){return t.editUser(e.row.uid)}}},[t._v("编辑")]),t._v(" "),r("el-dropdown",{attrs:{trigger:"click"}},[r("span",{staticClass:"el-dropdown-link"},[t._v("\n              更多"),r("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),t._v(" "),r("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[t.checkPermi(["admin:user:topdetail"])?r("el-dropdown-item",{nativeOn:{click:function(r){return t.onDetails(e.row.uid)}}},[t._v("账户详情")]):t._e(),t._v(" "),t.checkPermi(["admin:user:operate:founds"])?r("el-dropdown-item",{nativeOn:{click:function(r){return t.editPoint(e.row.uid)}}},[t._v("积分余额")]):t._e(),t._v(" "),t.checkPermi(["admin:user:group"])?r("el-dropdown-item",{nativeOn:{click:function(r){return t.setBatch("group",e.row)}}},[t._v("设置分组")]):t._e(),t._v(" "),t.checkPermi(["admin:user:tag"])?r("el-dropdown-item",{nativeOn:{click:function(r){return t.setBatch("label",e.row)}}},[t._v("设置标签")]):t._e(),t._v(" "),t.checkPermi(["admin:user:update:phone"])?r("el-dropdown-item",{nativeOn:{click:function(r){return t.setPhone(e.row)}}},[t._v("修改手机号")]):t._e(),t._v(" "),t.checkPermi(["admin:user:update:level"])?r("el-dropdown-item",{nativeOn:{click:function(r){return t.onLevel(e.row.uid,e.row.level)}}},[t._v("修改用户等级")]):t._e(),t._v(" "),t.checkPermi(["admin:user:update:spread"])?r("el-dropdown-item",{nativeOn:{click:function(r){return t.setExtension(e.row)}}},[t._v("修改上级推广人")]):t._e(),t._v(" "),e.row.spreadUid&&e.row.spreadUid>0&&t.checkPermi(["admin:retail:spread:clean"])?r("el-dropdown-item",{nativeOn:{click:function(r){return t.clearSpread(e.row)}}},[t._v("清除上级推广人")]):t._e()],1)],1)]}}])})],1),t._v(" "),r("div",{staticClass:"block"},[r("el-pagination",{attrs:{"page-sizes":[15,30,45,60],"page-size":t.userFrom.limit,"current-page":t.userFrom.page,layout:"total, sizes, prev, pager, next, jumper",total:t.tableData.total},on:{"size-change":t.handleSizeChange,"current-change":t.pageChange}})],1)],1),t._v(" "),r("el-dialog",{attrs:{title:"修改推广人",visible:t.extensionVisible,width:"500px","before-close":t.handleCloseExtension},on:{"update:visible":function(e){t.extensionVisible=e}}},[r("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],ref:"formExtension",staticClass:"formExtension mt20",attrs:{model:t.formExtension,rules:t.ruleInline,"label-width":"120px"},nativeOn:{submit:function(t){t.preventDefault()}}},[r("el-form-item",{attrs:{label:"用户头像：",prop:"image"}},[r("div",{staticClass:"upLoadPicBox",on:{click:t.modalPicTap}},[t.formExtension.image?r("div",{staticClass:"pictrue"},[r("img",{attrs:{src:t.formExtension.image}})]):r("div",{staticClass:"upLoad"},[r("i",{staticClass:"el-icon-camera cameraIconfont"})])])])],1),t._v(" "),r("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.onSubExtension("formExtension")}}},[t._v("确 定")])],1)],1),t._v(" "),r("el-dialog",{attrs:{title:"用户列表",visible:t.userVisible,width:"900px"},on:{"update:visible":function(e){t.userVisible=e}}},[t.userVisible?r("user-list",{on:{getTemplateRow:t.getTemplateRow}}):t._e(),t._v(" "),r("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{on:{click:function(e){t.userVisible=!1}}},[t._v("取 消")]),t._v(" "),r("el-button",{attrs:{type:"primary"},on:{click:function(e){t.userVisible=!1}}},[t._v("确 定")])],1)],1),t._v(" "),r("el-dialog",{attrs:{title:"设置",visible:t.dialogVisible,width:"500px","before-close":t.handleClose},on:{"update:visible":function(e){t.dialogVisible=e}}},[r("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],ref:"dynamicValidateForm",staticClass:"demo-dynamic",attrs:{model:t.dynamicValidateForm,"label-width":"100px"}},["group"===t.batchName?r("el-form-item",{key:"1",attrs:{prop:"groupId",label:"用户分组",rules:[{required:!0,message:"请选择用户分组",trigger:"change"}]}},[r("el-select",{staticStyle:{width:"80%"},attrs:{placeholder:"请选择分组",filterable:""},model:{value:t.dynamicValidateForm.groupId,callback:function(e){t.$set(t.dynamicValidateForm,"groupId",e)},expression:"dynamicValidateForm.groupId"}},t._l(t.groupList,(function(t,e){return r("el-option",{key:e,attrs:{value:t.id,label:t.groupName}})})),1)],1):r("el-form-item",{attrs:{prop:"groupId",label:"用户标签",rules:[{required:!0,message:"请选择用户标签",trigger:"change"}]}},[r("el-select",{staticStyle:{width:"80%"},attrs:{placeholder:"请选择标签",filterable:""},model:{value:t.dynamicValidateForm.groupId,callback:function(e){t.$set(t.dynamicValidateForm,"groupId",e)},expression:"dynamicValidateForm.groupId"}},t._l(t.labelLists,(function(t,e){return r("el-option",{key:e,attrs:{value:t.id,label:t.name}})})),1)],1)],1),t._v(" "),r("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{on:{click:t.handleClose}},[t._v("取 消")]),t._v(" "),r("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.submitForm("dynamicValidateForm")}}},[t._v("确 定")])],1)],1),t._v(" "),r("el-dialog",{attrs:{title:"编辑",visible:t.visible,width:"600px"},on:{"update:visible":function(e){t.visible=e}}},[t.visible?r("edit-from",{attrs:{uid:t.uid},on:{resetForm:t.resetForm}}):t._e()],1),t._v(" "),r("el-dialog",{attrs:{title:"积分余额",visible:t.VisiblePoint,width:"500px","close-on-click-modal":!1,"before-close":t.handlePointClose},on:{"update:visible":function(e){t.VisiblePoint=e}}},[r("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.loadingPoint,expression:"loadingPoint"}],ref:"PointValidateForm",staticClass:"demo-dynamic",attrs:{model:t.PointValidateForm,"label-width":"100px"}},[r("el-form-item",{attrs:{label:"修改余额",required:""}},[r("el-radio-group",{model:{value:t.PointValidateForm.moneyType,callback:function(e){t.$set(t.PointValidateForm,"moneyType",e)},expression:"PointValidateForm.moneyType"}},[r("el-radio",{attrs:{label:1}},[t._v("增加")]),t._v(" "),r("el-radio",{attrs:{label:2}},[t._v("减少")])],1)],1),t._v(" "),r("el-form-item",{attrs:{label:"余额",required:""}},[r("el-input-number",{attrs:{type:"text",precision:2,step:.1,min:0,max:999999},model:{value:t.PointValidateForm.moneyValue,callback:function(e){t.$set(t.PointValidateForm,"moneyValue",e)},expression:"PointValidateForm.moneyValue"}})],1),t._v(" "),r("el-form-item",{attrs:{label:"修改积分",required:""}},[r("el-radio-group",{model:{value:t.PointValidateForm.integralType,callback:function(e){t.$set(t.PointValidateForm,"integralType",e)},expression:"PointValidateForm.integralType"}},[r("el-radio",{attrs:{label:1}},[t._v("增加")]),t._v(" "),r("el-radio",{attrs:{label:2}},[t._v("减少")])],1)],1),t._v(" "),r("el-form-item",{attrs:{label:"积分",required:""}},[r("el-input-number",{attrs:{type:"text","step-strictly":"",min:0,max:999999},model:{value:t.PointValidateForm.integralValue,callback:function(e){t.$set(t.PointValidateForm,"integralValue",e)},expression:"PointValidateForm.integralValue"}})],1)],1),t._v(" "),r("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{on:{click:t.handlePointClose}},[t._v("取 消")]),t._v(" "),r("el-button",{attrs:{type:"primary",loading:t.loadingBtn},on:{click:function(e){return t.submitPointForm("PointValidateForm")}}},[t._v("确 定")])],1)],1),t._v(" "),t.uid?r("el-dialog",{attrs:{title:"用户详情",visible:t.Visible,width:"1100px","before-close":t.Close},on:{"update:visible":function(e){t.Visible=e}}},[t.Visible?r("user-details",{ref:"userDetails",attrs:{uid:t.uid}}):t._e()],1):t._e(),t._v(" "),r("el-dialog",{attrs:{title:"设置",visible:t.levelVisible,width:"600px","before-close":t.Close},on:{"update:visible":function(e){t.levelVisible=e}}},[r("level-edit",{attrs:{levelInfo:t.levelInfo,levelList:t.levelList}})],1)],1)},n=[],a=r("c24f"),o=r("21d2"),l=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("el-form",{ref:"ruleForm",staticClass:"demo-ruleForm",attrs:{model:t.ruleForm,rules:t.rules,"label-width":"100px"}},[r("el-form-item",{attrs:{label:"用户编号："}},[r("el-input",{staticClass:"selWidth",attrs:{disabled:""},model:{value:t.ruleForm.id,callback:function(e){t.$set(t.ruleForm,"id",e)},expression:"ruleForm.id"}})],1),t._v(" "),r("el-form-item",{attrs:{label:"用户地址："}},[r("el-input",{staticClass:"selWidth",model:{value:t.ruleForm.addres,callback:function(e){t.$set(t.ruleForm,"addres",e)},expression:"ruleForm.addres"}})],1),t._v(" "),r("el-form-item",{attrs:{label:"用户备注："}},[r("el-input",{staticClass:"selWidth",attrs:{type:"textarea"},model:{value:t.ruleForm.mark,callback:function(e){t.$set(t.ruleForm,"mark",e)},expression:"ruleForm.mark"}})],1),t._v(" "),r("el-form-item",{attrs:{label:"用户分组："}},[r("el-select",{staticClass:"selWidth",attrs:{placeholder:"请选择",clearable:"",filterable:""},model:{value:t.ruleForm.groupId,callback:function(e){t.$set(t.ruleForm,"groupId",e)},expression:"ruleForm.groupId"}},t._l(t.groupList,(function(t,e){return r("el-option",{key:e,attrs:{value:t.id,label:t.groupName}})})),1)],1),t._v(" "),r("el-form-item",{attrs:{label:"用户标签："}},[r("el-select",{staticClass:"selWidth",attrs:{placeholder:"请选择",clearable:"",filterable:"",multiple:""},model:{value:t.labelData,callback:function(e){t.labelData=e},expression:"labelData"}},t._l(t.labelLists,(function(t,e){return r("el-option",{key:e,attrs:{value:t.id,label:t.name}})})),1)],1),t._v(" "),r("el-form-item",{attrs:{label:"推广员"}},[r("el-radio-group",{model:{value:t.ruleForm.isPromoter,callback:function(e){t.$set(t.ruleForm,"isPromoter",e)},expression:"ruleForm.isPromoter"}},[r("el-radio",{attrs:{label:!0}},[t._v("开启")]),t._v(" "),r("el-radio",{attrs:{label:!1}},[t._v("关闭")])],1)],1),t._v(" "),r("el-form-item",{attrs:{label:"状态"}},[r("el-radio-group",{model:{value:t.ruleForm.status,callback:function(e){t.$set(t.ruleForm,"status",e)},expression:"ruleForm.status"}},[r("el-radio",{attrs:{label:!0}},[t._v("开启")]),t._v(" "),r("el-radio",{attrs:{label:!1}},[t._v("关闭")])],1)],1),t._v(" "),r("el-form-item",[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:user:update"],expression:"['admin:user:update']"}],attrs:{type:"primary"},on:{click:function(e){return t.submitForm("ruleForm")}}},[t._v("提交")]),t._v(" "),r("el-button",{on:{click:function(e){return t.resetForm("ruleForm")}}},[t._v("取消")])],1)],1)},s=[],u=r("61f7");function c(t){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},c(t)}function d(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */d=function(){return e};var t,e={},r=Object.prototype,i=r.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},a="function"==typeof Symbol?Symbol:{},o=a.iterator||"@@iterator",l=a.asyncIterator||"@@asyncIterator",s=a.toStringTag||"@@toStringTag";function u(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,r){return t[e]=r}}function m(t,e,r,i){var a=e&&e.prototype instanceof y?e:y,o=Object.create(a.prototype),l=new E(i||[]);return n(o,"_invoke",{value:P(t,r,l)}),o}function p(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=m;var f="suspendedStart",h="suspendedYield",v="executing",b="completed",g={};function y(){}function _(){}function w(){}var x={};u(x,o,(function(){return this}));var k=Object.getPrototypeOf,F=k&&k(k(V([])));F&&F!==r&&i.call(F,o)&&(x=F);var L=w.prototype=y.prototype=Object.create(x);function C(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function O(t,e){function r(n,a,o,l){var s=p(t[n],t,a);if("throw"!==s.type){var u=s.arg,d=u.value;return d&&"object"==c(d)&&i.call(d,"__await")?e.resolve(d.__await).then((function(t){r("next",t,o,l)}),(function(t){r("throw",t,o,l)})):e.resolve(d).then((function(t){u.value=t,o(u)}),(function(t){return r("throw",t,o,l)}))}l(s.arg)}var a;n(this,"_invoke",{value:function(t,i){function n(){return new e((function(e,n){r(t,i,e,n)}))}return a=a?a.then(n,n):n()}})}function P(e,r,i){var n=f;return function(a,o){if(n===v)throw Error("Generator is already running");if(n===b){if("throw"===a)throw o;return{value:t,done:!0}}for(i.method=a,i.arg=o;;){var l=i.delegate;if(l){var s=j(l,i);if(s){if(s===g)continue;return s}}if("next"===i.method)i.sent=i._sent=i.arg;else if("throw"===i.method){if(n===f)throw n=b,i.arg;i.dispatchException(i.arg)}else"return"===i.method&&i.abrupt("return",i.arg);n=v;var u=p(e,r,i);if("normal"===u.type){if(n=i.done?b:h,u.arg===g)continue;return{value:u.arg,done:i.done}}"throw"===u.type&&(n=b,i.method="throw",i.arg=u.arg)}}}function j(e,r){var i=r.method,n=e.iterator[i];if(n===t)return r.delegate=null,"throw"===i&&e.iterator.return&&(r.method="return",r.arg=t,j(e,r),"throw"===r.method)||"return"!==i&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+i+"' method")),g;var a=p(n,e.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,g;var o=a.arg;return o?o.done?(r[e.resultName]=o.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,g):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,g)}function I(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function S(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function E(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(I,this),this.reset(!0)}function V(e){if(e||""===e){var r=e[o];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,a=function r(){for(;++n<e.length;)if(i.call(e,n))return r.value=e[n],r.done=!1,r;return r.value=t,r.done=!0,r};return a.next=a}}throw new TypeError(c(e)+" is not iterable")}return _.prototype=w,n(L,"constructor",{value:w,configurable:!0}),n(w,"constructor",{value:_,configurable:!0}),_.displayName=u(w,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===_||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,u(t,s,"GeneratorFunction")),t.prototype=Object.create(L),t},e.awrap=function(t){return{__await:t}},C(O.prototype),u(O.prototype,l,(function(){return this})),e.AsyncIterator=O,e.async=function(t,r,i,n,a){void 0===a&&(a=Promise);var o=new O(m(t,r,i,n),a);return e.isGeneratorFunction(r)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},C(L),u(L,s,"Generator"),u(L,o,(function(){return this})),u(L,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var i in e)r.push(i);return r.reverse(),function t(){for(;r.length;){var i=r.pop();if(i in e)return t.value=i,t.done=!1,t}return t.done=!0,t}},e.values=V,E.prototype={constructor:E,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(S),!e)for(var r in this)"t"===r.charAt(0)&&i.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function n(i,n){return l.type="throw",l.arg=e,r.next=i,n&&(r.method="next",r.arg=t),!!n}for(var a=this.tryEntries.length-1;a>=0;--a){var o=this.tryEntries[a],l=o.completion;if("root"===o.tryLoc)return n("end");if(o.tryLoc<=this.prev){var s=i.call(o,"catchLoc"),u=i.call(o,"finallyLoc");if(s&&u){if(this.prev<o.catchLoc)return n(o.catchLoc,!0);if(this.prev<o.finallyLoc)return n(o.finallyLoc)}else if(s){if(this.prev<o.catchLoc)return n(o.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return n(o.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&i.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var a=n;break}}a&&("break"===t||"continue"===t)&&a.tryLoc<=e&&e<=a.finallyLoc&&(a=null);var o=a?a.completion:{};return o.type=t,o.arg=e,a?(this.method="next",this.next=a.finallyLoc,g):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),g},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),S(r),g}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var i=r.completion;if("throw"===i.type){var n=i.arg;S(r)}return n}}throw Error("illegal catch attempt")},delegateYield:function(e,r,i){return this.delegate={iterator:V(e),resultName:r,nextLoc:i},"next"===this.method&&(this.arg=t),g}},e}function m(t,e,r,i,n,a,o){try{var l=t[a](o),s=l.value}catch(t){return void r(t)}l.done?e(s):Promise.resolve(s).then(i,n)}function p(t){return function(){var e=this,r=arguments;return new Promise((function(i,n){var a=t.apply(e,r);function o(t){m(a,i,n,o,l,"next",t)}function l(t){m(a,i,n,o,l,"throw",t)}o(void 0)}))}}var f={id:null,mark:"",addres:"",groupId:"",level:"",isPromoter:!1,status:!1},h={name:"UserEdit",props:{uid:{type:Number,default:null}},data:function(){return{ruleForm:Object.assign({},f),groupData:[],labelData:[],labelLists:[],levelList:[],groupList:[],rules:{}}},mounted:function(){this.uid&&this.userInfo(),this.groupLists(),this.levelLists(),this.getTagList()},methods:{userInfo:function(){var t=this;Object(a["A"])({id:this.uid}).then(function(){var e=p(d().mark((function e(r){return d().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.ruleForm={id:r.uid,mark:r.mark,status:r.status,addres:r.addres,groupId:Number(r.groupId)||"",level:r.level||"",isPromoter:r.isPromoter,tagId:r.tagId||""},t.labelData=r.tagId?r.tagId.split(",").map(Number):[];case 2:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}())},groupLists:function(){var t=this;Object(a["f"])({page:1,limit:9999}).then(function(){var e=p(d().mark((function e(r){return d().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.groupList=r.list;case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}())},getTagList:function(){var t=this;Object(a["t"])({page:1,limit:9999}).then((function(e){t.labelLists=e.list}))},levelLists:function(){var t=this;Object(a["m"])().then(function(){var e=p(d().mark((function e(r){return d().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.levelList=r.list;case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}())},submitForm:Object(u["a"])((function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return!1;e.ruleForm.tagId=e.labelData.join(","),Object(a["D"])({id:e.ruleForm.id},e.ruleForm).then(function(){var t=p(d().mark((function t(r){return d().wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.$message.success("编辑成功"),e.$parent.$parent.visible=!1,e.$parent.$parent.getList();case 3:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())}))})),resetForm:function(t){this.$refs[t].resetFields(),this.$emit("resetForm")}}},v=h,b=(r("f076"),r("2877")),g=Object(b["a"])(v,l,s,!1,null,"48a67e34",null),y=g.exports,_=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",[t.psInfo?r("div",{staticClass:"acea-row row-middle border_bottom pb-24"},[r("div",{staticClass:"avatar mr20"},[r("img",{attrs:{src:t.psInfo.user.avatar}})]),t._v(" "),r("div",{staticClass:"dashboard-workplace-header-tip"},[r("p",{staticClass:"dashboard-workplace-header-tip-title",domProps:{textContent:t._s(t.psInfo.user.nickname||"-")}}),t._v(" "),r("div",{staticClass:"dashboard-workplace-header-tip-desc"},[r("span",{staticClass:"dashboard-workplace-header-tip-desc-sp pb-1"},[t._v("余额: "+t._s(t.psInfo.balance))]),t._v(" "),r("span",{staticClass:"dashboard-workplace-header-tip-desc-sp pb-1"},[t._v("总计订单: "+t._s(t.psInfo.allOrderCount))]),t._v(" "),r("span",{staticClass:"dashboard-workplace-header-tip-desc-sp pb-1"},[t._v("总消费金额: "+t._s(t.psInfo.allConsumeCount))]),t._v(" "),r("span",{staticClass:"dashboard-workplace-header-tip-desc-sp"},[t._v("积分: "+t._s(t.psInfo.integralCount))]),t._v(" "),r("span",{staticClass:"dashboard-workplace-header-tip-desc-sp"},[t._v("本月订单: "+t._s(t.psInfo.mothOrderCount))]),t._v(" "),r("span",{staticClass:"dashboard-workplace-header-tip-desc-sp"},[t._v("本月消费金额: "+t._s(t.psInfo.mothConsumeCount))])])])]):t._e(),t._v(" "),r("el-row",{staticClass:"ivu-mt mt20",attrs:{align:"middle",gutter:10}},[r("el-col",{attrs:{span:4}},[r("el-menu",{staticClass:"el-menu-vertical-demo",attrs:{"default-active":"0"},on:{select:t.changeType}},t._l(t.list,(function(e,i){return r("el-menu-item",{key:i,attrs:{name:e.val,index:e.val}},[r("span",{attrs:{slot:"title"},slot:"title"},[t._v(t._s(e.label))])])})),1)],1),t._v(" "),r("el-col",{attrs:{span:20}},[r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"tabNumWidth",attrs:{data:t.tableData.data,"max-height":"400"}},t._l(t.columns,(function(t,e){return r("el-table-column",{key:e,attrs:{prop:t.key,label:t.title,width:"item.minWidth","show-overflow-tooltip":!0}})})),1),t._v(" "),r("div",{staticClass:"block"},[r("el-pagination",{attrs:{"page-sizes":[6,12,18,24],"page-size":t.tableFrom.limit,"current-page":t.tableFrom.page,layout:"total, sizes, prev, pager, next, jumper",total:t.tableData.total},on:{"size-change":t.handleSizeChange,"current-change":t.pageChange}})],1)],1)],1)],1)},w=[],x=r("b7be"),k={name:"UserDetails",props:{uid:{type:Number,default:null}},data:function(){return{loading:!1,columns:[],Visible:!1,list:[{val:"0",label:"消费记录"},{val:"1",label:"积分明细"},{val:"2",label:"签到记录"},{val:"3",label:"持有优惠券"},{val:"4",label:"余额变动"},{val:"5",label:"好友关系"}],tableData:{data:[],total:0},tableFrom:{page:1,limit:6,type:"0",userId:""},psInfo:null}},mounted:function(){this.uid&&(this.getHeader(),this.getInfo())},methods:{changeType:function(t){this.tableFrom.type=t,"1"===t?this.integral():this.getInfo()},integral:function(){var t=this;this.loading=!0,Object(x["E"])({limit:this.tableFrom.limit,page:this.tableFrom.page},{uid:this.uid}).then((function(e){t.tableData.data=e.list,t.tableData.total=e.total,t.columns=[{title:"来源/用途",key:"title",minWidth:120},{title:"积分变化",key:"integral",minWidth:120},{title:"变化后积分",key:"balance",minWidth:120},{title:"日期",key:"updateTime",minWidth:120},{title:"备注",key:"mark",minWidth:120}],t.loading=!1})).catch((function(e){t.loading=!1}))},getInfo:function(){var t=this;this.tableFrom.userId=this.uid,this.loading=!0,Object(a["j"])(this.tableFrom).then((function(e){switch(t.tableData.data=e.list,t.tableData.total=e.total,t.tableFrom.type){case"0":t.columns=[{title:"订单ID",key:"orderId",minWidth:250},{title:"收货人",key:"realName",minWidth:90},{title:"商品数量",key:"totalNum",minWidth:80},{title:"商品总价",key:"totalPrice",minWidth:90},{title:"实付金额",key:"payPrice",minWidth:90},{title:"交易完成时间",key:"payTime",minWidth:160}];break;case"2":t.columns=[{title:"动作",key:"title",minWidth:120},{title:"获得积分",key:"number",minWidth:120},{title:"签到时间",key:"createTime",minWidth:120},{title:"备注",key:"title",minWidth:120}];break;case"3":t.columns=[{title:"优惠券名称",key:"name",minWidth:120},{title:"面值",key:"money",minWidth:120},{title:"有效期",key:"endTime",minWidth:120},{title:"最低消费额",key:"minPrice",minWidth:120},{title:"兑换时间",key:"updateTime",minWidth:120}];break;case"4":t.columns=[{title:"变动金额",key:"number",minWidth:120},{title:"变动后",key:"balance",minWidth:120},{title:"类型",key:"title",minWidth:120},{title:"创建时间",key:"add_time",minWidth:120},{title:"备注",key:"mark",minWidth:120}];break;default:t.columns=[{title:"ID",key:"uid",minWidth:120},{title:"昵称",key:"nickname",minWidth:120},{title:"等级",key:"level",minWidth:120},{title:"加入时间",key:"createTime",minWidth:120}]}t.loading=!1})).catch((function(){t.loading=!1}))},pageChange:function(t){this.tableFrom.page=t,"1"===this.tableFrom.type?this.integral():this.getInfo()},handleSizeChange:function(t){this.tableFrom.limit=t,"1"===this.tableFrom.type?this.integral():this.getInfo()},getHeader:function(){var t=this;Object(a["x"])({userId:this.uid}).then((function(e){t.psInfo=e}))}}},F=k,L=(r("abfb"),Object(b["a"])(F,_,w,!1,null,"b239fb30",null)),C=L.exports,O=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("el-form",{ref:"ruleForm",staticClass:"demo-ruleForm",attrs:{model:t.ruleForm,"label-width":"100px"}},[r("el-form-item",[r("el-alert",{attrs:{title:"请勿频繁更改，以免计算产生混乱！",type:"warning"}})],1),t._v(" "),r("el-form-item",{attrs:{label:"用户等级","label-width":"100px"}},[r("el-select",{attrs:{clearable:"",placeholder:"请选择"},on:{change:t.currentSel},model:{value:t.ruleForm.levelId,callback:function(e){t.$set(t.ruleForm,"levelId",e)},expression:"ruleForm.levelId"}},t._l(t.levelList,(function(t){return r("el-option",{key:t.grade,attrs:{label:t.name,value:t.id}})})),1)],1),t._v(" "),""!=t.grade&&t.grade<t.levelInfo.gradeLevel?r("el-form-item",{attrs:{label:"扣除经验","label-width":"100px"}},[r("el-switch",{model:{value:t.ruleForm.isSub,callback:function(e){t.$set(t.ruleForm,"isSub",e)},expression:"ruleForm.isSub"}})],1):t._e(),t._v(" "),r("el-form-item",[r("el-button",{on:{click:function(e){return t.resetForm("ruleForm")}}},[t._v("取消")]),t._v(" "),r("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.submitForm("ruleForm")}}},[t._v("确定")])],1)],1)},P=[],j={props:{levelInfo:{type:Object,default:{}},levelList:{type:Array,default:[]}},data:function(){return{grade:"",levelStatus:!1,ruleForm:{isSub:!1,levelId:"",uid:this.levelInfo.uid}}},created:function(){this.ruleForm.levelId=this.levelInfo.level?Number(this.levelInfo.level):""},watch:{levelInfo:function(t){this.ruleForm.uid=t.uid||0,this.ruleForm.levelId=this.levelInfo.level?Number(this.levelInfo.level):t.levelId}},methods:{submitForm:Object(u["a"])((function(t){var e=this;this.$refs[t].validate((function(r){if(!r)return!1;Object(a["B"])(e.ruleForm).then((function(r){e.$message.success("编辑成功"),e.$parent.$parent.getList(),e.$parent.$parent.levelVisible=!1,e.$refs[t].resetFields(),e.grade=""}))}))})),currentSel:function(){var t=this;this.levelList.forEach((function(e){e.id==t.ruleForm.levelId&&(t.grade=e.grade)}))},resetForm:function(t){var e=this;this.$nextTick((function(){e.$refs[t].resetFields(),e.grade=""})),this.$parent.$parent.levelVisible=!1}}},I=j,S=Object(b["a"])(I,O,P,!1,null,null,null),E=S.exports,V=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"divBox"},[r("el-card",{staticClass:"box-card"},[r("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[r("el-form",{attrs:{inline:""}},[r("el-form-item",[r("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入用户名称"},model:{value:t.tableFrom.keywords,callback:function(e){t.$set(t.tableFrom,"keywords",e)},expression:"tableFrom.keywords"}},[r("el-button",{attrs:{slot:"append",icon:"el-icon-search"},on:{click:t.search},slot:"append"})],1)],1)],1)],1),t._v(" "),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{data:t.tableData.data,width:"800px",size:"small"}},[r("el-table-column",{attrs:{label:"",width:"40"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("el-radio",{attrs:{label:e.row.uid},nativeOn:{change:function(r){return t.getTemplateRow(e.$index,e.row)}},model:{value:t.templateRadio,callback:function(e){t.templateRadio=e},expression:"templateRadio"}},[t._v(" ")])]}}])}),t._v(" "),r("el-table-column",{attrs:{prop:"uid",label:"ID","min-width":"60"}}),t._v(" "),r("el-table-column",{attrs:{prop:"nickname",label:"微信用户名称","min-width":"130"}}),t._v(" "),r("el-table-column",{attrs:{label:"用户头像","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(t){return[r("div",{staticClass:"demo-image__preview"},[r("el-image",{staticClass:"tabImage",attrs:{src:t.row.avatar,"preview-src-list":[t.row.avatar]}})],1)]}}])}),t._v(" "),r("el-table-column",{attrs:{label:"性别","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("span",[t._v(t._s(t._f("saxFilter")(e.row.sex)))])]}}])}),t._v(" "),r("el-table-column",{attrs:{label:"地区","min-width":"130"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("span",[t._v(t._s(e.row.addres))])]}}])})],1),t._v(" "),r("div",{staticClass:"block"},[r("el-pagination",{attrs:{"page-sizes":[10,20,30,40],"page-size":t.tableFrom.limit,"current-page":t.tableFrom.page,layout:"total, sizes, prev, pager, next, jumper",total:t.tableData.total},on:{"size-change":t.handleSizeChange,"current-change":t.pageChange}})],1)],1)],1)},D=[],$={name:"UserList",filters:{saxFilter:function(t){var e={0:"未知",1:"男",2:"女"};return e[t]},statusFilter:function(t){var e={wechat:"微信用户",routine:"小程序用户"};return e[t]}},data:function(){return{templateRadio:0,loading:!1,tableData:{data:[],total:0},tableFrom:{page:1,limit:10,keywords:""}}},mounted:function(){this.getList()},methods:{getTemplateRow:function(t,e){this.$emit("getTemplateRow",e)},getList:function(){var t=this;this.loading=!0,Object(a["C"])(this.tableFrom).then((function(e){t.tableData.data=e.list,t.tableData.total=e.total,t.loading=!1})).catch((function(e){t.$message.error(e.message),t.loading=!1}))},search:function(){var t=this;this.loading=!0,Object(a["C"])({keywords:this.tableFrom.keywords}).then((function(e){t.tableData.data=e.list,t.tableData.total=e.total,t.loading=!1})).catch((function(e){t.$message.error(e.message),t.loading=!1}))},pageChange:function(t){this.tableFrom.page=t,this.getList()},handleSizeChange:function(t){this.tableFrom.limit=t,this.getList()}}},T=$,N=Object(b["a"])(T,V,D,!1,null,"44b1ead6",null),W=N.exports,z=r("2f2c"),A=(r("a78e"),r("e350"));function R(t){return R="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},R(t)}function B(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */B=function(){return e};var t,e={},r=Object.prototype,i=r.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},a="function"==typeof Symbol?Symbol:{},o=a.iterator||"@@iterator",l=a.asyncIterator||"@@asyncIterator",s=a.toStringTag||"@@toStringTag";function u(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,r){return t[e]=r}}function c(t,e,r,i){var a=e&&e.prototype instanceof b?e:b,o=Object.create(a.prototype),l=new I(i||[]);return n(o,"_invoke",{value:C(t,r,l)}),o}function d(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=c;var m="suspendedStart",p="suspendedYield",f="executing",h="completed",v={};function b(){}function g(){}function y(){}var _={};u(_,o,(function(){return this}));var w=Object.getPrototypeOf,x=w&&w(w(S([])));x&&x!==r&&i.call(x,o)&&(_=x);var k=y.prototype=b.prototype=Object.create(_);function F(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function L(t,e){function r(n,a,o,l){var s=d(t[n],t,a);if("throw"!==s.type){var u=s.arg,c=u.value;return c&&"object"==R(c)&&i.call(c,"__await")?e.resolve(c.__await).then((function(t){r("next",t,o,l)}),(function(t){r("throw",t,o,l)})):e.resolve(c).then((function(t){u.value=t,o(u)}),(function(t){return r("throw",t,o,l)}))}l(s.arg)}var a;n(this,"_invoke",{value:function(t,i){function n(){return new e((function(e,n){r(t,i,e,n)}))}return a=a?a.then(n,n):n()}})}function C(e,r,i){var n=m;return function(a,o){if(n===f)throw Error("Generator is already running");if(n===h){if("throw"===a)throw o;return{value:t,done:!0}}for(i.method=a,i.arg=o;;){var l=i.delegate;if(l){var s=O(l,i);if(s){if(s===v)continue;return s}}if("next"===i.method)i.sent=i._sent=i.arg;else if("throw"===i.method){if(n===m)throw n=h,i.arg;i.dispatchException(i.arg)}else"return"===i.method&&i.abrupt("return",i.arg);n=f;var u=d(e,r,i);if("normal"===u.type){if(n=i.done?h:p,u.arg===v)continue;return{value:u.arg,done:i.done}}"throw"===u.type&&(n=h,i.method="throw",i.arg=u.arg)}}}function O(e,r){var i=r.method,n=e.iterator[i];if(n===t)return r.delegate=null,"throw"===i&&e.iterator.return&&(r.method="return",r.arg=t,O(e,r),"throw"===r.method)||"return"!==i&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+i+"' method")),v;var a=d(n,e.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,v;var o=a.arg;return o?o.done?(r[e.resultName]=o.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function P(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function j(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function I(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(P,this),this.reset(!0)}function S(e){if(e||""===e){var r=e[o];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,a=function r(){for(;++n<e.length;)if(i.call(e,n))return r.value=e[n],r.done=!1,r;return r.value=t,r.done=!0,r};return a.next=a}}throw new TypeError(R(e)+" is not iterable")}return g.prototype=y,n(k,"constructor",{value:y,configurable:!0}),n(y,"constructor",{value:g,configurable:!0}),g.displayName=u(y,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===g||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,y):(t.__proto__=y,u(t,s,"GeneratorFunction")),t.prototype=Object.create(k),t},e.awrap=function(t){return{__await:t}},F(L.prototype),u(L.prototype,l,(function(){return this})),e.AsyncIterator=L,e.async=function(t,r,i,n,a){void 0===a&&(a=Promise);var o=new L(c(t,r,i,n),a);return e.isGeneratorFunction(r)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},F(k),u(k,s,"Generator"),u(k,o,(function(){return this})),u(k,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var i in e)r.push(i);return r.reverse(),function t(){for(;r.length;){var i=r.pop();if(i in e)return t.value=i,t.done=!1,t}return t.done=!0,t}},e.values=S,I.prototype={constructor:I,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(j),!e)for(var r in this)"t"===r.charAt(0)&&i.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function n(i,n){return l.type="throw",l.arg=e,r.next=i,n&&(r.method="next",r.arg=t),!!n}for(var a=this.tryEntries.length-1;a>=0;--a){var o=this.tryEntries[a],l=o.completion;if("root"===o.tryLoc)return n("end");if(o.tryLoc<=this.prev){var s=i.call(o,"catchLoc"),u=i.call(o,"finallyLoc");if(s&&u){if(this.prev<o.catchLoc)return n(o.catchLoc,!0);if(this.prev<o.finallyLoc)return n(o.finallyLoc)}else if(s){if(this.prev<o.catchLoc)return n(o.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return n(o.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&i.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var a=n;break}}a&&("break"===t||"continue"===t)&&a.tryLoc<=e&&e<=a.finallyLoc&&(a=null);var o=a?a.completion:{};return o.type=t,o.arg=e,a?(this.method="next",this.next=a.finallyLoc,v):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),j(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var i=r.completion;if("throw"===i.type){var n=i.arg;j(r)}return n}}throw Error("illegal catch attempt")},delegateYield:function(e,r,i){return this.delegate={iterator:S(e),resultName:r,nextLoc:i},"next"===this.method&&(this.arg=t),v}},e}function G(t,e,r,i,n,a,o){try{var l=t[a](o),s=l.value}catch(t){return void r(t)}l.done?e(s):Promise.resolve(s).then(i,n)}function U(t){return function(){var e=this,r=arguments;return new Promise((function(i,n){var a=t.apply(e,r);function o(t){G(a,i,n,o,l,"next",t)}function l(t){G(a,i,n,o,l,"throw",t)}o(void 0)}))}}function q(t,e,r){return(e=M(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function M(t){var e=H(t,"string");return"symbol"==R(e)?e:e+""}function H(t,e){if("object"!=R(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var i=r.call(t,e||"default");if("object"!=R(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var J={name:"UserIndex",components:{editFrom:y,userDetails:C,userList:W,levelEdit:E},filters:{sexFilter:function(t){var e={0:"未知",1:"男",2:"女",3:"保密"};return e[t]}},data:function(){return q({formExtension:{image:"",spreadUid:"",userId:""},ruleInline:{},extensionVisible:!1,userVisible:!1,levelInfo:"",pickerOptions:this.$timeOptions,loadingBtn:!1,PointValidateForm:{integralType:2,integralValue:0,moneyType:2,moneyValue:0,uid:""},loadingPoint:!1,VisiblePoint:!1,visible:!1,userIds:"",dialogVisible:!1,levelVisible:!1,levelData:[],groupData:[],labelData:[],selData:[],labelPosition:"right",collapse:!1,props:{children:"child",label:"name",value:"name",emitPath:!1},propsCity:{children:"child",label:"name",value:"name"},headeNum:[{type:"",name:"全部用户"},{type:"wechat",name:"微信公众号用户"},{type:"routine",name:"微信小程序用户"},{type:"h5",name:"H5用户"}],listLoading:!0,tableData:{data:[],total:0},loginType:"",userFrom:{labelId:"",userType:"",sex:"",isPromoter:"",country:"",payCount:"",accessType:0,dateLimit:"",keywords:"",province:"",city:"",page:1,limit:15,level:"",groupId:""},grid:{xl:8,lg:12,md:12,sm:24,xs:24},levelList:[],labelLists:[],groupList:[],selectedData:[],timeVal:[],addresData:[],dynamicValidateForm:{groupId:[]},loading:!1,groupIdFrom:[],selectionList:[],batchName:"",uid:0,Visible:!1,keyNum:0,address:[],multipleSelectionAll:[],idKey:"uid"},"uid","")},activated:function(){this.userFrom.keywords="",this.loginType="0",this.getList(1)},mounted:function(){this.getList(),this.groupLists(),this.levelLists(),this.getTagList(),this.getCityList()},methods:{checkPermi:A["a"],setPhone:function(t){var e=this;this.$prompt("修改手机号",{confirmButtonText:"确定",cancelButtonText:"取消",inputErrorMessage:"请输入修改手机号",inputType:"text",inputValue:t.phone,inputPlaceholder:"请输入手机号",closeOnClickModal:!1,inputValidator:function(t){if(!t)return"请填写手机号"}}).then((function(r){var i=r.value;Object(a["y"])({id:t.uid,phone:i}).then((function(){e.$message.success("编辑成功"),e.getList()}))})).catch((function(){e.$message.info("取消输入")}))},clearSpread:function(t){var e=this;this.$modalSure("解除【"+t.nickname+"】的上级推广人吗").then((function(){Object(o["d"])(t.uid).then((function(t){e.$message.success("清除成功"),e.getList()}))}))},onSubExtension:function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return!1;Object(a["z"])(e.formExtension).then((function(t){e.$message.success("设置成功"),e.extensionVisible=!1,e.getList()}))}))},getTemplateRow:function(t){this.formExtension.image=t.avatar,this.formExtension.spreadUid=t.uid},setExtension:function(t){this.formExtension={image:"",spreadUid:"",userId:t.uid},this.extensionVisible=!0},handleCloseExtension:function(){this.extensionVisible=!1},modalPicTap:function(){this.userVisible=!0},resetForm:function(){this.visible=!1},reset:function(t){this.userFrom={labelId:"",userType:"",sex:"",isPromoter:"",country:"",payCount:"",accessType:0,dateLimit:"",keywords:"",province:"",city:"",page:1,limit:15,level:"",groupId:""},this.levelData=[],this.groupData=[],this.labelData=[],this.timeVal=[],this.getList()},getCityList:function(){var t=U(B().mark((function t(){var e;return B().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,z["c"]();case 2:e=t.sent,this.addresData=e;case 4:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),sendNews:function(){if(0===this.selectionList.length)return this.$message.warning("请先选择用户");this.$modalArticle((function(t){}),"send")},onSend:function(){if(0===this.selectionList.length)return this.$message.warning("请选择要设置的用户");var t=this;this.$modalCoupon("send",this.keyNum+=1,[],(function(e){t.formValidate.give_coupon_ids=[],t.couponData=[],e.map((function(e){t.formValidate.give_coupon_ids.push(e.coupon_id),t.couponData.push(e.title)})),t.selectionList=[]}),this.userIds,"user")},Close:function(){this.Visible=!1,this.levelVisible=!1},onDetails:function(t){this.uid=t,this.Visible=!0},onLevel:function(t,e){var r=new Object;this.levelList.forEach((function(t){t.id==e&&(r.gradeLevel=t.grade)})),r.uid=t,r.level=e,this.levelInfo=r,this.levelVisible=!0},editPoint:function(t){this.uid=t,this.VisiblePoint=!0},submitPointForm:Object(u["a"])((function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return!1;e.PointValidateForm.uid=e.uid,e.loadingBtn=!0,Object(a["b"])(e.PointValidateForm).then((function(t){e.$message.success("设置成功"),e.loadingBtn=!1,e.handlePointClose(),e.getList()})).catch((function(){e.loadingBtn=!1}))}))})),handlePointClose:function(){this.VisiblePoint=!1,this.PointValidateForm={integralType:2,integralValue:0,moneyType:2,moneyValue:0,uid:""}},editUser:function(t){this.uid=t,this.visible=!0},submitForm:function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return!1;e.loading=!0,"group"===e.batchName?Object(a["g"])({groupId:e.dynamicValidateForm.groupId,id:e.userIds}).then((function(t){e.$message.success("设置成功"),e.loading=!1,e.handleClose(),e.getList()})).catch((function(){e.loading=!1})):Object(a["u"])({tagId:e.dynamicValidateForm.groupId.join(","),id:e.userIds}).then((function(t){e.$message.success("设置成功"),e.loading=!1,e.handleClose(),e.getList()})).catch((function(){e.loading=!1}))}))},setBatch:function(t,e){if(this.batchName=t,e?(this.userIds=e.uid,"group"===this.batchName?this.dynamicValidateForm.groupId=e.groupId?Number(e.groupId):"":this.dynamicValidateForm.groupId=e.tagId?e.tagId.split(",").map(Number):[]):this.dynamicValidateForm.groupId="",0===this.multipleSelectionAll.length&&!e)return this.$message.warning("请选择要设置的用户");this.dialogVisible=!0},handleClose:function(){this.dialogVisible=!1,this.$refs["dynamicValidateForm"].resetFields()},onSelectTab:function(t){var e=this;this.selectionList=t,setTimeout((function(){e.changePageCoreRecordData();var t=[];e.multipleSelectionAll.length&&(e.multipleSelectionAll.map((function(e){t.push(e.uid)})),e.userIds=t.join(","))}),50)},userSearchs:function(){this.userFrom.page=1,this.getList()},changeCountry:function(){"OTHER"!==this.userFrom.country&&this.userFrom.country||(this.selectedData=[],this.userFrom.province="",this.userFrom.city="",this.address=[])},handleChange:function(t){this.userFrom.province=t[0],this.userFrom.city=t[1]},onchangeTime:function(t){this.timeVal=t,this.userFrom.dateLimit=t?this.timeVal.join(","):""},groupLists:function(){var t=this;Object(a["f"])({page:1,limit:9999}).then(function(){var e=U(B().mark((function e(r){return B().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.groupList=r.list;case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}())},getTagList:function(){var t=this;Object(a["t"])({page:1,limit:9999}).then((function(e){t.labelLists=e.list}))},levelLists:function(){var t=this;Object(a["m"])().then(function(){var e=U(B().mark((function e(r){return B().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.levelList=r,localStorage.setItem("levelKey",JSON.stringify(r));case 2:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}())},getList:function(t){var e=this;this.listLoading=!0,this.userFrom.page=t||this.userFrom.page,this.userFrom.userType=this.loginType,0==this.loginType&&(this.userFrom.userType=""),this.userFrom.level=this.levelData.join(","),this.userFrom.groupId=this.groupData.join(","),this.userFrom.labelId=this.labelData.join(","),Object(a["C"])(this.userFrom).then((function(t){e.tableData.data=t.list,e.tableData.total=t.total,e.$nextTick((function(){this.setSelectRow()})),e.listLoading=!1})).catch((function(){e.listLoading=!1})),this.checkedCities=this.$cache.local.has("user_stroge")?this.$cache.local.getJSON("user_stroge"):this.checkedCities},setSelectRow:function(){if(this.multipleSelectionAll&&!(this.multipleSelectionAll.length<=0)){var t=this.idKey,e=[];this.multipleSelectionAll.forEach((function(r){e.push(r[t])})),this.$refs.table.clearSelection();for(var r=0;r<this.tableData.data.length;r++)e.indexOf(this.tableData.data[r][t])>=0&&this.$refs.table.toggleRowSelection(this.tableData.data[r],!0)}},changePageCoreRecordData:function(){var t=this.idKey,e=this;if(this.multipleSelectionAll.length<=0)this.multipleSelectionAll=this.selectionList;else{var r=[];this.multipleSelectionAll.forEach((function(e){r.push(e[t])}));var i=[];this.selectionList.forEach((function(n){i.push(n[t]),r.indexOf(n[t])<0&&e.multipleSelectionAll.push(n)}));var n=[];this.tableData.data.forEach((function(e){i.indexOf(e[t])<0&&n.push(e[t])})),n.forEach((function(i){if(r.indexOf(i)>=0)for(var n=0;n<e.multipleSelectionAll.length;n++)if(e.multipleSelectionAll[n][t]==i){e.multipleSelectionAll.splice(n,1);break}}))}},pageChange:function(t){this.changePageCoreRecordData(),this.userFrom.page=t,this.getList()},handleSizeChange:function(t){this.changePageCoreRecordData(),this.userFrom.limit=t,this.getList()},handleDelete:function(t,e){var r=this;this.$modalSure().then((function(){productDeleteApi(t).then((function(){r.$message.success("删除成功"),r.getList()}))}))},onchangeIsShow:function(t){var e=this;t.isShow?putOnShellApi(t.id).then((function(){e.$message.success("上架成功"),e.getList()})).catch((function(){t.isShow=!t.isShow})):offShellApi(t.id).then((function(){e.$message.success("下架成功"),e.getList()})).catch((function(){t.isShow=!t.isShow}))}}},K=J,Y=(r("f9e5"),Object(b["a"])(K,i,n,!1,null,"7d591b94",null));e["default"]=Y.exports},f076:function(t,e,r){"use strict";r("b2bf")},f9e5:function(t,e,r){"use strict";r("0a52")}}]);