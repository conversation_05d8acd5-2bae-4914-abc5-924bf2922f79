package com.ylpz.core.common.request;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ylpz.core.common.annotation.StringContains;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 订单列表请求对象
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("store_order")
@ApiModel(value = "StoreOrderSearchRequest对象", description = "订单列表请求对象")
public class StoreOrderSearchRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "创建时间区间")
    private String dateLimit;

    @ApiModelProperty(
        value = "订单状态（all 总数； 未支付 unPaid； 未发货 notShipped；待收货 spike；待评价 bargain；已完成 complete；待核销 toBeWrittenOff；退款中:refunding；已退款:refunded；已删除:deleted;" +
                "auditAddressAll:全部审核地址;toBeAuditAddress:待审核收货地址;auditAddressPass:收货地址审核通过；auditAddressRefuse:收货地址审核失败;")
    @StringContains(limitValues = {"all", "unPaid", "notShipped", "spike", "bargain", "complete", "toBeWrittenOff",
        "refunding", "refunded", "deleted","auditAddressAll","toBeAuditAddress","auditAddressPass","auditAddressRefuse",}, message = "未知的订单状态")
    private String status;

    @ApiModelProperty(value = "订单类型：0普通订单，1-视频号订单, 2-全部订单")
    @NotNull(message = "订单类型不能为空")
    @Range(min = 0, max = 2, message = "未知的订单类型")
    private Integer type;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    protected String storeName;

    @ApiModelProperty(value = "订单备注")
    private String remark;

    @ApiModelProperty(value = "快递单号")
    private String expressNo;

}
