package com.ylpz.core.common.request;

import javax.validation.constraints.NotBlank;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "SystemThemeIconRequest对象", description = "系统主题图标请求对象")
public class SystemThemeIconRequest {

    @ApiModelProperty(value = "图标ID")
    private Integer id;

    @NotBlank(message = "图标名称不能为空")
    @ApiModelProperty(value = "图标名称", required = true)
    private String name;

    @NotBlank(message = "图标图片不能为空")
    @ApiModelProperty(value = "图标图片路径", required = true)
    private String iconImage;

    @ApiModelProperty(value = "排序")
    private Integer sort = 0;
}