package com.ylpz.admin.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ylpz.core.common.page.CommonPage;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.core.common.request.SearchAndPageRequest;
import com.ylpz.core.common.request.StoreDiscountRequest;
import com.ylpz.core.common.request.StoreDiscountSearchRequest;
import com.ylpz.core.common.response.CommonResult;
import com.ylpz.core.common.response.StoreDiscountInfoResponse;
import com.ylpz.core.service.StoreDiscountService;
import com.ylpz.model.discount.StoreDiscount;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * 折扣表 前端控制器
 */
@Slf4j
@RestController
@RequestMapping("api/admin/marketing/discount")
@Api(tags = "营销 -- 折扣")
public class StoreDiscountController {

    @Autowired
    private StoreDiscountService storeDiscountService;

    /**
     * 分页显示折扣表
     * @param request 搜索条件
     * @param pageParamRequest 分页参数
     */
    //@PreAuthorize("hasAuthority('admin:discount:list')")
    @ApiOperation(value = "分页列表")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public CommonResult<CommonPage<StoreDiscount>> getList(@Validated StoreDiscountSearchRequest request, @Validated PageParamRequest pageParamRequest) {
        CommonPage<StoreDiscount> storeDiscountCommonPage = CommonPage.restPage(storeDiscountService.getList(request, pageParamRequest));
        return CommonResult.success(storeDiscountCommonPage);
    }

    /**
     * 保存折扣表
     * @param request StoreDiscountRequest 新增参数
     */
    //@PreAuthorize("hasAuthority('admin:discount:save')")
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public CommonResult<String> save(@RequestBody @Validated StoreDiscountRequest request) {
        if (storeDiscountService.create(request)) {
            return CommonResult.success();
        } else {
            return CommonResult.failed();
        }
    }

    /**
     * 是否有效
     * @param id integer id
     */
    //@PreAuthorize("hasAuthority('admin:discount:update:status')")
    @ApiOperation(value = "修改")
    @RequestMapping(value = "/update/status", method = RequestMethod.POST)
    public CommonResult<String> updateStatus(@RequestParam Integer id, @RequestParam Boolean status) {
        if (storeDiscountService.updateStatus(id, status)) {
            return CommonResult.success();
        } else {
            return CommonResult.failed();
        }
    }

    /**
     * 详情
     * @param id integer id
     */
    //@PreAuthorize("hasAuthority('admin:discount:info')")
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/info", method = RequestMethod.POST)
    @ApiImplicitParam(name="id", value="折扣ID", required = true)
    public CommonResult<StoreDiscountInfoResponse> info(@RequestParam Integer id) {
        return CommonResult.success(storeDiscountService.info(id));
    }

    /**
     * 发送折扣列表
     * @param searchAndPageRequest 搜索分页参数
     */
    //@PreAuthorize("hasAuthority('admin:discount:send:list')")
    @ApiOperation(value = "发送折扣列表")
    @RequestMapping(value = "/send/list", method = RequestMethod.GET)
    public CommonResult<CommonPage<StoreDiscount>> getSendList(@Validated SearchAndPageRequest searchAndPageRequest) {
        CommonPage<StoreDiscount> storeDiscountCommonPage = CommonPage.restPage(storeDiscountService.getSendList(searchAndPageRequest));
        return CommonResult.success(storeDiscountCommonPage);
    }

    /**
     * 删除折扣
     * @param id 折扣id
     */
    //@PreAuthorize("hasAuthority('admin:discount:delete')")
    @ApiOperation(value = "删除折扣")
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public CommonResult<StoreDiscountInfoResponse> delete(@RequestParam Integer id) {
        if (storeDiscountService.delete(id)) {
            return CommonResult.success("删除成功");
        } else {
            return CommonResult.failed("删除失败");
        }
    }
} 