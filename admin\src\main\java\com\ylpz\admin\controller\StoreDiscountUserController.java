package com.ylpz.admin.controller;

import com.ylpz.core.common.page.CommonPage;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.core.common.request.StoreDiscountUserRequest;
import com.ylpz.core.common.request.StoreDiscountUserSearchRequest;
import com.ylpz.core.common.response.CommonResult;
import com.ylpz.core.common.response.StoreDiscountUserResponse;
import com.ylpz.core.service.StoreDiscountUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 折扣发放记录表 前端控制器
 */
@Slf4j
@RestController
@RequestMapping("api/admin/marketing/discount/user")
@Api(tags = "营销 -- 折扣 -- 领取记录")
public class StoreDiscountUserController {

    @Autowired
    private StoreDiscountUserService storeDiscountUserService;

    /**
     * 分页显示折扣发放记录表
     * @param request 搜索条件
     * @param pageParamRequest 分页参数
     */
    //@PreAuthorize("hasAuthority('admin:discount:user:list')")
    @ApiOperation(value = "分页列表")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public CommonResult<CommonPage<StoreDiscountUserResponse>> getList(@Validated StoreDiscountUserSearchRequest request, @Validated PageParamRequest pageParamRequest) {
        CommonPage<StoreDiscountUserResponse> storeDiscountUserCommonPage = CommonPage.restPage(storeDiscountUserService.getList(request, pageParamRequest));
        return CommonResult.success(storeDiscountUserCommonPage);
    }

    /**
     * 领券
     * @param storeDiscountUserRequest 新增参数
     */
    //@PreAuthorize("hasAuthority('admin:discount:user:receive')")
    @ApiOperation(value = "领券")
    @RequestMapping(value = "/receive", method = RequestMethod.POST)
    public CommonResult<String> receive(@Validated StoreDiscountUserRequest storeDiscountUserRequest) {
        if(storeDiscountUserService.receive(storeDiscountUserRequest)) {
            return CommonResult.success();
        } else {
            return CommonResult.failed();
        }
    }
} 