# 电商商城系统

这是一个基于Java Spring Boot的电商商城后台管理系统，提供商品管理、订单管理、用户管理、分销返佣、会员体系等完整的电商解决方案。

## 项目概述

本项目是一个功能完整的电商商城系统，支持B2C电商业务模式，包含后台管理系统、小程序接口、分销体系、会员等级管理、营销活动等核心功能。系统采用微服务架构设计，具有良好的扩展性和维护性。

## 技术栈

### 后端技术
- **框架**: Spring Boot 2.2.6.RELEASE
- **数据库**: MySQL 8.0
- **ORM**: MyBatis-Plus 3.5.7
- **缓存**: Redis 6.0
- **连接池**: Druid 1.1.20
- **文档**: Swagger 2.9.2
- **工具库**: Hutool、Lombok、FastJSON
- **图片处理**: Thumbnailator 0.4.8
- **二维码**: ZXing 3.3.3
- **JWT**: JJWT 0.9.1

### 开发环境
- **JDK**: 1.8+
- **Maven**: 3.6+
- **IDE**: IntelliJ IDEA / Eclipse
- **数据库**: MySQL 8.0+
- **缓存**: Redis 6.0+

## 项目架构

### 模块结构
```
mall/
├── admin/          # 后台管理模块 - 提供管理员操作界面和API
├── core/           # 核心业务模块 - 包含所有业务逻辑和服务层
├── entity/         # 实体类模块 - 数据库实体映射
├── model/          # 数据模型模块 - 请求响应对象和VO
└── mini/           # 小程序接口模块 - 提供前端和小程序API
```

### 技术架构
- **表现层**: Spring MVC + RESTful API
- **业务层**: Spring Service + 事务管理
- **持久层**: MyBatis-Plus + MySQL
- **缓存层**: Redis + Spring Cache
- **安全层**: JWT Token + 权限控制
- **文件存储**: 本地存储 + 云存储(七牛云/阿里云OSS/腾讯云COS)

## 快速开始

### 环境准备
1. **安装JDK 1.8+**
2. **安装MySQL 8.0+**
3. **安装Redis 6.0+**
4. **安装Maven 3.6+**

### 数据库配置
1. 创建数据库：
```sql
CREATE DATABASE mall CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 导入数据库脚本（如果有的话）

3. 修改数据库连接配置：
   - 开发环境：`admin/src/main/resources/application-dev.yml`
   - 生产环境：`admin/src/main/resources/application-prod.yml`

### 启动项目

#### 后台管理系统启动
```bash
# 进入admin模块
cd admin

# 编译打包
mvn clean package -DskipTests

# 启动应用
java -jar target/admin-1.0-SNAPSHOT.jar --spring.profiles.active=dev
```

#### 小程序接口启动
```bash
# 进入mini模块
cd mini

# 编译打包
mvn clean package -DskipTests

# 启动应用
java -jar target/mini-1.0-SNAPSHOT.jar
```

### 访问地址
- **后台管理系统**: http://localhost:8080
- **小程序接口**: http://localhost:8080/api
- **API文档**: http://localhost:8080/swagger-ui.html

### 默认账号
- **管理员账号**: admin
- **管理员密码**: 123456

## 部署说明

### 生产环境部署

#### 1. 服务器环境准备
```bash
# 安装JDK
sudo yum install java-1.8.0-openjdk

# 安装MySQL
sudo yum install mysql-server

# 安装Redis
sudo yum install redis

# 启动服务
sudo systemctl start mysql
sudo systemctl start redis
sudo systemctl enable mysql
sudo systemctl enable redis
```

#### 2. 应用部署
```bash
# 创建应用目录
sudo mkdir -p /opt/mall
cd /opt/mall

# 上传jar包
# admin-1.0-SNAPSHOT.jar
# mini-1.0-SNAPSHOT.jar

# 创建启动脚本
sudo vim start.sh
```

启动脚本内容：
```bash
#!/bin/bash
# 后台管理系统
nohup java -jar admin-1.0-SNAPSHOT.jar --spring.profiles.active=prod > admin.log 2>&1 &

# 小程序接口
nohup java -jar mini-1.0-SNAPSHOT.jar --spring.profiles.active=prod > mini.log 2>&1 &

echo "应用启动完成"
```

#### 3. Nginx配置（可选）
```nginx
server {
    listen 80;
    server_name your-domain.com;

    # 后台管理
    location /admin {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    # API接口
    location /api {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 配置说明

### 数据库配置
```yaml
spring:
  datasource:
    driver-class-name: com.mysql.jdbc.Driver
    url: **********************************************************************************************************
    username: root
    password: your_password
```

### Redis配置
```yaml
spring:
  redis:
    host: localhost
    port: 6379
    password: your_redis_password
    database: 0
```

### 文件上传配置
```yaml
crmeb:
  imagePath: /home/<USER>/  # 图片存储路径
```

## 功能模块

### 商品管理
- 商品列表查询
- 商品新增/编辑/删除
- 商品上架/下架
  - 立即上架：商品立即上架开始售卖
  - 定时开售：设置商品在特定时间自动上架售卖
  - 暂不售卖，放入仓库：商品暂时不售卖，保存在仓库中
- 商品分类管理
- 商品规格管理
  - 支持设置商品规格库存
  - 支持设置商品规格价格
  - 支持设置商品规格图片
  - 支持设置商品规格数量（新增）
- 商品属性管理
- 商品搭配管理
  - 支持创建商品搭配组合
  - 支持为每个搭配中的商品设置数量
  - 支持搭配优惠价设置
  - 支持组合标签功能
  - 支持优惠叠加功能
  - 支持实际销量和库存管理
  - 支持上架状态管理和批量操作
  - 支持在商品列表中查询商品参与的组合活动（新增）

## 佣金返现系统

### 佣金系统概述
佣金返现系统是一个基于多级分销的返利机制，支持二级分销模式，允许分销员通过推广获取佣金。系统包含以下核心功能：

1. 分销员管理
2. 佣金计算规则
3. 佣金记录管理
4. 佣金提现管理
5. 佣金解冻机制

### 数据结构

#### 主要表结构

1. **user表** - 用户表
   - `uid`: 用户ID
   - `spread_uid`: 推广员ID (上级分销员)
   - `spread_time`: 成为推广员时间
   - `is_promoter`: 是否为推广员(分销员)
   - `brokerage_price`: 佣金金额
   - `spread_count`: 下级人数
   - `promoter_time`: 成为分销员时间

2. **user_brokerage_record表** - 用户佣金记录表
   - `id`: 记录ID
   - `uid`: 用户ID
   - `link_id`: 关联ID (订单号、提现ID等)
   - `link_type`: 关联类型 (order-订单, extract-提现, yue-余额)
   - `type`: 类型 (1-增加, 2-扣减)
   - `price`: 佣金金额
   - `balance`: 操作后余额
   - `status`: 状态 (1-订单创建, 2-冻结期, 3-完成, 4-失效(订单退款), 5-提现申请)
   - `frozen_time`: 冻结期时间(天)
   - `thaw_time`: 解冻时间
   - `brokerage_level`: 分销等级 (1-一级, 2-二级)

3. **user_extract表** - 用户提现表
   - `id`: 提现记录ID
   - `uid`: 用户ID
   - `extract_type`: 提现类型 (bank-银行卡, alipay-支付宝, weixin-微信)
   - `extract_price`: 提现金额
   - `status`: 状态 (-1-未通过, 0-审核中, 1-已提现)

### 系统配置项

系统中的佣金配置通过系统配置表(`system_config`)进行管理，主要包括以下配置项：

| 配置键 | 说明 | 默认值 |
| ------ | ---- | ------ |
| brokerage_func_status | 是否启用分销功能 | 1 (启用) |
| store_brokerage_status | 分销模式 (1-指定分销，2-人人分销) | 2 |
| store_brokerage_ratio | 一级返佣比例 | 整数存储，例如80表示80% |
| store_brokerage_two | 二级返佣比例 | 整数存储，例如20表示20% |
| store_brokerage_quota | 分销额度 | -1表示关闭，0表示用户购买金额大于等于设置金额时自动成为分销员 |
| extract_time | 佣金冻结天数 | 0 |
| user_extract_min_price | 用户提现最低金额 | 根据系统设置 |
| user_extract_bank | 提现银行 | 根据系统设置 |
| brokerage_bindind | 分销关系绑定 (0-所有用户，1-新用户) | 0 |
| store_brokerage_is_bubble | 是否展示分销气泡 (0-不展示，1-展示) | 1 |

### 核心业务流程

#### 1. 成为分销员
用户可以通过以下两种方式成为分销员：
- 系统设置为人人分销模式时，所有用户默认可成为分销员
- 达到消费门槛自动成为分销员，由配置`store_brokerage_quota`控制

```java
// 分销员逻辑
if (!user.getIsPromoter()) {
    String funcStatus = systemConfigService.getValueByKey(SysConfigConstants.CONFIG_KEY_BROKERAGE_FUNC_STATUS);
    if ("1".equals(funcStatus)) {
        String broQuota = systemConfigService.getValueByKey(SysConfigConstants.CONFIG_KEY_STORE_BROKERAGE_QUOTA);
        if (!"-1".equals(broQuota) && storeOrder.getPayPrice().compareTo(new BigDecimal(broQuota)) >= 0) {
            // 符合条件，设置为分销员
            user.setIsPromoter(true);
            user.setPromoterTime(cn.hutool.core.date.DateUtil.date());
        }
    }
}
```

## 奖励金与佣金返现功能

### 1. 功能概述

系统支持两种会员激励模式：
1. **佣金返现**：基于用户等级进行不同比例的返现
2. **奖励金**：针对特定行为（升级、首单、充值、排行榜等）的额外奖励

这些功能通过会员参数配置表（`system_member_param_config`）进行统一管理，用户佣金记录表（`user_brokerage_record`）记录所有佣金与奖励金变动。

### 2. 佣金返现功能

佣金返现是基于用户等级实现的不同比例返现：
- SVIP会员：默认返现比例10%
- VIP会员：默认返现比例6%
- 普通会员：默认返现比例3%

SVIP会员可以启用自动返现功能，订单支付后直接返还佣金给用户自己。

### 3. 奖励金功能

系统支持多种奖励金类型：

| 奖励类型 | 说明 | 默认金额/比例 |
| -------- | ---- | ------------ |
| 推广普通会员升级为VIP | 推广的普通会员升级为VIP后，给推广人一次性奖励 | 200元 |
| 推广会员充值 | 推广的会员充值时，按充值金额的一定比例给推广人奖励 | 10% |
| 推广新用户首单购买 | 推广的新用户首次下单，按订单金额的一定比例给推广人奖励 | 10% |
| 周排行榜奖励 | 根据每周销售业绩排名，给予前几名的用户固定金额奖励 | 第一名500元，第二名300元等 |
| 月排行榜奖励 | 根据每月销售业绩排名，给予前几名的用户固定金额奖励 | 根据配置决定 |
| 季排行榜奖励 | 根据每季度销售业绩排名，给予前几名的用户固定金额奖励 | 根据配置决定 |

### 4. 奖励金管理界面

奖励金管理界面提供以下功能：
1. 显示奖励金合计金额
2. 提供时间范围、奖励类型、人员等筛选条件
3. 显示奖励记录列表，包含奖励人员、金额、类型、时间、奖励信息等
4. 针对排行榜奖励，显示详细的排名和奖励信息

### 5. 接口说明

| 接口路径 | 方法 | 说明 |
| -------- | ---- | ---- |
| /api/admin/member/bonus/list | GET | 获取奖励金记录列表 |
| /api/admin/member/bonus/total | GET | 获取奖励金合计金额 |
| /api/admin/member/bonus/distribute/rank | POST | 手动发放排行榜奖励金 |
| /api/admin/member/param/getForm | GET | 获取会员参数配置（包含奖励金和佣金返现配置） |
| /api/admin/member/param/saveForm | POST | 保存会员参数配置（包含奖励金和佣金返现配置） |

### 6. 奖励金发放流程

1. 触发奖励金发放事件（如：用户升级、新用户首单等）
2. 查询对应的奖励金规则配置
1. 检查是否开启分销功能
2. 检查订单是否为营销活动订单（营销产品不参与分佣）
3. 检查用户是否有上级推广人
4. 获取参与分佣的人员（支持两级分销）
5. 计算各级分销员应得佣金
6. 生成佣金记录（初始状态为冻结）

```java
// 佣金计算核心代码
private BigDecimal calculateCommission(MyRecord record, Integer orderId) {
    // ... [代码省略]

    // 查询对应等级的分销比例
    Integer index = record.getInt("index");
    String key = "";
    if (index == 1) {
        key = Constants.CONFIG_KEY_STORE_BROKERAGE_RATE_ONE; // 一级返佣比例
    }
    if (index == 2) {
        key = Constants.CONFIG_KEY_STORE_BROKERAGE_RATE_TWO; // 二级返佣比例
    }
    String rate = systemConfigService.getValueByKey(key);

    // 佣金比例整数存储，例如80表示80%，计算时需除以100
    BigDecimal rateBigDecimal = new BigDecimal(rate).divide(BigDecimal.TEN.multiply(BigDecimal.TEN));

    // ... [产品佣金计算逻辑]
}
```

#### 3. 佣金冻结与解冻
- 新生成的佣金记录会根据系统配置进入冻结期
- 定时任务会处理冻结期满的佣金记录，将其状态更新为完成，并增加到用户可用佣金中

```java
// 佣金解冻处理
public void brokerageThaw() {
    // 查询需要解冻的佣金
    List<UserBrokerageRecord> thawList = findThawList();
    for (UserBrokerageRecord record : thawList) {
        // 查询对应的用户
        User user = userService.getById(record.getUid());
        if (ObjectUtil.isNull(user)) continue;

        record.setStatus(BrokerageRecordConstants.BROKERAGE_RECORD_STATUS_COMPLETE);
        // 计算佣金余额
        BigDecimal balance = user.getBrokeragePrice().add(record.getPrice());
        record.setBalance(balance);

        // 分佣处理
        Boolean execute = transactionTemplate.execute(e -> {
            updateById(record);
            userService.operationBrokerage(record.getUid(), record.getPrice(), user.getBrokeragePrice(), "add");
            return Boolean.TRUE;
        });
    }
}
```

#### 4. 佣金提现
- 用户可申请提现自己的可用佣金
- 提现申请需满足最低提现金额要求
- 提现申请需经过后台管理员审核
- 提现成功后会扣减用户可用佣金

#### 5. 佣金记录状态
佣金记录状态流转：
1. 订单创建 -> 生成佣金记录（status=1）
2. 进入冻结期 -> 佣金记录状态更新（status=2）
3. 冻结期结束 -> 佣金解冻完成（status=3）
4. 如订单退款 -> 佣金失效（status=4）
5. 申请提现 -> 提现申请中（status=5）

### 佣金相关接口

#### 分销配置管理接口
```
GET /api/admin/store/retail/spread/manage/get   // 获取分销配置
POST /api/admin/store/retail/spread/manage/set  // 保存分销配置
```

#### 分销员管理接口
```
GET /api/admin/store/retail/list  // 分销员列表
```

#### 佣金记录查询接口
```
GET /api/admin/user/brokerage/record  // 佣金记录查询
```

### 提现管理接口
```
GET /api/admin/finance/extract/list  // 提现申请列表
POST /api/admin/finance/extract/verify/{id}/{status}  // 提现审核
```

### 改造思路建议

1. **多级分销支持**：当前系统仅支持两级分销，可扩展至多级分销，通过动态配置每级分佣比例
2. **佣金分配规则优化**：增加按产品分类设置不同分佣比例的功能
3. **分销员等级体系**：引入分销员等级，不同等级享受不同分佣比例
4. **自动提现功能**：增加符合条件自动提现到微信或支付宝的功能
5. **分销活动支持**：特定活动期间设置特殊分佣比例
6. **分销统计分析**：增强分销数据分析功能，提供更直观的数据可视化
7. **分销推广工具**：提供更丰富的推广素材和链接生成工具
8. **分销裂变功能**：支持以优惠券或积分形式激励用户裂变推广

### 优惠券使用时间类型功能升级

系统对优惠券功能进行了升级，新增以下特性：

#### 1. 用券时间类型设置
- 将原有的"是否固定使用时间"字段改为"用券时间类型"，支持三种模式：
  - 固定时间：指定具体的使用开始和结束时间
  - 领取后天数：优惠券从领取当天开始计算，可在指定天数内使用
  - 领取后增加天数后可用：优惠券领取后需要等待指定天数后才能使用，并可在之后的指定天数内使用

#### 2. 数据表结构变更
在`store_coupon`表中进行了以下变更：
```sql
-- 修改is_fixed_time字段为use_time_type
ALTER TABLE `store_coupon` CHANGE COLUMN `is_fixed_time` `use_time_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '用券时间类型，1-固定时间，2-领取后天数，3-领取后增加天数';

-- 添加after_days字段
ALTER TABLE `store_coupon` ADD COLUMN `after_days` int(11) DEFAULT NULL COMMENT '领取后需等待天数后才可使用' AFTER `day`;
```

#### 3. 字段说明
- `use_time_type`: 用券时间类型
  - 1: 固定时间 - 使用固定的开始时间和结束时间
  - 2: 领取后天数 - 从领取日开始计算，可使用N天
  - 3: 领取后增加天数后可用 - 领取后需等待M天才能使用，然后可使用N天
- `day`: 可使用天数，对应类型2和类型3
- `after_days`: 领取后需等待天数，对应类型3

### 商品搭配功能升级

系统对商品搭配功能进行了全面升级，新增以下特性：

#### 1. 组合标签功能
- 支持为商品搭配添加多个标签（逗号分隔）
- 标签可用于前端搜索和筛选
- 标签可以表示搭配的适用场景、风格、活动等属性

#### 2. 优惠叠加功能
- 支持设置商品搭配是否允许使用满减券
- 开启后，消费者在购买搭配商品时可同时使用满减券
- 满减券将按照搭配的优惠价计算折扣

#### 3. 实际销量与库存管理
- 系统自动记录商品搭配的实际销量
- 支持设置商品搭配的库存数量（stock字段）
- 下单时自动扣减库存，防止超卖

#### 4. 商品上架状态管理
- 支持三种上架状态：
  - 暂不售卖放入仓库（storeStatus=0）：商品保存但不显示在前台
  - 立即上架（storeStatus=1）：商品立即在前台展示并可购买
  - 定时上架（storeStatus=2）：设置特定时间点自动上架售卖
- 定时上架功能可用于新品预告、活动预热等场景

#### 5. 商品搭配状态分类
- 在售中：上架状态(storeStatus=1) 且 有库存(stock>0) 且 未删除(isDel=0)
- 已售罄：上架状态(storeStatus=1) 且 无库存(stock<=0) 且 未删除(isDel=0)
- 仓库中：未上架(storeStatus!=1) 或 已删除(isDel=1)

#### 6. 批量操作功能
- 批量上架/下架商品搭配
- 批量删除商品搭配
- 批量设置库存

### 商品搭配API接口

#### 1. 商品搭配创建
```
POST /api/admin/product/combination
```
参数示例：
```json
{
  "name": "冬季暖心套装",
  "description": "冬季必备暖心组合",
  "comboTags": "冬季,保暖,限时特惠",
  "combinationPrice": 199.00,
  "initialSales": 50,
  "stock": 200,
  "allowDiscount": 1,
  "storeStatus": 1,
  "productIds": [101, 202, 303]
}
```

#### 2. 更新商品上架状态
```
PUT /api/admin/product/combination/{id}/storeStatus/{storeStatus}
```

#### 3. 批量更新商品上架状态
```
PUT /api/admin/product/combination/batch/storeStatus/{storeStatus}
```

#### 4. 更新库存数量
```
PUT /api/admin/product/combination/{id}/stock/{stock}
```

#### 5. 批量更新库存数量
```
PUT /api/admin/product/combination/batch/stock/{stock}
```

#### 6. 删除商品搭配
```
DELETE /api/admin/product/combination/{id}
```

#### 7. 批量删除商品搭配
```
DELETE /api/admin/product/combination/batch
```

#### 8. 获取商品搭配状态统计
```
GET /api/admin/product/combination/status/count
```

#### 9. 分页查询商品搭配列表（支持状态筛选）
```
GET /api/admin/product/combination/page
```
参数说明：
- `page`: 页码，默认值1
- `size`: 每页数量，默认值10
- `name`: 搭配名称，可选
- `statusType`: 状态类型，可选，默认值0
  - 0: 全部
  - 1: 在售中（上架且有库存且未删除）
  - 2: 已售罄（上架但无库存且未删除）
  - 3: 仓库中（未上架或已删除）

#### 10. 根据商品ID查询组合列表
```
GET /api/admin/product/combination/queryByProductId
```
参数说明：
- `productId`: 商品ID，必填

该接口用于查询指定商品ID相关的所有组合活动，返回结果包含组合名称、价格、库存、标签、状态等信息，常用于商品列表中的组合查询功能。

### 商品搭配数据表结构

```sql
-- 商品搭配表结构
CREATE TABLE `store_product_combination` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '搭配ID',
    `name` varchar(100) NOT NULL COMMENT '搭配组合名称',
    `description` varchar(500) DEFAULT NULL COMMENT '搭配描述',
    `combo_tags` varchar(255) DEFAULT NULL COMMENT '组合标签（逗号分隔）',
    `combination_price` decimal(10, 2) NOT NULL COMMENT '组合优惠价',
    `initial_sales` int(11) NOT NULL DEFAULT '0' COMMENT '初始已售数量（虚拟销量基础值）',
    `actual_sales` int(11) NOT NULL DEFAULT '0' COMMENT '实际销量',
    `stock` int(11) NOT NULL DEFAULT '0' COMMENT '库存数量',
    `allow_discount` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否允许使用满减券：0-不允许，1-允许',
    `start_time` datetime DEFAULT NULL COMMENT '售卖开始时间',
    `end_time` datetime DEFAULT NULL COMMENT '售卖结束时间',
    `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否启用：0-禁用，1-启用',
    `store_status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '商品上架状态：0-暂不售卖放入仓库，1-立即上架，2-定时上架',
    `sale_time` int(11) NOT NULL DEFAULT '0' COMMENT '定时开售时间（时间戳，0表示实时开售）',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_name` (`name`),
    KEY `idx_status` (`status`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品搭配表';

-- 商品搭配明细表
CREATE TABLE `store_product_combination_detail` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '明细ID',
    `combination_id` bigint(20) NOT NULL COMMENT '搭配组合ID',
    `product_id` bigint(20) NOT NULL COMMENT '商品ID',
    `quantity` int(11) NOT NULL DEFAULT 1 COMMENT '商品数量',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_combination_id` (`combination_id`),
    KEY `idx_product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品搭配明细表';
```

### 运费模板数据表结构

```sql
-- 运费模板表
CREATE TABLE `shipping_templates` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `name` varchar(255) NOT NULL COMMENT '模板名称',
  `type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '计费方式',
  `appoint` tinyint(1) NOT NULL DEFAULT '0' COMMENT '指定包邮',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用：0-禁用，1-启用',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='运费模板';

-- 运费模板指定区域费用
CREATE TABLE `shipping_templates_region` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `temp_id` int(11) NOT NULL DEFAULT '0' COMMENT '模板ID',
  `city_id` int(11) NOT NULL DEFAULT '0' COMMENT '城市ID',
  `title` text COMMENT '描述',
  `first` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '首件',
  `first_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '首件运费',
  `renewal` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '续件',
  `renewal_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '续件运费',
  `type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '计费方式 1按件数 2按重量 3按体积',
  `uniqid` varchar(32) NOT NULL DEFAULT '' COMMENT '分组唯一值',
  `status` tinyint(1) DEFAULT '0' COMMENT '是否无效',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='运费模板指定区域费用';

-- 运费模板包邮
CREATE TABLE `shipping_templates_free` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `temp_id` int(11) NOT NULL DEFAULT '0' COMMENT '模板ID',
  `city_id` int(11) NOT NULL DEFAULT '0' COMMENT '城市ID',
  `title` text COMMENT '描述',
  `number` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '包邮件数',
  `price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '包邮金额',
  `type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '计费方式',
  `uniqid` varchar(32) NOT NULL DEFAULT '' COMMENT '分组唯一值',
  `status` tinyint(1) DEFAULT '0' COMMENT '是否无效',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='运费模板包邮';
```

### 运费模板API接口

1. 获取运费模板列表: `GET /api/admin/express/shipping/templates/list`
2. 获取启用状态的运费模板列表: `GET /api/admin/express/shipping/templates/enabled/list`
3. 新增运费模板: `POST /api/admin/express/shipping/templates/save`
4. 修改运费模板: `POST /api/admin/express/shipping/templates/update`
5. 运费模板详情: `GET /api/admin/express/shipping/templates/info`
6. 删除运费模板: `GET /api/admin/express/shipping/templates/delete`
7. 修改运费模板启用状态: `POST /api/admin/express/shipping/templates/set_status`

### 运费模板启用状态功能

系统支持对运费模板设置启用状态：

1. 启用状态说明：
   - 启用（status=1）：商品可以使用该运费模板
   - 禁用（status=0）：商品不能使用该运费模板

2. 业务应用：
   - 只有启用状态的运费模板才能在商品管理中选择
   - 系统提供单独的接口获取所有启用状态的运费模板列表
   - 可以通过接口快速修改运费模板的启用状态

3. 接口使用示例：
   - 修改运费模板启用状态：
     ```
     POST /api/admin/express/shipping/templates/set_status?id=1&status=0
     ```
   - 获取所有启用状态的运费模板：
     ```
     GET /api/admin/express/shipping/templates/enabled/list
     ```

### 订单管理
- 订单列表查询
- 订单详情
- 订单状态修改
- 订单发货

### 用户管理
- 用户列表查询
- 用户详情
- 用户权限管理

### 会员等级管理
- 会员等级列表查询
- 会员等级详情查看
- 会员等级新增/编辑/删除
- 会员等级启用/禁用

#### 会员等级API接口

系统支持多级会员体系，可为不同会员等级配置特权，包括折扣、优惠券、生日特权等。

#### 1. 获取会员等级详情
```
GET /api/admin/system/user/level/info/{id}
```

参数说明：
- `id`: 会员等级ID

返回数据示例：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "userLevel": {
      "id": 1,
      "name": "普通会员",
      "experience": 0,
      "isShow": true,
      "grade": 1,
      "discount": 100,
      "discountEnabled": true,
      "icon": "http://example.com/icon.png",
      "experienceSource": "自购消费,会员充值",
      "autoUpgrade": true,
      "registrationInfo": false,
      "couponId": 101,
      "couponEnabled": true,
      "birthCouponEnabled": true,
      "birthCouponId": 102,
      "commissionEnabled": false,
      "commissionRate": 0,
      "customerServiceEnabled": false,
      "customerServiceWechat": "",
      "isDel": false,
      "updateTime": "2023-10-01 12:00:00",
      "createTime": "2023-10-01 12:00:00"
    },
    "memberCoupon": {
      "id": 101,
      "name": "会员专享券",
      "type": 3,
      "useMinPrice": 100.00,
      "couponPrice": 20.00,
      "couponType": 1,
      "useTimeType": 1,
      "useStartTime": "2023-10-01 00:00:00",
      "useEndTime": "2023-12-31 23:59:59"
      // 其他优惠券字段...
    },
    "birthCoupon": {
      "id": 102,
      "name": "生日礼遇券",
      "type": 3,
      "useMinPrice": 50.00,
      "couponPrice": 15.00,
      "couponType": 1,
      "useTimeType": 2,
      "day": 30
      // 其他优惠券字段...
    }
  }
}
```

说明：
- `userLevel`: 原始会员等级对象
- `memberCoupon`: 会员券信息，仅当该等级设置了会员券且已启用时返回
- `birthCoupon`: 生日券信息，仅当该等级设置了生日券且已启用时返回

#### 2. 获取会员等级列表
```
GET /api/admin/system/user/level/list
```

#### 3. 新增会员等级
```
POST /api/admin/system/user/level/save
```

#### 4. 删除会员等级
```
POST /api/admin/system/user/level/delete/{id}
```

#### 5. 更新会员等级
```
POST /api/admin/system/user/level/update/{id}
```

#### 6. 启用/禁用会员等级
```
POST /api/admin/system/user/level/use
```

### 会员等级功能说明

系统支持多级会员等级设置，具有以下特性：

1. **会员等级基本信息**
   - 等级名称：会员等级的显示名称
   - 会员等级：数值，决定等级高低
   - 达到经验：升级到该等级所需经验值
   - 会员图标：等级显示图标

2. **会员折扣功能**
   - 享受折扣：会员购物时享受的折扣比例
   - 是否启用会员折扣：控制该等级是否享受折扣

3. **成长值来源设置**
   - 自购消费：购物消费可获得成长值
   - 推广金额：推广他人消费可获得成长值
   - 会员充值：充值可获得成长值
   - 是否达到成长值自动升级：控制是否自动升级

4. **会员专属权益**
   - 是否启用优惠券赠送：控制是否赠送优惠券
   - 是否启用生日券：控制是否赠送生日券
   - 是否启用佣金返现：控制是否开启分销返佣
   - 是否启用专属客服：控制是否提供专属客服服务

该功能主要用于提升用户忠诚度，鼓励用户消费和推广，同时为不同等级会员提供差异化服务。

### 售后管理
- 售后列表查询
- 售后详情查看
- 售后状态修改（同意退款、同意退货、同意换货、同意补寄、拒绝）

### 用户管理

#### 用户列表查询功能升级

系统对用户查询功能进行了升级，新增以下特性：

1. **成为会员时间范围查询**
   - 支持按成为会员时间范围进行筛选
   - 开始时间和结束时间可分开设置
   - 系统使用用户创建时间作为成为会员时间

2. **查询参数说明**
   - `memberTimeStart`: 成为会员开始时间，格式：yyyy-MM-dd HH:mm:ss
   - `memberTimeEnd`: 成为会员结束时间，格式：yyyy-MM-dd HH:mm:ss

该功能主要用于精准筛选特定时间段注册的用户，方便运营人员进行用户分析和营销活动策划。

## 接口说明

### 商品接口
1. 获取商品列表: `GET /api/admin/store/product/list`
2. 新增商品: `POST /api/admin/store/product/save`
3. 修改商品: `POST /api/admin/store/product/update`
4. 商品详情: `GET /api/admin/store/product/info/{id}`
5. 删除商品: `GET /api/admin/store/product/delete/{id}`
6. 上架商品: `GET /api/admin/store/product/putOnShell/{id}`
7. 下架商品: `GET /api/admin/store/product/offShell/{id}`

### 商品接口返回字段说明
商品接口返回的数据中，除了基本信息外，还包含以下特殊字段：

1. `image`: 商品主图原图
2. `thumbnailImage`: 商品缩略图，尺寸为336*336像素，用于列表和缩略图展示
3. `sliderImage`: 商品轮播图
4. `detailImages`: 商品详情图

## 商品图示信息配置
商品在新增和修改时支持以下图示信息配置：

1. 售后服务图示：
   - 支持买家申请换货
   - 商品详情页将展示支持换货的说明
   - 7天无理由退货

2. 限购图示：
   - 限制每人可购买数量
     - 设置每人最大购买数量
   - 只允许特定用户购买
     - 可以指定允许购买的用户ID列表
     - 系统会自动验证购买者是否在允许列表中

## 自动缩略图功能

系统支持在上传图片的同时自动生成缩略图，便于在网站不同场景下加载合适尺寸的图片，提高页面访问性能。

### 功能介绍
- 上传图片时自动生成缩略图
- 缩略图命名规则为"原图片名称 + thumbnailImage"
- 缩略图与原图存储在相同路径下
- 支持本地存储和云存储（七牛云、阿里云OSS、腾讯云COS）

### 使用方法
上传图片时，系统会自动生成缩略图，并在返回的数据中包含缩略图URL：
```json
{
  "fileName": "example.jpg",
  "url": "/image/public/product/2023/12/01/example.jpg",
  "thumbnailUrl": "/image/public/product/2023/12/01/examplethumbnailImage.jpg"
}
```

### 配置说明
- 缩略图尺寸默认为336*336像素
- 可以通过系统配置修改缩略图尺寸
- 配置项键名：`upload_image_thumbnail_size`
- 配置值格式：`宽x高`，如：`336x336`
- 配置路径：后台 > 系统设置 > 缩略图设置

### 技术实现
- 使用Thumbnailator库处理图片生成缩略图
- 缩略图信息会保存到系统附件表中
- 自动处理各种存储方式下的缩略图上传

## 特定用户购买限制功能使用说明

管理员可以限制某些商品只能由特定用户购买。这对于限量版商品、会员特供商品或预售商品特别有用。

### 配置步骤：
1. 在商品编辑页面，勾选"只允许特定用户购买"选项
2. 添加允许购买该商品的用户ID列表
3. 保存商品信息

### 工作原理：
- 系统会在用户下单时自动验证用户ID是否在允许列表中
- 验证会在多个环节进行：加入购物车、预下单和创建订单时
- 对于没有购买权限的用户，系统会显示明确的错误信息
- 管理员可以随时更新允许购买的用户列表

### 数据表：
- `store_product_allowed_user` - 存储商品和允许购买用户的关联关系

## 售后管理模块
系统支持以下售后类型和状态管理：

1. 售后筛选条件：
   - 临期待处理
   - 未发货退款待处理
   - 已发货退款待处理
   - 退货待处理
   - 换货/补寄待处理
   - 退货待商家收货
   - 换货待商家收货
   - 仲裁待处理

2. 售后列表展示信息：
   - 订单编号
   - 售后号
   - 申请下单时间
   - 产品照片
   - 产品名称
   - 净重
   - 收货人
   - 商品ID
   - 商品SKU
   - 售后属性
   - 退款金额
   - 订单金额
   - 件数
   - 售后状态
   - 原因分类
   - 物流状态
   - 查看详情

3. 售后操作：
   - 支持批量或单个操作
   - 同意退款
   - 同意退货
   - 同意换货
   - 同意补寄
   - 拒绝申请

4. 技术实现：
   - 利用现有订单表结构（store_order, store_order_info, store_order_status）
   - 通过订单状态和退款状态组合表示不同售后状态
   - 操作记录保存在订单状态表中

## 商品批量操作功能

除了现有的批量上架、下架、加入回收站功能外，系统还支持以下批量操作：

### 批量设置商品属性

管理员可以批量设置多个商品的属性，包括：

1. 批量设置虚拟销量
   - 可以为多个商品统一设置相同的虚拟销量
   - 虚拟销量将影响商品的展示排名和热门度

2. 批量设置快递方式
   - 可以为多个商品统一设置邮费类型（统一邮费或运费模板）
   - 设置为统一邮费时，需要指定邮费金额
   - 设置为运费模板时，需要指定运费模板ID

### API接口

批量设置商品属性: `POST /api/admin/store/product/batchSetting`

请求参数说明：
```json
{
  "ids": [1, 2, 3],              // 商品ID列表，必填
  "setFicti": true,              // 是否设置虚拟销量，可选
  "ficti": 100,                  // 虚拟销量，当setFicti为true时必填
  "setFreightType": true,        // 是否设置快递方式，可选
  "freightType": 0,              // 运费类型：0-统一邮费，1-运费模板，当setFreightType为true时必填
  "postage": 10.00,              // 邮费，当freightType为0时必填
  "tempId": 1                    // 运费模板ID，当freightType为1时必填
}
```

返回结果：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": null
}
```

## 城市地区分类功能

系统支持按照一二区、三区、港澳台对城市进行分类管理，方便在业务中对不同地区进行差异化处理。

### 区域分类定义

系统将城市划分为三种区域类型：

1. **一二区** - 包括：北京市、天津市、河北省、山西省、内蒙古自治区、辽宁省等大部分东部和中部省份
2. **三区** - 包括：西藏自治区、宁夏回族自治区、新疆维吾尔自治区
3. **港澳台** - 包括：香港特别行政区、澳门特别行政区、台湾省

### 使用方式

1. 在城市管理中可设置和查看城市的区域分类
2. 可以通过API按区域分类获取城市列表：
   ```
   GET /api/admin/system/city/list/region?regionType=1
   ```
3. 可以通过API按区域分组获取城市列表：
   ```
   GET /api/admin/system/city/list/grouped
   ```
   返回格式为：
   ```json
   {
     "oneAndTwoArea": [...一二区城市列表...],
     "threeArea": [...三区城市列表...],
     "hkMacaoTaiwan": [...港澳台地区列表...],
     "all": [...所有城市列表...]
   }
   ```
4. 详细使用方法请参考 [城市地区分类功能说明文档](docs/city_region_type.md)

## 系统功能

### 活动氛围图标功能

系统对活动模块进行了升级，新增了氛围图标功能，主要特性如下：

#### 1. 功能说明
- 支持为每个活动设置专属的氛围图标
- 氛围图标可用于前端页面装饰，提升活动视觉效果和用户体验
- 与主图不同，氛围图标通常更为轻量和精致，可作为活动的视觉点缀元素

#### 2. 数据结构变更
在`store_activity`表中新增了以下字段：
```sql
ALTER TABLE `store_activity` ADD COLUMN `atmosphere_icon` varchar(255) DEFAULT NULL COMMENT '氛围图标' AFTER `image`;
```

#### 3. 使用场景
- 节日主题活动：使用节日元素作为氛围图标
- 促销活动：使用折扣标识、限时标识等作为氛围图标
- 新品首发：使用新品标识作为氛围图标
- 品牌联名：使用品牌logo作为氛围图标

#### 4. 技术实现
- 前端支持氛围图标的上传和预览
- 后端接口支持氛围图标的保存和获取
- 支持在活动列表和详情页面展示氛围图标

### 会员券发券时间功能

会员券（类型为3）增加了发券时间功能，支持以下发券方式：

1. **生日发券**：根据会员的生日信息，自动发放优惠券
   - 生日当天：在会员生日当天发放优惠券
   - 生日当周：在会员生日所在周发放优惠券
   - 生日当月：在会员生日所在月发放优惠券

2. **每月固定日期发券**：在每月指定日期自动向所有会员发放优惠券
   - 可设置每月的1-31日中的任一天

### 使用方法

1. 创建会员券时，可以选择发券时间类型：
   - 无限制：不限制发券时间，手动发放
   - 生日发券：选择生日当天、生日当周或生日当月
   - 每月指定日期：选择每月的某一天（1-31日）

2. 系统会根据设置的发券时间自动执行发券任务：
   - 每天凌晨1点：处理生日当天和每月指定日期的发券
   - 每周一凌晨2点：处理生日当周的发券
   - 每月1号凌晨3点：处理生日当月的发券

3. 会员需要在个人资料中设置生日信息才能收到生日券

### 数据库更新

系统对store_coupon表进行了以下字段扩展：
- send_time_type：发券时间类型（0-无限制 1-生日 2-每月指定日期）
- birth_send_type：生日发券类型（1-生日当天 2-生日当周 3-生日当月）
- month_send_day：每月发券日期

执行以下SQL更新数据库：
```sql
ALTER TABLE `store_coupon` ADD COLUMN `send_time_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '发券时间类型 0-无限制 1-生日 2-每月指定日期' AFTER `after_days`;
ALTER TABLE `store_coupon` ADD COLUMN `birth_send_type` tinyint(1) DEFAULT NULL COMMENT '生日发券类型 1-生日当天 2-生日当周 3-生日当月' AFTER `send_time_type`;
ALTER TABLE `store_coupon` ADD COLUMN `month_send_day` int(11) DEFAULT NULL COMMENT '每月发券日期' AFTER `birth_send_type`;
```

### 长图片自动分割功能

系统提供了长图片自动分割功能，可以将超过设定高度的长图片自动分割为多张图片，便于在移动端展示。

#### 1. 功能特性
- 自动检测长图片：当图片高度超过宽度的2倍时，自动识别为长图片并进行分割
- 智能分割处理：分割高度等于图片自身的宽度，保持良好的展示效果
- 原图和分割后的图片都会保存，可以根据需要选择使用
- 存储优化：分割图片信息存储在原图记录中，避免数据库冗余

#### 2. 数据表结构变更
在`system_attachment`表中新增了以下字段：
```sql
ALTER TABLE system_attachment ADD COLUMN is_long_image tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为长图片 0否 1是';
ALTER TABLE system_attachment ADD COLUMN split_image_urls text COMMENT '分割图片路径列表，以逗号分隔';
```

#### 3. 长图片判断逻辑
- 长图片判断标准：图片高度 > 图片宽度 × 2
- 分割高度计算：每段分割的高度 = 图片宽度
- 例如：宽度为800px的图片，分割高度为800px；宽度为1000px的图片，分割高度为1000px
- 合理处理最后一部分：如果最后一部分高度小于宽度的一半，则合并到前一部分，不单独分割
- 优化文件命名：分割图片使用主文件名称（不是原始上传文件名，而是系统生成的文件名）加上"_part序号"的方式命名

#### 4. 使用方式
长图片上传后，系统会自动检测并处理：
- 对于普通图片：正常上传并生成缩略图
- 对于长图片：自动分割并同时保存原图和分割后的图片
- 前端展示时可以根据`is_long_image`字段判断是否为长图片，选择合适的展示方式
- 删除长图片时会同时删除所有分割的图片文件，避免残留文件占用存储空间

### 优惠券功能升级

系统对优惠券功能进行了升级，新增以下特性：

#### 1. 会员等级限制
- 支持为优惠券设置会员等级限制条件
- 可指定优惠券仅对特定会员等级可用（普通会员、VIP会员、SVIP会员）
- 支持多选，满足任一会员等级条件即可领取

#### 2. 过期提醒功能
- 支持设置优惠券过期前自动提醒
- 可配置提醒时间（比如过期前1-30天）
- 系统自动检测即将过期的优惠券并发送提醒通知

#### 3. 使用说明功能
- 支持为每张优惠券添加详细的使用说明
- 使用说明支持富文本内容，最多2000字符
- 使用说明将在用户查看优惠券详情时展示

#### 4. 会员等级筛选功能
- 支持在优惠券分页查询时按会员等级ID进行筛选
- 可精确查找特定会员等级可用的优惠券
- 筛选逻辑智能匹配会员等级ID是否存在于优惠券的会员等级限制中

#### 5. 优惠券查询API
```
GET /api/admin/marketing/coupon/list
```

参数说明：
- `name`: 优惠券名称，可选，支持模糊查询
- `type`: 优惠券类型，可选，1-满减券，2-新人专享券，3-会员专享券
- `status`: 状态，可选，true-启用，false-禁用
- `memberLevelId`: 会员等级ID，可选，用于筛选指定会员等级可用的优惠券
- `page`: 页码，默认值1
- `limit`: 每页数量，默认值10

#### 6. 数据表结构变更
在`store_coupon`表中进行了以下变更：
```sql
-- 添加会员等级限制、过期提醒、使用说明字段
ALTER TABLE `store_coupon` ADD COLUMN `member_levels` varchar(255) DEFAULT NULL COMMENT '可发放会员等级限制，多个用逗号分隔' AFTER `month_send_day`;
ALTER TABLE `store_coupon` ADD COLUMN `expire_notice` tinyint(1) NOT NULL DEFAULT 0 COMMENT '过期提醒 0-不提醒 1-提醒' AFTER `member_levels`;
ALTER TABLE `store_coupon` ADD COLUMN `expire_notice_days` int(11) DEFAULT NULL COMMENT '过期前提醒天数' AFTER `expire_notice`;
ALTER TABLE `store_coupon` ADD COLUMN `use_description` text DEFAULT NULL COMMENT '券使用说明' AFTER `expire_notice_days`;
```

### 优惠券领取限制功能升级

系统对优惠券领取功能进行了升级，新增以下特性：

#### 1. 领取次数限制
- 支持两种领取次数限制模式：
  - 每人仅限一次：用户只能领取一次该优惠券
  - 每人多次可领：用户可多次领取该优惠券，可设置具体领取次数上限

#### 2. 客户领取限制
- 支持三种客户领取限制模式：
  - 不限制，所有人可领：所有用户都可以领取该优惠券
  - 指定会员等级：只有特定会员等级的用户可以领取该优惠券
  - 指定用户标签：只有拥有特定标签的用户可以领取该优惠券

#### 3. 数据表结构变更
在`store_coupon`表中新增了以下字段：
```sql
ALTER TABLE `store_coupon`
    ADD COLUMN `receive_limit_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '领取次数限制：1-每人仅限一次，2-每人多次可领' AFTER `last_total`,
    ADD COLUMN `receive_limit_count` int(11) DEFAULT 1 COMMENT '每人可领取次数，receive_limit_type=2时生效' AFTER `receive_limit_type`,
    ADD COLUMN `customer_limit_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '领取客户限制：1-不限制，所有人可领，2-指定会员等级，3-指定用户标签' AFTER `receive_limit_count`,
    ADD COLUMN `customer_level_ids` varchar(255) DEFAULT NULL COMMENT '指定可领取的会员等级ID，多个以逗号分隔，customer_limit_type=2时生效' AFTER `customer_limit_type`,
    ADD COLUMN `customer_tag_ids` varchar(255) DEFAULT NULL COMMENT '指定可领取的用户标签ID，多个以逗号分隔，customer_limit_type=3时生效' AFTER `customer_level_ids`;
```

#### 4. 新增API接口
- 获取会员等级列表接口，用于选择指定会员等级
```
GET /api/admin/marketing/coupon/user/level/list
```

- 获取用户标签列表接口，用于选择指定用户标签
```
GET /api/admin/marketing/coupon/user/tag/list
```

#### 5. 字段说明
- `receive_limit_type`: 领取次数限制类型
  - 1: 每人仅限一次
  - 2: 每人多次可领
- `receive_limit_count`: 每人可领取次数，当receive_limit_type=2时生效
- `customer_limit_type`: 领取客户限制类型
  - 1: 不限制，所有人可领
  - 2: 指定会员等级
  - 3: 指定用户标签
- `customer_level_ids`: '指定可领取的会员等级ID，多个以逗号分隔，当customer_limit_type=2时生效
- `customer_tag_ids`: '指定可领取的用户标签ID，多个以逗号分隔，当customer_limit_type=3时生效
```

### 商品分类功能升级

系统对商品分类功能进行了升级，新增以下特性：

#### 1. 分类扩展字段2
- 支持为商品分类添加第二个扩展字段(extra2)
- 可用于存储更多分类相关的扩展信息
- 与原有extra字段配合使用，提供更丰富的分类管理功能

#### 2. 数据表结构变更
在`category`表中新增了extra2字段：
```sql
ALTER TABLE `category` ADD COLUMN `extra2` text COMMENT '扩展字段2' AFTER `extra`;
```

#### 3. 相关API字段说明
分类相关接口新增了以下字段：
- `extra2`: 分类扩展字段2，可存储更多分类相关信息

### 优惠券门槛类型功能

系统对优惠券功能进行了升级，新增以下特性：

#### 1. 优惠券门槛类型设置
- 新增优惠券门槛类型字段，支持两种类型：
  - 有门槛(满减券)：需要满足最低消费金额才能使用的优惠券
  - 无门槛：无需满足最低消费金额即可使用的优惠券

#### 2. 门槛类型与优惠券面值、最低消费的关系
- 有门槛优惠券：必须设置最低消费金额(minPrice > 0)，用户消费满足最低金额后才能使用
- 无门槛优惠券：最低消费金额自动设为0，用户无需满足消费金额即可使用

#### 3. 数据表结构变更
在`store_coupon`和`store_coupon_user`表中新增了coupon_type字段：
```sql
ALTER TABLE `store_coupon`
    ADD COLUMN `coupon_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '优惠券门槛类型 1 有门槛(满减券), 2 无门槛' AFTER `money`;

ALTER TABLE `store_coupon_user`
    ADD COLUMN `coupon_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '优惠券门槛类型 1 有门槛(满减券), 2 无门槛' AFTER `money`;
```

#### 4. 字段说明
- `coupon_type`: 优惠券门槛类型
  - 1: 有门槛(满减券) - 需要满足最低消费金额才能使用的优惠券
  - 2: 无门槛 - 无需满足最低消费金额即可使用的优惠券
- `money`: 优惠券面值
- `min_price`: 最低消费金额，有门槛时必须大于0，无门槛时固定为0

### 会员折扣功能

系统新增会员折扣功能，支持为不同会员等级设置不同的折扣优惠。

#### 1. 会员折扣功能说明
- 支持为不同会员等级设置专属折扣
- 支持全场通用、指定商品可用、指定商品不可用三种模式
- 支持设置折扣值（如8折、9折等）
- 支持批量操作（启用/禁用、复制、删除）
- 支持查看会员折扣的使用统计数据

#### 2. 数据表设计
会员折扣表 `store_member_discount` 结构如下：
```sql
CREATE TABLE `store_member_discount` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '会员折扣ID',
    `name` varchar(64) NOT NULL COMMENT '折扣名称',
    `member_levels` varchar(255) NOT NULL COMMENT '会员等级限制，多个用逗号分隔',
    `discount` decimal(10, 2) NOT NULL COMMENT '折扣值',
    `use_type` tinyint(1) NOT NULL COMMENT '适用范围 1 全场通用, 2 指定商品可用, 3 指定商品不可用',
    `product_ids` varchar(1000) DEFAULT NULL COMMENT '指定商品ID，多个用逗号分隔',
    `description` varchar(255) DEFAULT NULL COMMENT '描述说明',
    `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
    `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态（0：关闭，1：开启）',
    `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 状态（0：否，1：是）',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员折扣表';
```

#### 3. 会员折扣API接口

##### 3.1 获取会员折扣列表
```
GET /api/admin/marketing/member/discount/list
```
参数说明：
- `keywords`: 折扣名称关键词，可选
- `status`: 状态，可选
- `page`: 页码，默认值1
- `limit`: 每页数量，默认值10

##### 3.2 新增会员折扣
```
POST /api/admin/marketing/member/discount/save
```
参数示例：
```json
{
  "name": "VIP会员8折",
  "memberLevels": "2,3",
  "discount": 8.00,
  "useType": 1,
  "productIds": "",
  "description": "VIP会员专享折扣",
  "sort": 0
}
```

##### 3.3 修改会员折扣
```
POST /api/admin/marketing/member/discount/update
```
参数示例（与新增类似，但需要包含id）：
```json
{
  "id": 1,
  "name": "VIP会员8折",
  "memberLevels": "2,3",
  "discount": 8.00,
  "useType": 1,
  "productIds": "",
  "description": "VIP会员专享折扣（已更新）",
  "sort": 0
}
```

##### 3.4 修改会员折扣状态
```
POST /api/admin/marketing/member/discount/update/status
```
参数说明：
- `id`: 折扣ID，必填
- `status`: 状态值，必填，true-启用，false-禁用

##### 3.5 获取会员折扣详情
```
GET /api/admin/marketing/member/discount/info
```
参数说明：
- `id`: 折扣ID，必填

##### 3.6 删除会员折扣
```
POST /api/admin/marketing/member/discount/delete
```
参数说明：
- `id`: 折扣ID，必填

##### 3.7 复制会员折扣
```
POST /api/admin/marketing/member/discount/copy
```
参数说明：
- `id`: 折扣ID，必填

##### 3.8 获取可用会员等级列表
```
GET /api/admin/marketing/member/discount/level/list
```

##### 3.9 获取数据统计
```
GET /api/admin/marketing/member/discount/statistics
```

#### 4. 使用说明
会员折扣功能用于为不同等级的会员提供不同的折扣优惠：
1. 创建会员折扣：设置折扣名称、会员等级、折扣值及适用范围
2. 启用会员折扣：设置会员折扣状态为启用
3. 会员购物时系统自动应用对应等级的折扣
4. 支持查看统计数据，了解各折扣的使用情况

#### 5. 注意事项
- 可为同一会员等级设置多个折扣，系统会自动选择最优惠的折扣使用
- 折扣值以小数表示，如8折输入8.00
- 指定商品可用/不可用时，需填写商品ID，多个用逗号分隔

#### 6. 会员佣金返现
- 新增配置"是否启用佣金返现"选项
- 开启后，用户可享受指定比例的分销提成
- 支持为不同会员等级设置不同的佣金返现比例
- 系统会根据用户等级自动设置相应的分销提成比例

#### 7. 专属客服服务
- 新增配置"是否启用专属客服"选项
- 开启后，用户可查看专属客服微信号进行咨询
- 支持为不同会员等级设置不同的客服微信号
- 客服信息将在会员中心展示

#### 8. 会员折扣启用控制
- 新增配置"是否启用会员折扣"选项
- 开启后，用户可享受会员等级设定的商品折扣优惠
- 关闭后，即使设置了折扣值，也不会实际应用折扣
- 支持为不同会员等级灵活控制折扣功能的启用状态
- 可与其他优惠方式组合使用，实现灵活的营销策略

#### 数据库表结构变更
在`system_user_level`表中新增了以下字段：
```sql
ALTER TABLE `system_user_level`
ADD COLUMN `experience_source` varchar(255) NOT NULL DEFAULT '' COMMENT '成长值来源（多选：自购消费、推广金额、会员充值，用逗号分隔）' AFTER `icon`,
ADD COLUMN `auto_upgrade` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否达到成长值自动升级 1=是,0=否' AFTER `experience_source`,
ADD COLUMN `registration_info` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否需要注册信息(获取手机号) 1=是,0=否' AFTER `auto_upgrade`,
ADD COLUMN `coupon_id` int(11) NULL DEFAULT NULL COMMENT '补充资料赠送的优惠券ID' AFTER `registration_info`,
ADD COLUMN `coupon_enabled` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否开启优惠券赠送 1=是,0=否' AFTER `coupon_id`,
ADD COLUMN `birth_coupon_enabled` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否启用生日券 1=是,0=否' AFTER `coupon_enabled`,
ADD COLUMN `birth_coupon_id` int(11) NULL DEFAULT NULL COMMENT '生日券ID' AFTER `birth_coupon_enabled`,
ADD COLUMN `commission_enabled` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否启用佣金返现 1=是,0=否' AFTER `birth_coupon_id`,
ADD COLUMN `commission_rate` int(11) NULL DEFAULT NULL COMMENT '分销提成比例' AFTER `commission_enabled`,
ADD COLUMN `customer_service_enabled` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否启用专属客服 1=是,0=否' AFTER `commission_rate`,
ADD COLUMN `customer_service_wechat` varchar(50) NULL DEFAULT NULL COMMENT '客服微信号' AFTER `customer_service_enabled`,
ADD COLUMN `discount_enabled` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否启用会员折扣 1=是,0=否' AFTER `discount`;
```

在`user`表中新增了以下字段：
```sql
ALTER TABLE `user`
ADD COLUMN `commission_rate` int(11) NULL DEFAULT 0 COMMENT '分销提成比例' AFTER `brokerage_price`;
```

### 会员成长值配置功能

系统新增了会员成长值配置功能，可以灵活设置不同来源的成长值获取规则：

#### 1. 成长值配置管理
- 支持三种成长值来源类型的配置：自购商品、推广金额、会员充值
- 可为每种来源类型设置不同的获取数量、单位和比例
- 支持启用/禁用特定的成长值来源
- 支持对成长值来源进行排序和备注说明

#### 2. 数据表结构
```sql
CREATE TABLE `system_experience_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配置id',
  `source_type` varchar(50) NOT NULL COMMENT '成长值来源类型：自购商品、推广金额、会员充值',
  `number` int(11) NOT NULL DEFAULT '0' COMMENT '获取数量',
  `unit` varchar(20) NOT NULL COMMENT '单位：元/件',
  `ratio` int(11) NOT NULL DEFAULT '0' COMMENT '获取比例',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用：1-启用，0-禁用',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_source_type` (`source_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='成长值配置表';
```

#### 3. 成长值配置API接口

##### 3.1 分页查询成长值配置
```
GET /api/admin/system/experience/config/list
```
参数说明：
- `page`: 页码，默认值1
- `limit`: 每页数量，默认值10

##### 3.2 获取所有成长值配置列表
```
GET /api/admin/system/experience/config/all
```

##### 3.3 新增成长值配置
```
POST /api/admin/system/experience/config/save
```
参数示例：
```json
{
  "sourceType": "自购商品",
  "number": 1,
  "unit": "件",
  "ratio": 1,
  "status": true,
  "sort": 0,
  "remark": "购买单件商品获得1点成长值"
}
```

##### 3.4 更新成长值配置
```
POST /api/admin/system/experience/config/update
```
参数示例：
```json
{
  "id": 1,
  "sourceType": "自购商品",
  "number": 2,
  "unit": "件",
  "ratio": 1,
  "status": true,
  "sort": 0,
  "remark": "购买单件商品获得2点成长值"
}
```

##### 3.5 删除成长值配置
```
POST /api/admin/system/experience/config/delete/{id}
```

##### 3.6 更新成长值配置状态
```
POST /api/admin/system/experience/config/status?id={id}&status={status}
```
参数说明：
- `id`: 配置ID，必填
- `status`: 状态值，必填，true-启用，false-禁用

#### 数据库表结构变更
在`system_user_level`表中新增了以下字段：
```
```

### 会员参数设置功能

系统对会员参数设置功能进行了全面升级，新增以下特性：

#### 1. 成长值设置
- 支持设置不同来源的成长值获取规则
- 支持自购商品、会员充值、推广金额等多种来源类型
- 可配置每消费数量和获取比例，精确控制成长值获取

#### 2. 提现设置
- 支持设置提现等级限制（可多选SVIP会员、VIP会员等），一条记录控制多种会员类型
- 可配置提现金额限制（单次最小金额、单次最大金额）
- 可设置提现次数限制（每月最大次数）
- 支持设置提现手续费比例

#### 3. 奖励金设置
- 支持设置不同奖励金来源的奖励规则
- 包括推广普通会员升级为VIP、推广会员充值等场景
- 支持设置周排行榜、月排行榜和季排行榜奖励
- 可灵活配置榜单排名显示数量（TOP10、TOP20、TOP30等）
- 可灵活配置奖励金额或比例

#### 4. 佣金返现设置
- 支持为不同会员类型（SVIP会员、VIP会员、普通会员）设置不同的佣金返现比例
- 可设置佣金返现比例（百分比）
- 用户可根据会员等级获得相应的佣金返现
- 支持SVIP会员自购返现功能：开启后，SVIP会员自购买后将获得佣金返现

#### 数据表结构变更
在系统中新增了`system_member_param_config`表：
```sql
CREATE TABLE IF NOT EXISTS `system_member_param_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配置id',
  `config_type` int(11) NOT NULL COMMENT '配置类型：1-成长值设置，2-提现设置，3-佣金返现设置，4-奖励金设置',
  `source_type` varchar(50) DEFAULT NULL COMMENT '来源类型：自购商品、推广金额、会员充值等',
  `number` int(11) DEFAULT '0' COMMENT '每消费数量',
  `unit` varchar(20) DEFAULT NULL COMMENT '单位：元/件',
  `ratio` int(11) DEFAULT '0' COMMENT '获取比例',
  `withdraw_type` varchar(50) DEFAULT NULL COMMENT '提现类型：SVIP会员，VIP会员，普通会员',
  `withdraw_value` decimal(10,2) DEFAULT NULL COMMENT '提现规则',
  `min_amount` decimal(10,2) DEFAULT NULL COMMENT '提现金额限制：单次最小金额',
  `max_amount` decimal(10,2) DEFAULT NULL COMMENT '提现金额限制：单次最大金额',
  `max_times` int(11) DEFAULT NULL COMMENT '提现次数限制：每月最大次数',
  `fee_rate` decimal(10,2) DEFAULT NULL COMMENT '提现手续费：百分比',
  `auto_refund` tinyint(1) DEFAULT '0' COMMENT '是否SVIP自购返现：1-启用，0-禁用',
  `withdraw_levels` varchar(100) DEFAULT NULL COMMENT '提现等级限制：多个用逗号分隔，如"SVIP会员,VIP会员"',
  `rank_display_count` int(11) DEFAULT '20' COMMENT '榜单排名显示数量：如20表示TOP20',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用：1-启用，0-禁用',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员参数设置表';
```

#### 会员参数设置相关API接口

##### 1. 分页查询会员参数设置
```
GET /api/admin/system/member/param/list
```
参数说明：
- `page`: 页码，默认值1
- `limit`: 每页数量，默认值10

##### 2. 根据配置类型获取会员参数设置列表
```
GET /api/admin/system/member/param/list/{configType}
```
参数说明：
- `configType`: 配置类型（1-成长值设置，2-提现设置，3-奖励金设置，4-佣金返现设置），必填

##### 3. 获取所有会员参数设置列表
```
GET /api/admin/system/member/param/all
```

##### 4. 新增会员参数设置
```
POST /api/admin/system/member/param/save
```
请求体示例（成长值设置）：
```json
{
  "configType": 1,
  "sourceType": "自购商品",
  "number": 1,
  "unit": "件",
  "ratio": 1,
  "status": true,
  "sort": 0,
  "remark": "购买单件商品获得1点成长值"
}
```

请求体示例（提现设置）：
```json
{
  "configType": 2,
  "withdrawLevels": "SVIP会员,VIP会员",
  "minAmount": 100.00,
  "maxAmount": 5000.00,
  "maxTimes": 3,
  "feeRate": 0.00,
  "status": true,
  "sort": 1,
  "remark": "SVIP和VIP会员提现设置"
}
```

请求体示例（奖励金设置-排行榜）：
```json
{
  "configType": 3,
  "sourceType": "周排行榜奖励",
  "number": 500,
  "unit": "元",
  "ratio": 0,
  "rankDisplayCount": 20,
  "status": true,
  "sort": 2,
  "remark": "周排行榜第一名奖励500元，显示TOP20"
}
```

请求体示例（佣金返现设置）：
```json
{
  "configType": 4,
  "withdrawType": "SVIP会员",
  "ratio": 10,
  "autoRefund": true,
  "status": true,
  "sort": 0,
  "remark": "SVIP会员佣金返现比例10%，开启SVIP自购返现"
}
```

##### 5. 更新会员参数设置
```
POST /api/admin/system/member/param/update
```
请求体示例（与新增接口类似，但需要包含id字段）

##### 6. 删除会员参数设置
```
POST /api/admin/system/member/param/delete/{id}
```
参数说明：
- `id`: 配置ID，必填

##### 7. 更新会员参数设置状态
```
POST /api/admin/system/member/param/status
```
参数说明：
- `id`: 配置ID，必填
- `status`: 状态值（true-启用，false-禁用），必填

### 用户等级注册信息扩展功能

系统对用户等级的注册信息功能进行了扩展，新增以下特性：

#### 1. 注册信息收集扩展
- 原有功能仅支持设置是否必须收集手机号
- 新增支持设置是否必须收集头像和昵称（作为一个选项）以及生日信息
- 可以根据不同会员等级的需求，灵活配置需要收集的用户信息

#### 2. 数据表结构变更
在`system_user_level`表中进行了以下变更：
```sql
ALTER TABLE `system_user_level`
CHANGE COLUMN `registration_info` `phone_required` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否需要手机号 1=是,0=否',
ADD COLUMN `avatar_nickname_required` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否需要头像和昵称 1=是,0=否' AFTER `phone_required`,
ADD COLUMN `birthday_required` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否需要生日 1=是,0=否' AFTER `avatar_nickname_required`;
```

#### 3. 字段说明
- `phone_required`: 是否需要手机号 1=是,0=否
- `avatar_nickname_required`: 是否需要头像和昵称 1=是,0=否
- `birthday_required`: 是否需要生日 1=是,0=否

### 会员余额管理功能

系统提供了完整的会员余额管理功能，包括余额查询、明细查看、余额操作等。

#### 1. 会员余额概览
- 支持查看会员余额总计
- 支持查看VIP充值金额总计
- 支持查看累计返现和差额总计
- 支持查看累计消费金额总计
- 支持查看累计提现金额总计

#### 2. 会员余额列表
- 支持查看所有会员的余额信息
- 支持按关键词搜索会员
- 支持按时间范围筛选
- 支持查看明细和增减款操作

#### 3. 会员余额明细
- 支持查看指定会员的余额变动明细
- 支持按交易类型筛选（充值、消费、返现等）
- 支持按收支类型筛选（收入、支出）
- 支持按时间范围筛选

#### 4. 余额操作功能
- 支持对会员余额进行增加或减少操作
- 所有操作都会记录详细的操作日志
- 支持输入操作备注

### 会员余额API接口

#### 1. 获取会员余额统计
```
GET /api/admin/user/balance/statistics
```
返回示例：
```json
{
  "code": 200,
  "data": {
    "totalBalance": 6000.00,
    "totalRecharge": 6000.00,
    "totalRefund": 6000.00,
    "totalConsume": 6000.00,
    "totalWithdrawal": 6000.00
  },
  "message": "操作成功"
}
```

#### 2. 获取会员余额列表
```
GET /api/admin/user/balance/list
```
参数说明：
- `page`: 页码，默认值1
- `limit`: 每页数量，默认值10
- `keywords`: 搜索关键词（会员昵称、手机号、真实姓名）
- `dateLimit`: 日期范围，例如"today"、"yesterday"、"last7days"、"last30days"等

#### 3. 获取指定用户余额信息
```
GET /api/admin/user/balance/info/{id}
```
参数说明：
- `id`: 用户ID

返回示例：
```json
{
  "code": 200,
  "data": {
    "nowMoney": 5569.00,
    "recharge": 2000.00,
    "orderStatusSum": 2000.00,
    "refund": 2000.00,
    "withdrawal": 2000.00,
    "totalIncome": 2000.00
  },
  "message": "操作成功"
}
```

#### 4. 会员余额操作
```
POST /api/admin/user/balance/update
```
请求参数：
```json
{
  "uid": 123,
  "moneyValue": 100.00,
  "moneyType": 1,
  "integralValue": 0,
  "integralType": 1
}
```
参数说明：
- `uid`: 用户ID
- `moneyValue`: 操作金额
- `moneyType`: 操作类型，1-增加，2-减少
- `integralValue`: 积分值（可选）
- `integralType`: 积分操作类型（可选），1-增加，2-减少

#### 5. 获取会员余额明细
```
GET /api/admin/user/balance/detail
```
参数说明：
- `uid`: 用户ID（可选）
- `pm`: 收支类型，0-支出，1-收入（可选）
- `category`: 明细类别（可选），例如"now_money"表示资金
- `dateLimit`: 日期范围，例如"today"、"yesterday"、"last7days"、"last30days"等（可选）
- `startTime`: 开始时间，格式为"yyyy-MM-dd HH:mm:ss"（可选，优先级高于dateLimit）
- `endTime`: 结束时间，格式为"yyyy-MM-dd HH:mm:ss"（可选，优先级高于dateLimit）
- `page`: 页码，默认值1
- `limit`: 每页数量，默认值10

### 提现功能接口

#### 1. 提现申请列表
```
GET /api/admin/finance/apply/list
```

参数说明：
- `page`: 页码，默认值1
- `limit`: 每页数量，默认值20
- `keywords`: 搜索关键字（微信号/姓名/支付宝账号/银行卡号/失败原因）
- `extractType`: 提现方式（bank=银行卡，alipay=支付宝，weixin=微信）
- `status`: 审核状态（-1=未通过，0=审核中，1=已提现）
- `dateLimit`: 时间范围（today,yesterday,lately7,lately15,lately30,month,year,/yyyy-MM-dd hh:mm:ss,yyyy-MM-dd hh:mm:ss/）
- `extractNumber`: 提现编号

返回数据字段说明：
- `id`: 提现ID
- `uid`: 用户ID
- `realName`: 姓名
- `extractPrice`: 提现金额
- `fee`: 提现手续费
- `actualAmount`: 实际到账金额
- `extractType`: 提现方式（bank=银行卡，alipay=支付宝，weixin=微信）
- `bankCode`: 银行卡号
- `bankName`: 银行名称
- `alipayCode`: 支付宝账号
- `wechat`: 微信号
- `qrcodeUrl`: 收款码
- `status`: 状态（-1=未通过，0=审核中，1=已提现）
- `paymentNo`: 付款流水号
- `createTime`: 申请时间
- `handleTime`: 处理时间
- `failTime`: 驳回时间
- `failMsg`: 驳回原因

#### 2. 提现申请审核
```
POST /api/admin/finance/apply/apply
```

参数说明：
- `id`: 提现申请ID，必填
- `status`: 审核状态，必填（-1=未通过，0=审核中，1=已提现）
- `backMessage`: 驳回原因，当status=-1时必填

#### 3. 批量审核提现申请
```
POST /api/admin/finance/apply/batch
```

参数说明：
- `ids`: 提现申请ID列表，逗号分隔，必填
- `status`: 审核状态，必填（-1=未通过，0=审核中，1=已提现）
- `backMessage`: 驳回原因，当status=-1时必填

#### 4. 提现申请修改
```
POST /api/admin/finance/apply/update
```

参数说明：
- `id`: 提现申请ID，必填
- `realName`: 姓名，必填
- `extractType`: 提现方式，必填（bank=银行卡，alipay=支付宝，weixin=微信）
- `bankCode`: 银行卡号，当extractType=bank时必填
- `bankName`: 银行名称，当extractType=bank时必填
- `alipayCode`: 支付宝账号，当extractType=alipay时必填
- `wechat`: 微信号，当extractType=weixin时必填
- `extractPrice`: 提现金额，必填
- `mark`: 备注，可选

#### 5. 更新付款流水号
```
POST /api/admin/finance/apply/payment
```

参数说明：
- `id`: 提现申请ID，必填
- `paymentNo`: 付款流水号，必填

#### 6. 提现统计
```
POST /api/admin/finance/apply/balance
```

参数说明：
- `dateLimit`: 时间范围，可选（today,yesterday,lately7,lately15,lately30,month,year,/yyyy-MM-dd hh:mm:ss,yyyy-MM-dd hh:mm:ss/）

返回数据示例：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "withdrawn": 1000.00,      // 已提现金额
    "unDrawn": 500.00,         // 未提现金额
    "commissionTotal": 1500.00, // 佣金总金额
    "toBeWithdrawn": 200.00     // 待提现金额（审核中）
  }
}
```

#### 7. 提现记录详情
```
GET /api/admin/finance/apply/info
```

参数说明：
- `id`: 提现记录ID，必填

### 提现功能说明

系统支持多种提现方式，包括银行卡、支付宝和微信，具有以下特性：

1. **提现申请流程**
   - 用户发起提现申请，填写提现信息
   - 系统冻结用户对应金额
   - 管理员审核提现申请
   - 审核通过后，系统记录提现成功，计算手续费，确定实际到账金额
   - 审核不通过，系统返还冻结金额给用户

2. **提现方式**
   - 银行卡：需填写姓名、银行卡号、银行名称
   - 支付宝：需填写姓名、支付宝账号、收款码（可选）
   - 微信：需填写姓名、微信号、收款码（可选）

3. **提现限制与费用**
   - 最低提现金额可在系统配置中设置
   - 提现手续费率可在系统配置中设置
   - 实际到账金额 = 提现金额 - 手续费
   - 用户提现金额不能超过可提现余额

4. **提现状态**
   - 审核中（status=0）：提现申请已提交，等待管理员审核
   - 已提现（status=1）：管理员审核通过，提现成功
   - 未通过（status=-1）：管理员审核不通过，提现失败

5. **批量审核功能**
   - 支持批量审核提现申请
   - 可同时审核多个提现申请
   - 批量驳回时需填写统一的驳回原因

6. **付款流水号管理**
   - 支持为已审核通过的提现记录添加付款流水号
   - 付款流水号可用于对账和跟踪支付流程
   - 通过API接口可更新付款流水号

该功能主要用于管理用户的提现申请，提高财务管理效率，保障用户资金安全。

## 提现功能

提现功能允许用户将账户中的余额或佣金提取到指定的银行账户、支付宝账户或微信账户。

### 提现流程

1. 用户提交提现申请，选择提现方式并输入提现金额和账户信息
2. 管理员审核提现申请
3. 审核通过后，系统自动计算手续费并更新状态
4. 管理员可以填写付款流水号用于记录实际打款信息
5. 用户收到提现金额

### 提现设置

提现功能支持以下设置：

1. **提现金额限制**：设置每笔提现的最小金额和最大金额限制，超出限制范围的提现申请将被拒绝
2. **会员等级限制**：可以限制指定会员等级才能进行提现操作，如SVIP会员、VIP会员等
3. **提现手续费**：可以设置提现的手续费率，例如10%表示提现金额的10%将被收取为手续费
4. **每月提现次数限制**：可以限制用户每月最多可以提现的次数

### API接口

#### 1. 提现列表查询
- 接口地址：`/api/admin/finance/apply/list`
- 请求方式：`GET`
- 参数：
  - `keywords`: 搜索关键词，可搜索微信号/姓名/支付宝账号/银行卡号/失败原因
  - `status`: 提现状态，-1=已拒绝，0=审核中，1=已提现
  - `extractType`: 提现方式，bank=银行卡，alipay=支付宝，weixin=微信
  - `dateLimit`: 时间范围
  - `page`: 页码
  - `limit`: 每页条数

#### 2. 提现审核
- 接口地址：`/api/admin/finance/apply/apply`
- 请求方式：`POST`
- 参数：
  - `id`: 提现申请ID
  - `status`: 审核状态，-1=未通过，1=通过
  - `backMessage`: 驳回原因（当status=-1时必填）

#### 3. 批量审核提现申请
- 接口地址：`/api/admin/finance/apply/batch`
- 请求方式：`POST`
- 参数：
  - `ids`: 提现申请ID列表，逗号分隔
  - `status`: 审核状态，-1=未通过，1=通过
  - `backMessage`: 驳回原因（当status=-1时必填）

#### 4. 更新付款流水号
- 接口地址：`/api/admin/finance/apply/payment`
- 请求方式：`POST`
- 参数：
  - `id`: 提现申请ID
  - `paymentNo`: 付款流水号

#### 5. 提现统计
- 接口地址：`/api/admin/finance/apply/balance`
- 请求方式：`POST`
- 参数：
  - `dateLimit`: 时间限制，格式可以是today,yesterday,lately7,lately15,lately30,month,year或具体日期范围

### 提现记录字段说明

| 字段名 | 说明 | 类型 |
|-------|-----|------|
| id | 提现记录ID | int |
| uid | 用户ID | int |
| realName | 用户真实姓名 | string |
| extractType | 提现方式(bank=银行卡,alipay=支付宝,weixin=微信) | string |
| bankCode | 银行卡号 | string |
| bankName | 银行名称 | string |
| alipayCode | 支付宝账号 | string |
| wechat | 微信号 | string |
| extractPrice | 提现金额 | decimal |
| fee | 提现手续费 | decimal |
| mark | 备注 | string |
| balance | 账户余额 | decimal |
| failMsg | 无效原因 | string |
| status | 状态(-1=未通过,0=审核中,1=已提现) | int |
| createTime | 申请时间 | datetime |
| updateTime | 更新时间 | datetime |
| failTime | 拒绝时间 | datetime |
| paymentNo | 付款流水号 | string |

### 提现限制配置

在会员参数设置中的提现设置可以配置以下内容：

1. **提现等级限制**：选择允许提现的会员等级，可多选
2. **提现金额限制**：
   - 最低提现金额：单笔提现的最小金额
   - 最高提现金额：单笔提现的最大金额
3. **提现手续费**：按提现金额的百分比收取手续费
4. **每月提现次数限制**：
   - 不限制次数：不限制用户每月提现次数
   - 限制次数：设置用户每月最大提现次数

## 销售数据功能

### 功能概述

销售数据功能提供了完整的销售数据统计和分析能力，支持按会员等级、时间范围、手机号等多维度查询销售数据，帮助管理员了解各会员的销售表现。

#### 1. 功能特性

- **多维度筛选**：支持按手机号、时间范围、会员等级进行筛选
- **销售统计**：统计销售金额、订单数量、待售数量、已售数量、自购金额等关键指标
- **明细查看**：支持查看每个会员的详细销售明细
- **数据导出**：支持将销售数据导出为Excel文件
- **实时更新**：数据实时更新，确保统计准确性

#### 2. 数据字段说明

| 字段名 | 说明 | 计算方式 |
| ------ | ---- | -------- |
| SVIP会员 | 会员昵称和手机号 | 从用户表获取 |
| 销售金额 | 该会员的总销售金额 | 统计所有已支付订单的实付金额 |
| 订单数量 | 该会员的总订单数量 | 统计所有订单数量 |
| 待结算返现 | 待结算的佣金返现金额 | 统计创建状态和冻结期状态的佣金记录 |
| 已结算返现 | 已结算的佣金返现金额 | 统计完成状态的佣金记录 |
| 自购金额 | 该会员自己购买的金额 | 统计会员自己下单的金额 |

#### 3. 销售数据API接口

##### 3.1 获取销售数据列表
```
GET /api/admin/sales/data/list
```
参数说明：
- `mobile`: 手机号，可选，支持模糊查询
- `startTime`: 开始时间，格式：yyyy-MM-dd HH:mm:ss
- `endTime`: 结束时间，格式：yyyy-MM-dd HH:mm:ss
- `memberLevel`: 会员等级，可选（1-VIP会员，2-SVIP会员）
- `page`: 页码，默认值1
- `limit`: 每页数量，默认值10

返回数据示例：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "list": [
      {
        "uid": 123,
        "nickname": "张三",
        "phone": "13800138000",
        "memberLevel": "SVIP会员",
        "salesAmount": 14829.34,
        "orderCount": 43,
        "pendingBrokerageAmount": 150.00,
        "settledBrokerageAmount": 1852.50,
        "selfPurchaseAmount": 4523.00
      }
    ],
    "total": 100,
    "pageNum": 1,
    "pageSize": 10
  }
}
```

##### 3.2 获取销售数据统计
```
GET /api/admin/sales/data/statistics
```
参数说明：
- `mobile`: 手机号，可选
- `startTime`: 开始时间，可选
- `endTime`: 结束时间，可选
- `memberLevel`: 会员等级，可选

返回数据示例：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "totalSalesAmount": 1482934.00,
    "totalOrderCount": 4300,
    "totalPendingBrokerageAmount": 15000.00,
    "totalSettledBrokerageAmount": 185200.50,
    "totalSelfPurchaseAmount": 452300.00,
    "totalMemberCount": 150
  }
}
```

##### 3.3 获取会员销售明细
```
GET /api/admin/sales/data/detail/{uid}
```
参数说明：
- `uid`: 用户ID，必填
- `startTime`: 开始时间，可选
- `endTime`: 结束时间，可选
- `page`: 页码，默认值1
- `limit`: 每页数量，默认值10

返回数据示例：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "list": [
      {
        "orderId": "************",
        "orderTime": "2023-12-01 10:30:00",
        "orderAmount": 299.00,
        "payAmount": 279.00,
        "orderStatus": "已完成",
        "productName": "商品名称",
        "quantity": 2
      }
    ],
    "total": 50,
    "pageNum": 1,
    "pageSize": 10
  }
}
```

#### 4. 使用说明

1. **访问销售数据管理**
   - 登录管理后台
   - 进入"会员管理" -> "销售数据"

2. **筛选销售数据**
   - 输入手机号进行精确或模糊查询
   - 选择时间范围查看特定时期的销售数据
   - 选择会员等级筛选特定等级的会员数据

3. **查看销售明细**
   - 点击"查看明细"按钮查看该会员的详细订单信息
   - 支持按时间范围筛选明细数据

4. **数据导出**
   - 点击"导出"按钮将当前筛选条件下的数据导出为Excel文件
   - 导出文件包含所有显示的字段信息

#### 5. 技术实现

- **后端技术**：Spring Boot + MyBatis-Plus
- **数据库**：基于现有的订单表和用户表进行统计查询
- **缓存优化**：对频繁查询的统计数据进行Redis缓存
- **性能优化**：使用索引优化查询性能，支持分页查询
- **导出功能**：使用Apache POI实现Excel导出
- **权限控制**：基于Spring Security实现细粒度权限控制

#### 6. 部署说明

##### 6.1 数据库初始化
```bash
# 1. 创建销售数据视图和索引
mysql -u root -p your_database < sql/create_sales_data_view.sql

# 2. 插入权限配置
mysql -u root -p your_database < sql/insert_sales_data_permissions.sql

# 3. 插入测试数据（可选）
mysql -u root -p your_database < sql/insert_test_sales_data.sql
```

##### 6.2 权限配置
1. 登录管理后台
2. 进入"系统管理" -> "角色管理"
3. 为相应角色分配销售数据相关权限：
   - `admin:sales:data:list` - 查看销售数据列表
   - `admin:sales:data:statistics` - 查看销售数据统计
   - `admin:sales:data:detail` - 查看销售明细
   - `admin:sales:data:export` - 导出销售数据

##### 6.3 系统配置
在系统配置中设置以下参数：
- `sales_data_enabled`: 是否启用销售数据功能（默认：1）
- `sales_data_cache_time`: 缓存时间，单位秒（默认：300）
- `sales_data_export_limit`: 导出限制条数（默认：10000）

##### 6.4 功能测试
```bash
# 执行API测试脚本
chmod +x scripts/test_sales_data_api.sh
./scripts/test_sales_data_api.sh http://localhost:8080 your_admin_token
```

#### 7. 文件结构

```
销售数据功能相关文件：
├── core/src/main/java/com/ylpz/core/
│   ├── common/request/SalesDataRequest.java          # 销售数据查询请求对象
│   ├── common/response/SalesDataResponse.java        # 销售数据响应对象
│   ├── common/response/SalesDataStatisticsResponse.java # 销售数据统计响应对象
│   ├── common/response/SalesDataDetailResponse.java  # 销售数据明细响应对象
│   ├── service/SalesDataService.java                 # 销售数据服务接口
│   └── service/impl/SalesDataServiceImpl.java        # 销售数据服务实现类
├── admin/src/main/java/com/ylpz/admin/
│   └── controller/SalesDataController.java           # 销售数据控制器
├── admin/src/test/java/com/ylpz/admin/
│   └── controller/SalesDataControllerTest.java       # 销售数据控制器测试类
├── sql/
│   ├── create_sales_data_view.sql                    # 创建销售数据视图和索引
│   ├── insert_sales_data_permissions.sql             # 插入权限配置
│   └── insert_test_sales_data.sql                    # 插入测试数据
├── docs/
│   └── 销售数据功能使用说明.md                        # 功能使用说明文档
└── scripts/
    └── test_sales_data_api.sh                        # API测试脚本
```

## 佣金返现和奖励金功能完善总结

### 功能完善情况

经过本次完善，佣金返现和奖励金功能已经具备了完整的实现：

#### ✅ 已完成的功能

1. **数据库表结构完整**：
   - `user_brokerage_record` 表：用于记录所有佣金和奖励金变动
   - `system_member_param_config` 表：用于配置佣金返现和奖励金规则
   - 添加了用户手机号和等级字段到记录实体类

2. **佣金返现功能**：
   - 支持按会员等级设置不同返现比例（SVIP 10%、VIP 6%、普通会员 3%）
   - 支持SVIP会员自动返现功能
   - 完整的佣金状态流转（创建→冻结→完成/失效/提现）
   - 支持按会员类型筛选查询
   - 完善的用户信息填充（昵称、手机号、等级）

3. **奖励金功能**：
   - 支持推广升级奖励（200元）
   - 支持推广充值奖励（10%）
   - 支持首单购买奖励（10%）
   - 支持排行榜奖励（周/月/季度）
   - 支持按奖励类型精确筛选
   - 完善的用户信息关联查询

4. **管理接口完善**：
   - 佣金返现记录查询接口（支持会员类型筛选）
   - 奖励金记录查询接口（支持奖励类型筛选）
   - 会员参数配置接口
   - 合计金额计算接口

#### 🔧 本次完善的内容

1. **实体类完善**：
   - 在`UserBrokerageRecord`中添加了`userPhone`和`userLevel`字段
   - 完善了用户信息的展示

2. **服务层完善**：
   - 在`UserService`中添加了`getUsersByPhone`方法支持模糊查询
   - 完善了`MemberBonusServiceImpl`中的用户信息填充逻辑
   - 完善了`UserCommissionRecordServiceImpl`中的用户信息填充逻辑
   - 添加了用户等级名称转换方法

3. **查询功能优化**：
   - 优化了奖励金查询的link_type筛选逻辑
   - 优化了佣金返现查询的会员类型筛选
   - 完善了手机号模糊查询功能
   - 添加了用户等级信息的显示

4. **数据库脚本**：
   - 创建了`check_and_create_member_param_config.sql`脚本
   - 包含表结构创建和默认数据初始化

### 使用说明

#### 1. 数据库初始化
```sql
-- 执行会员参数配置表创建脚本
SOURCE sql/check_and_create_member_param_config.sql;
```

#### 2. 佣金返现管理
- 访问佣金返现管理界面
- 可按会员类型（SVIP、VIP、普通会员）筛选
- 可按时间范围筛选
- 可按用户手机号搜索
- 显示用户昵称、手机号、等级等完整信息

#### 3. 奖励金管理
- 访问奖励金管理界面
- 可按奖励类型（升级、充值、首单、排行榜）筛选
- 可按时间范围筛选
- 可按用户信息搜索
- 显示详细的奖励信息和用户信息

#### 4. 会员参数配置
- 可配置不同会员等级的佣金返现比例
- 可配置各种奖励金规则
- 可设置SVIP自购返现功能
- 可配置提现规则和限制

### 接口列表

| 接口路径 | 方法 | 说明 |
| -------- | ---- | ---- |
| /api/admin/member/commission/list | GET | 获取佣金返现记录列表 |
| /api/admin/member/commission/total | GET | 获取佣金返现合计金额 |
| /api/admin/member/bonus/list | GET | 获取奖励金记录列表 |
| /api/admin/member/bonus/total | GET | 获取奖励金合计金额 |
| /api/admin/member/bonus/distribute/rank | POST | 手动发放排行榜奖励金 |
| /api/admin/member/param/list | GET | 获取会员参数配置列表 |
| /api/admin/member/param/save | POST | 保存会员参数配置 |

### 技术特点

1. **数据一致性**：所有佣金和奖励金记录统一存储在`user_brokerage_record`表中，通过`link_type`字段区分类型
2. **灵活配置**：通过`system_member_param_config`表实现灵活的规则配置
3. **完整信息**：查询结果包含用户的完整信息（昵称、手机号、等级）
4. **精确筛选**：支持按多种条件进行精确筛选和查询
5. **扩展性强**：架构设计支持后续功能扩展

该功能现在已经完全满足管理后台对佣金返现和奖励金管理的需求，可以投入正式使用。

## 开发指南

### 代码结构说明

#### 1. 控制器层 (Controller)
- **位置**: `admin/src/main/java/com/ylpz/admin/controller/`
- **职责**: 处理HTTP请求，参数验证，调用服务层
- **命名规范**: `XxxController.java`

#### 2. 服务层 (Service)
- **位置**: `core/src/main/java/com/ylpz/core/service/`
- **职责**: 业务逻辑处理，事务管理
- **命名规范**: `XxxService.java` (接口), `XxxServiceImpl.java` (实现)

#### 3. 数据访问层 (DAO)
- **位置**: `core/src/main/java/com/ylpz/core/dao/`
- **职责**: 数据库操作，SQL映射
- **命名规范**: `XxxDao.java`

#### 4. 实体类 (Entity)
- **位置**: `entity/src/main/java/com/ylpz/entity/`
- **职责**: 数据库表映射
- **命名规范**: 与数据库表名对应

#### 5. 模型类 (Model)
- **位置**: `model/src/main/java/com/ylpz/model/`
- **职责**: 请求响应对象，VO对象
- **命名规范**: `XxxRequest.java`, `XxxResponse.java`

### 开发规范

#### 1. 接口设计规范
```java
@RestController
@RequestMapping("/api/admin/xxx")
@Api(tags = "XXX管理")
public class XxxController {

    @GetMapping("/list")
    @ApiOperation("获取XXX列表")
    public CommonResult<PageInfo<XxxResponse>> list(
        @Valid XxxSearchRequest request,
        @Valid PageParamRequest pageRequest) {
        // 实现逻辑
    }
}
```

#### 2. 服务层规范
```java
@Service
@Transactional(rollbackFor = Exception.class)
public class XxxServiceImpl implements XxxService {

    @Override
    public PageInfo<XxxResponse> getList(XxxSearchRequest request, PageParamRequest pageRequest) {
        // 业务逻辑实现
    }
}
```

#### 3. 异常处理规范
- 使用统一异常处理：`CrmebException`
- 业务异常使用明确的错误码和错误信息
- 系统异常记录详细日志

#### 4. 日志规范
```java
@Slf4j
public class XxxServiceImpl {

    public void someMethod() {
        log.info("开始执行XXX操作，参数：{}", param);
        try {
            // 业务逻辑
            log.info("XXX操作执行成功");
        } catch (Exception e) {
            log.error("XXX操作执行失败", e);
            throw new CrmebException("操作失败");
        }
    }
}
```

### 数据库设计规范

#### 1. 表命名规范
- 使用小写字母和下划线
- 表名要有明确的业务含义
- 例如：`user`, `store_product`, `user_brokerage_record`

#### 2. 字段命名规范
- 使用小写字母和下划线
- 主键统一使用 `id`
- 创建时间：`create_time`
- 更新时间：`update_time`
- 逻辑删除：`is_del`

#### 3. 索引设计
- 主键自动创建聚簇索引
- 外键字段创建普通索引
- 查询频繁的字段创建索引
- 组合查询创建联合索引

### 缓存使用规范

#### 1. Redis Key命名规范
```java
// 用户信息缓存
public static final String USER_INFO_KEY = "user:info:";

// 商品信息缓存
public static final String PRODUCT_INFO_KEY = "product:info:";

// 配置信息缓存
public static final String CONFIG_KEY = "config:";
```

#### 2. 缓存更新策略
- 查询时先查缓存，缓存不存在再查数据库
- 更新数据时同步更新缓存
- 删除数据时同步删除缓存
- 设置合理的过期时间

### 安全规范

#### 1. 权限控制
- 所有管理接口需要登录验证
- 敏感操作需要权限验证
- 使用JWT Token进行身份认证

#### 2. 数据验证
- 前端传参必须进行验证
- 使用 `@Valid` 注解进行参数校验
- 敏感数据进行加密存储

#### 3. SQL注入防护
- 使用MyBatis-Plus的条件构造器
- 禁止直接拼接SQL语句
- 对用户输入进行转义处理

**注意**: 本文档会随着项目的更新而持续完善，请关注最新版本。