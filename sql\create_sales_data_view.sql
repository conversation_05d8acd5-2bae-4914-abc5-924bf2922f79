-- 销售数据功能相关SQL脚本

-- 创建销售数据统计视图
CREATE OR REPLACE VIEW v_sales_data_statistics AS
SELECT 
    u.id AS uid,
    u.nickname,
    u.phone,
    u.avatar,
    u.level AS member_level_id,
    CASE 
        WHEN u.level = 0 OR u.level IS NULL THEN '普通会员'
        WHEN u.level = 1 THEN 'VIP会员'
        WHEN u.level = 2 THEN 'SVIP会员'
        ELSE '普通会员'
    END AS member_level,
    u.create_time,
    COALESCE(sales_stats.sales_amount, 0) AS sales_amount,
    COALESCE(sales_stats.order_count, 0) AS order_count,
    COALESCE(sales_stats.pending_count, 0) AS pending_count,
    COALESCE(sales_stats.completed_count, 0) AS completed_count,
    COALESCE(sales_stats.self_purchase_amount, 0) AS self_purchase_amount
FROM user u
LEFT JOIN (
    SELECT 
        o.uid,
        SUM(CASE WHEN o.paid = 1 AND o.pay_price IS NOT NULL THEN o.pay_price ELSE 0 END) AS sales_amount,
        COUNT(*) AS order_count,
        SUM(CASE WHEN o.status IN (0, 1) THEN 1 ELSE 0 END) AS pending_count,
        SUM(CASE WHEN o.status IN (2, 3) THEN 1 ELSE 0 END) AS completed_count,
        SUM(CASE WHEN o.paid = 1 AND o.pay_price IS NOT NULL THEN o.pay_price ELSE 0 END) AS self_purchase_amount
    FROM store_order o
    WHERE o.is_del = 0
    GROUP BY o.uid
) sales_stats ON u.id = sales_stats.uid
WHERE u.status = 1;

-- 为销售数据查询创建索引
CREATE INDEX IF NOT EXISTS idx_user_phone ON user(phone);
CREATE INDEX IF NOT EXISTS idx_user_level ON user(level);
CREATE INDEX IF NOT EXISTS idx_user_status ON user(status);
CREATE INDEX IF NOT EXISTS idx_store_order_uid_time ON store_order(uid, create_time);
CREATE INDEX IF NOT EXISTS idx_store_order_status ON store_order(status);
CREATE INDEX IF NOT EXISTS idx_store_order_paid ON store_order(paid);

-- 创建销售数据明细查询的存储过程
DELIMITER //

CREATE PROCEDURE GetSalesDataDetail(
    IN p_uid INT,
    IN p_start_time DATETIME,
    IN p_end_time DATETIME,
    IN p_page INT,
    IN p_limit INT
)
BEGIN
    DECLARE v_offset INT DEFAULT 0;
    
    SET v_offset = (p_page - 1) * p_limit;
    
    SELECT 
        o.id AS order_id,
        o.order_id AS order_no,
        o.create_time AS order_time,
        o.total_price AS order_amount,
        o.pay_price AS pay_amount,
        CASE 
            WHEN o.status = 0 THEN '待支付'
            WHEN o.status = 1 THEN '待发货'
            WHEN o.status = 2 THEN '待收货'
            WHEN o.status = 3 THEN '已完成'
            WHEN o.status = 9 THEN '已取消'
            ELSE '未知'
        END AS order_status,
        o.pay_type,
        1 AS is_self_purchase,
        oi.product_name,
        oi.pay_num AS quantity,
        oi.image AS product_image
    FROM store_order o
    LEFT JOIN store_order_info oi ON o.id = oi.order_id
    WHERE o.uid = p_uid
        AND o.is_del = 0
        AND (p_start_time IS NULL OR o.create_time >= p_start_time)
        AND (p_end_time IS NULL OR o.create_time <= p_end_time)
    ORDER BY o.create_time DESC
    LIMIT v_offset, p_limit;
END //

DELIMITER ;

-- 创建销售数据统计查询的存储过程
DELIMITER //

CREATE PROCEDURE GetSalesDataStatistics(
    IN p_mobile VARCHAR(20),
    IN p_nickname VARCHAR(50),
    IN p_member_level INT,
    IN p_start_time DATETIME,
    IN p_end_time DATETIME
)
BEGIN
    SELECT 
        COUNT(DISTINCT u.id) AS total_member_count,
        COALESCE(SUM(sales_stats.sales_amount), 0) AS total_sales_amount,
        COALESCE(SUM(sales_stats.order_count), 0) AS total_order_count,
        COALESCE(SUM(sales_stats.pending_count), 0) AS total_pending_count,
        COALESCE(SUM(sales_stats.completed_count), 0) AS total_completed_count,
        COALESCE(SUM(sales_stats.self_purchase_amount), 0) AS total_self_purchase_amount
    FROM user u
    LEFT JOIN (
        SELECT 
            o.uid,
            SUM(CASE WHEN o.paid = 1 AND o.pay_price IS NOT NULL THEN o.pay_price ELSE 0 END) AS sales_amount,
            COUNT(*) AS order_count,
            SUM(CASE WHEN o.status IN (0, 1) THEN 1 ELSE 0 END) AS pending_count,
            SUM(CASE WHEN o.status IN (2, 3) THEN 1 ELSE 0 END) AS completed_count,
            SUM(CASE WHEN o.paid = 1 AND o.pay_price IS NOT NULL THEN o.pay_price ELSE 0 END) AS self_purchase_amount
        FROM store_order o
        WHERE o.is_del = 0
            AND (p_start_time IS NULL OR o.create_time >= p_start_time)
            AND (p_end_time IS NULL OR o.create_time <= p_end_time)
        GROUP BY o.uid
    ) sales_stats ON u.id = sales_stats.uid
    WHERE u.status = 1
        AND (p_mobile IS NULL OR u.phone LIKE CONCAT('%', p_mobile, '%'))
        AND (p_nickname IS NULL OR u.nickname LIKE CONCAT('%', p_nickname, '%'))
        AND (p_member_level IS NULL OR u.level = p_member_level);
END //

DELIMITER ;

-- 创建销售数据列表查询的存储过程
DELIMITER //

CREATE PROCEDURE GetSalesDataList(
    IN p_mobile VARCHAR(20),
    IN p_nickname VARCHAR(50),
    IN p_member_level INT,
    IN p_start_time DATETIME,
    IN p_end_time DATETIME,
    IN p_page INT,
    IN p_limit INT
)
BEGIN
    DECLARE v_offset INT DEFAULT 0;
    
    SET v_offset = (p_page - 1) * p_limit;
    
    SELECT 
        u.id AS uid,
        u.nickname,
        u.phone,
        u.avatar,
        u.level AS member_level_id,
        CASE 
            WHEN u.level = 0 OR u.level IS NULL THEN '普通会员'
            WHEN u.level = 1 THEN 'VIP会员'
            WHEN u.level = 2 THEN 'SVIP会员'
            ELSE '普通会员'
        END AS member_level,
        u.create_time,
        COALESCE(sales_stats.sales_amount, 0) AS sales_amount,
        COALESCE(sales_stats.order_count, 0) AS order_count,
        COALESCE(sales_stats.pending_count, 0) AS pending_count,
        COALESCE(sales_stats.completed_count, 0) AS completed_count,
        COALESCE(sales_stats.self_purchase_amount, 0) AS self_purchase_amount
    FROM user u
    LEFT JOIN (
        SELECT 
            o.uid,
            SUM(CASE WHEN o.paid = 1 AND o.pay_price IS NOT NULL THEN o.pay_price ELSE 0 END) AS sales_amount,
            COUNT(*) AS order_count,
            SUM(CASE WHEN o.status IN (0, 1) THEN 1 ELSE 0 END) AS pending_count,
            SUM(CASE WHEN o.status IN (2, 3) THEN 1 ELSE 0 END) AS completed_count,
            SUM(CASE WHEN o.paid = 1 AND o.pay_price IS NOT NULL THEN o.pay_price ELSE 0 END) AS self_purchase_amount
        FROM store_order o
        WHERE o.is_del = 0
            AND (p_start_time IS NULL OR o.create_time >= p_start_time)
            AND (p_end_time IS NULL OR o.create_time <= p_end_time)
        GROUP BY o.uid
    ) sales_stats ON u.id = sales_stats.uid
    WHERE u.status = 1
        AND (p_mobile IS NULL OR u.phone LIKE CONCAT('%', p_mobile, '%'))
        AND (p_nickname IS NULL OR u.nickname LIKE CONCAT('%', p_nickname, '%'))
        AND (p_member_level IS NULL OR u.level = p_member_level)
    ORDER BY sales_stats.sales_amount DESC, u.create_time DESC
    LIMIT v_offset, p_limit;
END //

DELIMITER ;

-- 添加注释说明
ALTER TABLE user COMMENT = '用户表，包含会员等级信息';
ALTER TABLE store_order COMMENT = '订单表，用于销售数据统计';
ALTER TABLE store_order_info COMMENT = '订单详情表，包含商品信息';

-- 创建销售数据功能说明
INSERT INTO system_config (menu_name, menu_key, value, info, sort, status) 
VALUES ('销售数据功能', 'sales_data_enabled', '1', '是否启用销售数据功能：1-启用，0-禁用', 100, 1)
ON DUPLICATE KEY UPDATE value = '1', info = '是否启用销售数据功能：1-启用，0-禁用';
