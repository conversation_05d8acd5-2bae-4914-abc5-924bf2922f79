package com.ylpz.entity;

public interface StoreProductCombinationDefine {
    String combinationPrice = "combinationPrice";
    String description = "description";
    String updateTime = "updateTime";
    String initialSales = "initialSales";
    String storeStatus = "storeStatus";
    String saleTime = "saleTime";
    String createTime = "createTime";
    String allowDiscount = "allowDiscount";
    String comboTags = "comboTags";
    String name = "name";
    String details = "details";
    String startTime = "startTime";
    String endTime = "endTime";
    String id = "id";
    String stock = "stock";
    String actualSales = "actualSales";
    String status = "status";
}
