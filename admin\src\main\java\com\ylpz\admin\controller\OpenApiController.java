package com.ylpz.admin.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ylpz.core.service.OrderService;
import com.ylpz.model.order.StoreOrderPostageRequest;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("api/admin/openapi")
@Api(tags = "公开api")
public class OpenApiController {
    @Autowired
    private OrderService oddService;

    @PostMapping(value = "/express/fee")
    public Object expressFee(@RequestBody StoreOrderPostageRequest storeOrderPostageRequest) {
        return oddService.getFreightFee(storeOrderPostageRequest);
    }

}
