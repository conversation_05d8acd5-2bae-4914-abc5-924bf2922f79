package com.ylpz.admin.controller;

import com.ylpz.model.finance.UserExtract;
import com.ylpz.core.common.page.CommonPage;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.core.common.request.UserExtractRequest;
import com.ylpz.core.common.request.UserExtractSearchRequest;
import com.ylpz.core.common.response.BalanceResponse;
import com.ylpz.core.common.response.CommonResult;
import com.ylpz.core.common.response.UserExtractDetailResponse;
import com.ylpz.core.service.UserExtractService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户提现表 前端控制器
 */
@Slf4j
@RestController
@RequestMapping("api/admin/finance/apply")
@Api(tags = "财务 -- 提现申请")
public class UserExtractController {

    @Autowired
    private UserExtractService userExtractService;

    /**
     * 分页显示用户提现表
     * 
     * @param request          搜索条件
     * @param pageParamRequest 分页参数
     */
    // @PreAuthorize("hasAuthority('admin:finance:apply:list')")
    @ApiOperation(value = "分页列表")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public CommonResult<CommonPage<UserExtractDetailResponse>> getList(@Validated UserExtractSearchRequest request,
            @Validated PageParamRequest pageParamRequest) {
        CommonPage<UserExtractDetailResponse> userExtractDetailResponseCommonPage = CommonPage
                .restPage(userExtractService.getListWithDetail(request, pageParamRequest));
        return CommonResult.success(userExtractDetailResponseCommonPage);
    }

    /**
     * 修改用户提现表
     * 
     * @param id                 integer id
     * @param userExtractRequest 修改参数
     */
    // @PreAuthorize("hasAuthority('admin:finance:apply:update')")
    @ApiOperation(value = "修改")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public CommonResult<String> update(@RequestParam Integer id, @Validated UserExtractRequest userExtractRequest) {
        if (userExtractService.updateExtract(id, userExtractRequest)) {
            return CommonResult.success();
        } else {
            return CommonResult.failed();
        }
    }

    /**
     * 提现统计
     * 
     * @Param dateLimit 时间限制
     *        today,yesterday,lately7,lately15,lately30,month,year,/yyyy-MM-dd
     *        hh:mm:ss,yyyy-MM-dd hh:mm:ss/
     */
    // @PreAuthorize("hasAuthority('admin:finance:apply:balance')")
    @ApiOperation(value = "提现统计")
    @RequestMapping(value = "/balance", method = RequestMethod.POST)
    public CommonResult<BalanceResponse> balance(
            @RequestParam(value = "dateLimit", required = false, defaultValue = "") String dateLimit) {
        return CommonResult.success(userExtractService.getBalance(dateLimit));
    }

    /**
     * 提现审核
     * 
     * @param id          提现id
     * @param status      审核状态 -1 已拒绝 0 待审核 1 审核通过 2 提现成功 3 打款失败
     * @param backMessage 驳回原因
     * @return 审核结果
     */
    // @PreAuthorize("hasAuthority('admin:finance:apply:apply')")
    @ApiOperation(value = "提现申请审核")
    @RequestMapping(value = "/apply", method = RequestMethod.POST)
    public CommonResult<String> updateStatus(@RequestParam(value = "id") Integer id,
            @RequestParam(value = "status") Integer status,
            @RequestParam(value = "backMessage", required = false) String backMessage) {
        if (userExtractService.updateStatus(id, status, backMessage)) {
            return CommonResult.success();
        } else {
            return CommonResult.failed();
        }
    }

    /**
     * 获取提现记录详情
     * 
     * @param id 提现记录ID
     * @return 提现记录详情
     */
    // @PreAuthorize("hasAuthority('admin:finance:apply:info')")
    @ApiOperation(value = "提现记录详情")
    @RequestMapping(value = "/info", method = RequestMethod.GET)
    public CommonResult<UserExtractDetailResponse> info(@RequestParam(value = "id") Integer id) {
        UserExtractDetailResponse detailResponse = userExtractService.getDetailById(id);
        if (detailResponse != null) {
            return CommonResult.success(detailResponse);
        } else {
            return CommonResult.failed("未找到相关提现记录");
        }
    }

    /**
     * 批量审核提现申请
     * 
     * @param ids         提现申请ID列表，逗号分隔
     * @param status      审核状态 -1 已拒绝 0 待审核 1 审核通过 2 提现成功 3 打款失败
     * @param backMessage 驳回原因，当status=-1时必填
     * @return 审核结果
     */
    // @PreAuthorize("hasAuthority('admin:finance:apply:batch')")
    @ApiOperation(value = "批量审核提现申请")
    @RequestMapping(value = "/batch", method = RequestMethod.POST)
    public CommonResult<String> batchUpdateStatus(@RequestParam(value = "ids") String ids,
            @RequestParam(value = "status") Integer status,
            @RequestParam(value = "backMessage", required = false) String backMessage) {
        if (userExtractService.batchUpdateStatus(ids, status, backMessage)) {
            return CommonResult.success("批量审核成功");
        } else {
            return CommonResult.failed("批量审核失败");
        }
    }

    /**
     * 更新付款流水号
     * 
     * @param id        提现申请ID
     * @param paymentNo 付款流水号
     * @return 更新结果
     */
    // @PreAuthorize("hasAuthority('admin:finance:apply:payment')")
    @ApiOperation(value = "更新付款流水号")
    @RequestMapping(value = "/payment", method = RequestMethod.POST)
    public CommonResult<String> updatePaymentNo(@RequestParam(value = "id") Integer id,
            @RequestParam(value = "paymentNo") String paymentNo) {
        if (userExtractService.updatePaymentNo(id, paymentNo)) {
            return CommonResult.success("付款流水号更新成功");
        } else {
            return CommonResult.failed("付款流水号更新失败");
        }
    }
}
