package com.ylpz.entity;

public interface StoreOrderInfoDefine {
    String image = "image";
    String orderNo = "orderNo";
    String productId = "productId";
    String orderId = "orderId";
    String attrValueId = "attrValueId";
    String weight = "weight";
    String giveIntegral = "giveIntegral";
    String isSub = "isSub";
    String productName = "productName";
    String volume = "volume";
    String payNum = "payNum";
    String price = "price";
    String unique = "unique";
    String vipPrice = "vipPrice";
    String id = "id";
    String sku = "sku";
    String isReply = "isReply";
    String productType = "productType";
    String info = "info";
}
