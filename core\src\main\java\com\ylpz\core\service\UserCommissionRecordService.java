package com.ylpz.core.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.ylpz.core.common.request.CommissionRecordRequest;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.model.user.UserBrokerageRecord;

import java.math.BigDecimal;

/**
 * 用户佣金返现记录服务接口
 */
public interface UserCommissionRecordService extends IService<UserBrokerageRecord> {

    /**
     * 获取佣金返现记录列表
     * 
     * @param request          查询条件
     * @param pageParamRequest 分页参数
     * @return 分页数据
     */
    PageInfo<UserBrokerageRecord> getCommissionList(CommissionRecordRequest request, PageParamRequest pageParamRequest);

    /**
     * 获取佣金返现合计金额
     * 
     * @param request 查询条件
     * @return 合计金额
     */
    BigDecimal getCommissionTotal(CommissionRecordRequest request);

    /**
     * 获取用户佣金返现统计
     * 
     * @param uid 用户ID
     * @return 用户佣金返现统计
     */
    UserBrokerageRecord getUserCommissionStatistics(Integer uid);
}