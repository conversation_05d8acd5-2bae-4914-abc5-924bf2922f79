(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-b76d7688"],{"14c9":function(t,e,r){},bcaf:function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"divBox"},[r("el-card",{staticClass:"box-card"},[r("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[r("div",{staticClass:"container"},[r("el-form",{attrs:{inline:""}},[r("el-form-item",{attrs:{label:"砍价状态："}},[r("el-select",{staticClass:"filter-item selWidth mr20",attrs:{placeholder:"请选择",clearable:""},on:{change:function(e){return t.getList(1)}},model:{value:t.tableFrom.status,callback:function(e){t.$set(t.tableFrom,"status",e)},expression:"tableFrom.status"}},[r("el-option",{attrs:{label:"关闭",value:0}}),t._v(" "),r("el-option",{attrs:{label:"开启",value:1}})],1)],1),t._v(" "),r("el-form-item",{attrs:{label:"商品搜索："}},[r("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入商品名称、ID",clearable:""},model:{value:t.tableFrom.keywords,callback:function(e){t.$set(t.tableFrom,"keywords",e)},expression:"tableFrom.keywords"}},[r("el-button",{attrs:{slot:"append",icon:"el-icon-search"},on:{click:function(e){return t.getList(1)}},slot:"append"})],1)],1)],1)],1),t._v(" "),r("router-link",{attrs:{to:{path:"/marketing/bargain/creatBargain"}}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:bargain:save"],expression:"['admin:bargain:save']"}],staticClass:"mr10",attrs:{size:"mini",type:"primary"}},[t._v("添加砍价商品")])],1),t._v(" "),r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:export:excel:bargain"],expression:"['admin:export:excel:bargain']"}],staticClass:"mr10",attrs:{size:"mini"},on:{click:t.exportList}},[t._v("导出")])],1),t._v(" "),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"multipleTable",staticStyle:{width:"100%"},attrs:{data:t.tableData.data,size:"mini","header-cell-style":{fontWeight:"bold"}}},[r("el-table-column",{attrs:{prop:"id",label:"ID","min-width":"50"}}),t._v(" "),r("el-table-column",{attrs:{label:"砍价图片","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(t){return[r("div",{staticClass:"demo-image__preview"},[r("el-image",{staticStyle:{width:"36px",height:"36px"},attrs:{src:t.row.image,"preview-src-list":[t.row.image]}})],1)]}}])}),t._v(" "),r("el-table-column",{attrs:{label:"砍价名称",prop:"title","min-width":"300"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("el-popover",{attrs:{trigger:"hover",placement:"right","open-delay":800}},[r("div",{staticClass:"text_overflow",attrs:{slot:"reference"},slot:"reference"},[t._v(t._s(e.row.title))]),t._v(" "),r("div",{staticClass:"pup_card"},[t._v(t._s(e.row.title))])])]}}])}),t._v(" "),r("el-table-column",{attrs:{label:"砍价价格",prop:"price","min-width":"100",align:"center"}}),t._v(" "),r("el-table-column",{attrs:{label:"最低价",prop:"minPrice","min-width":"100",align:"center"}}),t._v(" "),r("el-table-column",{attrs:{label:"参与人数",prop:"countPeopleAll","min-width":"100",align:"center"}}),t._v(" "),r("el-table-column",{attrs:{label:"帮忙砍价人数",prop:"countPeopleHelp","min-width":"100",align:"center"}}),t._v(" "),r("el-table-column",{attrs:{label:"砍价成功人数",prop:"countPeopleSuccess","min-width":"100",align:"center"}}),t._v(" "),r("el-table-column",{attrs:{label:"限量","min-width":"100",prop:"quotaShow",align:"center"}}),t._v(" "),r("el-table-column",{attrs:{label:"限量剩余",prop:"surplusQuota","min-width":"100",align:"center"}}),t._v(" "),r("el-table-column",{attrs:{prop:"stopTime",label:"活动时间","min-width":"160"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n          "+t._s(e.row.startTime+" ~ "+e.row.stopTime)+"\n        ")]}}])}),t._v(" "),r("el-table-column",{attrs:{label:"砍价状态","min-width":"150"},scopedSlots:t._u([{key:"default",fn:function(e){return[t.checkPermi(["admin:bargain:update:status"])?r("el-switch",{attrs:{"active-value":!0,"inactive-value":!1,"active-text":"开启","inactive-text":"关闭"},on:{change:function(r){return t.onchangeIsShow(e.row)}},model:{value:e.row.status,callback:function(r){t.$set(e.row,"status",r)},expression:"scope.row.status"}}):t._e()]}}])}),t._v(" "),r("el-table-column",{attrs:{label:"操作","min-width":"130",fixed:"right",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("router-link",{attrs:{to:{path:"/marketing/bargain/creatBargain/"+e.row.id}}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:bargain:info"],expression:"['admin:bargain:info']"}],attrs:{type:"text",size:"small"}},[t._v("编辑")])],1),t._v(" "),r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:bargain:delete"],expression:"['admin:bargain:delete']"}],staticClass:"mr10",attrs:{type:"text",size:"small"},on:{click:function(r){return t.handleDelete(e.row.id,e.$index)}}},[t._v("删除")])]}}])})],1),t._v(" "),r("div",{staticClass:"block mb20"},[t.checkPermi(["admin:bargain:list"])?r("el-pagination",{attrs:{"page-sizes":[10,20,30,40],"page-size":t.tableFrom.limit,"current-page":t.tableFrom.page,layout:"total, sizes, prev, pager, next, jumper",total:t.tableData.total},on:{"size-change":t.handleSizeChange,"current-change":t.pageChange}}):t._e()],1)],1)],1)},i=[],a=r("b7be"),o=r("e350");function c(t){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},c(t)}function l(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */l=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,i=Object.defineProperty||function(t,e,r){t[e]=r.value},a="function"==typeof Symbol?Symbol:{},o=a.iterator||"@@iterator",s=a.asyncIterator||"@@asyncIterator",u=a.toStringTag||"@@toStringTag";function h(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{h({},"")}catch(t){h=function(t,e,r){return t[e]=r}}function f(t,e,r,n){var a=e&&e.prototype instanceof b?e:b,o=Object.create(a.prototype),c=new N(n||[]);return i(o,"_invoke",{value:j(t,r,c)}),o}function p(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var m="suspendedStart",d="suspendedYield",v="executing",y="completed",g={};function b(){}function w(){}function _(){}var x={};h(x,o,(function(){return this}));var L=Object.getPrototypeOf,k=L&&L(L(z([])));k&&k!==r&&n.call(k,o)&&(x=k);var E=_.prototype=b.prototype=Object.create(x);function P(t){["next","throw","return"].forEach((function(e){h(t,e,(function(t){return this._invoke(e,t)}))}))}function S(t,e){function r(i,a,o,l){var s=p(t[i],t,a);if("throw"!==s.type){var u=s.arg,h=u.value;return h&&"object"==c(h)&&n.call(h,"__await")?e.resolve(h.__await).then((function(t){r("next",t,o,l)}),(function(t){r("throw",t,o,l)})):e.resolve(h).then((function(t){u.value=t,o(u)}),(function(t){return r("throw",t,o,l)}))}l(s.arg)}var a;i(this,"_invoke",{value:function(t,n){function i(){return new e((function(e,i){r(t,n,e,i)}))}return a=a?a.then(i,i):i()}})}function j(e,r,n){var i=m;return function(a,o){if(i===v)throw Error("Generator is already running");if(i===y){if("throw"===a)throw o;return{value:t,done:!0}}for(n.method=a,n.arg=o;;){var c=n.delegate;if(c){var l=F(c,n);if(l){if(l===g)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(i===m)throw i=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);i=v;var s=p(e,r,n);if("normal"===s.type){if(i=n.done?y:d,s.arg===g)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(i=y,n.method="throw",n.arg=s.arg)}}}function F(e,r){var n=r.method,i=e.iterator[n];if(i===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,F(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),g;var a=p(i,e.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,g;var o=a.arg;return o?o.done?(r[e.resultName]=o.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,g):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,g)}function C(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function N(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(C,this),this.reset(!0)}function z(e){if(e||""===e){var r=e[o];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,a=function r(){for(;++i<e.length;)if(n.call(e,i))return r.value=e[i],r.done=!1,r;return r.value=t,r.done=!0,r};return a.next=a}}throw new TypeError(c(e)+" is not iterable")}return w.prototype=_,i(E,"constructor",{value:_,configurable:!0}),i(_,"constructor",{value:w,configurable:!0}),w.displayName=h(_,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,_):(t.__proto__=_,h(t,u,"GeneratorFunction")),t.prototype=Object.create(E),t},e.awrap=function(t){return{__await:t}},P(S.prototype),h(S.prototype,s,(function(){return this})),e.AsyncIterator=S,e.async=function(t,r,n,i,a){void 0===a&&(a=Promise);var o=new S(f(t,r,n,i),a);return e.isGeneratorFunction(r)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},P(E),h(E,u,"Generator"),h(E,o,(function(){return this})),h(E,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=z,N.prototype={constructor:N,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(O),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function i(n,i){return c.type="throw",c.arg=e,r.next=n,i&&(r.method="next",r.arg=t),!!i}for(var a=this.tryEntries.length-1;a>=0;--a){var o=this.tryEntries[a],c=o.completion;if("root"===o.tryLoc)return i("end");if(o.tryLoc<=this.prev){var l=n.call(o,"catchLoc"),s=n.call(o,"finallyLoc");if(l&&s){if(this.prev<o.catchLoc)return i(o.catchLoc,!0);if(this.prev<o.finallyLoc)return i(o.finallyLoc)}else if(l){if(this.prev<o.catchLoc)return i(o.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return i(o.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===t||"continue"===t)&&a.tryLoc<=e&&e<=a.finallyLoc&&(a=null);var o=a?a.completion:{};return o.type=t,o.arg=e,a?(this.method="next",this.next=a.finallyLoc,g):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),g},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),O(r),g}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var i=n.arg;O(r)}return i}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:z(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),g}},e}function s(t,e,r,n,i,a,o){try{var c=t[a](o),l=c.value}catch(t){return void r(t)}c.done?e(l):Promise.resolve(l).then(n,i)}function u(t){return function(){var e=this,r=arguments;return new Promise((function(n,i){var a=t.apply(e,r);function o(t){s(a,n,i,o,c,"next",t)}function c(t){s(a,n,i,o,c,"throw",t)}o(void 0)}))}}var h={name:"index",data:function(){return{tableFrom:{page:1,limit:20,keywords:"",status:null},listLoading:!0,tableData:{data:[],total:0},afterData:[]}},mounted:function(){var t=[{city_id:1,city_name:"北京",city_img:"http://dfknbdjknvkjsfnvlkjdn.png",city_country:"中国"},{city_id:2,city_name:"上海",city_img:"http://wergerbe.png",city_country:"中国"},{city_id:3,city_name:"广州",city_img:"http://hrthhr.png",city_country:"中国"},{city_id:4,city_name:"西雅图",city_img:"http://frevfd.png",city_country:"美国"},{city_id:5,city_name:"纽约",city_img:"http://反而个.png",city_country:"美国"}],e=[];t.forEach((function(t){var r={name:t.city_country,citys:[]},n={city_name:t.city_name,city_img:t.city_img,city_id:t.city_id};r.citys.push(n),e.push(r)}));var r=[],n={};e.forEach((function(t,i){n[t.name]?r.forEach((function(t){t.name===e[i].name&&(t.citys=t.citys.concat(e[i].citys))})):(r.push(t),n[t.name]=!0)})),this.getList()},methods:{checkPermi:o["a"],exportList:function(){Object(a["C"])({keywords:this.tableFrom.keywords,status:this.tableFrom.status}).then((function(t){window.open(t.fileName)}))},handleDelete:function(t,e){var r=this;this.$modal.confirm("确认删除该商品吗").then((function(){Object(a["a"])({id:t}).then((function(){r.$message.success("删除成功"),r.getList()}))})).catch((function(){}))},onchangeIsShow:function(t){var e=this;Object(a["g"])({id:t.id,status:t.status}).then(u(l().mark((function t(){return l().wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.$message.success("修改成功"),e.getList();case 2:case"end":return t.stop()}}),t)})))).catch((function(){t.status=!t.status}))},getList:function(t){var e=this;this.listLoading=!0,this.tableFrom.page=t||this.tableFrom.page,Object(a["c"])(this.tableFrom).then((function(t){e.tableData.data=t.list,e.tableData.total=t.total,e.listLoading=!1})).catch((function(t){e.listLoading=!1}))},pageChange:function(t){this.tableFrom.page=t,this.getList()},handleSizeChange:function(t){this.tableFrom.limit=t,this.getList()},add:function(){this.isCreate=0,this.dialogVisible=!0},handleClose:function(){this.dialogVisible=!1,this.editData={}}}},f=h,p=(r("bcdc"),r("2877")),m=Object(p["a"])(f,n,i,!1,null,"4b9cab55",null);e["default"]=m.exports},bcdc:function(t,e,r){"use strict";r("14c9")}}]);