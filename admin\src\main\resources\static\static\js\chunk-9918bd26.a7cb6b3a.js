(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-9918bd26"],{"10a9":function(t,e,n){"use strict";n.r(e);var r=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{ref:"container",staticClass:"pos-order-list"},[n("div",{staticClass:"nav acea-row row-around row-middle"},[n("div",{staticClass:"item",class:"unPaid"==t.where.status?"on":"",on:{click:function(e){return t.changeStatus("unPaid")}}},[t._v("\n      待付款\n    ")]),t._v(" "),n("div",{staticClass:"item",class:"notShipped"==t.where.status?"on":"",on:{click:function(e){return t.changeStatus("notShipped")}}},[t._v("\n      待发货\n    ")]),t._v(" "),n("div",{staticClass:"item",class:"spike"==t.where.status?"on":"",on:{click:function(e){return t.changeStatus("spike")}}},[t._v("\n      待收货\n    ")]),t._v(" "),n("div",{staticClass:"item",class:"toBeWrittenOff"==t.where.status?"on":"",on:{click:function(e){return t.changeStatus("toBeWrittenOff")}}},[t._v("\n      待核销\n    ")]),t._v(" "),n("div",{staticClass:"item",class:"complete"==t.where.status?"on":"",on:{click:function(e){return t.changeStatus("complete")}}},[t._v("\n      已完成\n    ")]),t._v(" "),n("div",{staticClass:"item",class:"refunding"==t.where.status?"on":"",on:{click:function(e){return t.changeStatus("refunding")}}},[t._v("\n      退款\n    ")])]),t._v(" "),n("div",{staticClass:"list"},[t.list.length>0?t._l(t.list,(function(e,r){return n("div",{key:r,staticClass:"item"},[n("div",{staticClass:"order-num acea-row row-middle",on:{click:function(n){return t.toDetail(e)}}},[t._v("\n          订单号："+t._s(e.orderId)+"\n          "),n("span",{staticClass:"time"},[t._v("下单时间："+t._s(e.createTime))])]),t._v(" "),t._l(e.productList,(function(r,i){return n("div",{key:i,staticClass:"pos-order-goods"},[n("div",{staticClass:"goods acea-row row-between-wrapper",on:{click:function(n){return t.toDetail(e)}}},[n("div",{staticClass:"picTxt acea-row row-between-wrapper"},[n("div",{staticClass:"pictrue"},[n("img",{attrs:{src:r.info.image}})]),t._v(" "),n("div",{staticClass:"text "},[n("div",{staticClass:"info line2"},[t._v("\n                    "+t._s(r.info.productName)+"\n                  ")]),t._v(" "),r.info.sku?n("div",{staticClass:"attr"},[t._v("\n                    "+t._s(r.info.sku)+"\n                  ")]):t._e()])]),t._v(" "),n("div",{staticClass:"money"},[n("div",{staticClass:"x-money"},[t._v("￥"+t._s(r.info.price))]),t._v(" "),n("div",{staticClass:"num"},[t._v("x"+t._s(r.info.payNum))]),t._v(" "),n("div",{staticClass:"y-money"})])])])})),t._v(" "),n("div",{staticClass:"public-total"},[t._v("\n          共"+t._s(e.totalNum?e.totalNum:1)+"件商品，应支付\n          "),n("span",{staticClass:"money"},[t._v("￥"+t._s(e.payPrice))]),t._v(" ( 邮费 ¥"+t._s(e.totalPostage?e.totalPostage:0)+")\n        ")]),t._v(" "),n("div",{staticClass:"operation acea-row row-between-wrapper"},[n("div",{staticClass:"more"}),t._v(" "),n("div",{staticClass:"acea-row row-middle"},[e.isAlterPrice||0!=e.paid?t._e():n("div",{staticClass:"bnt",on:{click:function(n){return t.modify(e,0)}}},[t._v("\n              一键改价\n            ")]),t._v(" "),n("div",{staticClass:"bnt",on:{click:function(n){return t.modify(e,1)}}},[t._v("订单备注")]),t._v(" "),"refunding"===t.where.status&&1===e.refundStatus?n("div",{staticClass:"bnt",on:{click:function(n){return t.modify(e,2)}}},[t._v("\n              立即退款\n            ")]):t._e(),t._v(" "),"refunding"===t.where.status&&1===e.refundStatus?n("div",{staticClass:"bnt",on:{click:function(n){return t.modify(e,3)}}},[t._v("\n              拒绝退款\n            ")]):t._e(),t._v(" "),"notShipped"===t.where.status&&2!==e.shippingType&&2!==e.refundStatus?n("router-link",{staticClass:"bnt",attrs:{to:"/javaMobile/orderDelivery/"+e.orderId+"/"+e.id}},[t._v("去发货\n            ")]):t._e(),t._v(" "),"toBeWrittenOff"===t.where.status&&2===e.shippingType&&t.isWriteOff&&0===e.refundStatus&&1==e.paid?n("router-link",{staticClass:"bnt",attrs:{to:"/javaMobile/orderCancellation"}},[t._v("去核销\n            ")]):t._e()],1)])],2)})):t._e(),t._v(" "),t.loading||0!==t.list.length?t._e():[n("div",{staticStyle:{"text-align":"center"}},[t._v("暂无数据")])]],2),t._v(" "),n("Loading",{attrs:{loaded:t.loaded,loading:t.loading}}),t._v(" "),t.orderInfo?n("PriceChange",{attrs:{change:t.change,orderInfo:t.orderInfo,status:t.status},on:{closechange:function(e){return t.changeclose(e)},getRefuse:t.getRefuse}}):t._e()],1)},i=[],o=n("4c60"),a=n("b798"),s=n("f8b7"),c=n("61f7"),u=n("69ae"),l=n("ed08");function f(t){return f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},f(t)}function h(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */h=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,i=Object.defineProperty||function(t,e,n){t[e]=n.value},o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",s=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function u(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,n){return t[e]=n}}function l(t,e,n,r){var o=e&&e.prototype instanceof w?e:w,a=Object.create(o.prototype),s=new $(r||[]);return i(a,"_invoke",{value:S(t,n,s)}),a}function d(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=l;var v="suspendedStart",p="suspendedYield",g="executing",y="completed",m={};function w(){}function _(){}function b(){}var x={};u(x,a,(function(){return this}));var k=Object.getPrototypeOf,C=k&&k(k(N([])));C&&C!==n&&r.call(C,a)&&(x=C);var O=b.prototype=w.prototype=Object.create(x);function L(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function j(t,e){function n(i,o,a,s){var c=d(t[i],t,o);if("throw"!==c.type){var u=c.arg,l=u.value;return l&&"object"==f(l)&&r.call(l,"__await")?e.resolve(l.__await).then((function(t){n("next",t,a,s)}),(function(t){n("throw",t,a,s)})):e.resolve(l).then((function(t){u.value=t,a(u)}),(function(t){return n("throw",t,a,s)}))}s(c.arg)}var o;i(this,"_invoke",{value:function(t,r){function i(){return new e((function(e,i){n(t,r,e,i)}))}return o=o?o.then(i,i):i()}})}function S(e,n,r){var i=v;return function(o,a){if(i===g)throw Error("Generator is already running");if(i===y){if("throw"===o)throw a;return{value:t,done:!0}}for(r.method=o,r.arg=a;;){var s=r.delegate;if(s){var c=E(s,r);if(c){if(c===m)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(i===v)throw i=y,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);i=g;var u=d(e,n,r);if("normal"===u.type){if(i=r.done?y:p,u.arg===m)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(i=y,r.method="throw",r.arg=u.arg)}}}function E(e,n){var r=n.method,i=e.iterator[r];if(i===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,E(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),m;var o=d(i,e.iterator,n.arg);if("throw"===o.type)return n.method="throw",n.arg=o.arg,n.delegate=null,m;var a=o.arg;return a?a.done?(n[e.resultName]=a.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,m):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,m)}function P(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function I(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function $(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(P,this),this.reset(!0)}function N(e){if(e||""===e){var n=e[a];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,o=function n(){for(;++i<e.length;)if(r.call(e,i))return n.value=e[i],n.done=!1,n;return n.value=t,n.done=!0,n};return o.next=o}}throw new TypeError(f(e)+" is not iterable")}return _.prototype=b,i(O,"constructor",{value:b,configurable:!0}),i(b,"constructor",{value:_,configurable:!0}),_.displayName=u(b,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===_||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,u(t,c,"GeneratorFunction")),t.prototype=Object.create(O),t},e.awrap=function(t){return{__await:t}},L(j.prototype),u(j.prototype,s,(function(){return this})),e.AsyncIterator=j,e.async=function(t,n,r,i,o){void 0===o&&(o=Promise);var a=new j(l(t,n,r,i),o);return e.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},L(O),u(O,c,"Generator"),u(O,a,(function(){return this})),u(O,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=N,$.prototype={constructor:$,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(I),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function i(r,i){return s.type="throw",s.arg=e,n.next=r,i&&(n.method="next",n.arg=t),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],s=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var c=r.call(a,"catchLoc"),u=r.call(a,"finallyLoc");if(c&&u){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=t,a.arg=e,o?(this.method="next",this.next=o.finallyLoc,m):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),m},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),I(n),m}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var i=r.arg;I(n)}return i}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:N(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),m}},e}function d(t,e,n,r,i,o,a){try{var s=t[o](a),c=s.value}catch(t){return void n(t)}s.done?e(c):Promise.resolve(c).then(r,i)}function v(t){return function(){var e=this,n=arguments;return new Promise((function(r,i){var o=t.apply(e,n);function a(t){d(o,r,i,a,s,"next",t)}function s(t){d(o,r,i,a,s,"throw",t)}a(void 0)}))}}var p={name:"AdminOrderList",components:{PriceChange:o["a"],Loading:a["a"]},props:{},data:function(){return{isWriteOff:Object(l["e"])(),current:"",change:!1,types:0,where:{page:1,limit:10,status:"unPaid"},list:[],loaded:!1,loading:!1,orderInfo:{},status:null}},watch:{"$route.params.types":function(t){var e=this;void 0!=t&&(e.where.status=t,e.init())},types:function(){this.getIndex()}},created:function(){n.e("chunk-2d0d6f43").then(n.t.bind(null,"756e",7))},mounted:function(){var t=this;this.where.status=this.$route.params.types,this.current="",this.getIndex(),this.$scroll(this.$refs.container,(function(){!t.loading&&t.getIndex()}))},methods:{more:function(t){this.current===t?this.current="":this.current=t},modify:function(t,e){this.change=!0,this.orderInfo=t,this.status=e},changeclose:function(t){this.change=t,this.init()},getRefuse:function(t,e){var n=this;Object(s["m"])({orderNo:t,reason:e}).then((function(){n.change=!1,n.$dialog.success("已拒绝退款"),n.init()})).catch((function(t){n.$dialog.error(t.message)}))},savePrice:function(){var t=v(h().mark((function t(e){var n,r,i,o,a,l;return h().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(n=this,r={},i=e.price,o=e.refundPrice,a=n.orderInfo.refundStatus,l=e.remark,0!=n.status||0!==a){t.next=15;break}return t.prev=2,t.next=5,this.$validator({price:[Object(c["f"])(c["f"].message("金额"))]}).validate({price:i});case 5:t.next=10;break;case 7:return t.prev=7,t.t0=t["catch"](2),t.abrupt("return",Object(u["b"])(t.t0));case 10:r.price=i,r.orderNo=e.orderId,Object(s["editPriceApi"])(r).then((function(){n.change=!1,n.$dialog.success("改价成功"),n.init()})).catch((function(t){n.$dialog.error(t.message)})),t.next=41;break;case 15:if(0!=n.status||1!==a){t.next=30;break}return t.prev=16,t.next=19,this.$validator({refundPrice:[Object(c["f"])(c["f"].message("金额")),Object(c["e"])(c["e"].message("金额"))]}).validate({refundPrice:o});case 19:t.next=24;break;case 21:return t.prev=21,t.t1=t["catch"](16),t.abrupt("return",Object(u["b"])(t.t1));case 24:r.amount=o,r.type=e.type,r.orderNo=e.orderId,Object(s["l"])(r).then((function(t){n.change=!1,n.$dialog.success("退款成功"),n.init()}),(function(t){n.change=!1,n.$dialog.error(t.message)})),t.next=41;break;case 30:return t.prev=30,t.next=33,this.$validator({remark:[Object(c["f"])(c["f"].message("备注"))]}).validate({remark:l});case 33:t.next=38;break;case 35:return t.prev=35,t.t2=t["catch"](30),t.abrupt("return",Object(u["b"])(t.t2));case 38:r.mark=l,r.orderNo=e.orderId,Object(s["j"])(r).then((function(t){n.change=!1,n.$dialog.success("提交成功"),n.init()}),(function(t){n.change=!1,n.$dialog.error(t.message)}));case 41:case"end":return t.stop()}}),t,this,[[2,7],[16,21],[30,35]])})));function e(e){return t.apply(this,arguments)}return e}(),init:function(){this.list=[],this.where.page=1,this.loaded=!1,this.loading=!1,this.getIndex(),this.current=""},getIndex:function(){var t=this;this.loading||this.loaded||(this.loading=!0,Object(s["g"])(this.where).then((function(e){t.loading=!1,t.loaded=e.list.length<t.where.limit,t.list.push.apply(t.list,e.list),t.where.page=t.where.page+1}),(function(e){t.$dialog.error(e.message)})))},changeStatus:function(t){this.where.status!=t&&(this.where.status=t,this.init())},toDetail:function(t){this.$router.push({path:"/javaMobile/orderDetail/"+t.orderId})},offlinePay:function(t){}}},g=p,y=(n("43e3"),n("2877")),m=Object(y["a"])(g,r,i,!1,null,"15282485",null);e["default"]=m.exports},"43e3":function(t,e,n){"use strict";n("bf03")},b798:function(t,e,n){"use strict";var r=function(){var t=this,e=t.$createElement,n=t._self._c||e;return t.loading&&!t.loaded?n("div",{staticClass:"Loads acea-row row-center-wrapper",staticStyle:{"margin-top":".2rem","font-size":"12px"}},[t.loading?[n("div",{staticClass:"iconfont icon-jiazai loading acea-row row-center-wrapper"}),t._v("\n    正在加载中\n  ")]:[t._v("\n    上拉加载更多\n  ")]],2):t._e()},i=[],o={name:"Loading",props:{loaded:Boolean,loading:Boolean},created:function(){n.e("chunk-2d0d6f43").then(n.t.bind(null,"756e",7))}},a=o,s=n("2877"),c=Object(s["a"])(a,r,i,!1,null,null,null);e["a"]=c.exports},bf03:function(t,e,n){}}]);