package com.ylpz.admin.controller;

import java.util.List;

import com.ylpz.core.common.vo.ShippingTemplatesVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ylpz.core.common.page.CommonPage;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.core.common.request.ShippingTemplatesRequest;
import com.ylpz.core.common.request.ShippingTemplatesSearchRequest;
import com.ylpz.core.common.response.CommonResult;
import com.ylpz.core.service.ShippingTemplatesService;
import com.ylpz.model.express.ShippingTemplates;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * 物流-模板控制器
 */
@Slf4j
@RestController
@RequestMapping("api/admin/express/shipping/templates")
@Api(tags = "设置 -- 物流 -- 模板")
public class ShippingTemplatesController {

    @Autowired
    private ShippingTemplatesService shippingTemplatesService;

    /**
     * 分页显示
     * @param request 搜索条件
     * @param pageParamRequest 分页参数
     */
    @PreAuthorize("hasAuthority('admin:shipping:templates:list')")
    @ApiOperation(value = "分页列表")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public CommonResult<CommonPage<ShippingTemplates>>  getList(@Validated ShippingTemplatesSearchRequest request, @Validated PageParamRequest pageParamRequest){
        CommonPage<ShippingTemplates> shippingTemplatesCommonPage = CommonPage.restPage(shippingTemplatesService.getList(request, pageParamRequest));
        return CommonResult.success(shippingTemplatesCommonPage);
    }

    /**
     * 新增
     * @param request 新增参数
     */
    @PreAuthorize("hasAuthority('admin:shipping:templates:save')")
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public CommonResult<String> save(@RequestBody @Validated ShippingTemplatesRequest request){
        if (shippingTemplatesService.create(request)) {
            return CommonResult.success();
        }
        return CommonResult.failed("新增运费模板失败");
    }

    /**
     * 删除
     * @param id Integer
     */
    @PreAuthorize("hasAuthority('admin:shipping:templates:delete')")
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @ApiImplicitParam(name="id", value="模板ID", required = true)
    public CommonResult<String> delete(@RequestParam(value = "id") Integer id){
        if(shippingTemplatesService.remove(id)){
            return CommonResult.success();
        }else{
            return CommonResult.failed();
        }
    }

    /**
     * 修改
     * @param id integer id
     * @param request ShippingTemplatesRequest 修改参数
     */
    @PreAuthorize("hasAuthority('admin:shipping:templates:update')")
    @ApiOperation(value = "修改")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public CommonResult<String> update(@RequestParam Integer id, @RequestBody @Validated ShippingTemplatesRequest request){
        if (shippingTemplatesService.update(id, request)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 查询信息
     * @param id Integer
     */
    @PreAuthorize("hasAuthority('admin:shipping:templates:info')")
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/info", method = RequestMethod.GET)
    @ApiImplicitParam(name="id", value="模板ID", required = true)
    public CommonResult<ShippingTemplatesVo> info(@RequestParam(value = "id") Integer id){
        return CommonResult.success(shippingTemplatesService.getInfo(id));
    }

    /**
     * 修改启用状态
     * @param id 模板ID
     * @param status 状态：0-禁用，1-启用
     */
    //@PreAuthorize("hasAuthority('admin:shipping:templates:status')")
    @ApiOperation(value = "修改启用状态")
    @RequestMapping(value = "/set_status", method = RequestMethod.POST)
    public CommonResult<String> setStatus(@RequestParam Integer id, @RequestParam Integer status){
        ShippingTemplates shippingTemplates = new ShippingTemplates();
        shippingTemplates.setId(id);
        shippingTemplates.setStatus(status);
        if (shippingTemplatesService.updateById(shippingTemplates)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 获取启用状态的模板列表
     */
    //@PreAuthorize("hasAuthority('admin:shipping:templates:enabled:list')")
    @ApiOperation(value = "获取启用状态模板列表")
    @RequestMapping(value = "/enabled/list", method = RequestMethod.GET)
    public CommonResult<List<ShippingTemplates>> getEnabledList(){
        List<ShippingTemplates> list = shippingTemplatesService.getEnabledList();
        return CommonResult.success(list);
    }
}



