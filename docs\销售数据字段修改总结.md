# 销售数据字段修改总结

## 修改概述

根据用户需求，将销售数据功能中的"待售数量"和"已售数量"字段修改为"待结算返现"和"已结算返现"，以更准确地反映佣金返现的状态。

## 字段变更详情

### 1. 响应对象字段变更

#### SalesDataResponse.java
- **原字段**：
  - `pendingCount` (Integer) - 待售数量
  - `completedCount` (Integer) - 已售数量

- **新字段**：
  - `pendingBrokerageAmount` (BigDecimal) - 待结算返现
  - `settledBrokerageAmount` (BigDecimal) - 已结算返现

#### SalesDataStatisticsResponse.java
- **原字段**：
  - `totalPendingCount` (Integer) - 总待售数量
  - `totalCompletedCount` (Integer) - 总已售数量

- **新字段**：
  - `totalPendingBrokerageAmount` (BigDecimal) - 总待结算返现
  - `totalSettledBrokerageAmount` (BigDecimal) - 总已结算返现

### 2. 数据计算逻辑变更

#### 原逻辑（基于订单状态）
```java
// 统计订单状态
if (order.getStatus() == 0 || order.getStatus() == 1) { // 待发货
    pendingCount++;
} else if (order.getStatus() == 2 || order.getStatus() == 3) { // 已完成
    completedCount++;
}
```

#### 新逻辑（基于佣金记录状态）
```java
// 根据佣金记录状态分类统计
if (record.getStatus() == BrokerageRecordConstants.BROKERAGE_RECORD_STATUS_CREATE ||
    record.getStatus() == BrokerageRecordConstants.BROKERAGE_RECORD_STATUS_FROZEN) {
    // 待结算：创建状态(1)和冻结期状态(2)
    pendingAmount = pendingAmount.add(record.getPrice());
} else if (record.getStatus() == BrokerageRecordConstants.BROKERAGE_RECORD_STATUS_COMPLETE) {
    // 已结算：完成状态(3)
    settledAmount = settledAmount.add(record.getPrice());
}
```

### 3. 佣金记录状态说明

根据 `BrokerageRecordConstants` 和 `UserBrokerageRecord` 实体类：

| 状态值 | 状态名称 | 说明 | 归类 |
|--------|----------|------|------|
| 1 | 订单创建 | 佣金记录刚创建 | 待结算 |
| 2 | 冻结期 | 佣金在冻结期内 | 待结算 |
| 3 | 完成 | 佣金已解冻并可用 | 已结算 |
| 4 | 失效 | 订单退款导致失效 | 不统计 |
| 5 | 提现申请 | 已申请提现 | 不统计 |

### 4. 数据库查询优化

#### 直接使用SQL统计查询
在Java代码中直接使用SQL进行佣金统计，不再使用视图和存储过程：

```java
// 构建佣金记录查询条件
LambdaQueryWrapper<UserBrokerageRecord> brokerageWrapper = new LambdaQueryWrapper<>();
brokerageWrapper.eq(UserBrokerageRecord::getUid, uid);
brokerageWrapper.eq(UserBrokerageRecord::getType, BrokerageRecordConstants.BROKERAGE_RECORD_TYPE_ADD);

// 根据状态分类统计
if (record.getStatus() == BrokerageRecordConstants.BROKERAGE_RECORD_STATUS_CREATE ||
    record.getStatus() == BrokerageRecordConstants.BROKERAGE_RECORD_STATUS_FROZEN) {
    // 待结算：创建状态(1)和冻结期状态(2)
    pendingAmount = pendingAmount.add(record.getPrice());
} else if (record.getStatus() == BrokerageRecordConstants.BROKERAGE_RECORD_STATUS_COMPLETE) {
    // 已结算：完成状态(3)
    settledAmount = settledAmount.add(record.getPrice());
}
```

#### 查询条件说明
- `br.type = 1`：只统计增加类型的佣金记录
- `br.status IN (1, 2)`：待结算状态（创建、冻结期）
- `br.status = 3`：已结算状态（完成）

## 修改的文件列表

### 1. 核心代码文件
- `core/src/main/java/com/ylpz/core/common/response/SalesDataResponse.java`
- `core/src/main/java/com/ylpz/core/common/response/SalesDataStatisticsResponse.java`
- `core/src/main/java/com/ylpz/core/service/impl/SalesDataServiceImpl.java`

### 2. 数据库脚本文件
- `sql/create_sales_data_view.sql`（简化为只包含索引创建）

### 3. 文档文件
- `README.md`
- `docs/销售数据功能使用说明.md`
- `docs/销售数据字段修改总结.md`（新增）

## API接口变更

### 1. 获取销售数据列表接口
**接口地址**：`GET /api/admin/sales/data/list`

**返回数据变更**：
```json
{
  "data": {
    "list": [
      {
        "uid": 123,
        "nickname": "张三",
        "phone": "13800138000",
        "memberLevel": "SVIP会员",
        "salesAmount": 14829.34,
        "orderCount": 43,
        "pendingBrokerageAmount": 150.00,    // 新字段：待结算返现
        "settledBrokerageAmount": 1852.50,   // 新字段：已结算返现
        "selfPurchaseAmount": 4523.00
      }
    ]
  }
}
```

### 2. 获取销售数据统计接口
**接口地址**：`GET /api/admin/sales/data/statistics`

**返回数据变更**：
```json
{
  "data": {
    "totalSalesAmount": 1482934.00,
    "totalOrderCount": 4300,
    "totalPendingBrokerageAmount": 15000.00,    // 新字段：总待结算返现
    "totalSettledBrokerageAmount": 185200.50,   // 新字段：总已结算返现
    "totalSelfPurchaseAmount": 452300.00,
    "totalMemberCount": 150
  }
}
```

### 3. 导出功能变更
Excel导出文件的表头也相应更新：
- 原表头：`["会员昵称", "手机号", "会员等级", "销售金额", "订单数量", "待售数量", "已售数量", "自购金额", "注册时间"]`
- 新表头：`["会员昵称", "手机号", "会员等级", "销售金额", "订单数量", "待结算返现", "已结算返现", "自购金额", "注册时间"]`

## 业务价值

### 1. 更准确的数据统计
- **原方案**：基于订单状态统计，无法准确反映佣金返现情况
- **新方案**：基于佣金记录状态统计，准确反映佣金的结算状态

### 2. 更好的业务理解
- **待结算返现**：帮助管理员了解还有多少佣金在冻结期或待处理
- **已结算返现**：帮助管理员了解已经完成结算的佣金总额

### 3. 更完整的财务管控
- 可以清楚地看到每个会员的佣金结算情况
- 有助于财务部门进行佣金成本核算
- 便于监控佣金发放的及时性

## 兼容性说明

### 1. 数据库兼容性
- 新增了对 `user_brokerage_record` 表的查询
- 保持了原有的订单统计逻辑
- 不影响现有数据

### 2. API兼容性
- 字段名称发生变化，前端需要相应调整
- 数据类型从 Integer 变更为 BigDecimal
- 建议前端做好向后兼容处理

### 3. 性能影响
- 新增了佣金记录表的关联查询
- 建议在 `user_brokerage_record` 表上创建相应索引：
  ```sql
  CREATE INDEX idx_user_brokerage_record_uid_status ON user_brokerage_record(uid, status);
  CREATE INDEX idx_user_brokerage_record_uid_type ON user_brokerage_record(uid, type);
  ```

## 测试建议

### 1. 单元测试
- 测试新的佣金计算逻辑
- 验证不同状态的佣金记录统计准确性

### 2. 集成测试
- 测试API接口返回数据的正确性
- 验证导出功能的字段变更

### 3. 性能测试
- 测试大数据量下的查询性能
- 验证新增关联查询的影响

## 部署注意事项

1. **数据库更新**：执行更新后的 `sql/create_sales_data_view.sql` 脚本（只包含索引创建）
2. **前端适配**：前端需要适配新的字段名称和数据类型
3. **文档更新**：更新相关的API文档和用户手册
4. **监控配置**：关注新查询的性能表现
5. **简化架构**：移除了不必要的视图和存储过程，直接在Java代码中使用SQL统计

## 总结

本次修改将销售数据功能从基于订单状态的统计改为基于佣金记录状态的统计，更准确地反映了佣金返现的实际情况。同时简化了架构设计，移除了不必要的视图和存储过程，直接在Java代码中使用SQL进行统计，提高了代码的可维护性和性能。
