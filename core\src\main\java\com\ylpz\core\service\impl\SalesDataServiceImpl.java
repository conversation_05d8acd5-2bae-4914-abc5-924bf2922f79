package com.ylpz.core.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ylpz.core.common.constants.BrokerageRecordConstants;
import com.ylpz.core.common.constants.Constants;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.core.common.request.SalesDataRequest;
import com.ylpz.core.common.response.SalesDataDetailResponse;
import com.ylpz.core.common.response.SalesDataResponse;
import com.ylpz.core.common.response.SalesDataStatisticsResponse;
import com.ylpz.core.common.utils.DateUtil;
import com.ylpz.core.dao.StoreOrderDao;
import com.ylpz.core.dao.StoreOrderInfoDao;
import com.ylpz.core.dao.UserBrokerageRecordDao;
import com.ylpz.core.dao.UserDao;
import com.ylpz.core.service.SalesDataService;
import com.ylpz.core.service.SystemUserLevelService;
import com.ylpz.model.order.StoreOrder;
import com.ylpz.model.order.StoreOrderInfo;
import com.ylpz.model.system.SystemUserLevel;
import com.ylpz.model.user.User;
import com.ylpz.model.user.UserBrokerageRecord;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 销售数据服务实现类
 */
@Service
public class SalesDataServiceImpl implements SalesDataService {

    @Autowired
    private UserDao userDao;

    @Autowired
    private StoreOrderDao storeOrderDao;

    @Autowired
    private StoreOrderInfoDao storeOrderInfoDao;

    @Autowired
    private UserBrokerageRecordDao userBrokerageRecordDao;

    @Autowired
    private SystemUserLevelService systemUserLevelService;

    /**
     * 获取销售数据列表
     */
    @Override
    public PageInfo<SalesDataResponse> getSalesDataList(SalesDataRequest request, PageParamRequest pageParamRequest) {
        // 构建用户查询条件
        LambdaQueryWrapper<User> userWrapper = new LambdaQueryWrapper<>();

        // 手机号筛选
        if (StrUtil.isNotBlank(request.getMobile())) {
            userWrapper.like(User::getPhone, request.getMobile());
        }

        // 昵称筛选
        if (StrUtil.isNotBlank(request.getNickname())) {
            userWrapper.like(User::getNickname, request.getNickname());
        }

        // 会员等级筛选
        if (ObjectUtil.isNotNull(request.getMemberLevel())) {
            userWrapper.eq(User::getLevel, request.getMemberLevel());
        }

        // 只查询正常状态的用户
        userWrapper.eq(User::getStatus, true);

        // 分页查询用户
        PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());
        List<User> userList = userDao.selectList(userWrapper);
        PageInfo<User> userPageInfo = new PageInfo<>(userList);

        // 转换为销售数据响应对象
        List<SalesDataResponse> responseList = new ArrayList<>();
        for (User user : userList) {
            SalesDataResponse response = convertToSalesDataResponse(user, request);
            responseList.add(response);
        }

        // 构建分页结果
        PageInfo<SalesDataResponse> pageInfo = new PageInfo<>();
        pageInfo.setList(responseList);
        pageInfo.setTotal(userPageInfo.getTotal());
        pageInfo.setPageNum(userPageInfo.getPageNum());
        pageInfo.setPageSize(userPageInfo.getPageSize());
        pageInfo.setPages(userPageInfo.getPages());

        return pageInfo;
    }

    /**
     * 获取销售数据统计
     */
    @Override
    public SalesDataStatisticsResponse getSalesDataStatistics(SalesDataRequest request) {
        // 构建用户查询条件
        LambdaQueryWrapper<User> userWrapper = new LambdaQueryWrapper<>();

        // 手机号筛选
        if (StrUtil.isNotBlank(request.getMobile())) {
            userWrapper.like(User::getPhone, request.getMobile());
        }

        // 昵称筛选
        if (StrUtil.isNotBlank(request.getNickname())) {
            userWrapper.like(User::getNickname, request.getNickname());
        }

        // 会员等级筛选
        if (ObjectUtil.isNotNull(request.getMemberLevel())) {
            userWrapper.eq(User::getLevel, request.getMemberLevel());
        }

        // 只查询正常状态的用户
        userWrapper.eq(User::getStatus, true);

        List<User> userList = userDao.selectList(userWrapper);

        SalesDataStatisticsResponse statistics = new SalesDataStatisticsResponse();
        statistics.setTotalMemberCount(userList.size());
        statistics.setTotalSalesAmount(BigDecimal.ZERO);
        statistics.setTotalOrderCount(0);
        statistics.setTotalPendingBrokerageAmount(BigDecimal.ZERO);
        statistics.setTotalSettledBrokerageAmount(BigDecimal.ZERO);
        statistics.setTotalSelfPurchaseAmount(BigDecimal.ZERO);

        // 统计每个用户的销售数据
        for (User user : userList) {
            SalesDataResponse userSalesData = convertToSalesDataResponse(user, request);

            statistics.setTotalSalesAmount(statistics.getTotalSalesAmount().add(userSalesData.getSalesAmount()));
            statistics.setTotalOrderCount(statistics.getTotalOrderCount() + userSalesData.getOrderCount());
            statistics.setTotalPendingBrokerageAmount(
                    statistics.getTotalPendingBrokerageAmount().add(userSalesData.getPendingBrokerageAmount()));
            statistics.setTotalSettledBrokerageAmount(
                    statistics.getTotalSettledBrokerageAmount().add(userSalesData.getSettledBrokerageAmount()));
            statistics.setTotalSelfPurchaseAmount(
                    statistics.getTotalSelfPurchaseAmount().add(userSalesData.getSelfPurchaseAmount()));
        }

        return statistics;
    }

    /**
     * 获取会员销售明细
     */
    @Override
    public PageInfo<SalesDataDetailResponse> getSalesDataDetail(Integer uid, SalesDataRequest request,
            PageParamRequest pageParamRequest) {
        // 构建订单查询条件
        LambdaQueryWrapper<StoreOrder> orderWrapper = new LambdaQueryWrapper<>();
        orderWrapper.eq(StoreOrder::getUid, uid);

        // 时间范围筛选
        if (StrUtil.isNotBlank(request.getStartTime())) {
            Date startTime = DateUtil.strToDate(request.getStartTime(), Constants.DATE_FORMAT);
            orderWrapper.ge(StoreOrder::getCreateTime, startTime);
        }
        if (StrUtil.isNotBlank(request.getEndTime())) {
            Date endTime = DateUtil.strToDate(request.getEndTime(), Constants.DATE_FORMAT);
            orderWrapper.le(StoreOrder::getCreateTime, endTime);
        }

        // 按创建时间倒序
        orderWrapper.orderByDesc(StoreOrder::getCreateTime);

        // 分页查询订单
        PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());
        List<StoreOrder> orderList = storeOrderDao.selectList(orderWrapper);
        PageInfo<StoreOrder> orderPageInfo = new PageInfo<>(orderList);

        // 转换为销售明细响应对象
        List<SalesDataDetailResponse> responseList = new ArrayList<>();
        for (StoreOrder order : orderList) {
            SalesDataDetailResponse response = convertToSalesDataDetailResponse(order);
            responseList.add(response);
        }

        // 构建分页结果
        PageInfo<SalesDataDetailResponse> pageInfo = new PageInfo<>();
        pageInfo.setList(responseList);
        pageInfo.setTotal(orderPageInfo.getTotal());
        pageInfo.setPageNum(orderPageInfo.getPageNum());
        pageInfo.setPageSize(orderPageInfo.getPageSize());
        pageInfo.setPages(orderPageInfo.getPages());

        return pageInfo;
    }

    /**
     * 转换用户为销售数据响应对象
     */
    private SalesDataResponse convertToSalesDataResponse(User user, SalesDataRequest request) {
        SalesDataResponse response = new SalesDataResponse();
        response.setUid(user.getId());
        response.setNickname(user.getNickname());
        response.setPhone(user.getPhone());
        response.setAvatar(user.getAvatar());
        response.setMemberLevelId(user.getLevel());

        // 设置会员等级名称
        String memberLevelName = getUserLevelName(user.getLevel());
        response.setMemberLevel(memberLevelName);

        // 设置创建时间
        if (user.getCreateTime() != null) {
            response.setCreateTime(DateUtil.dateToStr(user.getCreateTime(), Constants.DATE_FORMAT));
        }

        // 统计销售数据
        calculateSalesData(response, user.getId(), request);

        return response;
    }

    /**
     * 计算用户的销售数据
     */
    private void calculateSalesData(SalesDataResponse response, Integer uid, SalesDataRequest request) {
        // 构建订单查询条件
        LambdaQueryWrapper<StoreOrder> orderWrapper = new LambdaQueryWrapper<>();
        orderWrapper.eq(StoreOrder::getUid, uid);

        // 时间范围筛选
        if (StrUtil.isNotBlank(request.getStartTime())) {
            Date startTime = DateUtil.strToDate(request.getStartTime(), Constants.DATE_FORMAT);
            orderWrapper.ge(StoreOrder::getCreateTime, startTime);
        }
        if (StrUtil.isNotBlank(request.getEndTime())) {
            Date endTime = DateUtil.strToDate(request.getEndTime(), Constants.DATE_FORMAT);
            orderWrapper.le(StoreOrder::getCreateTime, endTime);
        }

        List<StoreOrder> orderList = storeOrderDao.selectList(orderWrapper);

        BigDecimal salesAmount = BigDecimal.ZERO;
        BigDecimal selfPurchaseAmount = BigDecimal.ZERO;
        int orderCount = orderList.size();

        for (StoreOrder order : orderList) {
            // 统计销售金额（已支付订单）
            if (order.getPaid() && order.getPayPrice() != null) {
                salesAmount = salesAmount.add(order.getPayPrice());
                // 自购金额（用户自己的订单）
                selfPurchaseAmount = selfPurchaseAmount.add(order.getPayPrice());
            }
        }

        // 计算佣金数据
        BigDecimal[] brokerageAmounts = calculateBrokerageData(uid, request);

        response.setSalesAmount(salesAmount);
        response.setSelfPurchaseAmount(selfPurchaseAmount);
        response.setOrderCount(orderCount);
        response.setPendingBrokerageAmount(brokerageAmounts[0]); // 待结算返现
        response.setSettledBrokerageAmount(brokerageAmounts[1]); // 已结算返现
    }

    /**
     * 计算用户的佣金数据
     *
     * @param uid     用户ID
     * @param request 查询条件
     * @return [待结算返现, 已结算返现]
     */
    private BigDecimal[] calculateBrokerageData(Integer uid, SalesDataRequest request) {
        // 构建佣金记录查询条件
        LambdaQueryWrapper<UserBrokerageRecord> brokerageWrapper = new LambdaQueryWrapper<>();
        brokerageWrapper.eq(UserBrokerageRecord::getUid, uid);
        brokerageWrapper.eq(UserBrokerageRecord::getType, BrokerageRecordConstants.BROKERAGE_RECORD_TYPE_ADD); // 只查询增加类型

        // 时间范围筛选
        if (StrUtil.isNotBlank(request.getStartTime())) {
            Date startTime = DateUtil.strToDate(request.getStartTime(), Constants.DATE_FORMAT);
            brokerageWrapper.ge(UserBrokerageRecord::getCreateTime, startTime);
        }
        if (StrUtil.isNotBlank(request.getEndTime())) {
            Date endTime = DateUtil.strToDate(request.getEndTime(), Constants.DATE_FORMAT);
            brokerageWrapper.le(UserBrokerageRecord::getCreateTime, endTime);
        }

        List<UserBrokerageRecord> brokerageList = userBrokerageRecordDao.selectList(brokerageWrapper);

        BigDecimal pendingAmount = BigDecimal.ZERO; // 待结算返现
        BigDecimal settledAmount = BigDecimal.ZERO; // 已结算返现

        for (UserBrokerageRecord record : brokerageList) {
            if (record.getPrice() != null) {
                // 根据状态分类统计
                if (record.getStatus() == BrokerageRecordConstants.BROKERAGE_RECORD_STATUS_CREATE ||
                        record.getStatus() == BrokerageRecordConstants.BROKERAGE_RECORD_STATUS_FROZEN) {
                    // 待结算：创建状态和冻结期状态
                    pendingAmount = pendingAmount.add(record.getPrice());
                } else if (record.getStatus() == BrokerageRecordConstants.BROKERAGE_RECORD_STATUS_COMPLETE) {
                    // 已结算：完成状态
                    settledAmount = settledAmount.add(record.getPrice());
                }
            }
        }

        return new BigDecimal[] { pendingAmount, settledAmount };
    }

    /**
     * 转换订单为销售明细响应对象
     */
    private SalesDataDetailResponse convertToSalesDataDetailResponse(StoreOrder order) {
        SalesDataDetailResponse response = new SalesDataDetailResponse();
        response.setOrderId(order.getId());
        response.setOrderNo(order.getOrderId());
        response.setOrderAmount(order.getTotalPrice());
        response.setPayAmount(order.getPayPrice());
        response.setIsSelfPurchase(1); // 默认为自购

        // 设置下单时间
        if (order.getCreateTime() != null) {
            response.setOrderTime(DateUtil.dateToStr(order.getCreateTime(), Constants.DATE_FORMAT));
        }

        // 设置订单状态
        response.setOrderStatus(getOrderStatusName(order.getStatus()));

        // 设置支付方式
        response.setPayType(order.getPayType());

        // 获取订单商品信息
        setOrderProductInfo(response, order.getId());

        return response;
    }

    /**
     * 设置订单商品信息
     */
    private void setOrderProductInfo(SalesDataDetailResponse response, Integer orderId) {
        LambdaQueryWrapper<StoreOrderInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StoreOrderInfo::getOrderId, orderId);
        List<StoreOrderInfo> orderInfoList = storeOrderInfoDao.selectList(wrapper);

        if (!orderInfoList.isEmpty()) {
            StoreOrderInfo orderInfo = orderInfoList.get(0);
            response.setProductName(orderInfo.getProductName());
            response.setQuantity(orderInfo.getPayNum());
            response.setProductImage(orderInfo.getImage());
        }
    }

    /**
     * 获取用户等级名称
     */
    private String getUserLevelName(Integer levelId) {
        if (levelId == null || levelId == 0) {
            return "普通会员";
        }

        SystemUserLevel userLevel = systemUserLevelService.getByLevelId(levelId);
        if (userLevel != null) {
            return userLevel.getName();
        }

        return "普通会员";
    }

    /**
     * 获取订单状态名称
     */
    private String getOrderStatusName(Integer status) {
        if (status == null) {
            return "未知";
        }

        switch (status) {
            case 0:
                return "待支付";
            case 1:
                return "待发货";
            case 2:
                return "待收货";
            case 3:
                return "已完成";
            case 9:
                return "已取消";
            default:
                return "未知";
        }
    }

    /**
     * 导出销售数据
     */
    @Override
    public void exportSalesData(SalesDataRequest request, HttpServletResponse response) throws IOException {
        // 获取所有销售数据（不分页）
        PageParamRequest pageRequest = new PageParamRequest();
        pageRequest.setPage(1);
        pageRequest.setLimit(10000); // 设置一个较大的限制

        PageInfo<SalesDataResponse> pageInfo = getSalesDataList(request, pageRequest);
        List<SalesDataResponse> dataList = pageInfo.getList();

        // 创建Excel工作簿
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("销售数据");

        // 创建标题行
        Row headerRow = sheet.createRow(0);
        String[] headers = { "会员昵称", "手机号", "会员等级", "销售金额", "订单数量", "待结算返现", "已结算返现", "自购金额", "注册时间" };

        // 设置标题样式
        CellStyle headerStyle = workbook.createCellStyle();
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);
        headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }

        // 填充数据行
        for (int i = 0; i < dataList.size(); i++) {
            Row row = sheet.createRow(i + 1);
            SalesDataResponse data = dataList.get(i);

            row.createCell(0).setCellValue(data.getNickname() != null ? data.getNickname() : "");
            row.createCell(1).setCellValue(data.getPhone() != null ? data.getPhone() : "");
            row.createCell(2).setCellValue(data.getMemberLevel() != null ? data.getMemberLevel() : "");
            row.createCell(3).setCellValue(data.getSalesAmount() != null ? data.getSalesAmount().doubleValue() : 0.0);
            row.createCell(4).setCellValue(data.getOrderCount() != null ? data.getOrderCount() : 0);
            row.createCell(5).setCellValue(
                    data.getPendingBrokerageAmount() != null ? data.getPendingBrokerageAmount().doubleValue() : 0.0);
            row.createCell(6).setCellValue(
                    data.getSettledBrokerageAmount() != null ? data.getSettledBrokerageAmount().doubleValue() : 0.0);
            row.createCell(7).setCellValue(
                    data.getSelfPurchaseAmount() != null ? data.getSelfPurchaseAmount().doubleValue() : 0.0);
            row.createCell(8).setCellValue(data.getCreateTime() != null ? data.getCreateTime() : "");
        }

        // 自动调整列宽
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }

        // 设置响应头
        String fileName = "销售数据_" + DateUtil.dateToStr(new Date(), "yyyyMMdd_HHmmss") + ".xlsx";
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));

        // 写入响应流
        workbook.write(response.getOutputStream());
        workbook.close();
    }
}
