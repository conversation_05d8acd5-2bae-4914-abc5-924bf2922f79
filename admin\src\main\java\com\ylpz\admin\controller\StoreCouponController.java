package com.ylpz.admin.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ylpz.core.common.page.CommonPage;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.core.common.request.SearchAndPageRequest;
import com.ylpz.core.common.request.StoreCouponRequest;
import com.ylpz.core.common.request.StoreCouponSearchRequest;
import com.ylpz.core.common.response.CommonResult;
import com.ylpz.core.common.response.StoreCouponInfoResponse;
import com.ylpz.core.common.response.StoreCouponResponse;
import com.ylpz.core.service.StoreCouponService;
import com.ylpz.core.service.StoreProductService;
import com.ylpz.core.service.SystemUserLevelService;
import com.ylpz.core.service.UserService;
import com.ylpz.core.service.UserTagService;
import com.ylpz.model.coupon.StoreCoupon;
import com.ylpz.model.product.StoreProduct;
import com.ylpz.model.system.SystemUserLevel;
import com.ylpz.model.user.UserTag;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;


/**
 * 优惠券表 前端控制器
 */
@Slf4j
@RestController
@RequestMapping("api/admin/marketing/coupon")
@Api(tags = "营销 -- 优惠券")
public class StoreCouponController {

    @Autowired
    private StoreCouponService storeCouponService;
    
    @Autowired
    private StoreProductService storeProductService;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private SystemUserLevelService systemUserLevelService;
    
    @Autowired
    private UserTagService userTagService;

    /**
     * 分页显示优惠券表
     * @param request 搜索条件
     * @param pageParamRequest 分页参数
     * @return 优惠券列表结果
     */
    @PreAuthorize("hasAuthority('admin:coupon:list')")
    @ApiOperation(value = "分页列表", notes = "支持按优惠券名称、类型、状态、会员等级ID筛选")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "name", value = "优惠券名称", paramType = "query"),
        @ApiImplicitParam(name = "type", value = "优惠券类型：1-满减券，2-新人专享券，3-会员专享券", paramType = "query"),
        @ApiImplicitParam(name = "status", value = "状态：false-关闭，true-开启", paramType = "query"),
        @ApiImplicitParam(name = "memberLevelId", value = "会员等级ID", paramType = "query")
    })
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public CommonResult<CommonPage<StoreCouponResponse>> getList(@Validated StoreCouponSearchRequest request, @Validated PageParamRequest pageParamRequest) {
        CommonPage<StoreCouponResponse> storeCouponCommonPage = CommonPage.restPage(storeCouponService.getListWithUseInfo(request, pageParamRequest));
        return CommonResult.success(storeCouponCommonPage);
    }

    /**
     * 保存优惠券表
     * @param request StoreCouponRequest 新增参数
     */
    @PreAuthorize("hasAuthority('admin:coupon:save')")
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public CommonResult<String> save(@RequestBody @Validated StoreCouponRequest request) {
        if (storeCouponService.create(request)) {
            return CommonResult.success();
        } else {
            return CommonResult.failed();
        }
    }
    
    /**
     * 修改优惠券
     * @param id 优惠券ID
     * @param request StoreCouponRequest 修改参数
     */
    @ApiOperation(value = "修改")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "优惠券ID", required = true, dataType = "int", paramType = "query")
    })
    public CommonResult<String> update(@RequestParam Integer id, @RequestBody @Validated StoreCouponRequest request) {
        if (storeCouponService.update(id, request)) {
            return CommonResult.success("修改成功");
        } else {
            return CommonResult.failed("修改失败");
        }
    }

    /**
     * 是否有效
     * @param id integer id
     */
    @PreAuthorize("hasAuthority('admin:coupon:update:status')")
    @ApiOperation(value = "修改状态")
    @RequestMapping(value = "/update/status", method = RequestMethod.POST)
    public CommonResult<String> updateStatus(@RequestParam Integer id, @RequestParam Boolean status) {
        if (storeCouponService.updateStatus(id, status)) {
            return CommonResult.success();
        } else {
            return CommonResult.failed();
        }
    }

    /**
     * 详情
     * @param id integer id
     */
    @PreAuthorize("hasAuthority('admin:coupon:info')")
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/info", method = RequestMethod.POST)
    @ApiImplicitParam(name="id", value="优惠券ID", required = true)
    public CommonResult<StoreCouponInfoResponse> info(@RequestParam Integer id) {
        return CommonResult.success(storeCouponService.info(id));
    }

    /**
     * 发送优惠券列表
     * @param searchAndPageRequest 搜索分页参数
     */
    @PreAuthorize("hasAuthority('admin:coupon:send:list')")
    @ApiOperation(value = "发送优惠券列表")
    @RequestMapping(value = "/send/list", method = RequestMethod.GET)
    public CommonResult<CommonPage<StoreCoupon>>  getSendList(@Validated SearchAndPageRequest searchAndPageRequest) {
        CommonPage<StoreCoupon> storeCouponCommonPage = CommonPage.restPage(storeCouponService.getSendList(searchAndPageRequest));
        return CommonResult.success(storeCouponCommonPage);
    }

    /**
     * 删除优惠券
     * @param id 优惠券id
     */
    @PreAuthorize("hasAuthority('admin:coupon:delete')")
    @ApiOperation(value = "删除优惠券")
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public CommonResult<StoreCouponInfoResponse> delete(@RequestParam Integer id) {
        if (storeCouponService.delete(id)) {
            return CommonResult.success("删除成功");
        } else {
            return CommonResult.failed("删除失败");
        }
    }

    /**
     * 获取商品列表，用于选择指定商品
     */
    @PreAuthorize("hasAuthority('admin:coupon:product:list')")
    @ApiOperation(value = "获取商品列表")
    @RequestMapping(value = "/product/list", method = RequestMethod.GET)
    public CommonResult<List<StoreProduct>> getProductList(@RequestParam(value = "keywords", required = false) String keywords) {
        return CommonResult.success(storeProductService.getProductListByKeywords(keywords));
    }

    /**
     * 获取会员等级列表
     */
    //@PreAuthorize("hasAuthority('admin:coupon:user:level:list')")
    @ApiOperation(value = "获取会员等级列表")
    @RequestMapping(value = "/user/level/list", method = RequestMethod.GET)
    public CommonResult<List<SystemUserLevel>> getUserLevelList() {
        return CommonResult.success(systemUserLevelService.findAllList());
    }
    
    /**
     * 获取用户标签列表
     */
    //@PreAuthorize("hasAuthority('admin:coupon:user:tag:list')")
    @ApiOperation(value = "获取用户标签列表")
    @RequestMapping(value = "/user/tag/list", method = RequestMethod.GET)
    public CommonResult<List<UserTag>> getUserTagList() {
        return CommonResult.success(userTagService.getAllList());
    }
}



