package com.ylpz.core.common.request;

import com.ylpz.core.validation.FictiValidator;
import com.ylpz.core.validation.FreightTypeValidator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 商品批量设置请求对象
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="StoreProductBatchSettingRequest对象", description="商品批量设置请求对象")
@FictiValidator(message = "当设置虚拟销量时，虚拟销量值不能为空")
@FreightTypeValidator(message = "当设置快递方式时，必须正确设置运费参数")
public class StoreProductBatchSettingRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "商品ID列表", required = true)
    @NotEmpty(message = "商品ID列表不能为空")
    private List<Integer> ids;

    @ApiModelProperty(value = "是否设置虚拟销量")
    private Boolean setFicti;

    @ApiModelProperty(value = "虚拟销量")
    private Integer ficti;

    @ApiModelProperty(value = "是否设置快递方式")
    private Boolean setFreightType;

    @ApiModelProperty(value = "运费类型：0-统一邮费，1-运费模板")
    private Integer freightType;

    @ApiModelProperty(value = "邮费")
    private BigDecimal postage;

    @ApiModelProperty(value = "运费模板ID")
    private Integer tempId;
} 