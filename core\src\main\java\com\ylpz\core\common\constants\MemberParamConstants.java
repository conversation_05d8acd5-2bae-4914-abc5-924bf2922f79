package com.ylpz.core.common.constants;

/**
 * 会员参数设置常量类
 */
public class MemberParamConstants {

    /** 会员参数配置类型—成长值设置 */
    public static final Integer CONFIG_TYPE_EXPERIENCE = 1;
    
    /** 会员参数配置类型—成长值设置（别名，兼容旧代码） */
    public static final Integer CONFIG_TYPE_GROWTH = CONFIG_TYPE_EXPERIENCE;

    /** 会员参数配置类型—提现设置 */
    public static final Integer CONFIG_TYPE_WITHDRAW = 2;

    /** 会员参数配置类型—佣金返现设置 */
    public static final Integer CONFIG_TYPE_COMMISSION = 3;
    
    /** 会员参数配置类型—奖励金设置 */
    public static final Integer CONFIG_TYPE_BONUS = 4;

    /** 成长值来源—自购商品 */
    public static final String EXPERIENCE_SOURCE_ORDER = "自购商品";
    
    /** 成长值来源—推广金额 */
    public static final String EXPERIENCE_SOURCE_PROMOTION = "推广金额";
    
    /** 成长值来源—会员充值 */
    public static final String EXPERIENCE_SOURCE_RECHARGE = "会员充值";
    
    /** 所有成长值来源类型数组 */
    public static final String[] EXPERIENCE_SOURCE_TYPES = {
        EXPERIENCE_SOURCE_ORDER,
        EXPERIENCE_SOURCE_PROMOTION,
        EXPERIENCE_SOURCE_RECHARGE
    };
    
    /** 提现类型—SVIP会员 */
    public static final String WITHDRAW_TYPE_SVIP = "SVIP会员";
    
    /** 提现类型—VIP会员 */
    public static final String WITHDRAW_TYPE_VIP = "VIP会员";
    
    /** 提现类型—普通会员 */
    public static final String WITHDRAW_TYPE_NORMAL = "普通会员";
    
    /** 所有提现类型数组 */
    public static final String[] WITHDRAW_TYPES = {
        WITHDRAW_TYPE_SVIP,
        WITHDRAW_TYPE_VIP,
        WITHDRAW_TYPE_NORMAL
    };
    
    /** 奖励金来源—推广普通会员升级为VIP */
    public static final String BONUS_SOURCE_UPGRADE = "推广普通会员升级为VIP";
    
    /** 奖励金来源—推广会员充值 */
    public static final String BONUS_SOURCE_RECHARGE = "推广会员充值";
    
    /** 奖励金来源—推广新用户首单购买 */
    public static final String BONUS_SOURCE_FIRST_ORDER = "推广新用户首单购买";
    
    /** 奖励金来源—周排行榜奖励 */
    public static final String BONUS_SOURCE_WEEKLY_RANK = "周排行榜奖励";
    
    /** 奖励金来源—月排行榜奖励 */
    public static final String BONUS_SOURCE_MONTHLY_RANK = "月排行榜奖励";
    
    /** 奖励金来源—季排行榜奖励 */
    public static final String BONUS_SOURCE_QUARTERLY_RANK = "季排行榜奖励";
    
    /** 所有奖励金来源类型数组 */
    public static final String[] BONUS_SOURCE_TYPES = {
        BONUS_SOURCE_UPGRADE,
        BONUS_SOURCE_RECHARGE,
        BONUS_SOURCE_FIRST_ORDER,
        BONUS_SOURCE_WEEKLY_RANK,
        BONUS_SOURCE_MONTHLY_RANK,
        BONUS_SOURCE_QUARTERLY_RANK
    };
} 