-- 销售数据功能测试数据脚本

-- 插入测试用户数据（SVIP会员）
INSERT INTO user (
    account, nickname, phone, level, status, is_promoter, 
    now_money, brokerage_price, integral, experience, 
    create_time, update_time
) VALUES 
('svip001', '张三', '***********', 2, 1, 1, 5000.00, 1000.00, 500, 1000, NOW(), NOW()),
('svip002', '李四', '***********', 2, 1, 1, 3000.00, 800.00, 300, 800, NOW(), NOW()),
('vip001', '王五', '***********', 1, 1, 1, 2000.00, 500.00, 200, 500, NOW(), NOW()),
('vip002', '赵六', '***********', 1, 1, 1, 1500.00, 300.00, 150, 300, NOW(), NOW()),
('normal001', '钱七', '***********', 0, 1, 0, 1000.00, 100.00, 100, 100, NOW(), NOW())
ON DUPLICATE KEY UPDATE 
    nickname = VALUES(nickname),
    phone = VALUES(phone),
    level = VALUES(level),
    update_time = NOW();

-- 获取插入的用户ID（假设从1000开始）
SET @svip_user1 = (SELECT id FROM user WHERE phone = '***********' LIMIT 1);
SET @svip_user2 = (SELECT id FROM user WHERE phone = '***********' LIMIT 1);
SET @vip_user1 = (SELECT id FROM user WHERE phone = '***********' LIMIT 1);
SET @vip_user2 = (SELECT id FROM user WHERE phone = '***********' LIMIT 1);
SET @normal_user1 = (SELECT id FROM user WHERE phone = '***********' LIMIT 1);

-- 插入测试商品数据
INSERT INTO store_product (
    store_name, keyword, cate_id, price, ot_price, vip_price, 
    postage, unit_name, sort, sales, stock, is_show, is_del, 
    is_postage, is_sub, temp_id, spec_type, activity, 
    create_time, update_time
) VALUES 
('测试商品A', '测试,商品', 1, 299.00, 399.00, 279.00, 0.00, '件', 1, 100, 1000, 1, 0, 1, 0, 1, 0, '', NOW(), NOW()),
('测试商品B', '测试,商品', 1, 199.00, 299.00, 179.00, 0.00, '件', 2, 50, 500, 1, 0, 1, 0, 1, 0, '', NOW(), NOW()),
('测试商品C', '测试,商品', 1, 99.00, 149.00, 89.00, 0.00, '件', 3, 200, 2000, 1, 0, 1, 0, 1, 0, '', NOW(), NOW())
ON DUPLICATE KEY UPDATE 
    store_name = VALUES(store_name),
    price = VALUES(price),
    update_time = NOW();

-- 获取插入的商品ID
SET @product1 = (SELECT id FROM store_product WHERE store_name = '测试商品A' LIMIT 1);
SET @product2 = (SELECT id FROM store_product WHERE store_name = '测试商品B' LIMIT 1);
SET @product3 = (SELECT id FROM store_product WHERE store_name = '测试商品C' LIMIT 1);

-- 插入测试订单数据
-- SVIP用户1的订单
INSERT INTO store_order (
    order_id, uid, real_name, user_phone, user_address, cart_id, 
    freight_price, total_num, total_price, total_postage, pay_price, 
    pay_postage, deduction_price, coupon_id, coupon_price, paid, 
    pay_time, pay_type, status, refund_status, refund_reason_wap_img, 
    refund_reason_wap_explain, refund_reason_time, refund_reason, 
    refund_price, delivery_name, delivery_type, delivery_id, gain_integral, 
    use_integral, back_integral, mark, is_del, unique, remark, 
    mer_id, is_mer_check, combination_id, pink_id, cost, 
    verify_code, store_id, shipping_type, is_channel, is_remind, 
    is_system_del, create_time, update_time
) VALUES 
(CONCAT('ORDER', DATE_FORMAT(NOW(), '%Y%m%d'), '001'), @svip_user1, '张三', '***********', '测试地址1', '', 
0.00, 2, 598.00, 0.00, 558.00, 0.00, 40.00, 0, 0.00, 1, 
NOW(), 'weixin', 3, 0, '', '', NULL, '', 0.00, '', '', '', 50, 0, 0, '', 0, '', '', 
1, 0, 0, 0, 500.00, '', 1, 1, 0, 0, 0, DATE_SUB(NOW(), INTERVAL 5 DAY), NOW()),

(CONCAT('ORDER', DATE_FORMAT(NOW(), '%Y%m%d'), '002'), @svip_user1, '张三', '***********', '测试地址1', '', 
0.00, 1, 199.00, 0.00, 179.00, 0.00, 20.00, 0, 0.00, 1, 
NOW(), 'weixin', 2, 0, '', '', NULL, '', 0.00, '', '', '', 20, 0, 0, '', 0, '', '', 
1, 0, 0, 0, 170.00, '', 1, 1, 0, 0, 0, DATE_SUB(NOW(), INTERVAL 3 DAY), NOW()),

(CONCAT('ORDER', DATE_FORMAT(NOW(), '%Y%m%d'), '003'), @svip_user1, '张三', '***********', '测试地址1', '', 
0.00, 3, 297.00, 0.00, 267.00, 0.00, 30.00, 0, 0.00, 1, 
NOW(), 'weixin', 1, 0, '', '', NULL, '', 0.00, '', '', '', 30, 0, 0, '', 0, '', '', 
1, 0, 0, 0, 250.00, '', 1, 1, 0, 0, 0, DATE_SUB(NOW(), INTERVAL 1 DAY), NOW());

-- SVIP用户2的订单
INSERT INTO store_order (
    order_id, uid, real_name, user_phone, user_address, cart_id, 
    freight_price, total_num, total_price, total_postage, pay_price, 
    pay_postage, deduction_price, coupon_id, coupon_price, paid, 
    pay_time, pay_type, status, refund_status, refund_reason_wap_img, 
    refund_reason_wap_explain, refund_reason_time, refund_reason, 
    refund_price, delivery_name, delivery_type, delivery_id, gain_integral, 
    use_integral, back_integral, mark, is_del, unique, remark, 
    mer_id, is_mer_check, combination_id, pink_id, cost, 
    verify_code, store_id, shipping_type, is_channel, is_remind, 
    is_system_del, create_time, update_time
) VALUES 
(CONCAT('ORDER', DATE_FORMAT(NOW(), '%Y%m%d'), '004'), @svip_user2, '李四', '***********', '测试地址2', '', 
0.00, 1, 299.00, 0.00, 279.00, 0.00, 20.00, 0, 0.00, 1, 
NOW(), 'weixin', 3, 0, '', '', NULL, '', 0.00, '', '', '', 30, 0, 0, '', 0, '', '', 
1, 0, 0, 0, 260.00, '', 1, 1, 0, 0, 0, DATE_SUB(NOW(), INTERVAL 7 DAY), NOW()),

(CONCAT('ORDER', DATE_FORMAT(NOW(), '%Y%m%d'), '005'), @svip_user2, '李四', '***********', '测试地址2', '', 
0.00, 2, 398.00, 0.00, 358.00, 0.00, 40.00, 0, 0.00, 1, 
NOW(), 'weixin', 1, 0, '', '', NULL, '', 0.00, '', '', '', 40, 0, 0, '', 0, '', '', 
1, 0, 0, 0, 320.00, '', 1, 1, 0, 0, 0, DATE_SUB(NOW(), INTERVAL 2 DAY), NOW());

-- VIP用户的订单
INSERT INTO store_order (
    order_id, uid, real_name, user_phone, user_address, cart_id, 
    freight_price, total_num, total_price, total_postage, pay_price, 
    pay_postage, deduction_price, coupon_id, coupon_price, paid, 
    pay_time, pay_type, status, refund_status, refund_reason_wap_img, 
    refund_reason_wap_explain, refund_reason_time, refund_reason, 
    refund_price, delivery_name, delivery_type, delivery_id, gain_integral, 
    use_integral, back_integral, mark, is_del, unique, remark, 
    mer_id, is_mer_check, combination_id, pink_id, cost, 
    verify_code, store_id, shipping_type, is_channel, is_remind, 
    is_system_del, create_time, update_time
) VALUES 
(CONCAT('ORDER', DATE_FORMAT(NOW(), '%Y%m%d'), '006'), @vip_user1, '王五', '***********', '测试地址3', '', 
0.00, 1, 199.00, 0.00, 179.00, 0.00, 20.00, 0, 0.00, 1, 
NOW(), 'weixin', 3, 0, '', '', NULL, '', 0.00, '', '', '', 20, 0, 0, '', 0, '', '', 
1, 0, 0, 0, 160.00, '', 1, 1, 0, 0, 0, DATE_SUB(NOW(), INTERVAL 4 DAY), NOW());

-- 获取插入的订单ID
SET @order1 = (SELECT id FROM store_order WHERE order_id = CONCAT('ORDER', DATE_FORMAT(NOW(), '%Y%m%d'), '001') LIMIT 1);
SET @order2 = (SELECT id FROM store_order WHERE order_id = CONCAT('ORDER', DATE_FORMAT(NOW(), '%Y%m%d'), '002') LIMIT 1);
SET @order3 = (SELECT id FROM store_order WHERE order_id = CONCAT('ORDER', DATE_FORMAT(NOW(), '%Y%m%d'), '003') LIMIT 1);
SET @order4 = (SELECT id FROM store_order WHERE order_id = CONCAT('ORDER', DATE_FORMAT(NOW(), '%Y%m%d'), '004') LIMIT 1);
SET @order5 = (SELECT id FROM store_order WHERE order_id = CONCAT('ORDER', DATE_FORMAT(NOW(), '%Y%m%d'), '005') LIMIT 1);
SET @order6 = (SELECT id FROM store_order WHERE order_id = CONCAT('ORDER', DATE_FORMAT(NOW(), '%Y%m%d'), '006') LIMIT 1);

-- 插入订单详情数据
INSERT INTO store_order_info (
    order_id, product_id, product_name, product_sku, product_type, 
    image, pay_num, price, ot_price, vip_price, integral, 
    gain_integral, product_price, total_price, refund_num, 
    is_del, unique, cart_info, create_time, update_time
) VALUES 
-- 订单1的商品详情
(@order1, @product1, '测试商品A', 'SKU001', 0, '/images/product1.jpg', 2, 299.00, 399.00, 279.00, 0, 50, 279.00, 558.00, 0, 0, '', '', NOW(), NOW()),

-- 订单2的商品详情
(@order2, @product2, '测试商品B', 'SKU002', 0, '/images/product2.jpg', 1, 199.00, 299.00, 179.00, 0, 20, 179.00, 179.00, 0, 0, '', '', NOW(), NOW()),

-- 订单3的商品详情
(@order3, @product3, '测试商品C', 'SKU003', 0, '/images/product3.jpg', 3, 99.00, 149.00, 89.00, 0, 30, 89.00, 267.00, 0, 0, '', '', NOW(), NOW()),

-- 订单4的商品详情
(@order4, @product1, '测试商品A', 'SKU001', 0, '/images/product1.jpg', 1, 299.00, 399.00, 279.00, 0, 30, 279.00, 279.00, 0, 0, '', '', NOW(), NOW()),

-- 订单5的商品详情
(@order5, @product2, '测试商品B', 'SKU002', 0, '/images/product2.jpg', 2, 199.00, 299.00, 179.00, 0, 40, 179.00, 358.00, 0, 0, '', '', NOW(), NOW()),

-- 订单6的商品详情
(@order6, @product2, '测试商品B', 'SKU002', 0, '/images/product2.jpg', 1, 199.00, 299.00, 179.00, 0, 20, 179.00, 179.00, 0, 0, '', '', NOW(), NOW());

-- 更新用户的消费统计
UPDATE user SET 
    pay_count = (SELECT COUNT(*) FROM store_order WHERE uid = user.id AND paid = 1),
    total_consume_money = (SELECT COALESCE(SUM(pay_price), 0) FROM store_order WHERE uid = user.id AND paid = 1),
    update_time = NOW()
WHERE id IN (@svip_user1, @svip_user2, @vip_user1, @vip_user2, @normal_user1);

-- 插入一些佣金记录（用于测试）
INSERT INTO user_brokerage_record (
    uid, link_id, link_type, type, title, price, balance, mark, 
    status, frozen_time, thaw_time, brokerage_level, create_time, update_time
) VALUES 
(@svip_user1, CONCAT('ORDER', DATE_FORMAT(NOW(), '%Y%m%d'), '001'), 'order', 1, '订单佣金', 55.80, 1055.80, '订单佣金返现', 3, 0, NOW(), 1, NOW(), NOW()),
(@svip_user2, CONCAT('ORDER', DATE_FORMAT(NOW(), '%Y%m%d'), '004'), 'order', 1, '订单佣金', 27.90, 827.90, '订单佣金返现', 3, 0, NOW(), 1, NOW(), NOW()),
(@vip_user1, CONCAT('ORDER', DATE_FORMAT(NOW(), '%Y%m%d'), '006'), 'order', 1, '订单佣金', 10.74, 510.74, '订单佣金返现', 3, 0, NOW(), 1, NOW(), NOW());

COMMIT;

-- 验证数据插入
SELECT '=== 用户数据 ===' AS info;
SELECT id, nickname, phone, level, 
    CASE 
        WHEN level = 0 OR level IS NULL THEN '普通会员'
        WHEN level = 1 THEN 'VIP会员'
        WHEN level = 2 THEN 'SVIP会员'
        ELSE '普通会员'
    END AS member_level,
    now_money, brokerage_price
FROM user 
WHERE phone IN ('***********', '***********', '***********', '***********', '***********');

SELECT '=== 订单数据 ===' AS info;
SELECT o.order_id, u.nickname, u.phone, o.total_price, o.pay_price, o.status,
    CASE 
        WHEN o.status = 0 THEN '待支付'
        WHEN o.status = 1 THEN '待发货'
        WHEN o.status = 2 THEN '待收货'
        WHEN o.status = 3 THEN '已完成'
        WHEN o.status = 9 THEN '已取消'
        ELSE '未知'
    END AS order_status
FROM store_order o
JOIN user u ON o.uid = u.id
WHERE u.phone IN ('***********', '***********', '***********', '***********', '***********')
ORDER BY o.create_time DESC;
