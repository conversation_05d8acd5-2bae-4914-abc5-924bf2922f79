package com.ylpz.admin.controller;

import com.github.pagehelper.PageInfo;
import com.ylpz.core.common.request.CommissionRecordRequest;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.core.common.response.CommonResult;
import com.ylpz.core.service.UserCommissionRecordService;
import com.ylpz.model.user.UserBrokerageRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

/**
 * 佣金返现控制器
 */
@Slf4j
@RestController
@RequestMapping("api/admin/finance/commission")
@Api(tags = "财务 -- 佣金返现")
public class UserCommissionController {

    @Autowired
    private UserCommissionRecordService userCommissionRecordService;

    /**
     * 获取佣金返现记录列表
     */
    @PreAuthorize("hasAuthority('admin:finance:commission:list')")
    @ApiOperation(value = "佣金返现记录列表")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public CommonResult<PageInfo<UserBrokerageRecord>> getCommissionList(
            @ModelAttribute CommissionRecordRequest request,
            @ModelAttribute PageParamRequest pageParamRequest) {
        PageInfo<UserBrokerageRecord> pageInfo = userCommissionRecordService.getCommissionList(request,
                pageParamRequest);
        return CommonResult.success(pageInfo);
    }

    /**
     * 获取佣金返现合计金额
     */
    @PreAuthorize("hasAuthority('admin:finance:commission:total')")
    @ApiOperation(value = "佣金返现合计金额")
    @RequestMapping(value = "/total", method = RequestMethod.GET)
    public CommonResult<BigDecimal> getCommissionTotal(@ModelAttribute CommissionRecordRequest request) {
        BigDecimal total = userCommissionRecordService.getCommissionTotal(request);
        return CommonResult.success(total);
    }

    /**
     * 获取用户佣金返现统计
     */
    @PreAuthorize("hasAuthority('admin:finance:commission:statistics')")
    @ApiOperation(value = "用户佣金返现统计")
    @RequestMapping(value = "/statistics/{uid}", method = RequestMethod.GET)
    public CommonResult<UserBrokerageRecord> getUserCommissionStatistics(@PathVariable Integer uid) {
        UserBrokerageRecord statistics = userCommissionRecordService.getUserCommissionStatistics(uid);
        return CommonResult.success(statistics);
    }
}