package com.ylpz.core.common.request;

import java.util.Date;
import java.util.List;

import javax.validation.constraints.NotBlank;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "SystemThemeRequest对象", description = "系统主题请求对象")
public class SystemThemeRequest {

    @ApiModelProperty(value = "主题ID")
    private Integer id;

    @NotBlank(message = "主题名称不能为空")
    @ApiModelProperty(value = "主题名称", required = true)
    private String name;

    @NotBlank(message = "背景图片不能为空")
    @ApiModelProperty(value = "背景图片路径", required = true)
    private String backgroundImage;

    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    @ApiModelProperty(value = "是否默认主题")
    private Boolean isDefault = false;

    @ApiModelProperty(value = "状态 0=禁用 1=启用")
    private Boolean status = true;

    @ApiModelProperty(value = "排序")
    private Integer sort = 0;

    @ApiModelProperty(value = "主题图标列表")
    private List<SystemThemeIconRequest> icons;
}