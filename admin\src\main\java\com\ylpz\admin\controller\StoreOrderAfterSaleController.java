package com.ylpz.admin.controller;

import com.ylpz.core.common.request.StoreOrderAddressAuditRequest;
import org.hibernate.validator.constraints.Range;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ylpz.core.common.page.CommonPage;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.core.common.request.StoreOrderRefundRequest;
import com.ylpz.core.common.request.StoreOrderSearchRequest;
import com.ylpz.core.common.request.StoreOrderSendRequest;
import com.ylpz.core.common.response.CommonResult;
import com.ylpz.core.common.response.StoreOrderCountItemResponse;
import com.ylpz.core.common.response.StoreOrderDetailResponse;
import com.ylpz.core.common.response.StoreOrderInfoResponse;
import com.ylpz.core.service.StoreOrderService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * 售后表 前端控制器
 */
@Slf4j
@RestController
@RequestMapping("api/admin/store/after-sale")
@Api(tags = "售后")
public class StoreOrderAfterSaleController {

    @Autowired
    private StoreOrderService storeOrderService;

    /**
     * 分页显示售后列表
     *
     * @param request 搜索条件
     * @param pageParamRequest 分页参数
     */
    @PreAuthorize("hasAuthority('admin:order:after:sale:list')")
    @ApiOperation(value = "分页列表") // 配合swagger使用
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public CommonResult<CommonPage<StoreOrderDetailResponse>> getList(@Validated StoreOrderSearchRequest request,
        @Validated PageParamRequest pageParamRequest) {
        // 设置查询条件，只查询售后相关的订单
        if (request.getStatus() == null || request.getStatus().isEmpty()) {
            request.setStatus("refunding"); // 默认查询退款中状态
        }
        return CommonResult.success(storeOrderService.getAdminList(request, pageParamRequest));
    }

    /**
     * 获取售后各状态数量
     */
    @PreAuthorize("hasAuthority('admin:order:after:sale:status:num')")
    @ApiOperation(value = "获取售后各状态数量")
    @RequestMapping(value = "/status/num", method = RequestMethod.GET)
    public CommonResult<StoreOrderCountItemResponse> getOrderStatusNum(
        @RequestParam(value = "dateLimit", defaultValue = "") String dateLimit,
        @RequestParam(value = "type", defaultValue = "2") @Range(min = 0, max = 2, message = "未知的订单类型") Integer type) {
        return CommonResult.success(storeOrderService.getOrderStatusNum(dateLimit, type));
    }

    /**
     * 临期待处理订单
     */
    @PreAuthorize("hasAuthority('admin:order:after:sale:list')")
    @ApiOperation(value = "临期待处理订单")
    @RequestMapping(value = "/list/near-expire", method = RequestMethod.GET)
    public CommonResult<CommonPage<StoreOrderDetailResponse>>
        getNearExpireList(@Validated StoreOrderSearchRequest request, @Validated PageParamRequest pageParamRequest) {
        // 设置查询条件，查询退款中且创建时间较早的订单
        request.setStatus("refunding");
        // 可以使用dateLimit来限制时间范围，例如7天前以上的订单
        if (request.getDateLimit() == null || request.getDateLimit().isEmpty()) {
            request.setDateLimit("lately7"); // 最近7天的订单
        }
        return CommonResult.success(storeOrderService.getAdminList(request, pageParamRequest));
    }

    /**
     * 未发货退款待处理订单
     */
    @PreAuthorize("hasAuthority('admin:order:after:sale:list')")
    @ApiOperation(value = "未发货退款待处理订单")
    @RequestMapping(value = "/list/refund-not-shipped", method = RequestMethod.GET)
    public CommonResult<CommonPage<StoreOrderDetailResponse>> getRefundNotShippedList(
        @Validated StoreOrderSearchRequest request, @Validated PageParamRequest pageParamRequest) {
        // 设置查询条件，查询未发货且退款中的订单
        request.setStatus("refunding");
        request.setExpressNo(""); // 快递单号为空表示未发货
        return CommonResult.success(storeOrderService.getAdminList(request, pageParamRequest));
    }

    /**
     * 已发货退款待处理订单
     */
    @PreAuthorize("hasAuthority('admin:order:after:sale:list')")
    @ApiOperation(value = "已发货退款待处理订单")
    @RequestMapping(value = "/list/refund-shipped", method = RequestMethod.GET)
    public CommonResult<CommonPage<StoreOrderDetailResponse>>
        getRefundShippedList(@Validated StoreOrderSearchRequest request, @Validated PageParamRequest pageParamRequest) {
        // 设置查询条件，查询已发货且退款中的订单
        request.setStatus("refunding");
        // 确保快递单号不为空
        if (request.getExpressNo() == null) {
            request.setExpressNo("NOT_NULL");
        }
        return CommonResult.success(storeOrderService.getAdminList(request, pageParamRequest));
    }

    /**
     * 售后订单详情
     */
    @PreAuthorize("hasAuthority('admin:order:after:sale:detail')")
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    public CommonResult<StoreOrderInfoResponse> info(@RequestParam(value = "orderNo") String orderNo) {
        return CommonResult.success(storeOrderService.info(orderNo));
    }

    /**
     * 同意退款
     */
    @PreAuthorize("hasAuthority('admin:order:refund')")
    @ApiOperation(value = "同意退款")
    @RequestMapping(value = "/agree/refund", method = RequestMethod.GET)
    public CommonResult<Boolean> agreeRefund(@Validated StoreOrderRefundRequest request) {
        return CommonResult.success(storeOrderService.refund(request));
    }

    /**
     * 拒绝退款
     */
    @PreAuthorize("hasAuthority('admin:order:refund:refuse')")
    @ApiOperation(value = "拒绝退款")
    @RequestMapping(value = "/refuse", method = RequestMethod.GET)
    public CommonResult<Object> refuseRefund(@RequestParam String orderNo, @RequestParam String reason) {
        if (storeOrderService.refundRefuse(orderNo, reason)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 订单备注
     */
    @PreAuthorize("hasAuthority('admin:order:mark')")
    @ApiOperation(value = "订单备注")
    @RequestMapping(value = "/mark", method = RequestMethod.POST)
    public CommonResult<String> mark(@RequestParam String orderNo, @RequestParam String mark) {
        if (storeOrderService.mark(orderNo, mark)) {
            return CommonResult.success();
        } else {
            return CommonResult.failed();
        }
    }

    /**
     * 发送货（用于换货和补寄）
     */
    @PreAuthorize("hasAuthority('admin:order:send')")
    @ApiOperation(value = "发送货")
    @RequestMapping(value = "/send", method = RequestMethod.POST)
    public CommonResult<Boolean> send(@RequestBody @Validated StoreOrderSendRequest request) {
        if (storeOrderService.send(request)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    @ApiOperation(value = "订单地址审核")
    @RequestMapping(value = "/auditAddress", method = RequestMethod.POST)
    public CommonResult<Boolean> auditAddress(@RequestBody @Validated StoreOrderAddressAuditRequest request) {
        return CommonResult.success(storeOrderService.auditOrderAddress(request.getOrderNo(), request.getStatus()));
    }

    /**
     * 获取待审核地址订单数量
     */
    @ApiOperation(value = "获取待审核地址订单数量")
    @RequestMapping(value = "/auditAddressStatus/num", method = RequestMethod.GET)
    public CommonResult<StoreOrderCountItemResponse> getPendingAddressAuditNum(
            @RequestParam(value = "dateLimit", defaultValue = "") String dateLimit,
            @RequestParam(value = "type", defaultValue = "2") @Range(min = 0, max = 2, message = "未知的订单类型") Integer type) {
        return CommonResult.success(storeOrderService.getPendingAddressAuditNum(dateLimit, type));
    }


    /**
     * 分页显示地址审核订单表
     *  @param request          搜索条件
     * @param pageParamRequest 分页参数
     */
    @PreAuthorize("hasAuthority('admin:order:list')")
    @ApiOperation(value = "分页显示地址审核订单表") //配合swagger使用
    @RequestMapping(value = "/auditAddressList", method = RequestMethod.GET)
    public CommonResult<CommonPage<StoreOrderDetailResponse>> getAuditAddressList(@Validated StoreOrderSearchRequest request, @Validated PageParamRequest pageParamRequest) {
        return CommonResult.success(storeOrderService.getAuditAddressList(request, pageParamRequest));
    }
}