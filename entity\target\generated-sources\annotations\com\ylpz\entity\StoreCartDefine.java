package com.ylpz.entity;

public interface StoreCartDefine {
    String productAttrValue = "productAttrValue";
    String productId = "productId";
    String seckillId = "seckillId";
    String combinationId = "combinationId";
    String updateTime = "updateTime";
    String isNew = "isNew";
    String pinkId = "pinkId";
    String type = "type";
    String cartNum = "cartNum";
    String uid = "uid";
    String storeProduct = "storeProduct";
    String productAttrUnique = "productAttrUnique";
    String createTime = "createTime";
    String bargainId = "bargainId";
    String id = "id";
    String status = "status";
}
