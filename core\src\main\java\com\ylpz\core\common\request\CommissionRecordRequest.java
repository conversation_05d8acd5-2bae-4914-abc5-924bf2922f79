package com.ylpz.core.common.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 佣金返现记录请求对象
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "CommissionRecordRequest对象", description = "佣金返现记录请求对象")
public class CommissionRecordRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户ID")
    private Integer uid;

    @ApiModelProperty(value = "用户手机号")
    private String mobile;

    @ApiModelProperty(value = "佣金返现类型: svip=SVIP会员返现, vip=VIP会员返现, normal=普通会员返现")
    private String commissionType;

    @ApiModelProperty(value = "关联订单号")
    private String linkId;

    @ApiModelProperty(value = "状态：1-已创建，2-冻结期，3-已完成，4-已失效")
    private Integer status;

    @ApiModelProperty(value = "查询开始时间")
    private Date startTime;

    @ApiModelProperty(value = "查询结束时间")
    private Date endTime;
}