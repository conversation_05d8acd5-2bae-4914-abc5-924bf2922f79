(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-79c44995"],{"0818":function(t,e,r){"use strict";r("fd61")},"2f2c":function(t,e,r){"use strict";r.d(e,"b",(function(){return u})),r.d(e,"c",(function(){return f})),r.d(e,"r",(function(){return m})),r.d(e,"d",(function(){return d})),r.d(e,"a",(function(){return p})),r.d(e,"g",(function(){return h})),r.d(e,"h",(function(){return g})),r.d(e,"j",(function(){return v})),r.d(e,"i",(function(){return y})),r.d(e,"e",(function(){return b})),r.d(e,"o",(function(){return w})),r.d(e,"q",(function(){return V})),r.d(e,"l",(function(){return x})),r.d(e,"m",(function(){return _})),r.d(e,"n",(function(){return k})),r.d(e,"p",(function(){return O})),r.d(e,"k",(function(){return j})),r.d(e,"f",(function(){return I}));var a=r("b775");function n(t){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},n(t)}function i(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,a)}return r}function o(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?i(Object(r),!0).forEach((function(e){l(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function l(t,e,r){return(e=s(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function s(t){var e=c(t,"string");return"symbol"==n(e)?e:e+""}function c(t,e){if("object"!=n(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var a=r.call(t,e||"default");if("object"!=n(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function u(t){return Object(a["a"])({url:"/admin/system/city/list",method:"get",params:o({},t)})}function f(){return Object(a["a"])({url:"/admin/system/city/list/tree",method:"get"})}function m(t){return Object(a["a"])({url:"/admin/system/city/update/status",method:"post",params:o({},t)})}function d(t){return Object(a["a"])({url:"/admin/system/city/update",method:"post",params:o({},t)})}function p(t){return Object(a["a"])({url:"/admin/system/city/info",method:"get",params:o({},t)})}function h(t){return Object(a["a"])({url:"/admin/express/list",method:"get",params:o({},t)})}function g(){return Object(a["a"])({url:"/admin/express/sync/express",method:"post"})}function v(t){return Object(a["a"])({url:"/admin/express/update/show",method:"post",data:t})}function y(t){return Object(a["a"])({url:"/admin/express/update",method:"post",data:t})}function b(t){return Object(a["a"])({url:"/admin/express/delete",method:"GET",params:o({},t)})}function w(t){return Object(a["a"])({url:"/admin/express/shipping/templates/list",method:"get",params:o({},t)})}function V(t){return Object(a["a"])({url:"/admin/express/shipping/templates/info",method:"get",params:o({},t)})}function x(t){return Object(a["a"])({url:"/admin/express/shipping/free/list",method:"get",params:o({},t)})}function _(t){return Object(a["a"])({url:"admin/express/shipping/region/list",method:"get",params:o({},t)})}function k(t){return Object(a["a"])({url:"admin/express/shipping/templates/save",method:"post",data:t})}function O(t,e){return Object(a["a"])({url:"admin/express/shipping/templates/update",method:"post",data:t,params:o({},e)})}function j(t){return Object(a["a"])({url:"admin/express/shipping/templates/delete",method:"get",params:t})}function I(t){return Object(a["a"])({url:"admin/express/info",method:"get",params:o({},t)})}},ad7c:function(t,e,r){"use strict";var a=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"Box"},[r("el-card",[r("div",{staticClass:"line-ht"},[t._v("生成的商品默认是没有上架的，请手动上架商品！\n      "),t.copyConfig.copyType&&1==t.copyConfig.copyType?r("span",[t._v("您当前剩余"+t._s(t.copyConfig.copyNum)+"条采集次数，\n        "),r("router-link",{attrs:{to:{path:"/operation/systemSms/pay?type=copy"}}},[r("span",{staticStyle:{color:"#1890ff"}},[t._v("增加采集次数")])])],1):t._e(),t._v(" "),t.copyConfig.copyType&&1!=t.copyConfig.copyType?r("el-link",{attrs:{type:"primary",underline:!1,href:"https://help.crmeb.net/crmeb_java/2103903",target:"_blank"}},[t._v("如何配置密钥\n      ")]):t._e(),t._v(" "),r("br"),t._v("\n      商品采集设置：设置 > 系统设置 > 第三方接口设置 > 采集商品配置（如配置一号通采集，请先登录一号通账号，无一号通，请选择99Api设置）\n    ")],1)]),t._v(" "),r("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],ref:"formValidate",staticClass:"formValidate mt20",attrs:{model:t.formValidate,rules:t.ruleInline,"label-width":"120px"},nativeOn:{submit:function(t){t.preventDefault()}}},[t.copyConfig.copyType&&1!=t.copyConfig.copyType?r("el-form-item",[r("el-radio-group",{model:{value:t.form,callback:function(e){t.form=e},expression:"form"}},[r("el-radio",{attrs:{label:1}},[t._v("淘宝")]),t._v(" "),r("el-radio",{attrs:{label:2}},[t._v("京东")]),t._v(" "),r("el-radio",{attrs:{label:5}},[t._v("天猫")])],1)],1):t._e(),t._v(" "),r("el-row",{attrs:{gutter:24}},[t.copyConfig.copyType?r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"链接地址："}},[r("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入链接地址",size:"small"},model:{value:t.url,callback:function(e){t.url=e},expression:"url"}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:product:copy:product","admin:product:import:product"],expression:"['admin:product:copy:product','admin:product:import:product']"}],attrs:{slot:"append",icon:"el-icon-search",size:"small"},on:{click:t.add},slot:"append"})],1)],1)],1):t._e(),t._v(" "),t.formValidate?r("el-col",[r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"商品名称：",prop:"storeName"}},[r("el-input",{attrs:{maxlength:"249",placeholder:"请输入商品名称"},model:{value:t.formValidate.storeName,callback:function(e){t.$set(t.formValidate,"storeName",e)},expression:"formValidate.storeName"}})],1)],1),t._v(" "),r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"商品简介："}},[r("el-input",{attrs:{maxlength:"250",type:"textarea",rows:3,placeholder:"请输入商品简介"},model:{value:t.formValidate.storeInfo,callback:function(e){t.$set(t.formValidate,"storeInfo",e)},expression:"formValidate.storeInfo"}})],1)],1),t._v(" "),r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"商品分类：",prop:"cateIds"}},[r("el-cascader",{staticClass:"selWidth",attrs:{options:t.merCateList,props:t.props2,clearable:"","show-all-levels":!1},model:{value:t.formValidate.cateIds,callback:function(e){t.$set(t.formValidate,"cateIds",e)},expression:"formValidate.cateIds"}})],1)],1),t._v(" "),r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"商品关键字：",prop:"keyword"}},[r("el-input",{attrs:{placeholder:"请输入商品关键字"},model:{value:t.formValidate.keyword,callback:function(e){t.$set(t.formValidate,"keyword",e)},expression:"formValidate.keyword"}})],1)],1),t._v(" "),r("el-col",t._b({},"el-col",t.grid,!1),[r("el-form-item",{attrs:{label:"单位：",prop:"unitName"}},[r("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入单位"},model:{value:t.formValidate.unitName,callback:function(e){t.$set(t.formValidate,"unitName",e)},expression:"formValidate.unitName"}})],1)],1),t._v(" "),r("el-col",t._b({},"el-col",t.grid,!1),[r("el-form-item",{attrs:{label:"运费模板：",prop:"tempId"}},[r("el-select",{staticClass:"selWidth",attrs:{placeholder:"请选择"},model:{value:t.formValidate.tempId,callback:function(e){t.$set(t.formValidate,"tempId",e)},expression:"formValidate.tempId"}},t._l(t.shippingList,(function(t){return r("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1)],1),t._v(" "),r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"商品封面图：",prop:"image"}},[r("div",{staticClass:"upLoadPicBox",on:{click:function(e){return t.modalPicTap("1")}}},[t.formValidate.image?r("div",{staticClass:"pictrue"},[r("img",{attrs:{src:t.formValidate.image}})]):r("div",{staticClass:"upLoad"},[r("i",{staticClass:"el-icon-camera cameraIconfont"})])])])],1),t._v(" "),r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"商品轮播图："}},[r("div",{staticClass:"acea-row"},t._l(t.formValidate.sliderImages,(function(e,a){return r("div",{key:a,staticClass:"lunBox mr5",attrs:{draggable:"false"},on:{dragstart:function(r){return t.handleDragStart(r,e)},dragover:function(r){return r.preventDefault(),t.handleDragOver(r,e)},dragenter:function(r){return t.handleDragEnter(r,e)},dragend:function(r){return t.handleDragEnd(r,e)}}},[r("div",{staticClass:"pictrue"},[r("img",{attrs:{src:e}})]),t._v(" "),r("el-button-group",[r("el-button",{attrs:{size:"mini"},nativeOn:{click:function(r){return t.checked(e,a)}}},[t._v("主图")]),t._v(" "),r("el-button",{attrs:{size:"mini"},nativeOn:{click:function(e){return t.handleRemove(a)}}},[t._v("移除")])],1)],1)})),0)])],1),t._v(" "),t.formValidate.specType||t.formValidate.attr.length?r("el-col",{staticClass:"noForm",attrs:{span:24}},[r("el-form-item",{staticClass:"labeltop",attrs:{label:"批量设置："}},[r("el-table",{staticClass:"tabNumWidth",attrs:{data:t.oneFormBatch,border:"",size:"mini"}},[r("el-table-column",{attrs:{align:"center",label:"图片","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("div",{staticClass:"upLoadPicBox",on:{click:function(e){return t.modalPicTap("1","pi")}}},[e.row.image?r("div",{staticClass:"pictrue pictrueTab"},[r("img",{attrs:{src:e.row.image}})]):r("div",{staticClass:"upLoad pictrueTab"},[r("i",{staticClass:"el-icon-camera cameraIconfont"})])])]}}],null,!1,3789294462)}),t._v(" "),t._l(t.attrValue,(function(e,a){return r("el-table-column",{key:a,attrs:{label:t.formThead[a].title,align:"center","min-width":"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("el-input",{staticClass:"priceBox",attrs:{type:"商品编号"===t.formThead[a].title?"text":"number",min:0},model:{value:e.row[a],callback:function(r){t.$set(e.row,a,r)},expression:"scope.row[iii]"}})]}}],null,!0)})})),t._v(" "),r("el-table-column",{attrs:{align:"center",label:"操作","min-width":"80"}},[[r("el-button",{staticClass:"submission",attrs:{type:"text"},on:{click:t.batchAdd}},[t._v("批量添加")])]],2)],2)],1)],1):t._e(),t._v(" "),r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"商品规格：",props:"spec_type","label-for":"spec_type"}},[r("el-table",{staticClass:"tabNumWidth",attrs:{data:t.formValidate.attrValue,border:"",size:"mini"}},[t.manyTabDate?t._l(t.manyTabDate,(function(e,a){return r("el-table-column",{key:a,attrs:{align:"center",label:t.manyTabTit[a].title,"min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("span",{staticClass:"priceBox",domProps:{textContent:t._s(e.row[a])}})]}}],null,!0)})})):t._e(),t._v(" "),r("el-table-column",{attrs:{align:"center",label:"图片","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("el-form-item",{attrs:{rules:[{required:!0,message:"请上传图片",trigger:"change"}],prop:"attrValue."+e.$index+".image"}},[r("div",{staticClass:"upLoadPicBox",on:{click:function(r){return t.modalPicTap("1","duo",e.$index)}}},[e.row.image?r("div",{staticClass:"pictrue pictrueTab"},[r("img",{attrs:{src:e.row.image}})]):r("div",{staticClass:"upLoad pictrueTab"},[r("i",{staticClass:"el-icon-camera cameraIconfont"})])])])]}}],null,!1,799156793)}),t._v(" "),t._l(t.attrValue,(function(e,a){return r("el-table-column",{key:a,attrs:{label:t.formThead[a].title,align:"center","min-width":"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("el-form-item",{attrs:{rules:[{required:!0,message:"请输入"+t.formThead[a].title,trigger:"blur"}],prop:"商品编号"!==t.formThead[a].title?"attrValue."+e.$index+"."+a:""}},[r("el-input",{staticClass:"priceBox",attrs:{type:"商品编号"===t.formThead[a].title?"text":"number"},model:{value:e.row[a],callback:function(r){t.$set(e.row,a,r)},expression:"scope.row[iii]"}})],1)]}}],null,!0)})})),t._v(" "),r("el-table-column",{attrs:{align:"center",label:"操作","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("el-button",{staticClass:"submission",attrs:{type:"text"},on:{click:function(r){return t.delAttrTable(e.$index)}}},[t._v("删除")])]}}],null,!1,2803824461)})],2)],1)],1),t._v(" "),r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"商品详情："}},[r("Tinymce",{model:{value:t.formValidate.content,callback:function(e){t.$set(t.formValidate,"content",e)},expression:"formValidate.content"}})],1)],1),t._v(" "),r("el-col",{attrs:{span:24}},[[r("el-row",{attrs:{gutter:24}},[r("el-col",{attrs:{span:24}},[[r("el-row",{attrs:{gutter:24}},[r("el-col",{attrs:{span:8}},[r("el-form-item",{attrs:{label:"排序："}},[r("el-input-number",{attrs:{max:9999,placeholder:"请输入排序",disabled:t.isDisabled},model:{value:t.formValidate.sort,callback:function(e){t.$set(t.formValidate,"sort",e)},expression:"formValidate.sort"}})],1)],1),t._v(" "),r("el-col",{attrs:{span:8}},[r("el-form-item",{attrs:{label:"积分："}},[r("el-input-number",{attrs:{placeholder:"请输入排序",disabled:t.isDisabled},model:{value:t.formValidate.giveIntegral,callback:function(e){t.$set(t.formValidate,"giveIntegral",e)},expression:"formValidate.giveIntegral"}})],1)],1),t._v(" "),r("el-col",{attrs:{span:8}},[r("el-form-item",{attrs:{label:"虚拟销量："}},[r("el-input-number",{attrs:{placeholder:"请输入排序",disabled:t.isDisabled},model:{value:t.formValidate.ficti,callback:function(e){t.$set(t.formValidate,"ficti",e)},expression:"formValidate.ficti"}})],1)],1)],1)]],2),t._v(" "),r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"商品推荐："}},[r("el-checkbox-group",{attrs:{size:"small",disabled:t.isDisabled},on:{change:t.onChangeGroup},model:{value:t.checkboxGroup,callback:function(e){t.checkboxGroup=e},expression:"checkboxGroup"}},t._l(t.recommend,(function(e,a){return r("el-checkbox",{key:a,attrs:{label:e.value}},[t._v(t._s(e.name))])})),1)],1)],1)],1)]],2),t._v(" "),r("el-col",{attrs:{span:24}},[r("el-form-item",[r("el-button",{staticClass:"submission",attrs:{type:"primary",loading:t.modal_loading},on:{click:function(e){return t.handleSubmit("formValidate")}}},[t._v("提交\n            ")])],1)],1)],1):t._e()],1)],1)],1)},n=[],i=r("73f5"),o=r("e7ac"),l=r("8256"),s=r("2f2c"),c=r("61f7");function u(t){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},u(t)}function f(t){return p(t)||d(t)||x(t)||m()}function m(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function d(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function p(t){if(Array.isArray(t))return _(t)}function h(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */h=function(){return e};var t,e={},r=Object.prototype,a=r.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",l=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function c(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(t){c=function(t,e,r){return t[e]=r}}function f(t,e,r,a){var i=e&&e.prototype instanceof b?e:b,o=Object.create(i.prototype),l=new E(a||[]);return n(o,"_invoke",{value:C(t,r,l)}),o}function m(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var d="suspendedStart",p="suspendedYield",g="executing",v="completed",y={};function b(){}function w(){}function V(){}var x={};c(x,o,(function(){return this}));var _=Object.getPrototypeOf,k=_&&_(_($([])));k&&k!==r&&a.call(k,o)&&(x=k);var O=V.prototype=b.prototype=Object.create(x);function j(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function I(t,e){function r(n,i,o,l){var s=m(t[n],t,i);if("throw"!==s.type){var c=s.arg,f=c.value;return f&&"object"==u(f)&&a.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,o,l)}),(function(t){r("throw",t,o,l)})):e.resolve(f).then((function(t){c.value=t,o(c)}),(function(t){return r("throw",t,o,l)}))}l(s.arg)}var i;n(this,"_invoke",{value:function(t,a){function n(){return new e((function(e,n){r(t,a,e,n)}))}return i=i?i.then(n,n):n()}})}function C(e,r,a){var n=d;return function(i,o){if(n===g)throw Error("Generator is already running");if(n===v){if("throw"===i)throw o;return{value:t,done:!0}}for(a.method=i,a.arg=o;;){var l=a.delegate;if(l){var s=T(l,a);if(s){if(s===y)continue;return s}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(n===d)throw n=v,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);n=g;var c=m(e,r,a);if("normal"===c.type){if(n=a.done?v:p,c.arg===y)continue;return{value:c.arg,done:a.done}}"throw"===c.type&&(n=v,a.method="throw",a.arg=c.arg)}}}function T(e,r){var a=r.method,n=e.iterator[a];if(n===t)return r.delegate=null,"throw"===a&&e.iterator.return&&(r.method="return",r.arg=t,T(e,r),"throw"===r.method)||"return"!==a&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+a+"' method")),y;var i=m(n,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,y;var o=i.arg;return o?o.done?(r[e.resultName]=o.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,y):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function N(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function S(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function E(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(N,this),this.reset(!0)}function $(e){if(e||""===e){var r=e[o];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,i=function r(){for(;++n<e.length;)if(a.call(e,n))return r.value=e[n],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(u(e)+" is not iterable")}return w.prototype=V,n(O,"constructor",{value:V,configurable:!0}),n(V,"constructor",{value:w,configurable:!0}),w.displayName=c(V,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,V):(t.__proto__=V,c(t,s,"GeneratorFunction")),t.prototype=Object.create(O),t},e.awrap=function(t){return{__await:t}},j(I.prototype),c(I.prototype,l,(function(){return this})),e.AsyncIterator=I,e.async=function(t,r,a,n,i){void 0===i&&(i=Promise);var o=new I(f(t,r,a,n),i);return e.isGeneratorFunction(r)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},j(O),c(O,s,"Generator"),c(O,o,(function(){return this})),c(O,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var a in e)r.push(a);return r.reverse(),function t(){for(;r.length;){var a=r.pop();if(a in e)return t.value=a,t.done=!1,t}return t.done=!0,t}},e.values=$,E.prototype={constructor:E,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(S),!e)for(var r in this)"t"===r.charAt(0)&&a.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function n(a,n){return l.type="throw",l.arg=e,r.next=a,n&&(r.method="next",r.arg=t),!!n}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],l=o.completion;if("root"===o.tryLoc)return n("end");if(o.tryLoc<=this.prev){var s=a.call(o,"catchLoc"),c=a.call(o,"finallyLoc");if(s&&c){if(this.prev<o.catchLoc)return n(o.catchLoc,!0);if(this.prev<o.finallyLoc)return n(o.finallyLoc)}else if(s){if(this.prev<o.catchLoc)return n(o.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return n(o.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&a.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var i=n;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=t,o.arg=e,i?(this.method="next",this.next=i.finallyLoc,y):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),y},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),S(r),y}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var a=r.completion;if("throw"===a.type){var n=a.arg;S(r)}return n}}throw Error("illegal catch attempt")},delegateYield:function(e,r,a){return this.delegate={iterator:$(e),resultName:r,nextLoc:a},"next"===this.method&&(this.arg=t),y}},e}function g(t,e,r,a,n,i,o){try{var l=t[i](o),s=l.value}catch(t){return void r(t)}l.done?e(s):Promise.resolve(s).then(a,n)}function v(t){return function(){var e=this,r=arguments;return new Promise((function(a,n){var i=t.apply(e,r);function o(t){g(i,a,n,o,l,"next",t)}function l(t){g(i,a,n,o,l,"throw",t)}o(void 0)}))}}function y(t,e,r){return(e=b(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function b(t){var e=w(t,"string");return"symbol"==u(e)?e:e+""}function w(t,e){if("object"!=u(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var a=r.call(t,e||"default");if("object"!=u(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function V(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=x(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var a=0,n=function(){};return{s:n,n:function(){return a>=t.length?{done:!0}:{done:!1,value:t[a++]}},e:function(t){throw t},f:n}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,o=!0,l=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return o=t.done,t},e:function(t){l=!0,i=t},f:function(){try{o||null==r.return||r.return()}finally{if(l)throw i}}}}function x(t,e){if(t){if("string"==typeof t)return _(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?_(t,e):void 0}}function _(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,a=Array(e);r<e;r++)a[r]=t[r];return a}var k=[{image:"",price:null,cost:null,otPrice:null,stock:null,barCode:"",weight:0,volume:0}],O={price:{title:"售价"},cost:{title:"成本价"},otPrice:{title:"原价"},stock:{title:"库存"},barCode:{title:"商品编号"},weight:{title:"重量（KG）"},volume:{title:"体积(m³)"}},j={name:"taoBao",components:{Tinymce:l["a"]},data:function(){return{loading:!1,formThead:Object.assign({},O),manyTabTit:{},manyTabDate:{},formValidate:null,form:1,props2:{children:"child",label:"name",value:"id",multiple:!0,emitPath:!1},checkboxGroup:[],recommend:[],modal_loading:!1,ManyAttrValue:[Object.assign({},k[0])],imgList:[],tempData:{page:1,limit:9999},shippingList:[],merCateList:[],images:"",url:"",modalPic:!1,isChoice:"",isDisabled:!1,ruleInline:{storeName:[{required:!0,message:"请输入商品名称",trigger:"blur"}],cateIds:[{required:!0,message:"请选择商品分类",trigger:"change",type:"array",min:"1"}],unitName:[{required:!0,message:"请输入单位",trigger:"blur"}],tempId:[{required:!0,message:"请选择运费模板",trigger:"change",type:"number"}],keyword:[{required:!0,message:"请输入商品关键字",trigger:"blur"}],attrValue:[{required:!0,message:"请上传商品轮播图",type:"array",trigger:"change"}]},grid:{xl:12,lg:12,md:12,sm:24,xs:24},copyConfig:{}}},created:function(){this.goodsCategory()},computed:{attrValue:function(){var t=Object.assign({},k[0]);return delete t.image,t},oneFormBatch:function(){var t=[Object.assign({},k[0])];return delete t[0].barCode,t}},watch:{"formValidate.attr":{handler:function(t){this.watCh(t)},immediate:!1,deep:!0}},mounted:function(){this.productGetTemplate(),this.getCopyConfig(),this.getGoodsType()},methods:{delAttrTable:function(t){this.formValidate.attrValue.splice(t,1)},getCopyConfig:function(){var t=this;Object(i["e"])().then((function(e){t.copyConfig=e}))},onChangeGroup:function(){this.checkboxGroup.includes("isGood")?this.formValidate.isGood=!0:this.formValidate.isGood=!1,this.checkboxGroup.includes("isBenefit")?this.formValidate.isBenefit=!0:this.formValidate.isBenefit=!1,this.checkboxGroup.includes("isBest")?this.formValidate.isBest=!0:this.formValidate.isBest=!1,this.checkboxGroup.includes("isNew")?this.formValidate.isNew=!0:this.formValidate.isNew=!1,this.checkboxGroup.includes("isHot")?this.formValidate.isHot=!0:this.formValidate.isHot=!1},batchAdd:function(){var t,e=V(this.formValidate.attrValue);try{for(e.s();!(t=e.n()).done;){var r=t.value;this.$set(r,"image",this.oneFormBatch[0].image),this.$set(r,"price",this.oneFormBatch[0].price),this.$set(r,"cost",this.oneFormBatch[0].cost),this.$set(r,"otPrice",this.oneFormBatch[0].otPrice),this.$set(r,"stock",this.oneFormBatch[0].stock),this.$set(r,"barCode",this.oneFormBatch[0].barCode),this.$set(r,"weight",this.oneFormBatch[0].weight),this.$set(r,"volume",this.oneFormBatch[0].volume)}}catch(a){e.e(a)}finally{e.f()}},watCh:function(t){var e={},r={};this.formValidate.attr.forEach((function(t,a){e[t.attrName]={title:t.attrName},r[t.attrName]=""})),this.formValidate.attrValue=this.attrFormat(t),this.manyTabTit=e,this.manyTabDate=r,this.formThead=Object.assign({},this.formThead,e)},attrFormat:function(t){var e=[],r=[];return a(t);function a(t){if(t.length>1)t.forEach((function(a,n){0===n&&(e=t[n]["attrValue"]);var i=[];e.forEach((function(e){t[n+1]&&t[n+1]["attrValue"]&&t[n+1]["attrValue"].forEach((function(a){var o=(0!==n?"":t[n]["attrName"]+"_")+e+"$&"+t[n+1]["attrName"]+"_"+a;if(i.push(o),n===t.length-2){var l={image:"",price:0,cost:0,otPrice:0,stock:0,barCode:"",weight:0,volume:0,brokerage:0,brokerage_two:0};for(var s in o.split("$&").forEach((function(t,e){var r=t.split("_");l["attrValue"]||(l["attrValue"]={}),l["attrValue"][r[0]]=r.length>1?r[1]:""})),l.attrValue)l[s]=l.attrValue[s];r.push(l)}}))})),e=i.length?i:[]}));else{var a=[];t.forEach((function(t,e){t["attrValue"].forEach((function(e,n){for(var i in a[n]=t["attrName"]+"_"+e,r[n]={image:"",price:0,cost:0,otPrice:0,stock:0,barCode:"",weight:0,volume:0,brokerage:0,brokerage_two:0,attrValue:y({},t["attrName"],e)},r[n].attrValue)r[n][i]=r[n].attrValue[i]}))})),e.push(a.join("$&"))}return r}},productGetTemplate:function(){var t=this;Object(s["o"])(this.tempData).then((function(e){t.shippingList=e.list}))},handleRemove:function(t){this.formValidate.sliderImages.splice(t,1),this.$forceUpdate()},checked:function(t,e){this.formValidate.image=t},goodsCategory:function(){var t=this;Object(i["d"])({status:-1,type:1}).then((function(e){t.merCateList=e}))},add:function(){var t=this;this.url?(this.loading=!0,1==this.copyConfig.copyType?Object(i["f"])({url:this.url}).then((function(e){var r=e.info;t.formValidate={image:t.$selfUtil.setDomain(r.image),sliderImage:r.sliderImage,storeName:r.storeName,storeInfo:r.storeInfo,keyword:r.keyword,cateIds:r.cateId?r.cateId.split(","):[],cateId:r.cateId,unitName:r.unitName,sort:0,isShow:0,isBenefit:0,isNew:0,isGood:0,isHot:0,isBest:0,tempId:r.tempId,attrValue:r.attrValue,attr:r.attr||[],selectRule:r.selectRule,isSub:!1,content:t.$selfUtil.replaceImgSrcHttps(r.content),specType:!!r.attr.length,id:r.id,giveIntegral:r.giveIntegral,ficti:r.ficti},r.isHot&&t.checkboxGroup.push("isHot"),r.isGood&&t.checkboxGroup.push("isGood"),r.isBenefit&&t.checkboxGroup.push("isBenefit"),r.isBest&&t.checkboxGroup.push("isBest"),r.isNew&&t.checkboxGroup.push("isNew");var a=JSON.parse(r.sliderImage),n=[];if(Object.keys(a).map((function(e){n.push(t.$selfUtil.setDomain(a[e]))})),t.formValidate.sliderImages=n,t.formValidate.attr.length){t.oneFormBatch[0].image=t.$selfUtil.setDomain(r.image);for(var i=0;i<t.formValidate.attr.length;i++)t.formValidate.attr[i].attrValue=JSON.parse(t.formValidate.attr[i].attrValues)}t.loading=!1})).catch((function(){t.loading=!1})):Object(i["g"])({url:this.url,form:this.form}).then((function(e){t.formValidate={image:t.$selfUtil.setDomain(e.image),sliderImage:e.sliderImage,storeName:e.storeName,storeInfo:e.storeInfo,keyword:e.keyword,cateIds:e.cateId?e.cateId.split(","):[],cateId:e.cateId,unitName:e.unitName,sort:0,isShow:0,isBenefit:0,isNew:0,isGood:0,isHot:0,isBest:0,tempId:e.tempId,attrValue:e.attrValue,attr:e.attr||[],selectRule:e.selectRule,isSub:!1,content:e.content,specType:!!e.attr.length,id:e.id,giveIntegral:e.giveIntegral,ficti:e.ficti};var r=JSON.parse(e.sliderImage),a=[];if(Object.keys(r).map((function(e){a.push(t.$selfUtil.setDomain(r[e]))})),t.formValidate.sliderImages=a,t.formValidate.attr.length){t.oneFormBatch[0].image=t.$selfUtil.setDomain(e.image);for(var n=0;n<t.formValidate.attr.length;n++)t.formValidate.attr[n].attrValue=JSON.parse(t.formValidate.attr[n].attrValues)}t.loading=!1})).catch((function(){t.loading=!1}))):this.$message.warning("请输入链接地址！")},handleSubmit:Object(c["a"])((function(t){var e=this,r=JSON.parse(JSON.stringify(this.formValidate));r.attr.forEach((function(t){t.attrValues=t.attrValue.join(",")})),r.cateId=r.cateIds.join(","),r.sliderImage=JSON.stringify(r.sliderImages),r.attrValue.forEach((function(t){t.attrValue=JSON.stringify(t.attrValue)})),this.$refs[t].validate((function(t){t?(e.modal_loading=!0,Object(i["j"])(r).then(function(){var t=v(h().mark((function t(r){return h().wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.$message.success("新增成功"),e.$emit("handleCloseMod",!1),e.modal_loading=!1;case 3:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(){e.modal_loading=!1}))):r.storeName&&r.cateId&&r.keyword&&r.unitName&&r.image||e.$message.warning("请填写完整商品信息！")}))})),modalPicTap:function(t,e,r){var a=this;this.$modalUpload((function(n){if("1"!==t||e||(a.formValidate.image=n[0].sattDir,a.OneattrValue[0].image=n[0].sattDir),"2"===t&&!e){if(n.length>10)return this.$message.warning("最多选择10张图片！");if(n.length+a.formValidate.sliderImages.length>10)return this.$message.warning("最多选择10张图片！");n.map((function(t){a.formValidate.sliderImages.push(t.sattDir)}))}"1"===t&&"dan"===e&&(a.OneattrValue[0].image=n[0].sattDir),"1"===t&&"duo"===e&&(a.formValidate.attrValue[r].image=n[0].sattDir),"1"===t&&"pi"===e&&(a.oneFormBatch[0].image=n[0].sattDir)}),t,"store")},handleDragStart:function(t,e){this.dragging=e},handleDragEnd:function(t,e){this.dragging=null},handleDragOver:function(t){t.dataTransfer.dropEffect="move"},handleDragEnter:function(t,e){if(t.dataTransfer.effectAllowed="move",e!==this.dragging){var r=f(this.formValidate.slider_image),a=r.indexOf(this.dragging),n=r.indexOf(e);r.splice.apply(r,[n,0].concat(f(r.splice(a,1)))),this.formValidate.slider_image=r}},getGoodsType:function(){var t=this;Object(o["a"])({gid:70}).then((function(e){var r=e.list,a=[],n=[],i=[{name:"是否热卖",value:"isHot"}],o=[{name:"",value:"isGood",type:"2"},{name:"",value:"isBenefit",type:"4"},{name:"",value:"isBest",type:"1"},{name:"",value:"isNew",type:"3"}];r.forEach((function(t){var e={};e.value=JSON.parse(t.value),e.id=t.id,e.gid=t.gid,e.status=t.status,a.push(e)})),a.forEach((function(t){var e={};e.name=t.value.fields[1].value,e.status=t.status,e.type=t.value.fields[3].value,n.push(e)})),o.forEach((function(t){n.forEach((function(e){t.type==e.type&&i.push({name:e.name,value:t.value,type:t.type})}))})),t.recommend=i}))}}},I=j,C=(r("0818"),r("2877")),T=Object(C["a"])(I,a,n,!1,null,"a28b6d5c",null);e["a"]=T.exports},e7ac:function(t,e,r){"use strict";r.d(e,"b",(function(){return n})),r.d(e,"d",(function(){return i})),r.d(e,"e",(function(){return o})),r.d(e,"c",(function(){return l})),r.d(e,"a",(function(){return s}));var a=r("b775");function n(t){var e={id:t.id};return Object(a["a"])({url:"/admin/system/group/delete",method:"GET",params:e})}function i(t){var e={keywords:t.keywords,page:t.page,limit:t.limit};return Object(a["a"])({url:"/admin/system/group/list",method:"GET",params:e})}function o(t){var e={formId:t.formId,info:t.info,name:t.name};return Object(a["a"])({url:"/admin/system/group/save",method:"POST",params:e})}function l(t){var e={formId:t.formId,info:t.info,name:t.name,id:t.id};return Object(a["a"])({url:"/admin/system/group/update",method:"POST",params:e})}function s(t){var e={gid:t.gid};return Object(a["a"])({url:"/admin/system/group/data/list",method:"GET",params:e})}},fd61:function(t,e,r){}}]);