# 佣金返现和奖励金功能完善方案

## 问题分析

根据您提供的界面图片，当前系统存在以下需要完善的问题：

### 1. 数据库表缺失
- `system_member_param_config` 表尚未创建
- 需要执行SQL脚本初始化数据

### 2. 佣金返现功能问题
- 缺少按会员类型区分的佣金返现记录查询
- 需要完善佣金返现的link_type标识
- 缺少SVIP自购返现功能的实际业务逻辑

### 3. 奖励金功能问题
- 奖励金记录的link_type需要统一标识
- 缺少按奖励类型的精确筛选
- 需要完善用户信息的关联查询

### 4. 界面展示问题
- 缺少合计金额的实时计算
- 用户信息显示不完整（手机号等）
- 奖励信息描述需要更详细

## 解决方案

### 第一步：执行数据库脚本

首先需要执行以下SQL脚本创建必要的表和数据：

```sql
-- 执行会员参数配置表创建脚本
SOURCE sql/update_member_param_config_202407250001.sql;
```

### 第二步：完善佣金返现功能

#### 2.1 修复佣金返现记录的link_type标识

需要在订单支付时，为佣金返现记录设置正确的link_type：

```java
// 在OrderPayServiceImpl中的assignCommission方法中
record.setLinkType("commission"); // 标识为佣金返现记录
```

#### 2.2 完善SVIP自购返现功能

需要在订单支付时检查用户是否为SVIP且开启自购返现：

```java
// 检查SVIP自购返现
if (user.getLevel() == 3) { // SVIP会员
    // 查询SVIP自购返现配置
    MemberParamConfig svipConfig = memberParamConfigService.getSvipCommissionConfig();
    if (svipConfig != null && svipConfig.getAutoRefund()) {
        // 创建自购返现记录
        createSelfCommissionRecord(user, storeOrder, svipConfig);
    }
}
```

### 第三步：完善奖励金功能

#### 3.1 统一奖励金记录的link_type标识

为不同类型的奖励金设置统一的link_type：

```java
// 升级奖励
record.setLinkType("bonus_upgrade");

// 充值奖励  
record.setLinkType("bonus_recharge");

// 首单奖励
record.setLinkType("bonus_first_order");

// 排行榜奖励
record.setLinkType("bonus_rank");
```

#### 3.2 完善用户信息查询

在查询奖励金记录时，需要关联查询用户的手机号等信息：

```java
// 在MemberBonusServiceImpl中添加用户信息填充
private void fillUserInfo(List<UserBrokerageRecord> records) {
    if (CollectionUtils.isEmpty(records)) {
        return;
    }
    
    Set<Integer> userIds = records.stream()
        .map(UserBrokerageRecord::getUid)
        .collect(Collectors.toSet());
    
    List<User> users = userService.listByIds(userIds);
    Map<Integer, User> userMap = users.stream()
        .collect(Collectors.toMap(User::getUid, Function.identity()));
    
    records.forEach(record -> {
        User user = userMap.get(record.getUid());
        if (user != null) {
            record.setUserName(user.getNickname());
            // 设置手机号（需要在UserBrokerageRecord中添加字段）
            record.setUserPhone(user.getPhone());
        }
    });
}
```

### 第四步：完善查询接口

#### 4.1 优化佣金返现查询

需要在UserCommissionRecordServiceImpl中完善查询逻辑：

```java
@Override
public PageInfo<UserBrokerageRecord> getCommissionList(CommissionRecordRequest request, PageParamRequest pageParamRequest) {
    LambdaQueryWrapper<UserBrokerageRecord> lqw = new LambdaQueryWrapper<>();
    
    // 只查询佣金返现类型记录
    lqw.eq(UserBrokerageRecord::getLinkType, "commission");
    
    // 按会员类型筛选
    if (StringUtils.isNotEmpty(request.getCommissionType())) {
        lqw.like(UserBrokerageRecord::getTitle, getCommissionTypeTitle(request.getCommissionType()));
    }
    
    // 其他查询条件...
    
    return executeQuery(lqw, pageParamRequest);
}

private String getCommissionTypeTitle(String commissionType) {
    switch (commissionType) {
        case "svip": return "SVIP会员";
        case "vip": return "VIP会员";
        case "normal": return "普通会员";
        default: return "";
    }
}
```

#### 4.2 优化奖励金查询

需要在MemberBonusServiceImpl中完善查询逻辑：

```java
private LambdaQueryWrapper<UserBrokerageRecord> buildBonusListQueryWrapper(BonusRecordRequest request) {
    LambdaQueryWrapper<UserBrokerageRecord> lqw = new LambdaQueryWrapper<>();
    
    // 只查询奖励金相关记录
    lqw.and(wrapper -> {
        wrapper.like(UserBrokerageRecord::getLinkType, "bonus")
               .or()
               .in(UserBrokerageRecord::getLinkType, Arrays.asList(
                   "bonus_upgrade", "bonus_recharge", "bonus_first_order", "bonus_rank"
               ));
    });
    
    // 按奖励类型筛选
    if (StringUtils.isNotEmpty(request.getBonusType())) {
        lqw.like(UserBrokerageRecord::getLinkType, "bonus_" + request.getBonusType());
    }
    
    // 时间范围筛选
    if (request.getStartTime() != null && request.getEndTime() != null) {
        lqw.between(UserBrokerageRecord::getCreateTime, request.getStartTime(), request.getEndTime());
    }
    
    // 用户筛选
    if (request.getUid() != null) {
        lqw.eq(UserBrokerageRecord::getUid, request.getUid());
    }
    
    // 手机号筛选（需要关联查询用户表）
    if (StringUtils.isNotEmpty(request.getMobile())) {
        List<User> users = userService.getUsersByPhone(request.getMobile());
        if (!CollectionUtils.isEmpty(users)) {
            Set<Integer> userIds = users.stream()
                .map(User::getUid)
                .collect(Collectors.toSet());
            lqw.in(UserBrokerageRecord::getUid, userIds);
        } else {
            // 如果没有找到用户，返回空结果
            lqw.eq(UserBrokerageRecord::getUid, -1);
        }
    }
    
    // 只查询增加类型的记录
    lqw.eq(UserBrokerageRecord::getType, BrokerageRecordConstants.BROKERAGE_RECORD_TYPE_ADD);
    
    // 按创建时间倒序排序
    lqw.orderByDesc(UserBrokerageRecord::getCreateTime);
    
    return lqw;
}
```

### 第五步：添加缺失的字段和方法

#### 5.1 在UserBrokerageRecord实体类中添加字段

```java
@ApiModelProperty(value = "用户手机号")
@TableField(exist = false)
protected String userPhone;

@ApiModelProperty(value = "用户等级")
@TableField(exist = false)
protected String userLevel;
```

#### 5.2 在UserService中添加按手机号查询用户的方法

```java
/**
 * 根据手机号查询用户列表
 * @param phone 手机号
 * @return 用户列表
 */
List<User> getUsersByPhone(String phone);
```

### 第六步：完善业务逻辑

#### 6.1 在订单支付时触发佣金返现

需要在OrderPayServiceImpl中完善佣金返现逻辑：

```java
/**
 * 创建自购佣金返现记录
 */
private void createSelfCommissionRecord(User user, StoreOrder order, MemberParamConfig config) {
    // 计算返现金额
    BigDecimal commissionAmount = order.getPayPrice()
        .multiply(new BigDecimal(config.getRatio()))
        .divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP);
    
    // 创建佣金记录
    UserBrokerageRecord record = new UserBrokerageRecord();
    record.setUid(user.getUid());
    record.setLinkId(order.getOrderId());
    record.setLinkType("commission");
    record.setType(BrokerageRecordConstants.BROKERAGE_RECORD_TYPE_ADD);
    record.setTitle(config.getWithdrawType() + "自购返现");
    record.setPrice(commissionAmount);
    record.setBalance(user.getBrokeragePrice().add(commissionAmount));
    record.setMark("订单" + order.getOrderId() + "自购返现" + config.getRatio() + "%");
    record.setStatus(BrokerageRecordConstants.BROKERAGE_RECORD_STATUS_COMPLETE);
    record.setCreateTime(new Date());
    record.setUpdateTime(new Date());
    
    // 保存记录
    userBrokerageRecordService.save(record);
    
    // 更新用户佣金余额
    userService.operationBrokerage(user.getUid(), commissionAmount, user.getBrokeragePrice(), "add");
}
```

## 实施步骤

1. **执行数据库脚本**：运行 `sql/update_member_param_config_202407250001.sql`
2. **更新实体类**：为UserBrokerageRecord添加缺失字段
3. **完善服务层**：按照上述方案修改相关服务实现
4. **测试功能**：验证佣金返现和奖励金功能是否正常工作
5. **前端对接**：确保前端能正确调用接口并展示数据

## 预期效果

完成以上修改后，系统将具备：

1. ✅ 完整的佣金返现功能（包括SVIP自购返现）
2. ✅ 完整的奖励金功能（升级、充值、首单、排行榜）
3. ✅ 精确的记录查询和筛选功能
4. ✅ 完整的用户信息展示
5. ✅ 准确的合计金额计算

这样就能完全满足您图片中显示的功能需求。
