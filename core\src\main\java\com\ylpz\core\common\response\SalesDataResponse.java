package com.ylpz.core.common.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 销售数据响应对象
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "SalesDataResponse对象", description = "销售数据响应对象")
public class SalesDataResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户ID")
    private Integer uid;

    @ApiModelProperty(value = "用户昵称")
    private String nickname;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "会员等级")
    private String memberLevel;

    @ApiModelProperty(value = "会员等级ID")
    private Integer memberLevelId;

    @ApiModelProperty(value = "销售金额")
    private BigDecimal salesAmount;

    @ApiModelProperty(value = "订单数量")
    private Integer orderCount;

    @ApiModelProperty(value = "待结算返现")
    private BigDecimal pendingBrokerageAmount;

    @ApiModelProperty(value = "已结算返现")
    private BigDecimal settledBrokerageAmount;

    @ApiModelProperty(value = "自购金额")
    private BigDecimal selfPurchaseAmount;

    @ApiModelProperty(value = "用户头像")
    private String avatar;

    @ApiModelProperty(value = "注册时间")
    private String createTime;
}
