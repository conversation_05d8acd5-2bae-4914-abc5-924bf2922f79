(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-de3394ac"],{"4a69":function(e,r,t){},"502a":function(e,r,t){"use strict";t.r(r);var i=function(){var e=this,r=e.$createElement,t=e._self._c||r;return t("div",{staticClass:"divBox"},[t("el-card",{staticClass:"box-card"},[t("el-form",{ref:"ruleForm",staticClass:"demo-ruleForm",attrs:{model:e.ruleForm,rules:e.rules,"label-width":"150px"}},[t("el-form-item",{attrs:{label:"折扣名称",prop:"name"}},[t("el-input",{staticStyle:{width:"350px"},attrs:{placeholder:"请输入折扣名称"},model:{value:e.ruleForm.name,callback:function(r){e.$set(e.ruleForm,"name",r)},expression:"ruleForm.name"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"折扣类型"}},[t("el-radio-group",{model:{value:e.ruleForm.type,callback:function(r){e.$set(e.ruleForm,"type",r)},expression:"ruleForm.type"}},[t("el-radio",{attrs:{label:1}},[e._v("手动领取")]),e._v(" "),t("el-radio",{attrs:{label:2}},[e._v("新人折扣")]),e._v(" "),t("el-radio",{attrs:{label:3}},[e._v("赠送折扣")])],1)],1),e._v(" "),t("el-form-item",{attrs:{label:"使用类型"}},[t("el-radio-group",{model:{value:e.ruleForm.useType,callback:function(r){e.$set(e.ruleForm,"useType",r)},expression:"ruleForm.useType"}},[t("el-radio",{attrs:{label:1}},[e._v("全场通用")]),e._v(" "),t("el-radio",{attrs:{label:2}},[e._v("商品折扣")]),e._v(" "),t("el-radio",{attrs:{label:3}},[e._v("品类折扣")])],1)],1),e._v(" "),t("el-form-item",{attrs:{label:"折扣率",prop:"discount"}},[t("el-input-number",{attrs:{min:0,max:1,placeholder:"0~1之间的小数"},model:{value:e.ruleForm.discount,callback:function(r){e.$set(e.ruleForm,"discount",r)},expression:"ruleForm.discount"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"是否限量",prop:"isLimited"}},[t("el-radio-group",{model:{value:e.ruleForm.isLimited,callback:function(r){e.$set(e.ruleForm,"isLimited",r)},expression:"ruleForm.isLimited"}},[t("el-radio",{attrs:{label:!0}},[e._v("限量")]),e._v(" "),t("el-radio",{attrs:{label:!1}},[e._v("不限量")])],1)],1),e._v(" "),e.ruleForm.isLimited?t("el-form-item",{attrs:{label:"发布数量",prop:"total"}},[t("el-input-number",{attrs:{min:1,label:"排序"},model:{value:e.ruleForm.total,callback:function(r){e.$set(e.ruleForm,"total",r)},expression:"ruleForm.total"}})],1):e._e(),e._v(" "),t("el-form-item",{attrs:{label:"领取是否限时",prop:"isForever"}},[t("el-radio-group",{model:{value:e.ruleForm.isForever,callback:function(r){e.$set(e.ruleForm,"isForever",r)},expression:"ruleForm.isForever"}},[t("el-radio",{attrs:{label:!0}},[e._v("限时")]),e._v(" "),t("el-radio",{attrs:{label:!1}},[e._v("不限时")])],1)],1),e._v(" "),e.ruleForm.isForever?t("el-form-item",{attrs:{label:"领取时间"}},[t("el-date-picker",{staticStyle:{width:"550px"},attrs:{type:"datetimerange","range-separator":"至","value-format":"yyyy-MM-dd HH:mm:ss","picker-options":e.pickerOptions,"start-placeholder":"开始日期","end-placeholder":"结束日期"},on:{blur:e.handleTimestamp},model:{value:e.isForeverTime,callback:function(r){e.isForeverTime=r},expression:"isForeverTime"}})],1):e._e(),e._v(" "),t("el-form-item",{attrs:{label:"是否固定使用时间",prop:"isFixedTime"}},[t("el-radio-group",{model:{value:e.ruleForm.isFixedTime,callback:function(r){e.$set(e.ruleForm,"isFixedTime",r)},expression:"ruleForm.isFixedTime"}},[t("el-radio",{attrs:{label:0}},[e._v("否")]),e._v(" "),t("el-radio",{attrs:{label:1}},[e._v("是")])],1)],1),e._v(" "),e.ruleForm.isFixedTime?t("el-form-item",{attrs:{label:"使用时间"}},[t("el-date-picker",{staticStyle:{width:"550px"},attrs:{type:"datetimerange","range-separator":"至","value-format":"yyyy-MM-dd HH:mm:ss","picker-options":e.pickerOptions,"start-placeholder":"开始日期","end-placeholder":"结束日期"},on:{blur:e.handleTimestamp},model:{value:e.useTime,callback:function(r){e.useTime=r},expression:"useTime"}})],1):e._e(),e._v(" "),t("el-form-item",{attrs:{label:"使用门槛"}},[t("el-radio-group",{model:{value:e.threshold,callback:function(r){e.threshold=r},expression:"threshold"}},[t("el-radio",{attrs:{label:!1}},[e._v("无门槛")]),e._v(" "),t("el-radio",{attrs:{label:!0}},[e._v("有门槛")])],1)],1),e._v(" "),e.threshold?t("el-form-item",{attrs:{label:"折扣最低消费",prop:"minPrice"}},[t("el-input-number",{attrs:{min:1,label:"描述文字"},model:{value:e.ruleForm.minPrice,callback:function(r){e.$set(e.ruleForm,"minPrice",r)},expression:"ruleForm.minPrice"}})],1):e._e(),e._v(" "),t("el-form-item",{attrs:{label:"排序",prop:"sort"}},[t("el-input-number",{attrs:{min:0,label:"排序"},model:{value:e.ruleForm.sort,callback:function(r){e.$set(e.ruleForm,"sort",r)},expression:"ruleForm.sort"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"状态",prop:"status"}},[t("el-radio-group",{model:{value:e.ruleForm.status,callback:function(r){e.$set(e.ruleForm,"status",r)},expression:"ruleForm.status"}},[t("el-radio",{attrs:{label:!0}},[e._v("开启")]),e._v(" "),t("el-radio",{attrs:{label:!1}},[e._v("关闭")])],1)],1),e._v(" "),t("el-form-item",[t("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:coupon:save"],expression:"['admin:coupon:save']"}],attrs:{size:"mini",type:"primary",loading:e.loading},on:{click:function(r){return e.submitForm("ruleForm")}}},[e._v("立即创建")])],1)],1)],1)],1)},l=[],a=t("b7be"),o=t("73f5"),s=t("61f7"),m={name:"creatCoupon",data:function(){return{pickerOptions:{disabledDate:function(e){return e.getTime()<Date.now()-864e5||e.getTime()>Date.now()+5184e7}},loading:!1,threshold:!1,termTime:[],props2:{children:"child",label:"name",value:"id",checkStrictly:!0,emitPath:!1},couponType:0,term:"termday",merCateList:[],ruleForm:{name:"",useType:1,type:2,discount:null,isLimited:!1,total:1,isForever:!1,isFixedTime:0,useStartTime:"",useEndTime:"",receiveStartTime:"",receiveEndTime:"",minPrice:1,sort:0,status:!1},isForeverTime:[],useTime:[],rules:{name:[{required:!0,message:"请输入折扣名称",trigger:"blur"}],day:[{required:!0,message:"请输入使用有效期限（天）",trigger:"blur"}],money:[{required:!0,message:"请输入优惠券面值",trigger:"blur"}],primaryKey:[{required:!0,message:"请选择品类",trigger:"change"}],checked:[{required:!0,message:"请至少选择一个商品",trigger:"change",type:"array"}],isForeverTime:[{required:!0,message:"请选择领取时间",trigger:"change",type:"array"}],isFixedTime:[{required:!0,message:"请选择固定时间",trigger:"change",type:"number"}],total:[{required:!0,message:"请输入发布数量",trigger:"blur"}],minPrice:[{required:!0,message:"请输入最低消费",trigger:"blur"}]}}},mounted:function(){this.getCategorySelect(),this.$route.params.id&&this.getInfo()},methods:{handleTimestamp:function(){},getCategorySelect:function(){var e=this;Object(o["d"])({status:-1,type:1}).then((function(r){e.merCateList=r,e.merCateList.map((function(r){e.$set(r,"disabled",!0)}))}))},getInfo:function(){var e=this;this.loading=!0,Object(a["s"])({id:this.$route.params.id}).then((function(r){var t=r.coupon;e.ruleForm={useType:t.useType,isFixedTime:t.isFixedTime,isForever:t.isForever,name:t.name,money:t.money,minPrice:t.minPrice,day:t.day,type:t.type,isLimited:t.isLimited,sort:t.sort,total:t.total,status:t.status,primaryKey:Number(t.primaryKey),checked:r.product||[]},0==t.minPrice?e.threshold=!1:e.threshold=!0,t.isForever?e.isForeverTime=[t.receiveStartTime,t.receiveEndTime]:e.isForeverTime=[],t.isFixedTime&&t.useStartTime&&t.useEndTime?e.termTime=[t.useStartTime,t.useEndTime]:e.termTime=[],e.loading=!1})).catch((function(r){e.loading=!1,e.$message.error(r.message)}))},handleRemove:function(e){this.ruleForm.checked.splice(e,1)},changeGood:function(){var e=this;this.$modalGoodList((function(r){e.ruleForm.checked=r}),"many",e.ruleForm.checked)},submitForm:Object(s["a"])((function(e){var r=this;this.ruleForm.isLimited||(this.ruleForm.total=null),this.ruleForm.isForever?this.isForeverTime.length&&(this.ruleForm.receiveStartTime=this.isForeverTime[0],this.ruleForm.receiveEndTime=this.isForeverTime[1]):(this.ruleForm.receiveStartTime="",this.ruleForm.receiveEndTime=""),this.ruleForm.isFixedTime?this.useTime.length&&(this.ruleForm.useStartTime=this.isForeverTime[0],this.ruleForm.useEndTime=this.isForeverTime[1]):(this.ruleForm.useStartTime="",this.ruleForm.useEndTime=""),this.threshold?this.ruleForm.minPrice=1:this.ruleForm.minPrice=0,this.$refs[e].validate((function(e){if(!e)return r.loading=!1,!1;r.loading=!0,Object(a["A"])(r.ruleForm).then((function(){r.$message.success("新增成功"),r.loading=!1,setTimeout((function(){r.$router.push({path:"/marketing/discount/list"})}),200)})).catch((function(){r.loading=!1}))}))}))}},u=m,n=(t("6fa6"),t("2877")),d=Object(n["a"])(u,i,l,!1,null,"41a593ae",null);r["default"]=d.exports},"6fa6":function(e,r,t){"use strict";t("4a69")}}]);