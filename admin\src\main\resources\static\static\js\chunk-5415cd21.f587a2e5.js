(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5415cd21"],{"2f2c":function(t,e,i){"use strict";i.d(e,"b",(function(){return u})),i.d(e,"c",(function(){return d})),i.d(e,"r",(function(){return m})),i.d(e,"d",(function(){return f})),i.d(e,"a",(function(){return p})),i.d(e,"g",(function(){return h})),i.d(e,"h",(function(){return g})),i.d(e,"j",(function(){return v})),i.d(e,"i",(function(){return b})),i.d(e,"e",(function(){return y})),i.d(e,"o",(function(){return w})),i.d(e,"q",(function(){return V})),i.d(e,"l",(function(){return _})),i.d(e,"m",(function(){return k})),i.d(e,"n",(function(){return x})),i.d(e,"p",(function(){return O})),i.d(e,"k",(function(){return T})),i.d(e,"f",(function(){return C}));var r=i("b775");function a(t){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},a(t)}function n(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,r)}return i}function s(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?n(Object(i),!0).forEach((function(e){o(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):n(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}function o(t,e,i){return(e=l(e))in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function l(t){var e=c(t,"string");return"symbol"==a(e)?e:e+""}function c(t,e){if("object"!=a(t)||!t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var r=i.call(t,e||"default");if("object"!=a(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function u(t){return Object(r["a"])({url:"/admin/system/city/list",method:"get",params:s({},t)})}function d(){return Object(r["a"])({url:"/admin/system/city/list/tree",method:"get"})}function m(t){return Object(r["a"])({url:"/admin/system/city/update/status",method:"post",params:s({},t)})}function f(t){return Object(r["a"])({url:"/admin/system/city/update",method:"post",params:s({},t)})}function p(t){return Object(r["a"])({url:"/admin/system/city/info",method:"get",params:s({},t)})}function h(t){return Object(r["a"])({url:"/admin/express/list",method:"get",params:s({},t)})}function g(){return Object(r["a"])({url:"/admin/express/sync/express",method:"post"})}function v(t){return Object(r["a"])({url:"/admin/express/update/show",method:"post",data:t})}function b(t){return Object(r["a"])({url:"/admin/express/update",method:"post",data:t})}function y(t){return Object(r["a"])({url:"/admin/express/delete",method:"GET",params:s({},t)})}function w(t){return Object(r["a"])({url:"/admin/express/shipping/templates/list",method:"get",params:s({},t)})}function V(t){return Object(r["a"])({url:"/admin/express/shipping/templates/info",method:"get",params:s({},t)})}function _(t){return Object(r["a"])({url:"/admin/express/shipping/free/list",method:"get",params:s({},t)})}function k(t){return Object(r["a"])({url:"admin/express/shipping/region/list",method:"get",params:s({},t)})}function x(t){return Object(r["a"])({url:"admin/express/shipping/templates/save",method:"post",data:t})}function O(t,e){return Object(r["a"])({url:"admin/express/shipping/templates/update",method:"post",data:t,params:s({},e)})}function T(t){return Object(r["a"])({url:"admin/express/shipping/templates/delete",method:"get",params:t})}function C(t){return Object(r["a"])({url:"admin/express/info",method:"get",params:s({},t)})}},"43c0":function(t,e,i){"use strict";i.r(e);var r,a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"divBox"},[i("el-card",{staticClass:"box-card"},[i("div",{staticClass:"steps"},[i("Steps",{attrs:{"steps-title":["商品基本信息","商品详情"],curStep:t.currentTab}})],1),t._v(" "),i("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.fullscreenLoading,expression:"fullscreenLoading"}],ref:"formValidate",attrs:{rules:t.ruleValidate,model:t.formValidate,"label-width":"120px"},nativeOn:{submit:function(t){t.preventDefault()}}},[i("el-row",{directives:[{name:"show",rawName:"v-show",value:0===t.currentTab,expression:"currentTab === 0"}],attrs:{gutter:24}},[i("div",{staticClass:"store-info"},[i("div",{staticClass:"info-title"},[t._v("商品基本信息")]),t._v(" "),i("div",{staticClass:"form-info"},[i("el-col",{attrs:{span:24}},[i("el-form-item",{staticStyle:{width:"60%"},attrs:{label:"商品名称：",prop:"storeName"}},[i("el-input",{attrs:{maxlength:"249",placeholder:"请输入商品名称",disabled:t.isDisabled},model:{value:t.formValidate.storeName,callback:function(e){t.$set(t.formValidate,"storeName",e)},expression:"formValidate.storeName"}})],1)],1),t._v(" "),i("el-col",{attrs:{span:24}},[i("el-form-item",{staticStyle:{width:"60%"},attrs:{label:"商品标题：",prop:"storeInfo"}},[i("el-input",{attrs:{maxlength:"249",placeholder:"请输入商品标题",disabled:t.isDisabled},model:{value:t.formValidate.storeInfo,callback:function(e){t.$set(t.formValidate,"storeInfo",e)},expression:"formValidate.storeInfo"}})],1)],1),t._v(" "),i("el-col",{attrs:{span:24}},[i("el-form-item",{attrs:{label:"商品图：",prop:"sliderImages"}},[i("div",{staticClass:"acea-row"},[t._l(t.formValidate.sliderImages,(function(e,r){return i("div",{key:r,staticClass:"pictrue",attrs:{draggable:"true"},on:{dragstart:function(i){return t.handleDragStart(i,e)},dragover:function(i){return i.preventDefault(),t.handleDragOver(i,e)},dragenter:function(i){return t.handleDragEnter(i,e)},dragend:function(i){return t.handleDragEnd(i,e)}}},[i("img",{attrs:{src:e}}),t._v(" "),t.isDisabled?t._e():i("i",{staticClass:"el-icon-error btndel",on:{click:function(e){return t.handleRemove(r)}}})])})),t._v(" "),t.formValidate.sliderImages.length<10&&!t.isDisabled?i("div",{staticClass:"upLoadPicBox",on:{click:function(e){return t.modalPicTap("2")}}},[i("div",{staticClass:"upLoad"},[i("i",{staticClass:"el-icon-plus cameraIconfont",staticStyle:{"font-size":"16px"}})])]):t._e()],2),t._v(" "),i("div",{staticClass:"detail-description"},[t._v("\n                  建议尺寸：750*750像素，可拖拽图片调整顺序，最多上传6张。\n                ")])])],1),t._v(" "),i("el-col",{attrs:{span:24}},[i("el-form-item",{attrs:{label:"主图视频："}},[i("div",{staticClass:"upLoadPicBox",attrs:{disabled:t.isDisabled},on:{click:function(e){return t.modalPicTap("3")}}},[t.formValidate.videoLink?i("div",{staticClass:"pictrue"},[i("video",{attrs:{src:t.formValidate.videoLink}})]):i("div",{staticClass:"upLoad"},[i("i",{staticClass:"el-icon-plus cameraIconfont",staticStyle:{"font-size":"16px"}})])]),t._v(" "),i("div",{staticClass:"detail-description"},[t._v("\n                  添加主图视频，建议时长9-30秒，视频宽高和商品图一致。\n                ")])])],1),t._v(" "),i("el-col",{attrs:{span:24}},[i("el-form-item",{attrs:{label:"商品分类：",prop:"cateIds"}},[i("el-select",{attrs:{multiple:"",disabled:t.isDisabled,placeholder:"请选择"},model:{value:t.formValidate.cateIds,callback:function(e){t.$set(t.formValidate,"cateIds",e)},expression:"formValidate.cateIds"}},t._l(t.merCateList,(function(t){return i("el-option",{key:t.id.toString(),attrs:{label:t.name,value:t.id.toString()}})})),1)],1)],1),t._v(" "),i("el-col",{attrs:{span:24}},[i("el-form-item",{staticStyle:{width:"60%"},attrs:{label:"商品卖点："}},[i("el-input",{attrs:{placeholder:"请输入商品卖点",disabled:t.isDisabled},model:{value:t.formValidate.keyword,callback:function(e){t.$set(t.formValidate,"keyword",e)},expression:"formValidate.keyword"}})],1)],1),t._v(" "),i("el-col",{attrs:{span:24}},[i("el-form-item",{attrs:{label:"商品分组："}},[i("el-checkbox-group",{attrs:{disabled:t.isDisabled},model:{value:t.formValidate.tag,callback:function(e){t.$set(t.formValidate,"tag",e)},expression:"formValidate.tag"}},[i("el-checkbox",{attrs:{label:1}},[t._v("人气爆款")]),t._v(" "),i("el-checkbox",{attrs:{label:2}},[t._v("热销推荐")])],1)],1)],1),t._v(" "),i("el-col",{attrs:{span:24}},[i("el-form-item",{staticStyle:{width:"60%"},attrs:{label:"初始销量："}},[i("el-input",{attrs:{maxlength:"249",placeholder:"请输入",disabled:t.isDisabled},model:{value:t.formValidate.popularity,callback:function(e){t.$set(t.formValidate,"popularity",e)},expression:"formValidate.popularity"}})],1)],1)],1)]),t._v(" "),i("div",{staticClass:"store-specification"},[i("div",{staticClass:"info-title"},[t._v("规格价格信息")]),t._v(" "),i("div",{staticClass:"form-info"},[i("el-col",{attrs:{span:24}},[i("el-form-item",{attrs:{label:"规格单位：",prop:"unitName"}},[i("el-select",{attrs:{disabled:t.isDisabled,placeholder:"请选择"},model:{value:t.formValidate.unitName,callback:function(e){t.$set(t.formValidate,"unitName",e)},expression:"formValidate.unitName"}},[i("el-option",{attrs:{label:"盒",value:"盒"}}),t._v(" "),i("el-option",{attrs:{label:"袋",value:"袋"}}),t._v(" "),i("el-option",{attrs:{label:"罐",value:"罐"}}),t._v(" "),i("el-option",{attrs:{label:"箱",value:"箱"}})],1)],1)],1),t._v(" "),i("el-col",{attrs:{span:24}},[i("el-form-item",{staticClass:"specification-table",attrs:{label:"规格明细："}},[i("div",{staticClass:"table-box"},[i("el-table",{staticClass:"tabNumWidth",attrs:{data:t.attrValueList,size:"mini"}},[i("el-table-column",{attrs:{align:"center",label:"图片","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("div",{staticClass:"upLoadPicBox",on:{click:function(i){return t.modalPicTap("1","duo",e.$index)}}},[e.row.image?i("div",{staticClass:"pictrue tabPic"},[i("img",{attrs:{src:e.row.image}})]):i("div",{staticClass:"upLoad tabPic"},[i("i",{staticClass:"el-icon-plus cameraIconfont",staticStyle:{"font-size":"16px"}})])])]}}])}),t._v(" "),t._l(t.attrObjInput,(function(e,r){return i("el-table-column",{key:r,attrs:{label:e.title,align:"center","min-width":"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-input",{staticClass:"priceBox",attrs:{disabled:t.isDisabled,maxlength:"9",min:"0.01"},on:{blur:function(i){return t.keyupEvent(r,e.row[r],e.$index,3)}},model:{value:e.row[r],callback:function(i){t.$set(e.row,r,i)},expression:"scope.row[iii]"}})]}}],null,!0)})})),t._v(" "),t.isDisabled?t._e():i("el-table-column",{key:"3",attrs:{align:"center",label:"操作","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-button",{staticClass:"submission",attrs:{type:"text"},on:{click:function(i){return t.delAttrTable(e.$index)}}},[t._v("删除")])]}}],null,!1,2803824461)})],2),t._v(" "),i("div",[i("el-button",{attrs:{type:"text"},on:{click:t.addAttrTable}},[i("i",{staticClass:"el-icon-plus"}),t._v(" 添加规格")])],1)],1)])],1)],1)]),t._v(" "),i("div",{staticClass:"store-logistics"},[i("div",{staticClass:"info-title"},[t._v("物流信息")]),t._v(" "),i("div",{staticClass:"form-info"},[i("el-col",{attrs:{span:24}},[i("el-form-item",{attrs:{prop:"deliveryType",label:"配送方式："}},[i("el-checkbox-group",{attrs:{size:"small",disabled:t.isDisabled},on:{change:t.onChangeGroup},model:{value:t.formValidate.deliveryType,callback:function(e){t.$set(t.formValidate,"deliveryType",e)},expression:"formValidate.deliveryType"}},[i("el-checkbox",{attrs:{label:0}},[t._v(" 快递发货 ")])],1)],1)],1),t._v(" "),i("el-col",{attrs:{span:24}},[i("el-form-item",{attrs:{label:"运费模板：",prop:"tempId"}},[i("span",{staticClass:"uniform-postage"},[i("span",[i("el-select",{staticClass:"mr20",staticStyle:{width:"100%"},attrs:{placeholder:"请选择",disabled:t.isDisabled},model:{value:t.formValidate.tempId,callback:function(e){t.$set(t.formValidate,"tempId",e)},expression:"formValidate.tempId"}},t._l(t.shippingList,(function(t){return i("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1),t._v(" "),i("span",[i("el-button",{directives:[{name:"show",rawName:"v-show",value:!t.isDisabled,expression:"!isDisabled"}],staticClass:"mr15",on:{click:t.addTem}},[t._v("运费模板")])],1)])])],1)],1)]),t._v(" "),i("div",{staticClass:"store-other"},[i("div",{staticClass:"info-title"},[t._v("其他信息")]),t._v(" "),i("div",{staticClass:"form-info"},[i("el-col",{attrs:{span:24}},[i("el-form-item",{attrs:{label:"上架时间："}},[i("el-radio-group",{attrs:{disabled:t.isDisabled},model:{value:t.isSaleTime,callback:function(e){t.isSaleTime=e},expression:"isSaleTime"}},[i("el-radio",{attrs:{label:!0}},[i("span",{staticClass:"uniform-postage"},[i("span",[t._v("立即上架")])])]),t._v(" "),i("el-radio",{attrs:{label:!1}},[i("span",{staticClass:"uniform-postage"},[i("span",[t._v("定时开售")]),t._v(" "),i("span",[i("el-form-item",[i("el-date-picker",{attrs:{disabled:t.isSaleTime||t.isDisabled,type:"datetime",placeholder:"选择开售时间"},model:{value:t.saleTime,callback:function(e){t.saleTime=e},expression:"saleTime"}})],1)],1)])])],1)],1)],1),t._v(" "),i("el-col",{attrs:{span:24}},[i("el-form-item",{attrs:{label:"售后服务："}},[i("el-checkbox",{attrs:{disabled:t.isDisabled},model:{value:t.formValidate.supportExchange,callback:function(e){t.$set(t.formValidate,"supportExchange",e)},expression:"formValidate.supportExchange"}},[t._v("\n                  支持买家申请换货\n                ")]),t._v(" "),i("el-checkbox",{attrs:{disabled:t.isDisabled},model:{value:t.formValidate.supportReturn7Days,callback:function(e){t.$set(t.formValidate,"supportReturn7Days",e)},expression:"formValidate.supportReturn7Days"}},[t._v("\n                  支持7天无理由退货\n                ")])],1)],1),t._v(" "),i("el-col",{attrs:{span:24}},[i("el-form-item",{attrs:{label:"限购："}},[i("div",[i("el-checkbox",{attrs:{disabled:t.isDisabled},model:{value:t.formValidate.limitPurchaseCount,callback:function(e){t.$set(t.formValidate,"limitPurchaseCount",e)},expression:"formValidate.limitPurchaseCount"}},[i("span",{staticClass:"uniform-postage"},[i("span",[t._v("限制每个人可购买数量")]),t._v(" "),i("span",[i("el-input",{attrs:{disabled:!t.formValidate.limitPurchaseCount||t.isDisabled,placeholder:"请输入内容"},model:{value:t.formValidate.maxPurchaseCount,callback:function(e){t.$set(t.formValidate,"maxPurchaseCount",e)},expression:"formValidate.maxPurchaseCount"}},[i("template",{slot:"append"},[t._v("个")])],2)],1)])])],1),t._v(" "),i("div",{staticStyle:{"margin-top":"10px"}},[i("el-checkbox",{attrs:{disabled:t.isDisabled},model:{value:t.formValidate.limitSpecificUsers,callback:function(e){t.$set(t.formValidate,"limitSpecificUsers",e)},expression:"formValidate.limitSpecificUsers"}},[i("span",{staticClass:"uniform-postage"},[i("span",[t._v("只允许特定用户购买")]),t._v(" "),i("span",[i("el-form-item",[i("el-select",{attrs:{disabled:!t.formValidate.limitSpecificUsers||t.isDisabled,multiple:"",placeholder:"请选择"},model:{value:t.formValidate.allowedUserIds,callback:function(e){t.$set(t.formValidate,"allowedUserIds",e)},expression:"formValidate.allowedUserIds"}},t._l(t.userList,(function(t){return i("el-option",{key:t.uid,attrs:{label:t.nickname,value:t.uid}})})),1)],1)],1)])])],1)])],1)],1)])]),t._v(" "),i("el-row",{directives:[{name:"show",rawName:"v-show",value:1==t.currentTab,expression:"currentTab == 1"}]},[i("el-col",{attrs:{span:24}},[i("div",{staticClass:"details-row"},[i("div",{staticClass:"preview-imgs"},[i("div",{staticClass:"preview-top"},[t._v("商品详情效果预览")]),t._v(" "),t._l(t.detailsImages,(function(e,r){return i("div",{key:e,staticClass:"details-img",attrs:{draggable:"true"},on:{dragstart:function(i){return t.handleDragStart(i,e)},dragover:function(i){return i.preventDefault(),t.handleDragOver(i,e)},dragenter:function(i){return t.handleDragEnterDetails(i,e)},dragend:function(i){return t.handleDragEnd(i,e)}}},[i("img",{attrs:{src:e}}),t._v(" "),t.isDisabled?t._e():i("i",{staticClass:"el-icon-error deleteBtn",on:{click:function(e){return t.handleRemoveDetailsImg(r)}}})])})),t._v(" "),t.detailsImages.length<10&&!t.isDisabled?i("div",{staticClass:"upLoadPicBox",on:{click:function(e){return t.modalPicTap("4")}}},[i("div",{staticClass:"upLoad"},[i("i",{staticClass:"el-icon-plus cameraIconfont",staticStyle:{"font-size":"16px"}})]),t._v(" "),i("div",{staticClass:"update-des"},[t._v("选择商品详情图片")])]):t._e()],2)])])],1),t._v(" "),i("el-row",{directives:[{name:"show",rawName:"v-show",value:2===t.currentTab,expression:"currentTab === 2"}]},[i("el-col",{attrs:{span:24}},[i("el-col",t._b({},"el-col",t.grid,!1),[i("el-form-item",{attrs:{label:"排序："}},[i("el-input-number",{attrs:{min:0,placeholder:"请输入排序",disabled:t.isDisabled},model:{value:t.formValidate.sort,callback:function(e){t.$set(t.formValidate,"sort",e)},expression:"formValidate.sort"}})],1)],1),t._v(" "),i("el-col",t._b({},"el-col",t.grid,!1),[i("el-form-item",{attrs:{label:"积分："}},[i("el-input-number",{attrs:{min:0,placeholder:"请输入排序",disabled:t.isDisabled},model:{value:t.formValidate.giveIntegral,callback:function(e){t.$set(t.formValidate,"giveIntegral",e)},expression:"formValidate.giveIntegral"}})],1)],1),t._v(" "),i("el-col",t._b({},"el-col",t.grid,!1),[i("el-form-item",{attrs:{label:"虚拟销量："}},[i("el-input-number",{attrs:{min:0,placeholder:"请输入排序",disabled:t.isDisabled},model:{value:t.formValidate.ficti,callback:function(e){t.$set(t.formValidate,"ficti",e)},expression:"formValidate.ficti"}})],1)],1)],1),t._v(" "),i("el-col",{attrs:{span:24}},[i("el-form-item",{attrs:{label:"商品推荐："}},[i("el-checkbox-group",{attrs:{size:"small",disabled:t.isDisabled},on:{change:t.onChangeGroup},model:{value:t.checkboxGroup,callback:function(e){t.checkboxGroup=e},expression:"checkboxGroup"}},t._l(t.recommend,(function(e,r){return i("el-checkbox",{key:r,attrs:{label:e.value}},[t._v(t._s(e.name))])})),1)],1)],1),t._v(" "),i("el-col",{attrs:{span:24}},[i("el-form-item",{attrs:{label:"活动优先级："}},[i("div",{staticClass:"color-list acea-row row-middle"},[t._l(t.formValidate.activity,(function(e){return i("div",{key:e,staticClass:"color-item",class:t.activity[e],attrs:{disabled:t.isDisabled,draggable:"true"},on:{dragstart:function(i){return t.handleDragStart(i,e)},dragover:function(i){return i.preventDefault(),t.handleDragOver(i,e)},dragenter:function(i){return t.handleDragEnterFont(i,e)},dragend:function(i){return t.handleDragEnd(i,e)}}},[t._v("\n                "+t._s(e)+"\n              ")])})),t._v(" "),i("div",{staticClass:"tip"},[t._v("可拖动按钮调整活动的优先展示顺序")])],2)])],1),t._v(" "),i("el-col",{attrs:{span:24}},[i("el-form-item",{staticClass:"proCoupon",attrs:{label:"优惠券："}},[i("div",{staticClass:"acea-row"},[t._l(t.formValidate.coupons,(function(e,r){return i("el-tag",{key:r,staticClass:"mr10 mb10",attrs:{closable:!t.isDisabled,"disable-transitions":!1},on:{close:function(i){return t.handleCloseCoupon(e)}}},[t._v("\n                "+t._s(e.name)+"\n              ")])})),t._v(" "),t.isDisabled?t._e():i("el-button",{staticClass:"mr15",on:{click:t.addCoupon}},[t._v("选择优惠券")])],2)])],1)],1),t._v(" "),i("div",{staticClass:"footer-btn"},[i("el-button",{directives:[{name:"show",rawName:"v-show",value:t.currentTab>0,expression:"currentTab > 0"}],staticClass:"submission priamry_border",on:{click:t.handleSubmitUp}},[t._v("上一步")]),t._v(" "),i("el-button",{directives:[{name:"show",rawName:"v-show",value:t.currentTab<1,expression:"currentTab < 1"}],staticClass:"submission",attrs:{type:"primary"},on:{click:function(e){return t.handleSubmitNest("formValidate")}}},[t._v("下一步")]),t._v(" "),i("el-button",{directives:[{name:"show",rawName:"v-show",value:(t.currentTab<2||t.$route.params.id)&&!t.isDisabled,expression:"(currentTab < 2 || $route.params.id) && !isDisabled"}],staticClass:"submission",attrs:{type:"primary"},on:{click:function(e){return t.handleSubmit("formValidate")}}},[t._v("提交")])],1)],1)],1),t._v(" "),i("CreatTemplates",{ref:"addTemplates",on:{getList:t.getShippingList}})],1)},n=[],s=i("8256"),o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"steps"},t._l(t.stepsTitle,(function(e,r){return i("div",{key:r,class:"step-items step-items"+(r+1)+" "+(1==t.curStep?"active":"")},[i("span",{staticClass:"step-num"},[t._v(t._s(r+1))]),t._v(" "),i("span",[t._v(t._s(e))])])})),0)},l=[],c={props:{stepsTitle:{type:Array,default:function(){return[]}},curStep:{type:Number,default:1}},data:function(){return{}},methods:{}},u=c,d=(i("54bc"),i("2877")),m=Object(d["a"])(u,o,l,!1,null,"fd745af0",null),f=m.exports,p=i("73f5"),h=(i("b7be"),i("2f2c")),g=i("e7ac"),v=(i("fca7"),i("ff3a")),b=i("5317"),y=i("61f7"),w=i("c24f");function V(t){return V="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},V(t)}function _(t){return O(t)||x(t)||D(t)||k()}function k(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function x(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function O(t){if(Array.isArray(t))return L(t)}function T(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,r)}return i}function C(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?T(Object(i),!0).forEach((function(e){N(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):T(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}function I(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */I=function(){return e};var t,e={},i=Object.prototype,r=i.hasOwnProperty,a=Object.defineProperty||function(t,e,i){t[e]=i.value},n="function"==typeof Symbol?Symbol:{},s=n.iterator||"@@iterator",o=n.asyncIterator||"@@asyncIterator",l=n.toStringTag||"@@toStringTag";function c(t,e,i){return Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(t){c=function(t,e,i){return t[e]=i}}function u(t,e,i,r){var n=e&&e.prototype instanceof v?e:v,s=Object.create(n.prototype),o=new D(r||[]);return a(s,"_invoke",{value:C(t,i,o)}),s}function d(t,e,i){try{return{type:"normal",arg:t.call(e,i)}}catch(t){return{type:"throw",arg:t}}}e.wrap=u;var m="suspendedStart",f="suspendedYield",p="executing",h="completed",g={};function v(){}function b(){}function y(){}var w={};c(w,s,(function(){return this}));var _=Object.getPrototypeOf,k=_&&_(_(L([])));k&&k!==i&&r.call(k,s)&&(w=k);var x=y.prototype=v.prototype=Object.create(w);function O(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function T(t,e){function i(a,n,s,o){var l=d(t[a],t,n);if("throw"!==l.type){var c=l.arg,u=c.value;return u&&"object"==V(u)&&r.call(u,"__await")?e.resolve(u.__await).then((function(t){i("next",t,s,o)}),(function(t){i("throw",t,s,o)})):e.resolve(u).then((function(t){c.value=t,s(c)}),(function(t){return i("throw",t,s,o)}))}o(l.arg)}var n;a(this,"_invoke",{value:function(t,r){function a(){return new e((function(e,a){i(t,r,e,a)}))}return n=n?n.then(a,a):a()}})}function C(e,i,r){var a=m;return function(n,s){if(a===p)throw Error("Generator is already running");if(a===h){if("throw"===n)throw s;return{value:t,done:!0}}for(r.method=n,r.arg=s;;){var o=r.delegate;if(o){var l=S(o,r);if(l){if(l===g)continue;return l}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(a===m)throw a=h,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);a=p;var c=d(e,i,r);if("normal"===c.type){if(a=r.done?h:f,c.arg===g)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(a=h,r.method="throw",r.arg=c.arg)}}}function S(e,i){var r=i.method,a=e.iterator[r];if(a===t)return i.delegate=null,"throw"===r&&e.iterator.return&&(i.method="return",i.arg=t,S(e,i),"throw"===i.method)||"return"!==r&&(i.method="throw",i.arg=new TypeError("The iterator does not provide a '"+r+"' method")),g;var n=d(a,e.iterator,i.arg);if("throw"===n.type)return i.method="throw",i.arg=n.arg,i.delegate=null,g;var s=n.arg;return s?s.done?(i[e.resultName]=s.value,i.next=e.nextLoc,"return"!==i.method&&(i.method="next",i.arg=t),i.delegate=null,g):s:(i.method="throw",i.arg=new TypeError("iterator result is not an object"),i.delegate=null,g)}function $(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function j(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function D(t){this.tryEntries=[{tryLoc:"root"}],t.forEach($,this),this.reset(!0)}function L(e){if(e||""===e){var i=e[s];if(i)return i.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,n=function i(){for(;++a<e.length;)if(r.call(e,a))return i.value=e[a],i.done=!1,i;return i.value=t,i.done=!0,i};return n.next=n}}throw new TypeError(V(e)+" is not iterable")}return b.prototype=y,a(x,"constructor",{value:y,configurable:!0}),a(y,"constructor",{value:b,configurable:!0}),b.displayName=c(y,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===b||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,y):(t.__proto__=y,c(t,l,"GeneratorFunction")),t.prototype=Object.create(x),t},e.awrap=function(t){return{__await:t}},O(T.prototype),c(T.prototype,o,(function(){return this})),e.AsyncIterator=T,e.async=function(t,i,r,a,n){void 0===n&&(n=Promise);var s=new T(u(t,i,r,a),n);return e.isGeneratorFunction(i)?s:s.next().then((function(t){return t.done?t.value:s.next()}))},O(x),c(x,l,"Generator"),c(x,s,(function(){return this})),c(x,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),i=[];for(var r in e)i.push(r);return i.reverse(),function t(){for(;i.length;){var r=i.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=L,D.prototype={constructor:D,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(j),!e)for(var i in this)"t"===i.charAt(0)&&r.call(this,i)&&!isNaN(+i.slice(1))&&(this[i]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var i=this;function a(r,a){return o.type="throw",o.arg=e,i.next=r,a&&(i.method="next",i.arg=t),!!a}for(var n=this.tryEntries.length-1;n>=0;--n){var s=this.tryEntries[n],o=s.completion;if("root"===s.tryLoc)return a("end");if(s.tryLoc<=this.prev){var l=r.call(s,"catchLoc"),c=r.call(s,"finallyLoc");if(l&&c){if(this.prev<s.catchLoc)return a(s.catchLoc,!0);if(this.prev<s.finallyLoc)return a(s.finallyLoc)}else if(l){if(this.prev<s.catchLoc)return a(s.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<s.finallyLoc)return a(s.finallyLoc)}}}},abrupt:function(t,e){for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i];if(a.tryLoc<=this.prev&&r.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var n=a;break}}n&&("break"===t||"continue"===t)&&n.tryLoc<=e&&e<=n.finallyLoc&&(n=null);var s=n?n.completion:{};return s.type=t,s.arg=e,n?(this.method="next",this.next=n.finallyLoc,g):this.complete(s)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),g},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var i=this.tryEntries[e];if(i.finallyLoc===t)return this.complete(i.completion,i.afterLoc),j(i),g}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var i=this.tryEntries[e];if(i.tryLoc===t){var r=i.completion;if("throw"===r.type){var a=r.arg;j(i)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,i,r){return this.delegate={iterator:L(e),resultName:i,nextLoc:r},"next"===this.method&&(this.arg=t),g}},e}function S(t,e,i,r,a,n,s){try{var o=t[n](s),l=o.value}catch(t){return void i(t)}o.done?e(l):Promise.resolve(l).then(r,a)}function $(t){return function(){var e=this,i=arguments;return new Promise((function(r,a){var n=t.apply(e,i);function s(t){S(n,r,a,s,o,"next",t)}function o(t){S(n,r,a,s,o,"throw",t)}s(void 0)}))}}function j(t,e){var i="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!i){if(Array.isArray(t)||(i=D(t))||e&&t&&"number"==typeof t.length){i&&(t=i);var r=0,a=function(){};return{s:a,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var n,s=!0,o=!1;return{s:function(){i=i.call(t)},n:function(){var t=i.next();return s=t.done,t},e:function(t){o=!0,n=t},f:function(){try{s||null==i.return||i.return()}finally{if(o)throw n}}}}function D(t,e){if(t){if("string"==typeof t)return L(t,e);var i={}.toString.call(t).slice(8,-1);return"Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?L(t,e):void 0}}function L(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,r=Array(e);i<e;i++)r[i]=t[i];return r}function N(t,e,i){return(e=P(e))in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function P(t){var e=E(t,"string");return"symbol"==V(e)?e:e+""}function E(t,e){if("object"!=V(t)||!t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var r=i.call(t,e||"default");if("object"!=V(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var F={attrName:{title:"规格名称"},price:{title:"价格(元)"},otPrice:{title:"原价"},stock:{title:"库存"},cost:{title:"成本价"},sort:{title:"排序"}},A={image:"",sliderImages:[],videoLink:"",sliderImage:"",storeName:"",storeInfo:"",saleTime:null,popularity:null,tag:[],keyword:"",cateIds:[],cateId:null,unitName:"",sort:0,maxPurchaseCount:"",deliveryType:[0],allowedUserIds:[],giveIntegral:0,freightType:0,supportExchange:!0,limitPurchaseCount:!1,limitSpecificUsers:!1,postage:null,ficti:0,isShow:!1,isBenefit:!1,isNew:!1,isGood:!1,isHot:!1,isBest:!1,tempId:"",attrValue:[{image:"",price:0,cost:0,otPrice:0,stock:0,barCode:"",weight:0,volume:0}],attr:[],selectRule:"",isSub:!1,content:"",specType:!0,id:0,couponIds:[],coupons:[],activity:["默认","秒杀","砍价","拼团"]},G={price:{title:"价格(元)"},otPrice:{title:"原价"},stock:{title:"库存"},cost:{title:"成本价"},barCode:{title:"商品编号"},weight:{title:"重量（KG）"},volume:{title:"体积(m³)"}},B={name:"ProductProductAdd",components:{Templates:b["a"],CreatTemplates:v["a"],Tinymce:s["a"],Steps:f},data:function(){return{isDisabled:"1"===this.$route.params.isDisabled,activity:{"默认":"red","秒杀":"blue","砍价":"green","拼团":"yellow"},props2:{children:"child",label:"name",value:"id",multiple:!0,emitPath:!1},saleTime:null,isSaleTime:!0,checkboxGroup:[],recommend:[],userList:[],tabs:[],detailsImages:[],fullscreenLoading:!1,props:{multiple:!0},active:0,attrValueList:[{attrName:"",image:"",price:0,otPrice:0,stock:0,cost:0,sort:0}],attrObjInput:Object.assign({},F),OneattrValue:[Object.assign({},A.attrValue[0])],ManyAttrValue:[Object.assign({},A.attrValue[0])],ruleList:[],merCateList:[],shippingList:[],formThead:Object.assign({},G),formValidate:Object.assign({},A),formDynamics:{ruleName:"",ruleValue:[]},tempData:{page:1,limit:9999},manyTabTit:{},manyTabDate:{},grid2:{xl:12,lg:12,md:12,sm:24,xs:24},formDynamic:{attrsName:"",attrsVal:""},isBtn:!1,manyFormValidate:[],currentTab:0,isChoice:"",grid:{xl:8,lg:8,md:12,sm:24,xs:24},ruleValidate:{storeName:[{required:!0,message:"请输入商品名称",trigger:"blur"}],cateIds:[{required:!0,message:"请选择商品分类",trigger:"change",type:"array",min:"1"}],unitName:[{required:!0,message:"请输入单位",trigger:"blur"}],storeInfo:[{required:!0,message:"请输入商品标题",trigger:"blur"}],tempId:[{required:!0,message:"请选择运费模板",trigger:"change"}],image:[{required:!0,message:"请上传商品图",trigger:"change"}],deliveryType:[{required:!0,message:"请选择配送方式",trigger:"change"}],freightType:[{required:!0,message:"请选择",trigger:"change"}],sliderImages:[{required:!0,message:"请上传商品图",type:"array",trigger:"change"}],specType:[{required:!0,message:"请选择商品规格",trigger:"change"}]},attrInfo:{},tableFrom:{page:1,limit:9999,keywords:""},tempRoute:{},keyNum:0,isAttr:!1,showAll:!1,videoLink:""}},computed:{attrValue:function(){var t=Object.assign({},A.attrValue[0]);return delete t.image,t},oneFormBatch:function(){var t=[Object.assign({},A.attrValue[0])];return delete t[0].barCode,t}},watch:{"formValidate.attr":{handler:function(t){this.formValidate.specType&&this.isAttr&&this.watCh(t)},immediate:!1,deep:!0},"formValidate.limitPurchaseCount":{handler:function(t){!1===t&&(this.formValidate.maxPurchaseCount=null)},immediate:!1,deep:!0},isSaleTime:{handler:function(t){!0===t&&(this.saleTime=null)},immediate:!1,deep:!0}},created:function(){this.tempRoute=Object.assign({},this.$route),this.$route.params.id&&this.formValidate.specType&&this.$watch("formValidate.attr",this.watCh)},mounted:function(){this.formValidate.sliderImages=[],this.$route.params.id&&(this.setTagsViewTitle(),this.getInfo()),this.getCategorySelect(),this.getShippingList(),this.getGoodsType(),this.getUserList()},methods:(r={keyupEvent:function(t,e,i,r){if("barCode"!==t){var a=/^\D*([0-9]\d*\.?\d{0,2})?.*$/;switch(r){case 1:this.oneFormBatch[i][t]=0==e?"stock"===t?0:.01:"stock"===t?parseInt(e):this.$set(this.oneFormBatch[i],t,e.toString().replace(a,"$1"));break;case 2:this.OneattrValue[i][t]=0==e?"stock"===t?0:.01:"stock"===t?parseInt(e):this.$set(this.OneattrValue[i],t,e.toString().replace(a,"$1"));break;default:this.ManyAttrValue[i][t]=0==e?"stock"===t?0:.01:"stock"===t?parseInt(e):this.$set(this.ManyAttrValue[i],t,e.toString().replace(a,"$1"));break}}},getUserList:function(){var t=this;Object(w["C"])({page:1,limit:999}).then((function(e){t.userList=e.list}))},handleCloseCoupon:function(t){this.isAttr=!0,this.formValidate.coupons.splice(this.formValidate.coupons.indexOf(t),1),this.formValidate.couponIds.splice(this.formValidate.couponIds.indexOf(t.id),1)},addCoupon:function(){var t=this;this.$modalCoupon("wu",this.keyNum+=1,this.formValidate.coupons,(function(e){t.formValidate.couponIds=[],t.formValidate.coupons=e,e.map((function(e){t.formValidate.couponIds.push(e.id)}))}),"")},setTagsViewTitle:function(){var t=this.isDisabled?"商品详情":"编辑商品",e=Object.assign({},this.tempRoute,{title:"".concat(t,"-").concat(this.$route.params.id)});this.$store.dispatch("tagsView/updateVisitedView",e)},onChangeGroup:function(){this.checkboxGroup.includes("isGood")?this.formValidate.isGood=!0:this.formValidate.isGood=!1,this.checkboxGroup.includes("isBenefit")?this.formValidate.isBenefit=!0:this.formValidate.isBenefit=!1,this.checkboxGroup.includes("isBest")?this.formValidate.isBest=!0:this.formValidate.isBest=!1,this.checkboxGroup.includes("isNew")?this.formValidate.isNew=!0:this.formValidate.isNew=!1,this.checkboxGroup.includes("isHot")?this.formValidate.isHot=!0:this.formValidate.isHot=!1},watCh:function(t){var e=this,i={},r={};this.formValidate.attr.forEach((function(t,e){i[t.attrName]={title:t.attrName},r[t.attrName]=""})),this.ManyAttrValue=this.attrFormat(t),this.ManyAttrValue.forEach((function(t,i){var r=Object.values(t.attrValue).sort().join("/");e.attrInfo[r]&&(e.ManyAttrValue[i]=e.attrInfo[r])})),this.attrInfo=[],this.ManyAttrValue.forEach((function(t){e.attrInfo[Object.values(t.attrValue).sort().join("/")]=t})),this.manyTabTit=i,this.manyTabDate=r,this.formThead=Object.assign({},this.formThead,i)},attrFormat:function(t){var e=[],i=[];return r(t);function r(t){if(t.length>1)t.forEach((function(r,a){0===a&&(e=t[a]["attrValue"]);var n=[];e&&(e.forEach((function(e){t[a+1]&&t[a+1]["attrValue"]&&t[a+1]["attrValue"].forEach((function(r){var s=(0!==a?"":t[a]["attrName"]+"_")+e+"$&"+t[a+1]["attrName"]+"_"+r;if(n.push(s),a===t.length-2){var o={image:"",price:0,cost:0,otPrice:0,stock:0,barCode:"",weight:0,volume:0,brokerage:0,brokerage_two:0};for(var l in s.split("$&").forEach((function(t,e){var i=t.split("_");o["attrValue"]||(o["attrValue"]={}),o["attrValue"][i[0]]=i.length>1?i[1]:""})),o.attrValue)o[l]=o.attrValue[l];i.push(o)}}))})),e=n.length?n:[])}));else{var r=[];t.forEach((function(t,e){t["attrValue"].forEach((function(e,a){for(var n in r[a]=t["attrName"]+"_"+e,i[a]={image:"",price:0,cost:0,otPrice:0,stock:0,barCode:"",weight:0,volume:0,brokerage:0,brokerage_two:0,attrValue:N({},t["attrName"],e)},i[a].attrValue)i[a][n]=i[a].attrValue[n]}))})),e.push(r.join("$&"))}return i}},addTem:function(){this.$refs.addTemplates.dialogVisible=!0,this.$refs.addTemplates.getCityList()},addRule:function(){var t=this;this.$modalAttr(this.formDynamics,(function(){t.productGetRule()}))},onChangeSpec:function(t){this.isAttr=!0,t&&this.productGetRule()}},N(N(N(N(N(N(N(N(N(N(r,"onChangeSpec",(function(t){this.isAttr=!0,t&&this.productGetRule()})),"confirm",(function(){var t=this;if(this.isAttr=!0,!this.formValidate.selectRule)return this.$message.warning("请选择属性");var e=[];this.ruleList.forEach((function(i){i.id===t.formValidate.selectRule&&i.ruleValue.forEach((function(t){e.push({attrName:t.value,attrValue:t.detail})})),t.formValidate.attr=e}))})),"getCategorySelect",(function(){var t=this;Object(p["d"])({status:-1,type:1}).then((function(e){t.merCateList=t.filerMerCateList(e),console.log(e,868686868);var i=[];e.forEach((function(t,e){i[e]=t,t.child&&(i[e].child=t.child.filter((function(t){return!0===t.status})))})),t.merCateList=t.filerMerCateList(i)}))})),"filerMerCateList",(function(t){return t.map((function(t){return t.child||(t.disabled=!0),t.label=t.name,t}))})),"productGetRule",(function(){var t=this;Object(p["z"])(this.tableFrom).then((function(e){for(var i=e.list,r=0;r<i.length;r++)i[r].ruleValue=JSON.parse(i[r].ruleValue);t.ruleList=i}))})),"getShippingList",(function(){var t=this;Object(h["o"])(this.tempData).then((function(e){t.shippingList=e.list}))})),"showInput",(function(t){this.$set(t,"inputVisible",!0)})),"onChangetype",(function(t){var e=this;1===t?(this.OneattrValue.map((function(t){e.$set(t,"brokerage",null),e.$set(t,"brokerageTwo",null)})),this.ManyAttrValue.map((function(t){e.$set(t,"brokerage",null),e.$set(t,"brokerageTwo",null)}))):(this.OneattrValue.map((function(t){delete t.brokerage,delete t.brokerageTwo,e.$set(t,"brokerage",null),e.$set(t,"brokerageTwo",null)})),this.ManyAttrValue.map((function(t){delete t.brokerage,delete t.brokerageTwo})))})),"delAttrTable",(function(t){this.attrValueList.splice(t,1)})),"addAttrTable",(function(t){this.attrValueList.push({attrName:"",image:"",price:0,otPrice:0,stock:0,cost:0,sort:0})})),N(N(N(N(N(N(N(N(N(N(r,"batchAdd",(function(){var t,e=j(this.ManyAttrValue);try{for(e.s();!(t=e.n()).done;){var i=t.value;this.$set(i,"image",this.oneFormBatch[0].image),this.$set(i,"price",this.oneFormBatch[0].price),this.$set(i,"cost",this.oneFormBatch[0].cost),this.$set(i,"otPrice",this.oneFormBatch[0].otPrice),this.$set(i,"stock",this.oneFormBatch[0].stock),this.$set(i,"barCode",this.oneFormBatch[0].barCode),this.$set(i,"weight",this.oneFormBatch[0].weight),this.$set(i,"volume",this.oneFormBatch[0].volume),this.$set(i,"brokerage",this.oneFormBatch[0].brokerage),this.$set(i,"brokerageTwo",this.oneFormBatch[0].brokerageTwo)}}catch(r){e.e(r)}finally{e.f()}})),"addBtn",(function(){this.clearAttr(),this.isBtn=!0})),"offAttrName",(function(){this.isBtn=!1})),"clearAttr",(function(){this.isAttr=!0,this.formDynamic.attrsName="",this.formDynamic.attrsVal=""})),"handleRemoveAttr",(function(t){this.isAttr=!0,this.formValidate.attr.splice(t,1),this.manyFormValidate.splice(t,1)})),"handleClose",(function(t,e){t.splice(e,1)})),"createAttrName",(function(){if(this.isAttr=!0,this.formDynamic.attrsName&&this.formDynamic.attrsVal){var t={attrName:this.formDynamic.attrsName,attrValue:[this.formDynamic.attrsVal]};this.formValidate.attr.push(t);var e={};this.formValidate.attr=this.formValidate.attr.reduce((function(t,i){return!e[i.attrName]&&(e[i.attrName]=t.push(i)),t}),[]),this.clearAttr(),this.isBtn=!1}else this.$Message.warning("请添加完整的规格！")})),"createAttr",(function(t,e){if(this.isAttr=!0,t){this.formValidate.attr[e].attrValue.push(t);var i={};this.formValidate.attr[e].attrValue=this.formValidate.attr[e].attrValue.reduce((function(t,e){return!i[e]&&(i[e]=t.push(e)),t}),[]),this.formValidate.attr[e].inputVisible=!1}else this.$message.warning("请添加属性")})),"showAllSku",(function(){0==this.isAttr?(this.isAttr=!0,this.formValidate.specType&&this.isAttr&&this.watCh(this.formValidate.attr)):1==this.isAttr&&(this.isAttr=!1,this.getInfo())})),"getInfo",(function(){var t=this;this.fullscreenLoading=!0,Object(p["l"])(this.$route.params.id).then(function(){var e=$(I().mark((function e(i){var r,a,n;return I().wrap((function(e){while(1)switch(e.prev=e.next){case 0:r=i,t.formValidate={image:t.$selfUtil.setDomain(r.image),sliderImage:r.sliderImage,sliderImages:JSON.parse(r.sliderImage),storeName:r.storeName,storeInfo:r.storeInfo,keyword:r.keyword,videoLink:r.videoLink,cateIds:r.cateId.split(","),cateId:r.cateId,unitName:r.unitName,sort:r.sort,isShow:r.isShow,isBenefit:r.isBenefit,isNew:r.isNew,deliveryType:r.deliveryType?r.deliveryTyp.split(","):[],freightType:r.freightType,postage:r.postage,supportExchange:r.supportExchange,supportReturn7Days:r.supportReturn7Days,limitPurchaseCount:r.limitPurchaseCount,maxPurchaseCount:r.maxPurchaseCount,limitSpecificUsers:r.limitSpecificUsers,allowedUserIds:r.allowedUserIds,isGood:r.isGood,isHot:r.isHot,isBest:r.isBest,tempId:r.tempId,tag:r.tag?r.tag.split(","):[],popularity:r.popularity,attr:r.attr,attrValue:r.attrValue,selectRule:r.selectRule,isSub:r.isSub,content:r.content,specType:r.specType,id:r.id,giveIntegral:r.giveIntegral,ficti:r.ficti,coupons:r.coupons,couponIds:r.couponIds,activity:r.activityStr?r.activityStr.split(","):["默认","秒杀","砍价","拼团"]},r.saleTime?(t.isSaleTime=!1,t.saleTime=r.saleTime):t.isSaleTime=!0,t.detailsImages=r.detailImages&&r.detailImages.split(","),console.log(t.detailImages,876),t.attrValueList=r.attrValue.map((function(t){var e=t.attrName,i=t.image,r=t.price,a=t.otPrice,n=t.stock,s=t.cost,o=t.sort;return{attrName:e,image:i,price:r,otPrice:a,stock:n,cost:s,sort:o}})),a=JSON.parse(r.sliderImage),n=[],Object.keys(a).map((function(e){n.push(t.$selfUtil.setDomain(a[e]))})),t.formValidate.sliderImages=[].concat(n),r.isHot&&t.checkboxGroup.push("isHot"),r.isGood&&t.checkboxGroup.push("isGood"),r.isBenefit&&t.checkboxGroup.push("isBenefit"),r.isBest&&t.checkboxGroup.push("isBest"),r.isNew&&t.checkboxGroup.push("isNew"),t.productGetRule(),t.fullscreenLoading=!1;case 17:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.fullscreenLoading=!1,t.$message.error(e.message)}))})),N(N(N(N(N(N(N(N(N(N(r,"handleRemove",(function(t){this.formValidate.sliderImages.splice(t,1)})),"handleRemoveDetailsImg",(function(t){this.detailsImages.splice(t,1)})),"modalPicTap",(function(t,e,i,r){console.log(t,e,i,7758585858);var a=this;a.isDisabled||this.$modalUpload((function(r){if("1"!==t||e||(a.formValidate.image=r[0].sattDir,a.OneattrValue[0].image=r[0].sattDir),"2"===t&&!e){if(r.length>6)return this.$message.warning("最多选择6张图片！");if(r.length+a.formValidate.sliderImages.length>6)return this.$message.warning("最多选择6张图片！");r.map((function(t){a.formValidate.sliderImages.push(t.sattDir)}))}if("1"===t&&"dan"===e&&(a.OneattrValue[0].image=r[0].sattDir),"1"===t&&"duo"===e&&(a.attrValueList[i].image=r[0].sattDir),"1"===t&&"pi"===e&&(a.oneFormBatch[0].image=r[0].sattDir),"3"===t&&(a.formValidate.videoLink=r[0].sattDir),"4"===t&&!e){if(r.length>6)return this.$message.warning("最多选择6张图片！");if(r.length+a.detailsImages.length>6)return this.$message.warning("最多选择6张图片！");r.map((function(t){a.detailsImages.push(t.sattDir)})),console.log(a.detailsImages,7758585858)}}),t,"content")})),"handleSubmitUp",(function(){this.currentTab--<0&&(this.currentTab=0)})),"handleSubmitNest",(function(t){var e=this;this.$refs[t].validate((function(t){t?e.currentTab++>2&&(e.currentTab=0):e.formValidate.store_name&&e.formValidate.cate_id&&e.formValidate.keyword&&e.formValidate.unit_name&&e.formValidate.store_info&&e.formValidate.image&&e.formValidate.slider_image||e.$message.warning("请填写完整商品信息！")}))})),"handleSubmit",Object(y["a"])((function(t){var e=this;this.onChangeGroup(),this.formValidate.cateId=this.formValidate.cateIds.join(","),this.formValidate.sliderImage=JSON.stringify(this.formValidate.sliderImages),this.saleTime?this.formValidate.saleTime=parseInt(new Date(this.saleTime).getTime().toString().slice(0,-3)):this.formValidate.saleTime=0;var i=this.attrValueList.map((function(t,i){return C(C({},t),{},{attrValue:JSON.stringify(N({},e.formValidate.unitName,t.attrName))})}));this.$refs[t].validate((function(t){t?(e.fullscreenLoading=!0,e.$route.params.id?Object(p["r"])(C(C({},e.formValidate),{},{deliveryType:e.formValidate.deliveryType&&e.formValidate.deliveryType.join(","),detailImages:e.detailsImages.join(","),tag:e.formValidate.tag.join(","),attrValue:i})).then(function(){var t=$(I().mark((function t(i){return I().wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.$message.success("编辑成功"),setTimeout((function(){e.$router.push({path:"/store/index"})}),500),e.fullscreenLoading=!1;case 3:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.fullscreenLoading=!1})):Object(p["j"])(C(C({},e.formValidate),{},{deliveryType:e.formValidate.deliveryType&&e.formValidate.deliveryType.join(","),detailImages:e.detailsImages.join(","),tag:e.formValidate.tag.join(","),attrValue:i})).then(function(){var t=$(I().mark((function t(i){return I().wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.$message.success("新增成功"),setTimeout((function(){e.$router.push({path:"/store/index"})}),500),e.fullscreenLoading=!1;case 3:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.fullscreenLoading=!1}))):e.formValidate.storeName&&e.formValidate.cateId&&e.formValidate.keyword&&e.formValidate.unitName&&e.formValidate.storeInfo&&e.formValidate.image&&e.formValidate.sliderImages||e.$message.warning("请填写完整商品信息！")}))}))),"validate",(function(t,e,i){!1===e&&this.$message.warning(i)})),"handleDragStart",(function(t,e){this.isDisabled||(this.dragging=e)})),"handleDragEnd",(function(t,e){this.isDisabled||(this.dragging=null)})),"handleDragOver",(function(t){this.isDisabled||(t.dataTransfer.dropEffect="move")})),N(N(N(N(r,"handleDragEnter",(function(t,e){if(!this.isDisabled){if(t.dataTransfer.effectAllowed="move",e===this.dragging)return;var i=_(this.formValidate.sliderImages),r=i.indexOf(this.dragging),a=i.indexOf(e);i.splice.apply(i,[a,0].concat(_(i.splice(r,1)))),this.formValidate.sliderImages=i}})),"handleDragEnterDetails",(function(t,e){if(!this.isDisabled){if(t.dataTransfer.effectAllowed="move",e===this.dragging)return;var i=_(this.detailsImages),r=i.indexOf(this.dragging),a=i.indexOf(e);i.splice.apply(i,[a,0].concat(_(i.splice(r,1)))),this.detailsImages=i}})),"handleDragEnterFont",(function(t,e){if(!this.isDisabled){if(t.dataTransfer.effectAllowed="move",e===this.dragging)return;var i=_(this.formValidate.activity),r=i.indexOf(this.dragging),a=i.indexOf(e);i.splice.apply(i,[a,0].concat(_(i.splice(r,1)))),this.formValidate.activity=i}})),"getGoodsType",(function(){var t=this;Object(g["a"])({gid:70}).then((function(e){var i=e.list,r=[],a=[],n=[{name:"是否热卖",value:"isGood"}],s=[{name:"",value:"isHot",type:"2"},{name:"",value:"isBenefit",type:"4"},{name:"",value:"isBest",type:"1"},{name:"",value:"isNew",type:"3"}];i.forEach((function(t){var e={};e.value=JSON.parse(t.value),e.id=t.id,e.gid=t.gid,e.status=t.status,r.push(e)})),r.forEach((function(t){var e={};e.name=t.value.fields[1].value,e.status=t.status,e.type=t.value.fields[3].value,a.push(e)})),s.forEach((function(t){a.forEach((function(e){t.type==e.type&&n.push({name:e.name,value:t.value,type:t.type})}))})),t.recommend=n}))})))},R=B,q=(i("ff03"),Object(d["a"])(R,a,n,!1,null,"61be3012",null));e["default"]=q.exports},5317:function(t,e,i){"use strict";var r=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div")},a=[],n=i("2877"),s={},o=Object(n["a"])(s,r,a,!1,null,null,null);e["a"]=o.exports},"54bc":function(t,e,i){"use strict";i("5da6")},"5da6":function(t,e,i){},af11:function(t,e,i){},cf0d:function(t,e,i){},e16d:function(t,e,i){"use strict";i("cf0d")},e7ac:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"d",(function(){return n})),i.d(e,"e",(function(){return s})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return l}));var r=i("b775");function a(t){var e={id:t.id};return Object(r["a"])({url:"/admin/system/group/delete",method:"GET",params:e})}function n(t){var e={keywords:t.keywords,page:t.page,limit:t.limit};return Object(r["a"])({url:"/admin/system/group/list",method:"GET",params:e})}function s(t){var e={formId:t.formId,info:t.info,name:t.name};return Object(r["a"])({url:"/admin/system/group/save",method:"POST",params:e})}function o(t){var e={formId:t.formId,info:t.info,name:t.name,id:t.id};return Object(r["a"])({url:"/admin/system/group/update",method:"POST",params:e})}function l(t){var e={gid:t.gid};return Object(r["a"])({url:"/admin/system/group/data/list",method:"GET",params:e})}},ff03:function(t,e,i){"use strict";i("af11")},ff3a:function(t,e,i){"use strict";var r=function(){var t=this,e=t.$createElement,i=t._self._c||e;return t.dialogVisible?i("el-dialog",{attrs:{title:"运费模板",visible:t.dialogVisible,width:"1000px","before-close":t.handleClose},on:{"update:visible":function(e){t.dialogVisible=e}}},[t.dialogVisible?i("el-form",{ref:"ruleForm",attrs:{model:t.ruleForm,"label-width":"120px",size:"mini",rules:t.rules}},[i("el-form-item",{attrs:{label:"模板名称",prop:"name"}},[i("el-input",{staticClass:"withs",attrs:{placeholder:"请输入模板名称"},model:{value:t.ruleForm.name,callback:function(e){t.$set(t.ruleForm,"name",e)},expression:"ruleForm.name"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"计费方式",prop:"type"}},[i("el-radio-group",{on:{change:function(e){return t.changeRadio(t.ruleForm.type)}},model:{value:t.ruleForm.type,callback:function(e){t.$set(t.ruleForm,"type",e)},expression:"ruleForm.type"}},[i("el-radio",{attrs:{label:1}},[t._v("按件数")]),t._v(" "),i("el-radio",{attrs:{label:2}},[t._v("按重量")]),t._v(" "),i("el-radio",{attrs:{label:3}},[t._v("按体积")])],1)],1),t._v(" "),i("el-form-item",{attrs:{label:"配送区域及运费",prop:"region"}},[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticClass:"tempBox",staticStyle:{width:"100%"},attrs:{data:t.ruleForm.region,border:"",fit:"","highlight-current-row":"",size:"mini"}},[i("el-table-column",{attrs:{align:"center",label:"可配送区域","min-width":"260"},scopedSlots:t._u([{key:"default",fn:function(e){return[0===e.$index?i("span",[t._v("默认全国")]):i("el-cascader",{staticStyle:{width:"98%"},attrs:{options:t.cityList,props:t.props,"collapse-tags":"",clearable:"",filterable:""},on:{change:t.changeRegion},model:{value:e.row.city_ids,callback:function(i){t.$set(e.row,"city_ids",i)},expression:"scope.row.city_ids"}})]}}],null,!1,41555841)}),t._v(" "),i("el-table-column",{attrs:{"min-width":"130px",align:"center",label:t.columns.title,prop:"first"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-form-item",{attrs:{rules:t.rules.first,prop:"region."+e.$index+".first"}},[i("el-input-number",{attrs:{"controls-position":"right","step-strictly":1===t.ruleForm.type,min:1===t.ruleForm.type?1:.1},model:{value:e.row.first,callback:function(i){t.$set(e.row,"first",i)},expression:"scope.row.first"}})],1)]}}],null,!1,2918704294)}),t._v(" "),i("el-table-column",{attrs:{"min-width":"120px",align:"center",label:"运费（元）",prop:"firstPrice"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-form-item",{attrs:{rules:t.rules.firstPrice,prop:"region."+e.$index+".firstPrice"}},[i("el-input-number",{attrs:{"controls-position":"right",min:0},model:{value:e.row.firstPrice,callback:function(i){t.$set(e.row,"firstPrice",i)},expression:"scope.row.firstPrice"}})],1)]}}],null,!1,3560784729)}),t._v(" "),i("el-table-column",{attrs:{"min-width":"120px",align:"center",label:t.columns.title2,prop:"renewal"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-form-item",{attrs:{rules:t.rules.renewal,prop:"region."+e.$index+".renewal"}},[i("el-input-number",{attrs:{"controls-position":"right","step-strictly":1===t.ruleForm.type,min:1===t.ruleForm.type?1:.1},model:{value:e.row.renewal,callback:function(i){t.$set(e.row,"renewal",i)},expression:"scope.row.renewal"}})],1)]}}],null,!1,3001982106)}),t._v(" "),i("el-table-column",{attrs:{"class-name":"status-col",align:"center",label:"续费（元）","min-width":"120",prop:"renewalPrice"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-form-item",{attrs:{rules:t.rules.renewalPrice,prop:"region."+e.$index+".renewalPrice"}},[i("el-input-number",{attrs:{"controls-position":"right",min:0},model:{value:e.row.renewalPrice,callback:function(i){t.$set(e.row,"renewalPrice",i)},expression:"scope.row.renewalPrice"}})],1)]}}],null,!1,1318028453)}),t._v(" "),i("el-table-column",{attrs:{align:"center",label:"操作","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[e.$index>0?i("el-button",{attrs:{type:"text",size:"small"},on:{click:function(i){return t.confirmEdit(t.ruleForm.region,e.$index)}}},[t._v("\n              删除\n            ")]):t._e()]}}],null,!1,3477974826)})],1)],1),t._v(" "),i("el-form-item",[i("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-edit"},on:{click:function(e){return t.addRegion(t.ruleForm.region)}}},[t._v("\n        添加配送区域\n      ")])],1),t._v(" "),i("el-form-item",{attrs:{label:"指定包邮",prop:"appoint"}},[i("el-radio-group",{model:{value:t.ruleForm.appoint,callback:function(e){t.$set(t.ruleForm,"appoint",e)},expression:"ruleForm.appoint"}},[i("el-radio",{attrs:{label:!0}},[t._v("开启")]),t._v(" "),i("el-radio",{attrs:{label:!1}},[t._v("关闭")])],1)],1),t._v(" "),!0===t.ruleForm.appoint?i("el-form-item",{attrs:{prop:"free"}},[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:t.ruleForm.free,border:"",fit:"","highlight-current-row":"",size:"mini"}},[i("el-table-column",{attrs:{align:"center",label:"选择地区","min-width":"220"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[i("el-cascader",{staticStyle:{width:"95%"},attrs:{options:t.cityList,props:t.props,"collapse-tags":"",clearable:""},model:{value:r.city_ids,callback:function(e){t.$set(r,"city_ids",e)},expression:"row.city_ids"}})]}}],null,!1,3891925036)}),t._v(" "),i("el-table-column",{attrs:{"min-width":"180px",align:"center",label:t.columns.title3},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[i("el-input-number",{attrs:{"controls-position":"right","step-strictly":1===t.ruleForm.type,min:1===t.ruleForm.type?1:.1},model:{value:r.number,callback:function(e){t.$set(r,"number",e)},expression:"row.number"}})]}}],null,!1,2163935474)}),t._v(" "),i("el-table-column",{attrs:{"min-width":"120px",align:"center",label:"包邮金额（元）"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[i("el-input-number",{attrs:{"controls-position":"right"},model:{value:r.price,callback:function(e){t.$set(r,"price",e)},expression:"row.price"}})]}}],null,!1,187737026)}),t._v(" "),i("el-table-column",{attrs:{align:"center",label:"操作","min-width":"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-button",{attrs:{type:"text",size:"small"},on:{click:function(i){return t.confirmEdit(t.ruleForm.free,e.$index)}}},[t._v("\n              删除\n            ")])]}}],null,!1,4029474057)})],1)],1):t._e(),t._v(" "),!0===t.ruleForm.appoint?i("el-form-item",[i("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-edit"},on:{click:function(e){return t.addFree(t.ruleForm.free)}}},[t._v("\n        添加指定包邮区域\n      ")])],1):t._e(),t._v(" "),i("el-form-item",{attrs:{label:"排序"}},[i("el-input",{staticClass:"withs",attrs:{placeholder:"请输入排序"},model:{value:t.ruleForm.sort,callback:function(e){t.$set(t.ruleForm,"sort",e)},expression:"ruleForm.sort"}})],1)],1):t._e(),t._v(" "),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(e){return t.onClose("ruleForm")}}},[t._v("取 消")]),t._v(" "),i("el-button",{attrs:{type:"primary",loading:t.loading},on:{click:function(e){return t.onsubmit("ruleForm")}}},[t._v("确 定")])],1)],1):t._e()},a=[],n=i("2f2c"),s=i("5c96"),o={name:"",type:1,appoint:!1,sort:0,region:[{first:0,firstPrice:0,renewal:0,renewalPrice:0,city_ids:[]}],undelivery:0,free:[],undelives:{},city_id3:[]},l="重量（kg）",c="体积（m³）",u=[{title:"首件",title2:"续件",title3:"包邮件数"},{title:"首件".concat(l),title2:"续件".concat(l),title3:"包邮".concat(l)},{title:"首件".concat(c),title2:"续件".concat(c),title3:"包邮".concat(c)}],d={name:"CreatTemplates",components:{},data:function(){return{loading:!1,rules:{name:[{required:!0,message:"请输入模板名称",trigger:"blur"}],free:[{type:"array",required:!0,message:"请至少添加一个地区",trigger:"change"}],appoint:[{required:!0,message:"请选择是否指定包邮",trigger:"change"}],undelivery:[{required:!0,message:"请选择是否指定区域不配送",trigger:"change"}],type:[{required:!0,message:"请选择计费方式",trigger:"change"}],region:[{required:!0,message:"请选择活动区域",trigger:"change"}],city_id3:[{type:"array",required:!0,message:"请至少选择一个地区",trigger:"change"}],first:[{required:!0,message:"请输入",trigger:"blur"}],renewal:[{required:!0,message:"请输入",trigger:"blur"}],firstPrice:[{required:!0,message:"请输入运费",trigger:"blur"}],renewalPrice:[{required:!0,message:"请输入续费",trigger:"blur"}]},nodeKey:"city_id",props:{children:"child",label:"name",value:"cityId",multiple:!0},dialogVisible:!1,ruleForm:Object.assign({},o),listLoading:!1,cityList:[],columns:{title:"首件",title2:"续件",title3:"包邮件数"},tempId:0,type:0}},mounted:function(){var t=this;setTimeout((function(){var e=JSON.parse(sessionStorage.getItem("cityList"));t.cityList=e}),1e3)},methods:{changType:function(t){this.type=t},onClose:function(t){this.dialogVisible=!1,this.$refs[t].resetFields()},confirmEdit:function(t,e){t.splice(e,1)},popoverHide:function(){},handleClose:function(){this.dialogVisible=!1,this.ruleForm={name:"",type:1,appoint:!1,sort:0,region:[{first:0,firstPrice:0,renewal:0,renewalPrice:0,city_ids:[]}],undelivery:0,free:[],undelives:{},city_id3:[]}},changeRegion:function(t){console.log(t)},changeRadio:function(t){this.columns=Object.assign({},u[t-1])},addRegion:function(t){t.push(Object.assign({},{first:0,firstPrice:0,renewal:0,renewalPrice:0,city_ids:[]}))},addFree:function(t){t.push(Object.assign({},{city_id:[],number:1,price:1,city_ids:[]}))},getInfo:function(t,e){var i=this;this.tempId=t;var r=s["Loading"].service({fullscreen:!0});n["q"]({id:t}).then((function(t){i.dialogVisible=!0;var e=t;i.ruleForm=Object.assign(i.ruleForm,{name:e.name,type:e.type,appoint:e.appoint,sort:e.sort}),i.columns=Object.assign({},u[i.ruleForm.type-1]),i.$nextTick((function(){r.close()})),i.shippingRegion(),e.appoint&&i.shippingFree()})).catch((function(t){i.$message.error(t.message),i.$nextTick((function(){r.close()}))}))},shippingRegion:function(){var t=this;n["m"]({tempId:this.tempId}).then((function(e){e.forEach((function(t,e){t.title=JSON.parse(t.title),t.city_ids=t.title})),t.ruleForm.region=e}))},shippingFree:function(){var t=this;n["l"]({tempId:this.tempId}).then((function(e){e.forEach((function(t,e){t.title=JSON.parse(t.title),t.city_ids=t.title})),t.ruleForm.free=e}))},getCityList:function(){var t=this;n["c"]().then((function(e){sessionStorage.setItem("cityList",JSON.stringify(e));var i=JSON.parse(sessionStorage.getItem("cityList"));t.cityList=i})).catch((function(e){t.$message.error(e.message)}))},change:function(t){return t.map((function(t){var e=[];t.city_ids.map((function(t){t.splice(0,1),e.push(t[0])})),t.city_id=e})),t},changeOne:function(t){var e=[];return t.map((function(t){t.splice(0,1),e.push(t[0])})),e},onsubmit:function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return!1;e.loading=!0;var i={appoint:e.ruleForm.appoint,name:e.ruleForm.name,sort:e.ruleForm.sort,type:e.ruleForm.type};e.ruleForm.region.forEach((function(t,e){t.title=t.city_ids.length>0?JSON.stringify(t.city_ids):JSON.stringify([[0,0]]);for(var i=0;i<t.city_ids.length;i++)t.city_ids[i].shift();t.cityId=t.city_ids.length>0?t.city_ids.join(","):"all"})),i.shippingTemplatesRegionRequestList=e.ruleForm.region,i.shippingTemplatesRegionRequestList.forEach((function(t,e){})),e.ruleForm.appoint&&(e.ruleForm.free.forEach((function(t,e){t.title=t.city_ids.length>0?JSON.stringify(t.city_ids):JSON.stringify([[0,0]]);for(var i=0;i<t.city_ids.length;i++)t.city_ids[i].shift();t.cityId=t.city_ids.length>0?t.city_ids.join(","):"all"})),i.shippingTemplatesFreeRequestList=e.ruleForm.free,i.shippingTemplatesFreeRequestList.forEach((function(t,e){}))),0===e.type?n["n"](i).then((function(t){e.$message.success("操作成功"),e.handleClose(),e.$nextTick((function(){e.dialogVisible=!1})),setTimeout((function(){e.$emit("getList")}),600),e.loading=!1})):n["p"](i,{id:e.tempId}).then((function(t){e.$message.success("操作成功"),setTimeout((function(){e.$emit("getList"),e.handleClose()}),600),e.$nextTick((function(){e.dialogVisible=!1})),e.loading=!1}))}))},clear:function(){this.ruleForm.name="",this.ruleForm.sort=0}}},m=d,f=(i("e16d"),i("2877")),p=Object(f["a"])(m,r,a,!1,null,"44a816e5",null);e["a"]=p.exports}}]);