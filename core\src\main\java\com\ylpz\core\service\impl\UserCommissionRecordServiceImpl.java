package com.ylpz.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.ylpz.core.common.constants.BrokerageRecordConstants;
import com.ylpz.core.common.constants.MemberParamConstants;
import com.ylpz.core.common.request.CommissionRecordRequest;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.core.dao.UserBrokerageRecordDao;
import com.ylpz.core.dao.UserDao;
import com.ylpz.core.service.UserCommissionRecordService;
import com.ylpz.model.user.User;
import com.ylpz.model.user.UserBrokerageRecord;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户佣金返现记录服务实现类
 */
@Service
public class UserCommissionRecordServiceImpl extends ServiceImpl<UserBrokerageRecordDao, UserBrokerageRecord>
        implements UserCommissionRecordService {

    @Resource
    private UserBrokerageRecordDao userBrokerageRecordDao;

    @Resource
    private UserDao userDao;

    /**
     * 获取佣金返现记录列表
     *
     * @param request          查询条件
     * @param pageParamRequest 分页参数
     * @return 分页数据
     */
    @Override
    public PageInfo<UserBrokerageRecord> getCommissionList(CommissionRecordRequest request,
            PageParamRequest pageParamRequest) {
        // 构建查询条件
        LambdaQueryWrapper<UserBrokerageRecord> lqw = new LambdaQueryWrapper<>();

        // 只查询佣金返现类型记录
        lqw.eq(UserBrokerageRecord::getLinkType, BrokerageRecordConstants.BROKERAGE_RECORD_LINK_TYPE_COMMISSION);

        // 用户筛选
        if (request.getUid() != null) {
            lqw.eq(UserBrokerageRecord::getUid, request.getUid());
        }

        // 通过手机号查询用户ID
        if (!StringUtils.isEmpty(request.getMobile())) {
            LambdaQueryWrapper<User> userQuery = new LambdaQueryWrapper<>();
            userQuery.eq(User::getPhone, request.getMobile());
            List<User> users = userDao.selectList(userQuery);
            if (!CollectionUtils.isEmpty(users)) {
                List<Integer> userIds = users.stream().map(User::getId).collect(Collectors.toList());
                lqw.in(UserBrokerageRecord::getUid, userIds);
            } else {
                // 如果没有找到用户，返回空结果
                return new PageInfo<>();
            }
        }

        // 按佣金返现类型筛选
        if (!StringUtils.isEmpty(request.getCommissionType())) {
            if ("svip".equals(request.getCommissionType())) {
                lqw.eq(UserBrokerageRecord::getTitle, BrokerageRecordConstants.BROKERAGE_RECORD_TITLE_COMMISSION_SVIP);
            } else if ("vip".equals(request.getCommissionType())) {
                lqw.eq(UserBrokerageRecord::getTitle, BrokerageRecordConstants.BROKERAGE_RECORD_TITLE_COMMISSION_VIP);
            } else if ("normal".equals(request.getCommissionType())) {
                lqw.eq(UserBrokerageRecord::getTitle,
                        BrokerageRecordConstants.BROKERAGE_RECORD_TITLE_COMMISSION_NORMAL);
            }
        }

        // 关联订单筛选
        if (!StringUtils.isEmpty(request.getLinkId())) {
            lqw.eq(UserBrokerageRecord::getLinkId, request.getLinkId());
        }

        // 状态筛选
        if (request.getStatus() != null) {
            lqw.eq(UserBrokerageRecord::getStatus, request.getStatus());
        }

        // 时间范围筛选
        if (request.getStartTime() != null && request.getEndTime() != null) {
            lqw.between(UserBrokerageRecord::getCreateTime, request.getStartTime(), request.getEndTime());
        }

        // 只查询增加类型的记录
        lqw.eq(UserBrokerageRecord::getType, BrokerageRecordConstants.BROKERAGE_RECORD_TYPE_ADD);

        // 按创建时间倒序排序
        lqw.orderByDesc(UserBrokerageRecord::getCreateTime);

        // 执行分页查询
        Page<UserBrokerageRecord> page = new Page<>(pageParamRequest.getPage(), pageParamRequest.getLimit());
        Page<UserBrokerageRecord> pageResult = userBrokerageRecordDao.selectPage(page, lqw);

        // 查询用户信息并填充
        List<UserBrokerageRecord> records = pageResult.getRecords();
        if (!CollectionUtils.isEmpty(records)) {
            fillUserInfo(records);
        }

        // 创建分页信息
        PageInfo<UserBrokerageRecord> pageInfo = new PageInfo<>(records);
        pageInfo.setPages((int) pageResult.getPages());
        pageInfo.setPageNum((int) pageResult.getCurrent());
        pageInfo.setPageSize((int) pageResult.getSize());
        pageInfo.setTotal(pageResult.getTotal());

        return pageInfo;
    }

    /**
     * 获取佣金返现合计金额
     *
     * @param request 查询条件
     * @return 合计金额
     */
    @Override
    public BigDecimal getCommissionTotal(CommissionRecordRequest request) {
        // 构建查询条件
        LambdaQueryWrapper<UserBrokerageRecord> lqw = new LambdaQueryWrapper<>();

        // 只查询佣金返现类型记录
        lqw.eq(UserBrokerageRecord::getLinkType, BrokerageRecordConstants.BROKERAGE_RECORD_LINK_TYPE_COMMISSION);

        // 用户筛选
        if (request.getUid() != null) {
            lqw.eq(UserBrokerageRecord::getUid, request.getUid());
        }

        // 通过手机号查询用户ID
        if (!StringUtils.isEmpty(request.getMobile())) {
            LambdaQueryWrapper<User> userQuery = new LambdaQueryWrapper<>();
            userQuery.eq(User::getPhone, request.getMobile());
            List<User> users = userDao.selectList(userQuery);
            if (!CollectionUtils.isEmpty(users)) {
                List<Integer> userIds = users.stream().map(User::getId).collect(Collectors.toList());
                lqw.in(UserBrokerageRecord::getUid, userIds);
            }
        }

        // 按佣金返现类型筛选
        if (!StringUtils.isEmpty(request.getCommissionType())) {
            if ("svip".equals(request.getCommissionType())) {
                lqw.eq(UserBrokerageRecord::getTitle, BrokerageRecordConstants.BROKERAGE_RECORD_TITLE_COMMISSION_SVIP);
            } else if ("vip".equals(request.getCommissionType())) {
                lqw.eq(UserBrokerageRecord::getTitle, BrokerageRecordConstants.BROKERAGE_RECORD_TITLE_COMMISSION_VIP);
            } else if ("normal".equals(request.getCommissionType())) {
                lqw.eq(UserBrokerageRecord::getTitle,
                        BrokerageRecordConstants.BROKERAGE_RECORD_TITLE_COMMISSION_NORMAL);
            }
        }

        // 关联订单筛选
        if (!StringUtils.isEmpty(request.getLinkId())) {
            lqw.eq(UserBrokerageRecord::getLinkId, request.getLinkId());
        }

        // 状态筛选
        if (request.getStatus() != null) {
            lqw.eq(UserBrokerageRecord::getStatus, request.getStatus());
        } else {
            // 默认只查询已完成状态的记录
            lqw.eq(UserBrokerageRecord::getStatus, BrokerageRecordConstants.BROKERAGE_RECORD_STATUS_COMPLETE);
        }

        // 时间范围筛选
        if (request.getStartTime() != null && request.getEndTime() != null) {
            lqw.between(UserBrokerageRecord::getCreateTime, request.getStartTime(), request.getEndTime());
        }

        // 只查询增加类型的记录
        lqw.eq(UserBrokerageRecord::getType, BrokerageRecordConstants.BROKERAGE_RECORD_TYPE_ADD);

        // 查询记录列表
        List<UserBrokerageRecord> records = userBrokerageRecordDao.selectList(lqw);

        // 计算合计金额
        if (CollectionUtils.isEmpty(records)) {
            return BigDecimal.ZERO;
        }

        return records.stream()
                .map(UserBrokerageRecord::getPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 获取用户佣金返现统计
     *
     * @param uid 用户ID
     * @return 用户佣金返现统计
     */
    @Override
    public UserBrokerageRecord getUserCommissionStatistics(Integer uid) {
        // 获取用户佣金返现总额
        LambdaQueryWrapper<UserBrokerageRecord> totalQuery = new LambdaQueryWrapper<>();
        totalQuery.eq(UserBrokerageRecord::getUid, uid);
        totalQuery.eq(UserBrokerageRecord::getLinkType, BrokerageRecordConstants.BROKERAGE_RECORD_LINK_TYPE_COMMISSION);
        totalQuery.eq(UserBrokerageRecord::getType, BrokerageRecordConstants.BROKERAGE_RECORD_TYPE_ADD);
        totalQuery.eq(UserBrokerageRecord::getStatus, BrokerageRecordConstants.BROKERAGE_RECORD_STATUS_COMPLETE);

        List<UserBrokerageRecord> records = userBrokerageRecordDao.selectList(totalQuery);

        // 创建统计记录
        UserBrokerageRecord statRecord = new UserBrokerageRecord();
        statRecord.setUid(uid);
        statRecord.setLinkType(BrokerageRecordConstants.BROKERAGE_RECORD_LINK_TYPE_COMMISSION);
        statRecord.setTitle("佣金返现统计");

        if (!CollectionUtils.isEmpty(records)) {
            // 累计返现金额
            BigDecimal totalAmount = records.stream()
                    .map(UserBrokerageRecord::getPrice)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            statRecord.setPrice(totalAmount);

            // 记录数量
            statRecord.setStatus(records.size());

            // 最近一次返现时间
            statRecord.setCreateTime(records.stream()
                    .map(UserBrokerageRecord::getCreateTime)
                    .max(java.util.Comparator.naturalOrder())
                    .orElse(null));
        } else {
            statRecord.setPrice(BigDecimal.ZERO);
            statRecord.setStatus(0);
        }

        return statRecord;
    }

    /**
     * 填充用户信息
     *
     * @param records 佣金记录列表
     */
    private void fillUserInfo(List<UserBrokerageRecord> records) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }

        List<Integer> userIds = records.stream()
                .map(UserBrokerageRecord::getUid)
                .distinct()
                .collect(Collectors.toList());

        LambdaQueryWrapper<User> userQuery = new LambdaQueryWrapper<>();
        userQuery.in(User::getId, userIds);
        List<User> users = userDao.selectList(userQuery);

        Map<Integer, User> userMap = new HashMap<>();
        for (User user : users) {
            userMap.put(user.getId(), user);
        }

        for (UserBrokerageRecord record : records) {
            User user = userMap.get(record.getUid());
            if (user != null) {
                record.setUserName(user.getNickname());
                record.setUserPhone(user.getPhone());
                record.setUserLevel(getUserLevelName(user.getLevel()));
            }
        }
    }

    /**
     * 根据用户等级获取等级名称
     *
     * @param level 用户等级
     * @return 等级名称
     */
    private String getUserLevelName(Integer level) {
        if (level == null) {
            return "普通会员";
        }
        switch (level) {
            case 1:
                return "普通会员";
            case 2:
                return "VIP会员";
            case 3:
                return "SVIP会员";
            default:
                return "普通会员";
        }
    }
}