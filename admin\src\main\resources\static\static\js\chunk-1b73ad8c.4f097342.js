(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1b73ad8c"],{"2f2c":function(t,e,r){"use strict";r.d(e,"b",(function(){return u})),r.d(e,"c",(function(){return m})),r.d(e,"r",(function(){return d})),r.d(e,"d",(function(){return f})),r.d(e,"a",(function(){return p})),r.d(e,"g",(function(){return g})),r.d(e,"h",(function(){return h})),r.d(e,"j",(function(){return y})),r.d(e,"i",(function(){return b})),r.d(e,"e",(function(){return v})),r.d(e,"o",(function(){return w})),r.d(e,"q",(function(){return _})),r.d(e,"l",(function(){return V})),r.d(e,"m",(function(){return x})),r.d(e,"n",(function(){return T})),r.d(e,"p",(function(){return k})),r.d(e,"k",(function(){return S})),r.d(e,"f",(function(){return O}));var i=r("b775");function n(t){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},n(t)}function a(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,i)}return r}function o(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?a(Object(r),!0).forEach((function(e){s(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):a(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function s(t,e,r){return(e=l(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function l(t){var e=c(t,"string");return"symbol"==n(e)?e:e+""}function c(t,e){if("object"!=n(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var i=r.call(t,e||"default");if("object"!=n(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function u(t){return Object(i["a"])({url:"/admin/system/city/list",method:"get",params:o({},t)})}function m(){return Object(i["a"])({url:"/admin/system/city/list/tree",method:"get"})}function d(t){return Object(i["a"])({url:"/admin/system/city/update/status",method:"post",params:o({},t)})}function f(t){return Object(i["a"])({url:"/admin/system/city/update",method:"post",params:o({},t)})}function p(t){return Object(i["a"])({url:"/admin/system/city/info",method:"get",params:o({},t)})}function g(t){return Object(i["a"])({url:"/admin/express/list",method:"get",params:o({},t)})}function h(){return Object(i["a"])({url:"/admin/express/sync/express",method:"post"})}function y(t){return Object(i["a"])({url:"/admin/express/update/show",method:"post",data:t})}function b(t){return Object(i["a"])({url:"/admin/express/update",method:"post",data:t})}function v(t){return Object(i["a"])({url:"/admin/express/delete",method:"GET",params:o({},t)})}function w(t){return Object(i["a"])({url:"/admin/express/shipping/templates/list",method:"get",params:o({},t)})}function _(t){return Object(i["a"])({url:"/admin/express/shipping/templates/info",method:"get",params:o({},t)})}function V(t){return Object(i["a"])({url:"/admin/express/shipping/free/list",method:"get",params:o({},t)})}function x(t){return Object(i["a"])({url:"admin/express/shipping/region/list",method:"get",params:o({},t)})}function T(t){return Object(i["a"])({url:"admin/express/shipping/templates/save",method:"post",data:t})}function k(t,e){return Object(i["a"])({url:"admin/express/shipping/templates/update",method:"post",data:t,params:o({},e)})}function S(t){return Object(i["a"])({url:"admin/express/shipping/templates/delete",method:"get",params:t})}function O(t){return Object(i["a"])({url:"admin/express/info",method:"get",params:o({},t)})}},4509:function(t,e,r){},4825:function(t,e,r){"use strict";r.r(e);var i=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"divBox"},[r("el-card",{staticClass:"box-card"},[r("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[r("el-steps",{attrs:{active:t.currentTab,"align-center":"","finish-status":"success"}},[r("el-step",{attrs:{title:"选择秒杀商品"}}),t._v(" "),r("el-step",{attrs:{title:"填写基础信息"}}),t._v(" "),r("el-step",{attrs:{title:"修改商品详情"}})],1)],1),t._v(" "),r("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.fullscreenLoading,expression:"fullscreenLoading"}],ref:"formValidate",staticClass:"formValidate mt20",attrs:{rules:t.ruleValidate,model:t.formValidate,"label-width":"150px"},nativeOn:{submit:function(t){t.preventDefault()}}},[r("div",{directives:[{name:"show",rawName:"v-show",value:0===t.currentTab,expression:"currentTab === 0"}]},[r("el-form-item",{attrs:{label:"选择商品：",prop:"image"}},[r("div",{staticClass:"upLoadPicBox",on:{click:t.changeGood}},[t.formValidate.image?r("div",{staticClass:"pictrue"},[r("img",{attrs:{src:t.formValidate.image}})]):r("div",{staticClass:"upLoad"},[r("i",{staticClass:"el-icon-camera cameraIconfont"})])])])],1),t._v(" "),r("div",{directives:[{name:"show",rawName:"v-show",value:1===t.currentTab,expression:"currentTab === 1"}]},[r("el-row",{attrs:{gutter:24}},[r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"商品主图：",prop:"image"}},[r("div",{staticClass:"upLoadPicBox",on:{click:function(e){return t.modalPicTap("1")}}},[t.formValidate.image?r("div",{staticClass:"pictrue"},[r("img",{attrs:{src:t.formValidate.image}})]):r("div",{staticClass:"upLoad"},[r("i",{staticClass:"el-icon-camera cameraIconfont"})])])])],1),t._v(" "),r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"商品轮播图：",prop:"imagess"}},[r("div",{staticClass:"acea-row"},[t._l(t.formValidate.imagess,(function(e,i){return r("div",{key:i,staticClass:"pictrue",attrs:{draggable:"true"},on:{dragstart:function(r){return t.handleDragStart(r,e)},dragover:function(r){return r.preventDefault(),t.handleDragOver(r,e)},dragenter:function(r){return t.handleDragEnter(r,e)},dragend:function(r){return t.handleDragEnd(r,e)}}},[r("img",{attrs:{src:e}}),t._v(" "),r("i",{staticClass:"el-icon-error btndel",on:{click:function(e){return t.handleRemove(i)}}})])})),t._v(" "),t.formValidate.imagess.length<10?r("div",{staticClass:"upLoadPicBox",on:{click:function(e){return t.modalPicTap("2")}}},[r("div",{staticClass:"upLoad"},[r("i",{staticClass:"el-icon-camera cameraIconfont"})])]):t._e()],2)])],1),t._v(" "),r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"商品标题：",prop:"title"}},[r("el-input",{attrs:{maxlength:"249",placeholder:"请输入商品名称"},model:{value:t.formValidate.title,callback:function(e){t.$set(t.formValidate,"title",e)},expression:"formValidate.title"}})],1)],1),t._v(" "),r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"秒杀活动简介："}},[r("el-input",{attrs:{type:"textarea",maxlength:"250",rows:3,placeholder:"请输入商品简介"},model:{value:t.formValidate.info,callback:function(e){t.$set(t.formValidate,"info",e)},expression:"formValidate.info"}})],1)],1),t._v(" "),r("el-col",t._b({},"el-col",t.grid2,!1),[r("el-form-item",{attrs:{label:"单位：",prop:"unitName"}},[r("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入单位"},model:{value:t.formValidate.unitName,callback:function(e){t.$set(t.formValidate,"unitName",e)},expression:"formValidate.unitName"}})],1)],1),t._v(" "),r("el-col",t._b({},"el-col",t.grid2,!1),[r("el-form-item",{attrs:{label:"运费模板：",prop:"tempId"}},[r("div",{staticClass:"acea-row"},[r("el-select",{staticClass:"selWidth",attrs:{placeholder:"请选择"},model:{value:t.formValidate.tempId,callback:function(e){t.$set(t.formValidate,"tempId",e)},expression:"formValidate.tempId"}},t._l(t.shippingList,(function(t){return r("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1)])],1),t._v(" "),r("el-col",t._b({},"el-col",t.grid2,!1),[r("el-form-item",{attrs:{label:"当天参与活动次数：",prop:"num"}},[r("el-input-number",{staticClass:"selWidth",attrs:{step:1,"step-strictly":"",min:1,placeholder:"请输入活动次数"},model:{value:t.formValidate.num,callback:function(e){t.$set(t.formValidate,"num",e)},expression:"formValidate.num"}})],1)],1),t._v(" "),r("el-col",t._b({},"el-col",t.grid2,!1),[r("el-form-item",{attrs:{label:"活动日期：",prop:"timeVal"}},[r("el-date-picker",{staticClass:"selWidth",attrs:{"value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",size:"small",type:"daterange",placement:"bottom-end","picker-options":t.pickerOptions,placeholder:"请选择活动日期"},on:{change:t.onchangeTime},model:{value:t.formValidate.timeVal,callback:function(e){t.$set(t.formValidate,"timeVal",e)},expression:"formValidate.timeVal"}})],1)],1),t._v(" "),r("el-col",t._b({},"el-col",t.grid2,!1),[r("el-form-item",{attrs:{label:"活动时间：",prop:"timeId"}},[r("el-select",{staticClass:"selWidth",attrs:{placeholder:"请选择"},model:{value:t.formValidate.timeId,callback:function(e){t.$set(t.formValidate,"timeId",e)},expression:"formValidate.timeId"}},t._l(t.seckillTime,(function(t){return r("el-option",{key:t.id,attrs:{label:t.name+" | "+t.time,value:t.id}})})),1)],1)],1),t._v(" "),r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"活动状态：",required:""}},[r("el-radio-group",{model:{value:t.formValidate.status,callback:function(e){t.$set(t.formValidate,"status",e)},expression:"formValidate.status"}},[r("el-radio",{staticClass:"radio",attrs:{label:0}},[t._v("关闭")]),t._v(" "),r("el-radio",{attrs:{label:1}},[t._v("开启")])],1)],1)],1),t._v(" "),r("el-col",{attrs:{span:24}},[r("el-form-item",{staticClass:"labeltop",attrs:{label:"商品属性：",required:""}},[r("el-table",{ref:"multipleTable",staticStyle:{width:"100%"},attrs:{data:t.ManyAttrValue,"tooltip-effect":"dark"},on:{"selection-change":t.handleSelectionChange}},[t.formValidate.specType?r("el-table-column",{key:"1",attrs:{type:"selection",width:"55"}}):t._e(),t._v(" "),t.manyTabDate&&t.formValidate.specType?t._l(t.manyTabDate,(function(e,i){return r("el-table-column",{key:i,attrs:{align:"center",label:t.manyTabTit[i].title,"min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("span",{staticClass:"priceBox",domProps:{textContent:t._s(e.row[i])}})]}}],null,!0)})})):t._e(),t._v(" "),r("el-table-column",{attrs:{align:"center",label:"图片","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("div",{staticClass:"upLoadPicBox",on:{click:function(r){return t.modalPicTap("1","duo",e.$index)}}},[e.row.image?r("div",{staticClass:"pictrue tabPic"},[r("img",{attrs:{src:e.row.image}})]):r("div",{staticClass:"upLoad tabPic"},[r("i",{staticClass:"el-icon-camera cameraIconfont"})])])]}}])}),t._v(" "),t._l(t.attrValue,(function(e,i){return r("el-table-column",{key:i,attrs:{label:t.formThead[i].title,align:"center","min-width":"145"},scopedSlots:t._u([{key:"default",fn:function(e){return["秒杀价"===t.formThead[i].title?r("el-input-number",{staticClass:"priceBox",attrs:{size:"small",min:0,precision:2,step:.1},model:{value:e.row[i],callback:function(r){t.$set(e.row,i,r)},expression:"scope.row[iii]"}}):"限量"===t.formThead[i].title?r("el-input-number",{staticClass:"priceBox",attrs:{size:"small",type:"number",min:1,max:e.row.stock,step:1,"step-strictly":""},model:{value:e.row[i],callback:function(r){t.$set(e.row,i,r)},expression:"scope.row[iii]"}}):r("span",{staticClass:"priceBox",domProps:{textContent:t._s(e.row[i])}})]}}],null,!0)})}))],2)],1)],1)],1)],1),t._v(" "),r("div",{directives:[{name:"show",rawName:"v-show",value:2===t.currentTab,expression:"currentTab === 2"}]},[r("el-form-item",{attrs:{label:"商品详情："}},[r("Tinymce",{model:{value:t.formValidate.content,callback:function(e){t.$set(t.formValidate,"content",e)},expression:"formValidate.content"}})],1)],1),t._v(" "),r("el-form-item",{staticStyle:{"margin-top":"30px"}},[r("el-button",{directives:[{name:"show",rawName:"v-show",value:!t.$route.params.id&&t.currentTab>0||t.$route.params.id&&2===t.currentTab,expression:"(!$route.params.id && currentTab > 0) || ($route.params.id && currentTab===2)"}],staticClass:"submission",attrs:{type:"primary",size:"small"},on:{click:t.handleSubmitUp}},[t._v("上一步")]),t._v(" "),r("el-button",{directives:[{name:"show",rawName:"v-show",value:0==t.currentTab,expression:"currentTab == 0"}],staticClass:"submission",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.handleSubmitNest1("formValidate")}}},[t._v("下一步")]),t._v(" "),r("el-button",{directives:[{name:"show",rawName:"v-show",value:1==t.currentTab,expression:"currentTab == 1"}],staticClass:"submission",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.handleSubmitNest2("formValidate")}}},[t._v("下一步")]),t._v(" "),r("el-button",{directives:[{name:"show",rawName:"v-show",value:2===t.currentTab,expression:"currentTab===2"},{name:"hasPermi",rawName:"v-hasPermi",value:["admin:seckill:update"],expression:"['admin:seckill:update']"}],staticClass:"submission",attrs:{loading:t.loading,type:"primary",size:"small"},on:{click:function(e){return t.handleSubmit("formValidate")}}},[t._v("提交")])],1)],1)],1),t._v(" "),r("CreatTemplates",{ref:"addTemplates",on:{getList:t.getShippingList}})],1)},n=[],a=r("8256"),o=r("73f5"),s=r("2f2c"),l=r("02df"),c=r("b7be"),u=r("ff3a"),m=r("61f7");function d(t){return d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},d(t)}function f(t){return y(t)||h(t)||g(t)||p()}function p(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function g(t,e){if(t){if("string"==typeof t)return b(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?b(t,e):void 0}}function h(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function y(t){if(Array.isArray(t))return b(t)}function b(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,i=Array(e);r<e;r++)i[r]=t[r];return i}function v(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */v=function(){return e};var t,e={},r=Object.prototype,i=r.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},a="function"==typeof Symbol?Symbol:{},o=a.iterator||"@@iterator",s=a.asyncIterator||"@@asyncIterator",l=a.toStringTag||"@@toStringTag";function c(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(t){c=function(t,e,r){return t[e]=r}}function u(t,e,r,i){var a=e&&e.prototype instanceof b?e:b,o=Object.create(a.prototype),s=new I(i||[]);return n(o,"_invoke",{value:$(t,r,s)}),o}function m(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=u;var f="suspendedStart",p="suspendedYield",g="executing",h="completed",y={};function b(){}function w(){}function _(){}var V={};c(V,o,(function(){return this}));var x=Object.getPrototypeOf,T=x&&x(x(F([])));T&&T!==r&&i.call(T,o)&&(V=T);var k=_.prototype=b.prototype=Object.create(V);function S(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function O(t,e){function r(n,a,o,s){var l=m(t[n],t,a);if("throw"!==l.type){var c=l.arg,u=c.value;return u&&"object"==d(u)&&i.call(u,"__await")?e.resolve(u.__await).then((function(t){r("next",t,o,s)}),(function(t){r("throw",t,o,s)})):e.resolve(u).then((function(t){c.value=t,o(c)}),(function(t){return r("throw",t,o,s)}))}s(l.arg)}var a;n(this,"_invoke",{value:function(t,i){function n(){return new e((function(e,n){r(t,i,e,n)}))}return a=a?a.then(n,n):n()}})}function $(e,r,i){var n=f;return function(a,o){if(n===g)throw Error("Generator is already running");if(n===h){if("throw"===a)throw o;return{value:t,done:!0}}for(i.method=a,i.arg=o;;){var s=i.delegate;if(s){var l=L(s,i);if(l){if(l===y)continue;return l}}if("next"===i.method)i.sent=i._sent=i.arg;else if("throw"===i.method){if(n===f)throw n=h,i.arg;i.dispatchException(i.arg)}else"return"===i.method&&i.abrupt("return",i.arg);n=g;var c=m(e,r,i);if("normal"===c.type){if(n=i.done?h:p,c.arg===y)continue;return{value:c.arg,done:i.done}}"throw"===c.type&&(n=h,i.method="throw",i.arg=c.arg)}}}function L(e,r){var i=r.method,n=e.iterator[i];if(n===t)return r.delegate=null,"throw"===i&&e.iterator.return&&(r.method="return",r.arg=t,L(e,r),"throw"===r.method)||"return"!==i&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+i+"' method")),y;var a=m(n,e.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,y;var o=a.arg;return o?o.done?(r[e.resultName]=o.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,y):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function j(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function C(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function I(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(j,this),this.reset(!0)}function F(e){if(e||""===e){var r=e[o];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,a=function r(){for(;++n<e.length;)if(i.call(e,n))return r.value=e[n],r.done=!1,r;return r.value=t,r.done=!0,r};return a.next=a}}throw new TypeError(d(e)+" is not iterable")}return w.prototype=_,n(k,"constructor",{value:_,configurable:!0}),n(_,"constructor",{value:w,configurable:!0}),w.displayName=c(_,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,_):(t.__proto__=_,c(t,l,"GeneratorFunction")),t.prototype=Object.create(k),t},e.awrap=function(t){return{__await:t}},S(O.prototype),c(O.prototype,s,(function(){return this})),e.AsyncIterator=O,e.async=function(t,r,i,n,a){void 0===a&&(a=Promise);var o=new O(u(t,r,i,n),a);return e.isGeneratorFunction(r)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},S(k),c(k,l,"Generator"),c(k,o,(function(){return this})),c(k,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var i in e)r.push(i);return r.reverse(),function t(){for(;r.length;){var i=r.pop();if(i in e)return t.value=i,t.done=!1,t}return t.done=!0,t}},e.values=F,I.prototype={constructor:I,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(C),!e)for(var r in this)"t"===r.charAt(0)&&i.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function n(i,n){return s.type="throw",s.arg=e,r.next=i,n&&(r.method="next",r.arg=t),!!n}for(var a=this.tryEntries.length-1;a>=0;--a){var o=this.tryEntries[a],s=o.completion;if("root"===o.tryLoc)return n("end");if(o.tryLoc<=this.prev){var l=i.call(o,"catchLoc"),c=i.call(o,"finallyLoc");if(l&&c){if(this.prev<o.catchLoc)return n(o.catchLoc,!0);if(this.prev<o.finallyLoc)return n(o.finallyLoc)}else if(l){if(this.prev<o.catchLoc)return n(o.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return n(o.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&i.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var a=n;break}}a&&("break"===t||"continue"===t)&&a.tryLoc<=e&&e<=a.finallyLoc&&(a=null);var o=a?a.completion:{};return o.type=t,o.arg=e,a?(this.method="next",this.next=a.finallyLoc,y):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),y},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),C(r),y}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var i=r.completion;if("throw"===i.type){var n=i.arg;C(r)}return n}}throw Error("illegal catch attempt")},delegateYield:function(e,r,i){return this.delegate={iterator:F(e),resultName:r,nextLoc:i},"next"===this.method&&(this.arg=t),y}},e}function w(t,e,r,i,n,a,o){try{var s=t[a](o),l=s.value}catch(t){return void r(t)}s.done?e(l):Promise.resolve(l).then(i,n)}function _(t){return function(){var e=this,r=arguments;return new Promise((function(i,n){var a=t.apply(e,r);function o(t){w(a,i,n,o,s,"next",t)}function s(t){w(a,i,n,o,s,"throw",t)}o(void 0)}))}}var V={image:"",images:"",imagess:[],title:"",info:"",num:1,unitName:"",sort:0,giveIntegral:0,ficti:0,isShow:!1,tempId:"",attrValue:[{image:"",price:0,cost:0,otPrice:0,stock:0,quota:1,barCode:"",weight:0,volume:0}],attr:[],selectRule:"",content:"",specType:!1,id:0,timeId:1,startTime:"",stopTime:"",timeVal:[],status:0},x={price:{title:"秒杀价"},cost:{title:"成本价"},otPrice:{title:"原价"},stock:{title:"库存"},quota:{title:"限量"},barCode:{title:"商品编号"},weight:{title:"重量（KG）"},volume:{title:"体积(m³)"}},T={name:"creatSeckill",components:{CreatTemplates:u["a"],Tinymce:a["a"]},data:function(){return{pickerOptions:{disabledDate:function(t){return t.getTime()<(new Date).setTime((new Date).getTime()-864e5)}},props2:{children:"child",label:"name",value:"id",multiple:!0,emitPath:!1},grid2:{xl:8,lg:10,md:12,sm:24,xs:24},currentTab:0,formThead:Object.assign({},x),formValidate:Object.assign({},V),loading:!1,fullscreenLoading:!1,merCateList:[],shippingList:[],seckillTime:[],ruleValidate:{productId:[{required:!0,message:"请选择商品",trigger:"change"}],title:[{required:!0,message:"请输入商品标题",trigger:"blur"}],attrValue:[{required:!0,message:"请选择商品属相",trigger:"change",type:"array",min:"1"}],num:[{required:!0,message:"请输入活动次数",trigger:"blur"}],unitName:[{required:!0,message:"请输入单位",trigger:"blur"}],info:[{required:!0,message:"请输入秒杀商品简介",trigger:"blur"}],tempId:[{required:!0,message:"请选择运费模板",trigger:"change"}],timeId:[{required:!0,message:"请选择活动时间",trigger:"change"}],image:[{required:!0,message:"请上传商品图",trigger:"change"}],imagess:[{required:!0,message:"请上传商品轮播图",type:"array",trigger:"change"}],specType:[{required:!0,message:"请选择商品规格",trigger:"change"}],timeVal:[{required:!0,message:"请选择活动日期",trigger:"change",type:"array"}]},manyTabDate:{},manyTabTit:{},attrInfo:{},tempRoute:{},multipleSelection:[],productId:0,OneattrValue:[Object.assign({},V.attrValue[0])],ManyAttrValue:[Object.assign({},V.attrValue[0])]}},computed:{attrValue:function(){var t=Object.assign({},V.attrValue[0]);return delete t.image,t}},created:function(){this.$watch("formValidate.attr",this.watCh),this.tempRoute=Object.assign({},this.$route)},mounted:function(){var t=this;Object(l["a"])(1).then((function(e){t.seckillTime=e.list})),this.formValidate.imagess=[],this.$route.params.id&&(this.setTagsViewTitle(),this.getInfo(),this.currentTab=1),this.getShippingList(),this.getCategorySelect()},methods:{watCh:function(t){var e={},r={};this.formValidate.attr.forEach((function(t,i){e[t.attrName]={title:t.attrName},r[t.attrName]=""})),this.manyTabTit=e,this.manyTabDate=r,this.formThead=Object.assign({},this.formThead,e)},handleRemove:function(t){this.formValidate.imagess.splice(t,1)},handleSelectionChange:function(t){this.multipleSelection=t},modalPicTap:function(t,e,r){var i=this;this.$modalUpload((function(n){if("1"!==t||e||(i.formValidate.image=n[0].sattDir,i.ManyAttrValue[0].image=n[0].sattDir),"2"===t&&!e){if(n.length>10)return this.$message.warning("最多选择10张图片！");if(n.length+i.formValidate.imagess.length>10)return this.$message.warning("最多选择10张图片！");n.map((function(t){i.formValidate.imagess.push(t.sattDir)}))}"1"===t&&"duo"===e&&(i.ManyAttrValue[r].image=n[0].sattDir)}),t,"content")},onchangeTime:function(t){this.formValidate.timeVal=t,this.formValidate.startTime=t?t[0]:"",this.formValidate.stopTime=t?t[1]:""},changeGood:function(){var t=this;this.$modalGoodList((function(e){t.formValidate.image=e.image,t.productId=e.id}))},handleSubmitNest1:function(){if(!this.formValidate.image)return this.$message.warning("请选择商品！");this.currentTab++,this.$route.params.id||this.getProdect(this.productId)},getCategorySelect:function(){var t=this;Object(o["d"])({status:-1,type:1}).then((function(e){t.merCateList=t.filerMerCateList(e)}))},filerMerCateList:function(t){return t.map((function(t){return t.child||(t.disabled=!0),t.label=t.name,t}))},getShippingList:function(){var t=this;Object(s["o"])(this.tempData).then((function(e){t.shippingList=e.list}))},addTem:function(){this.$refs.addTemplates.dialogVisible=!0,this.$refs.addTemplates.getCityList()},getInfo:function(){this.$route.params.id?this.getSekllProdect(this.$route.params.id):this.getProdect(this.productId)},getProdect:function(t){var e=this;this.fullscreenLoading=!0,Object(o["l"])(t).then(function(){var t=_(v().mark((function t(r){var i;return v().wrap((function(t){while(1)switch(t.prev=t.next){case 0:i=r,e.formValidate={image:e.$selfUtil.setDomain(i.image),imagess:JSON.parse(i.sliderImage),title:i.storeName,info:i.storeInfo,quota:"",unitName:i.unitName,sort:i.sort,isShow:i.isShow,tempId:i.tempId,attr:i.attr,attrValue:i.attrValue,selectRule:i.selectRule,content:i.content,specType:i.specType,productId:i.id,giveIntegral:i.giveIntegral,ficti:i.ficti,timeId:e.$route.params.id?Number(i.timeId):e.$route.params.timeId?Number(e.$route.params.timeId):"",startTime:i.startTime||"",stopTime:i.stopTime||"",timeVal:[],status:0,num:1},i.specType?(e.$nextTick((function(){i.attrValue.forEach((function(t){for(var r in t.quota=t.stock,t.attrValue=JSON.parse(t.attrValue),t.attrValue)t[r]=t.attrValue[r];t.image=e.$selfUtil.setDomain(t.image),e.$refs.multipleTable.toggleRowSelection(t,!0)}))})),e.ManyAttrValue=i.attrValue,e.multipleSelection=i.attrValue):(i.attrValue.forEach((function(t){t.quota=t.stock})),e.ManyAttrValue=i.attrValue),e.fullscreenLoading=!1;case 4:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.fullscreenLoading=!1}))},getSekllProdect:function(t){var e=this;this.fullscreenLoading=!0,Object(c["N"])({id:t}).then(function(){var t=_(v().mark((function t(r){var i;return v().wrap((function(t){while(1)switch(t.prev=t.next){case 0:i=r,e.formValidate={image:e.$selfUtil.setDomain(i.image),imagess:JSON.parse(i.sliderImage),title:i.storeName,info:i.storeInfo,quota:i.quota,unitName:i.unitName,sort:i.sort,isShow:i.isShow,tempId:i.tempId,attr:i.attr,attrValue:i.attrValue,selectRule:i.selectRule,content:i.content,specType:i.specType,productId:i.productId,giveIntegral:i.giveIntegral,ficti:i.ficti,timeId:Number(i.timeId),startTime:i.startTimeStr||"",stopTime:i.stopTimeStr||"",status:i.status,num:i.num,timeVal:i.startTimeStr&&i.stopTimeStr?[i.startTimeStr,i.stopTimeStr]:[],id:i.id},i.specType?(e.ManyAttrValue=i.attrValue,e.$nextTick((function(){e.ManyAttrValue.forEach((function(t,r){for(var i in t.attrValue=JSON.parse(t.attrValue),t.attrValue)t[i]=t.attrValue[i];t.image=e.$selfUtil.setDomain(t.image),t.id&&(e.$set(t,"price",t.price),e.$set(t,"quota",t.quota),e.$nextTick((function(){e.$refs.multipleTable.toggleRowSelection(t,!0)})))}))}))):e.ManyAttrValue=i.attrValue,e.fullscreenLoading=!1;case 4:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.fullscreenLoading=!1}))},handleSubmitNest2:function(t){var e=this;this.$refs[t].validate((function(t){return!!t&&(e.formValidate.specType&&0===e.multipleSelection.length?e.$message.warning("请填选择至少一个商品属性！"):void e.currentTab++)}))},handleSubmit:Object(m["a"])((function(t){var e=this;this.formValidate.specType?this.formValidate.attrValue=this.multipleSelection:this.formValidate.attrValue=this.ManyAttrValue,this.formValidate.images=JSON.stringify(this.formValidate.imagess),this.formValidate.attrValue.forEach((function(t){t.attrValue=JSON.stringify(t.attrValue)})),this.$refs[t].validate((function(r){r?(e.fullscreenLoading=!0,e.loading=!0,e.$route.params.id?Object(c["R"])({id:e.$route.params.id},e.formValidate).then(_(v().mark((function r(){return v().wrap((function(r){while(1)switch(r.prev=r.next){case 0:e.fullscreenLoading=!1,e.$message.success("编辑成功"),e.$router.push({path:"/marketing/seckill/list"}),e.$refs[t].resetFields(),e.formValidate.images=[],e.loading=!1;case 6:case"end":return r.stop()}}),r)})))).catch((function(){e.fullscreenLoading=!1,e.loading=!1})):Object(c["P"])(e.formValidate).then(function(){var r=_(v().mark((function r(i){return v().wrap((function(r){while(1)switch(r.prev=r.next){case 0:e.fullscreenLoading=!1,e.$message.success("新增成功"),e.$router.push({path:"/marketing/seckill/list"}),e.$refs[t].resetFields(),e.formValidate.images=[],e.loading=!1;case 6:case"end":return r.stop()}}),r)})));return function(t){return r.apply(this,arguments)}}()).catch((function(){e.fullscreenLoading=!1,e.loading=!1}))):e.formValidate.storeName&&e.formValidate.unitName&&e.formValidate.store_info&&e.formValidate.image&&e.formValidate.images||e.$message.warning("请填写完整商品信息！")}))})),handleSubmitUp:function(){this.currentTab--<0&&(this.currentTab=0)},setTagsViewTitle:function(){var t="编辑秒杀商品",e=Object.assign({},this.tempRoute,{title:"".concat(t,"-").concat(this.$route.params.id)});this.$store.dispatch("tagsView/updateVisitedView",e)},handleDragStart:function(t,e){this.dragging=e},handleDragEnd:function(t,e){this.dragging=null},handleDragOver:function(t){t.dataTransfer.dropEffect="move"},handleDragEnter:function(t,e){if(t.dataTransfer.effectAllowed="move",e!==this.dragging){var r=f(this.formValidate.imagess),i=r.indexOf(this.dragging),n=r.indexOf(e);r.splice.apply(r,[n,0].concat(f(r.splice(i,1)))),this.formValidate.imagess=r}}}},k=T,S=(r("5fb2"),r("2877")),O=Object(S["a"])(k,i,n,!1,null,"55ba8746",null);e["default"]=O.exports},"5fb2":function(t,e,r){"use strict";r("4509")},cf0d:function(t,e,r){},e16d:function(t,e,r){"use strict";r("cf0d")},ff3a:function(t,e,r){"use strict";var i=function(){var t=this,e=t.$createElement,r=t._self._c||e;return t.dialogVisible?r("el-dialog",{attrs:{title:"运费模板",visible:t.dialogVisible,width:"1000px","before-close":t.handleClose},on:{"update:visible":function(e){t.dialogVisible=e}}},[t.dialogVisible?r("el-form",{ref:"ruleForm",attrs:{model:t.ruleForm,"label-width":"120px",size:"mini",rules:t.rules}},[r("el-form-item",{attrs:{label:"模板名称",prop:"name"}},[r("el-input",{staticClass:"withs",attrs:{placeholder:"请输入模板名称"},model:{value:t.ruleForm.name,callback:function(e){t.$set(t.ruleForm,"name",e)},expression:"ruleForm.name"}})],1),t._v(" "),r("el-form-item",{attrs:{label:"计费方式",prop:"type"}},[r("el-radio-group",{on:{change:function(e){return t.changeRadio(t.ruleForm.type)}},model:{value:t.ruleForm.type,callback:function(e){t.$set(t.ruleForm,"type",e)},expression:"ruleForm.type"}},[r("el-radio",{attrs:{label:1}},[t._v("按件数")]),t._v(" "),r("el-radio",{attrs:{label:2}},[t._v("按重量")]),t._v(" "),r("el-radio",{attrs:{label:3}},[t._v("按体积")])],1)],1),t._v(" "),r("el-form-item",{attrs:{label:"配送区域及运费",prop:"region"}},[r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticClass:"tempBox",staticStyle:{width:"100%"},attrs:{data:t.ruleForm.region,border:"",fit:"","highlight-current-row":"",size:"mini"}},[r("el-table-column",{attrs:{align:"center",label:"可配送区域","min-width":"260"},scopedSlots:t._u([{key:"default",fn:function(e){return[0===e.$index?r("span",[t._v("默认全国")]):r("el-cascader",{staticStyle:{width:"98%"},attrs:{options:t.cityList,props:t.props,"collapse-tags":"",clearable:"",filterable:""},on:{change:t.changeRegion},model:{value:e.row.city_ids,callback:function(r){t.$set(e.row,"city_ids",r)},expression:"scope.row.city_ids"}})]}}],null,!1,41555841)}),t._v(" "),r("el-table-column",{attrs:{"min-width":"130px",align:"center",label:t.columns.title,prop:"first"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("el-form-item",{attrs:{rules:t.rules.first,prop:"region."+e.$index+".first"}},[r("el-input-number",{attrs:{"controls-position":"right","step-strictly":1===t.ruleForm.type,min:1===t.ruleForm.type?1:.1},model:{value:e.row.first,callback:function(r){t.$set(e.row,"first",r)},expression:"scope.row.first"}})],1)]}}],null,!1,2918704294)}),t._v(" "),r("el-table-column",{attrs:{"min-width":"120px",align:"center",label:"运费（元）",prop:"firstPrice"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("el-form-item",{attrs:{rules:t.rules.firstPrice,prop:"region."+e.$index+".firstPrice"}},[r("el-input-number",{attrs:{"controls-position":"right",min:0},model:{value:e.row.firstPrice,callback:function(r){t.$set(e.row,"firstPrice",r)},expression:"scope.row.firstPrice"}})],1)]}}],null,!1,3560784729)}),t._v(" "),r("el-table-column",{attrs:{"min-width":"120px",align:"center",label:t.columns.title2,prop:"renewal"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("el-form-item",{attrs:{rules:t.rules.renewal,prop:"region."+e.$index+".renewal"}},[r("el-input-number",{attrs:{"controls-position":"right","step-strictly":1===t.ruleForm.type,min:1===t.ruleForm.type?1:.1},model:{value:e.row.renewal,callback:function(r){t.$set(e.row,"renewal",r)},expression:"scope.row.renewal"}})],1)]}}],null,!1,3001982106)}),t._v(" "),r("el-table-column",{attrs:{"class-name":"status-col",align:"center",label:"续费（元）","min-width":"120",prop:"renewalPrice"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("el-form-item",{attrs:{rules:t.rules.renewalPrice,prop:"region."+e.$index+".renewalPrice"}},[r("el-input-number",{attrs:{"controls-position":"right",min:0},model:{value:e.row.renewalPrice,callback:function(r){t.$set(e.row,"renewalPrice",r)},expression:"scope.row.renewalPrice"}})],1)]}}],null,!1,1318028453)}),t._v(" "),r("el-table-column",{attrs:{align:"center",label:"操作","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[e.$index>0?r("el-button",{attrs:{type:"text",size:"small"},on:{click:function(r){return t.confirmEdit(t.ruleForm.region,e.$index)}}},[t._v("\n              删除\n            ")]):t._e()]}}],null,!1,3477974826)})],1)],1),t._v(" "),r("el-form-item",[r("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-edit"},on:{click:function(e){return t.addRegion(t.ruleForm.region)}}},[t._v("\n        添加配送区域\n      ")])],1),t._v(" "),r("el-form-item",{attrs:{label:"指定包邮",prop:"appoint"}},[r("el-radio-group",{model:{value:t.ruleForm.appoint,callback:function(e){t.$set(t.ruleForm,"appoint",e)},expression:"ruleForm.appoint"}},[r("el-radio",{attrs:{label:!0}},[t._v("开启")]),t._v(" "),r("el-radio",{attrs:{label:!1}},[t._v("关闭")])],1)],1),t._v(" "),!0===t.ruleForm.appoint?r("el-form-item",{attrs:{prop:"free"}},[r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:t.ruleForm.free,border:"",fit:"","highlight-current-row":"",size:"mini"}},[r("el-table-column",{attrs:{align:"center",label:"选择地区","min-width":"220"},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.row;return[r("el-cascader",{staticStyle:{width:"95%"},attrs:{options:t.cityList,props:t.props,"collapse-tags":"",clearable:""},model:{value:i.city_ids,callback:function(e){t.$set(i,"city_ids",e)},expression:"row.city_ids"}})]}}],null,!1,3891925036)}),t._v(" "),r("el-table-column",{attrs:{"min-width":"180px",align:"center",label:t.columns.title3},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.row;return[r("el-input-number",{attrs:{"controls-position":"right","step-strictly":1===t.ruleForm.type,min:1===t.ruleForm.type?1:.1},model:{value:i.number,callback:function(e){t.$set(i,"number",e)},expression:"row.number"}})]}}],null,!1,2163935474)}),t._v(" "),r("el-table-column",{attrs:{"min-width":"120px",align:"center",label:"包邮金额（元）"},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.row;return[r("el-input-number",{attrs:{"controls-position":"right"},model:{value:i.price,callback:function(e){t.$set(i,"price",e)},expression:"row.price"}})]}}],null,!1,187737026)}),t._v(" "),r("el-table-column",{attrs:{align:"center",label:"操作","min-width":"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("el-button",{attrs:{type:"text",size:"small"},on:{click:function(r){return t.confirmEdit(t.ruleForm.free,e.$index)}}},[t._v("\n              删除\n            ")])]}}],null,!1,4029474057)})],1)],1):t._e(),t._v(" "),!0===t.ruleForm.appoint?r("el-form-item",[r("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-edit"},on:{click:function(e){return t.addFree(t.ruleForm.free)}}},[t._v("\n        添加指定包邮区域\n      ")])],1):t._e(),t._v(" "),r("el-form-item",{attrs:{label:"排序"}},[r("el-input",{staticClass:"withs",attrs:{placeholder:"请输入排序"},model:{value:t.ruleForm.sort,callback:function(e){t.$set(t.ruleForm,"sort",e)},expression:"ruleForm.sort"}})],1)],1):t._e(),t._v(" "),r("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{on:{click:function(e){return t.onClose("ruleForm")}}},[t._v("取 消")]),t._v(" "),r("el-button",{attrs:{type:"primary",loading:t.loading},on:{click:function(e){return t.onsubmit("ruleForm")}}},[t._v("确 定")])],1)],1):t._e()},n=[],a=r("2f2c"),o=r("5c96"),s={name:"",type:1,appoint:!1,sort:0,region:[{first:0,firstPrice:0,renewal:0,renewalPrice:0,city_ids:[]}],undelivery:0,free:[],undelives:{},city_id3:[]},l="重量（kg）",c="体积（m³）",u=[{title:"首件",title2:"续件",title3:"包邮件数"},{title:"首件".concat(l),title2:"续件".concat(l),title3:"包邮".concat(l)},{title:"首件".concat(c),title2:"续件".concat(c),title3:"包邮".concat(c)}],m={name:"CreatTemplates",components:{},data:function(){return{loading:!1,rules:{name:[{required:!0,message:"请输入模板名称",trigger:"blur"}],free:[{type:"array",required:!0,message:"请至少添加一个地区",trigger:"change"}],appoint:[{required:!0,message:"请选择是否指定包邮",trigger:"change"}],undelivery:[{required:!0,message:"请选择是否指定区域不配送",trigger:"change"}],type:[{required:!0,message:"请选择计费方式",trigger:"change"}],region:[{required:!0,message:"请选择活动区域",trigger:"change"}],city_id3:[{type:"array",required:!0,message:"请至少选择一个地区",trigger:"change"}],first:[{required:!0,message:"请输入",trigger:"blur"}],renewal:[{required:!0,message:"请输入",trigger:"blur"}],firstPrice:[{required:!0,message:"请输入运费",trigger:"blur"}],renewalPrice:[{required:!0,message:"请输入续费",trigger:"blur"}]},nodeKey:"city_id",props:{children:"child",label:"name",value:"cityId",multiple:!0},dialogVisible:!1,ruleForm:Object.assign({},s),listLoading:!1,cityList:[],columns:{title:"首件",title2:"续件",title3:"包邮件数"},tempId:0,type:0}},mounted:function(){var t=this;setTimeout((function(){var e=JSON.parse(sessionStorage.getItem("cityList"));t.cityList=e}),1e3)},methods:{changType:function(t){this.type=t},onClose:function(t){this.dialogVisible=!1,this.$refs[t].resetFields()},confirmEdit:function(t,e){t.splice(e,1)},popoverHide:function(){},handleClose:function(){this.dialogVisible=!1,this.ruleForm={name:"",type:1,appoint:!1,sort:0,region:[{first:0,firstPrice:0,renewal:0,renewalPrice:0,city_ids:[]}],undelivery:0,free:[],undelives:{},city_id3:[]}},changeRegion:function(t){console.log(t)},changeRadio:function(t){this.columns=Object.assign({},u[t-1])},addRegion:function(t){t.push(Object.assign({},{first:0,firstPrice:0,renewal:0,renewalPrice:0,city_ids:[]}))},addFree:function(t){t.push(Object.assign({},{city_id:[],number:1,price:1,city_ids:[]}))},getInfo:function(t,e){var r=this;this.tempId=t;var i=o["Loading"].service({fullscreen:!0});a["q"]({id:t}).then((function(t){r.dialogVisible=!0;var e=t;r.ruleForm=Object.assign(r.ruleForm,{name:e.name,type:e.type,appoint:e.appoint,sort:e.sort}),r.columns=Object.assign({},u[r.ruleForm.type-1]),r.$nextTick((function(){i.close()})),r.shippingRegion(),e.appoint&&r.shippingFree()})).catch((function(t){r.$message.error(t.message),r.$nextTick((function(){i.close()}))}))},shippingRegion:function(){var t=this;a["m"]({tempId:this.tempId}).then((function(e){e.forEach((function(t,e){t.title=JSON.parse(t.title),t.city_ids=t.title})),t.ruleForm.region=e}))},shippingFree:function(){var t=this;a["l"]({tempId:this.tempId}).then((function(e){e.forEach((function(t,e){t.title=JSON.parse(t.title),t.city_ids=t.title})),t.ruleForm.free=e}))},getCityList:function(){var t=this;a["c"]().then((function(e){sessionStorage.setItem("cityList",JSON.stringify(e));var r=JSON.parse(sessionStorage.getItem("cityList"));t.cityList=r})).catch((function(e){t.$message.error(e.message)}))},change:function(t){return t.map((function(t){var e=[];t.city_ids.map((function(t){t.splice(0,1),e.push(t[0])})),t.city_id=e})),t},changeOne:function(t){var e=[];return t.map((function(t){t.splice(0,1),e.push(t[0])})),e},onsubmit:function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return!1;e.loading=!0;var r={appoint:e.ruleForm.appoint,name:e.ruleForm.name,sort:e.ruleForm.sort,type:e.ruleForm.type};e.ruleForm.region.forEach((function(t,e){t.title=t.city_ids.length>0?JSON.stringify(t.city_ids):JSON.stringify([[0,0]]);for(var r=0;r<t.city_ids.length;r++)t.city_ids[r].shift();t.cityId=t.city_ids.length>0?t.city_ids.join(","):"all"})),r.shippingTemplatesRegionRequestList=e.ruleForm.region,r.shippingTemplatesRegionRequestList.forEach((function(t,e){})),e.ruleForm.appoint&&(e.ruleForm.free.forEach((function(t,e){t.title=t.city_ids.length>0?JSON.stringify(t.city_ids):JSON.stringify([[0,0]]);for(var r=0;r<t.city_ids.length;r++)t.city_ids[r].shift();t.cityId=t.city_ids.length>0?t.city_ids.join(","):"all"})),r.shippingTemplatesFreeRequestList=e.ruleForm.free,r.shippingTemplatesFreeRequestList.forEach((function(t,e){}))),0===e.type?a["n"](r).then((function(t){e.$message.success("操作成功"),e.handleClose(),e.$nextTick((function(){e.dialogVisible=!1})),setTimeout((function(){e.$emit("getList")}),600),e.loading=!1})):a["p"](r,{id:e.tempId}).then((function(t){e.$message.success("操作成功"),setTimeout((function(){e.$emit("getList"),e.handleClose()}),600),e.$nextTick((function(){e.dialogVisible=!1})),e.loading=!1}))}))},clear:function(){this.ruleForm.name="",this.ruleForm.sort=0}}},d=m,f=(r("e16d"),r("2877")),p=Object(f["a"])(d,i,n,!1,null,"44a816e5",null);e["a"]=p.exports}}]);