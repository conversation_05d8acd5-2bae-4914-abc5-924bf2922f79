package com.ylpz.entity;

public interface WxCallbackDefine {
    String payerTotal = "payerTotal";
    String mchid = "mchid";
    String bankType = "bankType";
    String openid = "openid";
    String updateTime = "updateTime";
    String transactionId = "transactionId";
    String tradeState = "tradeState";
    String tradeStateDesc = "tradeStateDesc";
    String total = "total";
    String payerCurrency = "payerCurrency";
    String createTime = "createTime";
    String appid = "appid";
    String outTradeNo = "outTradeNo";
    String successTime = "successTime";
    String currency = "currency";
    String attach = "attach";
    String id = "id";
    String tradeType = "tradeType";
}
