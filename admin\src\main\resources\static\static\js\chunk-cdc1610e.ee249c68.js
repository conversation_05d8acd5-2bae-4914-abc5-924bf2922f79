(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-cdc1610e"],{"299e":function(e,t,r){"use strict";r("e540")},"2f2c":function(e,t,r){"use strict";r.d(t,"b",(function(){return u})),r.d(t,"c",(function(){return m})),r.d(t,"r",(function(){return d})),r.d(t,"d",(function(){return f})),r.d(t,"a",(function(){return p})),r.d(t,"g",(function(){return g})),r.d(t,"h",(function(){return h})),r.d(t,"j",(function(){return v})),r.d(t,"i",(function(){return y})),r.d(t,"e",(function(){return b})),r.d(t,"o",(function(){return w})),r.d(t,"q",(function(){return _})),r.d(t,"l",(function(){return V})),r.d(t,"m",(function(){return x})),r.d(t,"n",(function(){return T})),r.d(t,"p",(function(){return k})),r.d(t,"k",(function(){return O})),r.d(t,"f",(function(){return S}));var i=r("b775");function a(e){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a(e)}function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,i)}return r}function o(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach((function(t){l(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function l(e,t,r){return(t=s(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function s(e){var t=c(e,"string");return"symbol"==a(t)?t:t+""}function c(e,t){if("object"!=a(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var i=r.call(e,t||"default");if("object"!=a(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function u(e){return Object(i["a"])({url:"/admin/system/city/list",method:"get",params:o({},e)})}function m(){return Object(i["a"])({url:"/admin/system/city/list/tree",method:"get"})}function d(e){return Object(i["a"])({url:"/admin/system/city/update/status",method:"post",params:o({},e)})}function f(e){return Object(i["a"])({url:"/admin/system/city/update",method:"post",params:o({},e)})}function p(e){return Object(i["a"])({url:"/admin/system/city/info",method:"get",params:o({},e)})}function g(e){return Object(i["a"])({url:"/admin/express/list",method:"get",params:o({},e)})}function h(){return Object(i["a"])({url:"/admin/express/sync/express",method:"post"})}function v(e){return Object(i["a"])({url:"/admin/express/update/show",method:"post",data:e})}function y(e){return Object(i["a"])({url:"/admin/express/update",method:"post",data:e})}function b(e){return Object(i["a"])({url:"/admin/express/delete",method:"GET",params:o({},e)})}function w(e){return Object(i["a"])({url:"/admin/express/shipping/templates/list",method:"get",params:o({},e)})}function _(e){return Object(i["a"])({url:"/admin/express/shipping/templates/info",method:"get",params:o({},e)})}function V(e){return Object(i["a"])({url:"/admin/express/shipping/free/list",method:"get",params:o({},e)})}function x(e){return Object(i["a"])({url:"admin/express/shipping/region/list",method:"get",params:o({},e)})}function T(e){return Object(i["a"])({url:"admin/express/shipping/templates/save",method:"post",data:e})}function k(e,t){return Object(i["a"])({url:"admin/express/shipping/templates/update",method:"post",data:e,params:o({},t)})}function O(e){return Object(i["a"])({url:"admin/express/shipping/templates/delete",method:"get",params:e})}function S(e){return Object(i["a"])({url:"admin/express/info",method:"get",params:o({},e)})}},3184:function(e,t,r){"use strict";r.r(t);var i=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"divBox"},[r("el-card",{staticClass:"box-card"},[r("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[r("el-steps",{attrs:{active:e.currentTab,"align-center":"","finish-status":"success"}},[r("el-step",{attrs:{title:"选择拼团商品"}}),e._v(" "),r("el-step",{attrs:{title:"填写基础信息"}}),e._v(" "),r("el-step",{attrs:{title:"修改商品详情"}})],1)],1),e._v(" "),r("el-form",{directives:[{name:"loading",rawName:"v-loading",value:e.fullscreenLoading,expression:"fullscreenLoading"}],ref:"formValidate",staticClass:"formValidate mt20",attrs:{rules:e.ruleValidate,model:e.formValidate,"label-width":"180px"},nativeOn:{submit:function(e){e.preventDefault()}}},[r("div",{directives:[{name:"show",rawName:"v-show",value:0===e.currentTab,expression:"currentTab === 0"}]},[r("el-form-item",{attrs:{label:"选择商品：",prop:"image"}},[r("div",{staticClass:"upLoadPicBox",on:{click:e.changeGood}},[e.formValidate.image?r("div",{staticClass:"pictrue"},[r("img",{attrs:{src:e.formValidate.image}})]):r("div",{staticClass:"upLoad"},[r("i",{staticClass:"el-icon-camera cameraIconfont"})])])])],1),e._v(" "),r("div",{directives:[{name:"show",rawName:"v-show",value:1===e.currentTab,expression:"currentTab === 1"}]},[r("el-row",{attrs:{gutter:24}},[r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"商品主图：",prop:"image"}},[r("div",{staticClass:"upLoadPicBox",on:{click:function(t){return e.modalPicTap("1")}}},[e.formValidate.image?r("div",{staticClass:"pictrue"},[r("img",{attrs:{src:e.formValidate.image}})]):r("div",{staticClass:"upLoad"},[r("i",{staticClass:"el-icon-camera cameraIconfont"})])])])],1),e._v(" "),r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"商品轮播图：",prop:"imagelist"}},[r("div",{staticClass:"acea-row"},[e._l(e.formValidate.imagelist,(function(t,i){return r("div",{key:i,staticClass:"pictrue",attrs:{draggable:"true"},on:{dragstart:function(r){return e.handleDragStart(r,t)},dragover:function(r){return r.preventDefault(),e.handleDragOver(r,t)},dragenter:function(r){return e.handleDragEnter(r,t)},dragend:function(r){return e.handleDragEnd(r,t)}}},[r("img",{attrs:{src:t}}),e._v(" "),r("i",{staticClass:"el-icon-error btndel",on:{click:function(t){return e.handleRemove(i)}}})])})),e._v(" "),e.formValidate.imagelist.length<10?r("div",{staticClass:"upLoadPicBox",on:{click:function(t){return e.modalPicTap("2")}}},[r("div",{staticClass:"upLoad"},[r("i",{staticClass:"el-icon-camera cameraIconfont"})])]):e._e()],2)])],1),e._v(" "),r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"拼团名称：",prop:"title"}},[r("el-input",{attrs:{maxlength:"249",placeholder:"请输入拼团名称"},model:{value:e.formValidate.title,callback:function(t){e.$set(e.formValidate,"title",t)},expression:"formValidate.title"}})],1)],1),e._v(" "),r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"拼团简介：",prop:"info"}},[r("el-input",{attrs:{maxlength:"250",type:"textarea",rows:3,placeholder:"请输入拼团简介"},model:{value:e.formValidate.info,callback:function(t){e.$set(e.formValidate,"info",t)},expression:"formValidate.info"}})],1)],1),e._v(" "),r("el-col",e._b({},"el-col",e.grid2,!1),[r("el-form-item",{attrs:{label:"单位：",prop:"unitName"}},[r("el-input",{staticClass:"selWidthd",attrs:{placeholder:"请输入单位"},model:{value:e.formValidate.unitName,callback:function(t){e.$set(e.formValidate,"unitName",t)},expression:"formValidate.unitName"}})],1)],1),e._v(" "),r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"活动日期：",prop:"timeVal"}},[r("el-date-picker",{staticClass:"mr20",attrs:{type:"daterange","value-format":"yyyy-MM-dd",format:"yyyy-MM-dd","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions},on:{change:e.onchangeTime},model:{value:e.formValidate.timeVal,callback:function(t){e.$set(e.formValidate,"timeVal",t)},expression:"formValidate.timeVal"}}),e._v(" "),r("span",[e._v("设置活动开启结束时间，用户可以在设置时间内发起参与拼团")])],1)],1),e._v(" "),r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"拼团时效(单位 小时)：",prop:"effectiveTime"}},[r("el-input-number",{staticClass:"selWidthd mr20",attrs:{min:1,step:1,"step-strictly":"",placeholder:"请输入拼团人数"},model:{value:e.formValidate.effectiveTime,callback:function(t){e.$set(e.formValidate,"effectiveTime",t)},expression:"formValidate.effectiveTime"}}),e._v(" "),r("span",[e._v("用户发起拼团后开始计时，需在设置时间内邀请到规定好友人数参团，超过时效时间，则系统判定拼团失败，自动发起退款")])],1)],1),e._v(" "),r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"拼团人数：",prop:"people"}},[r("el-input-number",{staticClass:"selWidthd mr20",attrs:{min:2,step:1,"step-strictly":"",placeholder:"请输入拼团人数"},model:{value:e.formValidate.people,callback:function(t){e.$set(e.formValidate,"people",t)},expression:"formValidate.people"}}),e._v(" "),r("span",[e._v("单次拼团需要参与的用户数")])],1)],1),e._v(" "),r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"购买数量限制：",prop:"num"}},[r("el-input-number",{staticClass:"selWidthd mr20",attrs:{min:1,step:1,"step-strictly":"",placeholder:"请输入帮砍次数"},model:{value:e.formValidate.num,callback:function(t){e.$set(e.formValidate,"num",t)},expression:"formValidate.num"}}),e._v(" "),r("span",[e._v("活动时间内每个用户参与拼团的次数限制。例如设置为4，表示本次活动有效期内，每个用户最多可参与购买4次")])],1)],1),e._v(" "),r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"单次购买数量限制：",prop:"onceNum"}},[r("el-input-number",{staticClass:"selWidthd mr20",attrs:{min:1,max:e.formValidate.num,step:1,"step-strictly":"",placeholder:"请输入购买数量限制"},model:{value:e.formValidate.onceNum,callback:function(t){e.$set(e.formValidate,"onceNum",t)},expression:"formValidate.onceNum"}}),e._v(" "),r("span",[e._v("用户参与拼团时，一次购买最大数量限制。例如设置为2，表示参与拼团时，用户一次购买数量最大可选择2个")])],1)],1),e._v(" "),r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"补齐人数：",prop:"virtualRation"}},[r("el-input-number",{staticClass:"selWidthd mr20",attrs:{min:0,max:e.formValidate.people-1,step:1,"step-strictly":"",placeholder:"请输入补齐人数"},model:{value:e.formValidate.virtualRation,callback:function(t){e.$set(e.formValidate,"virtualRation",t)},expression:"formValidate.virtualRation"}}),e._v(" "),r("span",[e._v("当用户参与拼团后，成团时效内未成团情况下，设置补齐人数可虚拟成团。例如：成团人数为10人，补齐人数为4人，真实用户需要参与6人成团才可以在最后未成团时自动补齐虚拟人员")])],1)],1),e._v(" "),r("el-col",e._b({},"el-col",e.grid2,!1),[r("el-form-item",{attrs:{label:"排序：",prop:"sort"}},[r("el-input-number",{staticClass:"selWidthd",attrs:{max:9999,placeholder:"请输入排序"},model:{value:e.formValidate.sort,callback:function(t){e.$set(e.formValidate,"sort",t)},expression:"formValidate.sort"}})],1)],1),e._v(" "),r("el-col",e._b({},"el-col",e.grid2,!1),[r("el-form-item",{attrs:{label:"运费模板：",prop:"tempId"}},[r("div",{staticClass:"acea-row"},[r("el-select",{staticClass:"selWidthd",attrs:{placeholder:"请选择"},model:{value:e.formValidate.tempId,callback:function(t){e.$set(e.formValidate,"tempId",t)},expression:"formValidate.tempId"}},e._l(e.shippingList,(function(e){return r("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)])],1),e._v(" "),r("el-col",e._b({},"el-col",e.grid2,!1),[r("el-form-item",{attrs:{label:"活动状态：",required:""}},[r("el-radio-group",{model:{value:e.formValidate.isShow,callback:function(t){e.$set(e.formValidate,"isShow",t)},expression:"formValidate.isShow"}},[r("el-radio",{staticClass:"radio",attrs:{label:!1}},[e._v("关闭")]),e._v(" "),r("el-radio",{attrs:{label:!0}},[e._v("开启")])],1)],1)],1),e._v(" "),r("el-col",{attrs:{span:24}},[r("el-form-item",{staticClass:"labeltop",attrs:{label:"商品属性：",required:""}},[r("el-table",{ref:"multipleTable",staticStyle:{width:"100%"},attrs:{data:e.ManyAttrValue,"tooltip-effect":"dark"},on:{"selection-change":e.handleSelectionChange}},[e.formValidate.specType?r("el-table-column",{key:"1",attrs:{type:"selection",width:"55"}}):e._e(),e._v(" "),e.manyTabDate&&e.formValidate.specType?e._l(e.manyTabDate,(function(t,i){return r("el-table-column",{key:i,attrs:{align:"center",label:e.manyTabTit[i].title,"min-width":"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("span",{staticClass:"priceBox",domProps:{textContent:e._s(t.row[i])}})]}}],null,!0)})})):e._e(),e._v(" "),r("el-table-column",{attrs:{align:"center",label:"图片","min-width":"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("div",{staticClass:"upLoadPicBox",on:{click:function(r){return e.modalPicTap("1","duo",t.$index)}}},[t.row.image?r("div",{staticClass:"pictrue tabPic"},[r("img",{attrs:{src:t.row.image}})]):r("div",{staticClass:"upLoad tabPic"},[r("i",{staticClass:"el-icon-camera cameraIconfont"})])])]}}])}),e._v(" "),e._l(e.attrValue,(function(t,i){return r("el-table-column",{key:i,attrs:{label:e.formThead[i].title,align:"center","min-width":"140"},scopedSlots:e._u([{key:"default",fn:function(t){return["拼团价"===e.formThead[i].title?r("el-input-number",{staticClass:"priceBox",attrs:{size:"small",min:0,precision:2,step:.1},model:{value:t.row[i],callback:function(r){e.$set(t.row,i,r)},expression:"scope.row[iii]"}}):"限量"===e.formThead[i].title?r("el-input-number",{staticClass:"priceBox",attrs:{size:"small",type:"number",min:1,max:t.row.stock,step:1,"step-strictly":""},model:{value:t.row[i],callback:function(r){e.$set(t.row,i,r)},expression:"scope.row[iii]"}}):r("span",{staticClass:"priceBox",domProps:{textContent:e._s(t.row[i])}})]}}],null,!0)})}))],2)],1)],1)],1)],1),e._v(" "),r("div",{directives:[{name:"show",rawName:"v-show",value:2===e.currentTab,expression:"currentTab === 2"}]},[r("el-form-item",{attrs:{label:"商品详情："}},[r("Tinymce",{model:{value:e.formValidate.content,callback:function(t){e.$set(e.formValidate,"content",t)},expression:"formValidate.content"}})],1)],1),e._v(" "),r("el-form-item",{staticStyle:{"margin-top":"30px"}},[r("el-button",{directives:[{name:"show",rawName:"v-show",value:!e.$route.params.id&&e.currentTab>0||e.$route.params.id&&2===e.currentTab,expression:"(!$route.params.id && currentTab > 0) || ($route.params.id && currentTab===2)"}],staticClass:"submission",attrs:{type:"primary",size:"small"},on:{click:e.handleSubmitUp}},[e._v("上一步")]),e._v(" "),r("el-button",{directives:[{name:"show",rawName:"v-show",value:0==e.currentTab,expression:"currentTab == 0"}],staticClass:"submission",attrs:{type:"primary",size:"small"},on:{click:function(t){return e.handleSubmitNest1("formValidate")}}},[e._v("下一步")]),e._v(" "),r("el-button",{directives:[{name:"show",rawName:"v-show",value:1==e.currentTab,expression:"currentTab == 1"}],staticClass:"submission",attrs:{type:"primary",size:"small"},on:{click:function(t){return e.handleSubmitNest2("formValidate")}}},[e._v("下一步")]),e._v(" "),r("el-button",{directives:[{name:"show",rawName:"v-show",value:2===e.currentTab,expression:"currentTab===2"},{name:"hasPermi",rawName:"v-hasPermi",value:["admin:combination:update"],expression:"['admin:combination:update']"}],staticClass:"submission",attrs:{loading:e.loading,type:"primary",size:"small"},on:{click:function(t){return e.handleSubmit("formValidate")}}},[e._v("提交")])],1)],1)],1),e._v(" "),r("CreatTemplates",{ref:"addTemplates",on:{getList:e.getShippingList}})],1)},a=[],n=r("8256"),o=r("73f5"),l=r("2f2c"),s=r("02df"),c=r("b7be"),u=r("ff3a"),m=r("ed08"),d=r("61f7");function f(e){return f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},f(e)}function p(e){return y(e)||v(e)||h(e)||g()}function g(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function h(e,t){if(e){if("string"==typeof e)return b(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?b(e,t):void 0}}function v(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function y(e){if(Array.isArray(e))return b(e)}function b(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,i=Array(t);r<t;r++)i[r]=e[r];return i}function w(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */w=function(){return t};var e,t={},r=Object.prototype,i=r.hasOwnProperty,a=Object.defineProperty||function(e,t,r){e[t]=r.value},n="function"==typeof Symbol?Symbol:{},o=n.iterator||"@@iterator",l=n.asyncIterator||"@@asyncIterator",s=n.toStringTag||"@@toStringTag";function c(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,r){return e[t]=r}}function u(e,t,r,i){var n=t&&t.prototype instanceof y?t:y,o=Object.create(n.prototype),l=new P(i||[]);return a(o,"_invoke",{value:$(e,r,l)}),o}function m(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=u;var d="suspendedStart",p="suspendedYield",g="executing",h="completed",v={};function y(){}function b(){}function _(){}var V={};c(V,o,(function(){return this}));var x=Object.getPrototypeOf,T=x&&x(x(F([])));T&&T!==r&&i.call(T,o)&&(V=T);var k=_.prototype=y.prototype=Object.create(V);function O(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function S(e,t){function r(a,n,o,l){var s=m(e[a],e,n);if("throw"!==s.type){var c=s.arg,u=c.value;return u&&"object"==f(u)&&i.call(u,"__await")?t.resolve(u.__await).then((function(e){r("next",e,o,l)}),(function(e){r("throw",e,o,l)})):t.resolve(u).then((function(e){c.value=e,o(c)}),(function(e){return r("throw",e,o,l)}))}l(s.arg)}var n;a(this,"_invoke",{value:function(e,i){function a(){return new t((function(t,a){r(e,i,t,a)}))}return n=n?n.then(a,a):a()}})}function $(t,r,i){var a=d;return function(n,o){if(a===g)throw Error("Generator is already running");if(a===h){if("throw"===n)throw o;return{value:e,done:!0}}for(i.method=n,i.arg=o;;){var l=i.delegate;if(l){var s=L(l,i);if(s){if(s===v)continue;return s}}if("next"===i.method)i.sent=i._sent=i.arg;else if("throw"===i.method){if(a===d)throw a=h,i.arg;i.dispatchException(i.arg)}else"return"===i.method&&i.abrupt("return",i.arg);a=g;var c=m(t,r,i);if("normal"===c.type){if(a=i.done?h:p,c.arg===v)continue;return{value:c.arg,done:i.done}}"throw"===c.type&&(a=h,i.method="throw",i.arg=c.arg)}}}function L(t,r){var i=r.method,a=t.iterator[i];if(a===e)return r.delegate=null,"throw"===i&&t.iterator.return&&(r.method="return",r.arg=e,L(t,r),"throw"===r.method)||"return"!==i&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+i+"' method")),v;var n=m(a,t.iterator,r.arg);if("throw"===n.type)return r.method="throw",r.arg=n.arg,r.delegate=null,v;var o=n.arg;return o?o.done?(r[t.resultName]=o.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,v):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function j(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function C(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function P(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(j,this),this.reset(!0)}function F(t){if(t||""===t){var r=t[o];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,n=function r(){for(;++a<t.length;)if(i.call(t,a))return r.value=t[a],r.done=!1,r;return r.value=e,r.done=!0,r};return n.next=n}}throw new TypeError(f(t)+" is not iterable")}return b.prototype=_,a(k,"constructor",{value:_,configurable:!0}),a(_,"constructor",{value:b,configurable:!0}),b.displayName=c(_,s,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===b||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,_):(e.__proto__=_,c(e,s,"GeneratorFunction")),e.prototype=Object.create(k),e},t.awrap=function(e){return{__await:e}},O(S.prototype),c(S.prototype,l,(function(){return this})),t.AsyncIterator=S,t.async=function(e,r,i,a,n){void 0===n&&(n=Promise);var o=new S(u(e,r,i,a),n);return t.isGeneratorFunction(r)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},O(k),c(k,s,"Generator"),c(k,o,(function(){return this})),c(k,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var i in t)r.push(i);return r.reverse(),function e(){for(;r.length;){var i=r.pop();if(i in t)return e.value=i,e.done=!1,e}return e.done=!0,e}},t.values=F,P.prototype={constructor:P,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(C),!t)for(var r in this)"t"===r.charAt(0)&&i.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function a(i,a){return l.type="throw",l.arg=t,r.next=i,a&&(r.method="next",r.arg=e),!!a}for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n],l=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var s=i.call(o,"catchLoc"),c=i.call(o,"finallyLoc");if(s&&c){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(s){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&i.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var n=a;break}}n&&("break"===e||"continue"===e)&&n.tryLoc<=t&&t<=n.finallyLoc&&(n=null);var o=n?n.completion:{};return o.type=e,o.arg=t,n?(this.method="next",this.next=n.finallyLoc,v):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),C(r),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var i=r.completion;if("throw"===i.type){var a=i.arg;C(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(t,r,i){return this.delegate={iterator:F(t),resultName:r,nextLoc:i},"next"===this.method&&(this.arg=e),v}},t}function _(e,t,r,i,a,n,o){try{var l=e[n](o),s=l.value}catch(e){return void r(e)}l.done?t(s):Promise.resolve(s).then(i,a)}function V(e){return function(){var t=this,r=arguments;return new Promise((function(i,a){var n=e.apply(t,r);function o(e){_(n,i,a,o,l,"next",e)}function l(e){_(n,i,a,o,l,"throw",e)}o(void 0)}))}}var x={image:"",images:"",imagelist:[],title:"",info:"",num:1,unitName:"",sort:0,giveIntegral:0,ficti:0,isShow:!1,tempId:"",attrValue:[{image:"",price:0,cost:0,otPrice:0,stock:0,quota:1,weight:0,volume:0,barCode:""}],attr:[],selectRule:"",content:"",specType:!1,id:0,startTime:"",stopTime:"",timeVal:[],effectiveTime:0,people:2,virtualRation:0},T={price:{title:"拼团价"},cost:{title:"成本价"},otPrice:{title:"原价"},stock:{title:"库存"},quota:{title:"限量"},barCode:{title:"商品编号"},weight:{title:"重量（KG）"},volume:{title:"体积(m³)"}},k={name:"creatSeckill",components:{CreatTemplates:u["a"],Tinymce:n["a"]},data:function(){return{pickerOptions:{disabledDate:function(e){return e.getTime()<(new Date).setTime((new Date).getTime()-864e5)}},props2:{children:"child",label:"name",value:"id",multiple:!0,emitPath:!1},grid2:{xl:8,lg:10,md:12,sm:24,xs:24},currentTab:0,formThead:Object.assign({},T),formValidate:Object.assign({},x),loading:!1,fullscreenLoading:!1,merCateList:[],shippingList:[],seckillTime:[],ruleValidate:{productId:[{required:!0,message:"请选择商品",trigger:"change"}],title:[{required:!0,message:"请输入商品标题",trigger:"blur"}],attrValue:[{required:!0,message:"请选择商品属相",trigger:"change",type:"array",min:"1"}],num:[{required:!0,message:"请输入购买数量限制",trigger:"blur"}],unitName:[{required:!0,message:"请输入单位",trigger:"blur"}],info:[{required:!0,message:"请输入拼团商品简介",trigger:"blur"}],tempId:[{required:!0,message:"请选择运费模板",trigger:"change"}],image:[{required:!0,message:"请上传商品图",trigger:"change"}],imagelist:[{required:!0,message:"请上传商品轮播图",type:"array",trigger:"change"}],specType:[{required:!0,message:"请选择商品规格",trigger:"change"}],timeVal:[{required:!0,message:"请选择活动日期",trigger:"change",type:"array"}],virtualRation:[{required:!0,message:"请输入补齐人数",trigger:"blur"}],onceNum:[{required:!0,message:"请输入单次购买数量限制",trigger:"blur"}],people:[{required:!0,message:"请输入拼团人数",trigger:"blur"}],effectiveTime:[{required:!0,message:"请输入拼团时效",trigger:"blur"}]},manyTabDate:{},manyTabTit:{},attrInfo:{},tempRoute:{},multipleSelection:[],productId:0,radio:"",ManyAttrValue:[Object.assign({},x.attrValue[0])]}},computed:{attrValue:function(){var e=Object.assign({},x.attrValue[0]);return delete e.image,e}},created:function(){this.$watch("formValidate.attr",this.watCh),this.tempRoute=Object.assign({},this.$route)},mounted:function(){var e=this;Object(s["a"])(1).then((function(t){e.seckillTime=t.list})),this.formValidate.imagelist=[],this.$route.params.id&&(this.setTagsViewTitle(),this.getInfo(),this.currentTab=1),this.getShippingList(),this.getCategorySelect()},methods:{handleSelectionChange:function(e){e.map((function(e){e.checked=!0})),this.multipleSelection=e},watCh:function(e){var t={},r={};this.formValidate.attr.forEach((function(e,i){t[e.attrName]={title:e.attrName},r[e.attrName]=""})),this.manyTabTit=t,this.manyTabDate=r,this.formThead=Object.assign({},this.formThead,t)},handleRemove:function(e){this.formValidate.imagelist.splice(e,1)},modalPicTap:function(e,t,r){var i=this;this.$modalUpload((function(a){if("1"!==e||t||(i.formValidate.image=a[0].sattDir,i.ManyAttrValue[0].image=a[0].sattDir),"2"===e&&!t){if(a.length>10)return this.$message.warning("最多选择10张图片！");if(a.length+i.formValidate.imagelist.length>10)return this.$message.warning("最多选择10张图片！");a.map((function(e){i.formValidate.imagelist.push(e.sattDir)}))}"1"===e&&"duo"===t&&(i.ManyAttrValue[r].image=a[0].sattDir)}),e,"content")},onchangeTime:function(e){this.formValidate.timeVal=e,this.formValidate.startTime=e?e[0]:"",this.formValidate.stopTime=e?e[1]:""},changeGood:function(){var e=this;this.$modalGoodList((function(t){e.formValidate.image=t.image,e.productId=t.id}))},handleSubmitNest1:function(){if(!this.formValidate.image)return this.$message.warning("请选择商品！");this.currentTab++,this.$route.params.id||this.getProdect(this.productId)},getCategorySelect:function(){var e=this;Object(o["d"])({status:-1,type:1}).then((function(t){e.merCateList=e.filerMerCateList(t)}))},filerMerCateList:function(e){return e.map((function(e){return e.child||(e.disabled=!0),e.label=e.name,e}))},getShippingList:function(){var e=this;Object(l["o"])(this.tempData).then((function(t){e.shippingList=t.list}))},addTem:function(){this.$refs.addTemplates.dialogVisible=!0,this.$refs.addTemplates.getCityList()},getInfo:function(){this.$route.params.id?this.getSekllProdect(this.$route.params.id):this.getProdect(this.productId)},getProdect:function(e){var t=this;this.fullscreenLoading=!0,Object(o["l"])(e).then(function(){var e=V(w().mark((function e(r){return w().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.formValidate={image:t.$selfUtil.setDomain(r.image),imagelist:JSON.parse(r.sliderImage),title:r.storeName,info:r.storeInfo,quota:"",unitName:r.unitName,sort:r.sort,tempId:r.tempId,attr:r.attr,selectRule:r.selectRule,content:r.content,specType:r.specType,productId:r.id,giveIntegral:r.giveIntegral,ficti:r.ficti,startTime:r.startTime||"",stopTime:r.stopTime||"",timeVal:[],status:0,isShow:!1,num:1,isHost:!1,people:2,onceNum:1,virtualRation:"",effectiveTime:0,isPostage:!1},r.specType?(r.attrValue.forEach((function(e){for(var t in e.quota=e.stock,e.attrValue=JSON.parse(e.attrValue),e.attrValue)e[t]=e.attrValue[t]})),t.$nextTick((function(){r.attrValue.forEach((function(e){e.image=t.$selfUtil.setDomain(e.image),t.$refs.multipleTable.toggleRowSelection(e,!0),t.$set(e,"checked",!0)}))})),t.ManyAttrValue=r.attrValue,t.multipleSelection=r.attrValue):(r.attrValue.forEach((function(e){e.quota=e.stock})),t.ManyAttrValue=r.attrValue,t.formValidate.attr=r.attr),t.fullscreenLoading=!1;case 3:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.fullscreenLoading=!1}))},getSekllProdect:function(e){var t=this;this.fullscreenLoading=!0,Object(c["j"])({id:e}).then(function(){var e=V(w().mark((function e(r){return w().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.formValidate={image:t.$selfUtil.setDomain(r.image),imagelist:JSON.parse(r.sliderImage),title:r.title,info:r.info,unitName:r.unitName,sort:r.sort,tempId:r.tempId,attr:r.attr,selectRule:r.selectRule,content:r.content,specType:r.specType,productId:r.productId,giveIntegral:r.giveIntegral,ficti:r.ficti,timeVal:r.startTime&&r.stopTime?[Object(m["c"])(new Date(r.startTime),"yyyy-MM-dd"),Object(m["c"])(new Date(r.stopTime),"yyyy-MM-dd")]:[],status:r.status,isShow:r.isShow,num:r.num,isHost:r.isHost,people:r.people,onceNum:r.onceNum,virtualRation:r.virtualRation,effectiveTime:r.effectiveTime,isPostage:!1,startTime:r.startTime||"",stopTime:r.stopTime||"",id:r.id},r.specType?(t.ManyAttrValue=r.attrValue,t.$nextTick((function(){t.ManyAttrValue.forEach((function(e,r){for(var i in e.image=t.$selfUtil.setDomain(e.image),e.attrValue=JSON.parse(e.attrValue),e.attrValue)e[i]=e.attrValue[i];e.id&&(t.$set(e,"price",e.price),t.$set(e,"quota",e.quota),t.$nextTick((function(){t.$refs.multipleTable.toggleRowSelection(e,!0)})))}))}))):t.ManyAttrValue=r.attrValue,t.fullscreenLoading=!1;case 3:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.fullscreenLoading=!1}))},handleSubmitNest2:function(e){var t=this;this.$refs[e].validate((function(e){return!!e&&(t.formValidate.specType&&0===t.multipleSelection.length?t.$message.warning("请选择至少一个商品属性！"):void t.currentTab++)}))},handleSubmit:Object(d["a"])((function(e){var t=this;this.formValidate.specType?this.formValidate.attrValue=this.multipleSelection:this.formValidate.attrValue=this.ManyAttrValue,this.formValidate.attrValue.forEach((function(e){e.attrValue=JSON.stringify(e.attrValue)})),this.formValidate.images=JSON.stringify(this.formValidate.imagelist),this.formValidate.startTime=this.formValidate.timeVal[0],this.formValidate.stopTime=this.formValidate.timeVal[1],this.$refs[e].validate((function(r){r?(t.fullscreenLoading=!0,t.loading=!0,t.$route.params.id?Object(c["n"])({id:t.$route.params.id},t.formValidate).then(V(w().mark((function r(){return w().wrap((function(r){while(1)switch(r.prev=r.next){case 0:t.fullscreenLoading=!1,t.$message.success("编辑成功"),t.$router.push({path:"/marketing/groupBuy/groupGoods"}),t.$refs[e].resetFields(),t.formValidate.imagelist=[],t.loading=!1;case 6:case"end":return r.stop()}}),r)})))).catch((function(){t.fullscreenLoading=!1,t.loading=!1})):Object(c["l"])(t.formValidate).then(function(){var r=V(w().mark((function r(i){return w().wrap((function(r){while(1)switch(r.prev=r.next){case 0:t.fullscreenLoading=!1,t.$message.success("新增成功"),t.$router.push({path:"/marketing/groupBuy/groupGoods"}),t.$refs[e].resetFields(),t.formValidate.imagelist=[],t.loading=!1;case 6:case"end":return r.stop()}}),r)})));return function(e){return r.apply(this,arguments)}}()).catch((function(){t.fullscreenLoading=!1,t.loading=!1}))):t.formValidate.storeName&&t.formValidate.unitName&&t.formValidate.store_info&&t.formValidate.image&&t.formValidate.images||t.$message.warning("请填写完整商品信息！")}))})),handleSubmitUp:function(){this.currentTab--<0&&(this.currentTab=0)},setTagsViewTitle:function(){var e="编辑拼团商品",t=Object.assign({},this.tempRoute,{title:"".concat(e,"-").concat(this.$route.params.id)});this.$store.dispatch("tagsView/updateVisitedView",t)},handleDragStart:function(e,t){this.dragging=t},handleDragEnd:function(e,t){this.dragging=null},handleDragOver:function(e){e.dataTransfer.dropEffect="move"},handleDragEnter:function(e,t){if(e.dataTransfer.effectAllowed="move",t!==this.dragging){var r=p(this.formValidate.imagelist),i=r.indexOf(this.dragging),a=r.indexOf(t);r.splice.apply(r,[a,0].concat(p(r.splice(i,1)))),this.formValidate.imagelist=r}}}},O=k,S=(r("299e"),r("2877")),$=Object(S["a"])(O,i,a,!1,null,"93ec9c30",null);t["default"]=$.exports},cf0d:function(e,t,r){},e16d:function(e,t,r){"use strict";r("cf0d")},e540:function(e,t,r){},ff3a:function(e,t,r){"use strict";var i=function(){var e=this,t=e.$createElement,r=e._self._c||t;return e.dialogVisible?r("el-dialog",{attrs:{title:"运费模板",visible:e.dialogVisible,width:"1000px","before-close":e.handleClose},on:{"update:visible":function(t){e.dialogVisible=t}}},[e.dialogVisible?r("el-form",{ref:"ruleForm",attrs:{model:e.ruleForm,"label-width":"120px",size:"mini",rules:e.rules}},[r("el-form-item",{attrs:{label:"模板名称",prop:"name"}},[r("el-input",{staticClass:"withs",attrs:{placeholder:"请输入模板名称"},model:{value:e.ruleForm.name,callback:function(t){e.$set(e.ruleForm,"name",t)},expression:"ruleForm.name"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"计费方式",prop:"type"}},[r("el-radio-group",{on:{change:function(t){return e.changeRadio(e.ruleForm.type)}},model:{value:e.ruleForm.type,callback:function(t){e.$set(e.ruleForm,"type",t)},expression:"ruleForm.type"}},[r("el-radio",{attrs:{label:1}},[e._v("按件数")]),e._v(" "),r("el-radio",{attrs:{label:2}},[e._v("按重量")]),e._v(" "),r("el-radio",{attrs:{label:3}},[e._v("按体积")])],1)],1),e._v(" "),r("el-form-item",{attrs:{label:"配送区域及运费",prop:"region"}},[r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticClass:"tempBox",staticStyle:{width:"100%"},attrs:{data:e.ruleForm.region,border:"",fit:"","highlight-current-row":"",size:"mini"}},[r("el-table-column",{attrs:{align:"center",label:"可配送区域","min-width":"260"},scopedSlots:e._u([{key:"default",fn:function(t){return[0===t.$index?r("span",[e._v("默认全国")]):r("el-cascader",{staticStyle:{width:"98%"},attrs:{options:e.cityList,props:e.props,"collapse-tags":"",clearable:"",filterable:""},on:{change:e.changeRegion},model:{value:t.row.city_ids,callback:function(r){e.$set(t.row,"city_ids",r)},expression:"scope.row.city_ids"}})]}}],null,!1,41555841)}),e._v(" "),r("el-table-column",{attrs:{"min-width":"130px",align:"center",label:e.columns.title,prop:"first"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-form-item",{attrs:{rules:e.rules.first,prop:"region."+t.$index+".first"}},[r("el-input-number",{attrs:{"controls-position":"right","step-strictly":1===e.ruleForm.type,min:1===e.ruleForm.type?1:.1},model:{value:t.row.first,callback:function(r){e.$set(t.row,"first",r)},expression:"scope.row.first"}})],1)]}}],null,!1,2918704294)}),e._v(" "),r("el-table-column",{attrs:{"min-width":"120px",align:"center",label:"运费（元）",prop:"firstPrice"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-form-item",{attrs:{rules:e.rules.firstPrice,prop:"region."+t.$index+".firstPrice"}},[r("el-input-number",{attrs:{"controls-position":"right",min:0},model:{value:t.row.firstPrice,callback:function(r){e.$set(t.row,"firstPrice",r)},expression:"scope.row.firstPrice"}})],1)]}}],null,!1,3560784729)}),e._v(" "),r("el-table-column",{attrs:{"min-width":"120px",align:"center",label:e.columns.title2,prop:"renewal"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-form-item",{attrs:{rules:e.rules.renewal,prop:"region."+t.$index+".renewal"}},[r("el-input-number",{attrs:{"controls-position":"right","step-strictly":1===e.ruleForm.type,min:1===e.ruleForm.type?1:.1},model:{value:t.row.renewal,callback:function(r){e.$set(t.row,"renewal",r)},expression:"scope.row.renewal"}})],1)]}}],null,!1,3001982106)}),e._v(" "),r("el-table-column",{attrs:{"class-name":"status-col",align:"center",label:"续费（元）","min-width":"120",prop:"renewalPrice"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-form-item",{attrs:{rules:e.rules.renewalPrice,prop:"region."+t.$index+".renewalPrice"}},[r("el-input-number",{attrs:{"controls-position":"right",min:0},model:{value:t.row.renewalPrice,callback:function(r){e.$set(t.row,"renewalPrice",r)},expression:"scope.row.renewalPrice"}})],1)]}}],null,!1,1318028453)}),e._v(" "),r("el-table-column",{attrs:{align:"center",label:"操作","min-width":"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.$index>0?r("el-button",{attrs:{type:"text",size:"small"},on:{click:function(r){return e.confirmEdit(e.ruleForm.region,t.$index)}}},[e._v("\n              删除\n            ")]):e._e()]}}],null,!1,3477974826)})],1)],1),e._v(" "),r("el-form-item",[r("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-edit"},on:{click:function(t){return e.addRegion(e.ruleForm.region)}}},[e._v("\n        添加配送区域\n      ")])],1),e._v(" "),r("el-form-item",{attrs:{label:"指定包邮",prop:"appoint"}},[r("el-radio-group",{model:{value:e.ruleForm.appoint,callback:function(t){e.$set(e.ruleForm,"appoint",t)},expression:"ruleForm.appoint"}},[r("el-radio",{attrs:{label:!0}},[e._v("开启")]),e._v(" "),r("el-radio",{attrs:{label:!1}},[e._v("关闭")])],1)],1),e._v(" "),!0===e.ruleForm.appoint?r("el-form-item",{attrs:{prop:"free"}},[r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:e.ruleForm.free,border:"",fit:"","highlight-current-row":"",size:"mini"}},[r("el-table-column",{attrs:{align:"center",label:"选择地区","min-width":"220"},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.row;return[r("el-cascader",{staticStyle:{width:"95%"},attrs:{options:e.cityList,props:e.props,"collapse-tags":"",clearable:""},model:{value:i.city_ids,callback:function(t){e.$set(i,"city_ids",t)},expression:"row.city_ids"}})]}}],null,!1,3891925036)}),e._v(" "),r("el-table-column",{attrs:{"min-width":"180px",align:"center",label:e.columns.title3},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.row;return[r("el-input-number",{attrs:{"controls-position":"right","step-strictly":1===e.ruleForm.type,min:1===e.ruleForm.type?1:.1},model:{value:i.number,callback:function(t){e.$set(i,"number",t)},expression:"row.number"}})]}}],null,!1,2163935474)}),e._v(" "),r("el-table-column",{attrs:{"min-width":"120px",align:"center",label:"包邮金额（元）"},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.row;return[r("el-input-number",{attrs:{"controls-position":"right"},model:{value:i.price,callback:function(t){e.$set(i,"price",t)},expression:"row.price"}})]}}],null,!1,187737026)}),e._v(" "),r("el-table-column",{attrs:{align:"center",label:"操作","min-width":"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-button",{attrs:{type:"text",size:"small"},on:{click:function(r){return e.confirmEdit(e.ruleForm.free,t.$index)}}},[e._v("\n              删除\n            ")])]}}],null,!1,4029474057)})],1)],1):e._e(),e._v(" "),!0===e.ruleForm.appoint?r("el-form-item",[r("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-edit"},on:{click:function(t){return e.addFree(e.ruleForm.free)}}},[e._v("\n        添加指定包邮区域\n      ")])],1):e._e(),e._v(" "),r("el-form-item",{attrs:{label:"排序"}},[r("el-input",{staticClass:"withs",attrs:{placeholder:"请输入排序"},model:{value:e.ruleForm.sort,callback:function(t){e.$set(e.ruleForm,"sort",t)},expression:"ruleForm.sort"}})],1)],1):e._e(),e._v(" "),r("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{on:{click:function(t){return e.onClose("ruleForm")}}},[e._v("取 消")]),e._v(" "),r("el-button",{attrs:{type:"primary",loading:e.loading},on:{click:function(t){return e.onsubmit("ruleForm")}}},[e._v("确 定")])],1)],1):e._e()},a=[],n=r("2f2c"),o=r("5c96"),l={name:"",type:1,appoint:!1,sort:0,region:[{first:0,firstPrice:0,renewal:0,renewalPrice:0,city_ids:[]}],undelivery:0,free:[],undelives:{},city_id3:[]},s="重量（kg）",c="体积（m³）",u=[{title:"首件",title2:"续件",title3:"包邮件数"},{title:"首件".concat(s),title2:"续件".concat(s),title3:"包邮".concat(s)},{title:"首件".concat(c),title2:"续件".concat(c),title3:"包邮".concat(c)}],m={name:"CreatTemplates",components:{},data:function(){return{loading:!1,rules:{name:[{required:!0,message:"请输入模板名称",trigger:"blur"}],free:[{type:"array",required:!0,message:"请至少添加一个地区",trigger:"change"}],appoint:[{required:!0,message:"请选择是否指定包邮",trigger:"change"}],undelivery:[{required:!0,message:"请选择是否指定区域不配送",trigger:"change"}],type:[{required:!0,message:"请选择计费方式",trigger:"change"}],region:[{required:!0,message:"请选择活动区域",trigger:"change"}],city_id3:[{type:"array",required:!0,message:"请至少选择一个地区",trigger:"change"}],first:[{required:!0,message:"请输入",trigger:"blur"}],renewal:[{required:!0,message:"请输入",trigger:"blur"}],firstPrice:[{required:!0,message:"请输入运费",trigger:"blur"}],renewalPrice:[{required:!0,message:"请输入续费",trigger:"blur"}]},nodeKey:"city_id",props:{children:"child",label:"name",value:"cityId",multiple:!0},dialogVisible:!1,ruleForm:Object.assign({},l),listLoading:!1,cityList:[],columns:{title:"首件",title2:"续件",title3:"包邮件数"},tempId:0,type:0}},mounted:function(){var e=this;setTimeout((function(){var t=JSON.parse(sessionStorage.getItem("cityList"));e.cityList=t}),1e3)},methods:{changType:function(e){this.type=e},onClose:function(e){this.dialogVisible=!1,this.$refs[e].resetFields()},confirmEdit:function(e,t){e.splice(t,1)},popoverHide:function(){},handleClose:function(){this.dialogVisible=!1,this.ruleForm={name:"",type:1,appoint:!1,sort:0,region:[{first:0,firstPrice:0,renewal:0,renewalPrice:0,city_ids:[]}],undelivery:0,free:[],undelives:{},city_id3:[]}},changeRegion:function(e){console.log(e)},changeRadio:function(e){this.columns=Object.assign({},u[e-1])},addRegion:function(e){e.push(Object.assign({},{first:0,firstPrice:0,renewal:0,renewalPrice:0,city_ids:[]}))},addFree:function(e){e.push(Object.assign({},{city_id:[],number:1,price:1,city_ids:[]}))},getInfo:function(e,t){var r=this;this.tempId=e;var i=o["Loading"].service({fullscreen:!0});n["q"]({id:e}).then((function(e){r.dialogVisible=!0;var t=e;r.ruleForm=Object.assign(r.ruleForm,{name:t.name,type:t.type,appoint:t.appoint,sort:t.sort}),r.columns=Object.assign({},u[r.ruleForm.type-1]),r.$nextTick((function(){i.close()})),r.shippingRegion(),t.appoint&&r.shippingFree()})).catch((function(e){r.$message.error(e.message),r.$nextTick((function(){i.close()}))}))},shippingRegion:function(){var e=this;n["m"]({tempId:this.tempId}).then((function(t){t.forEach((function(e,t){e.title=JSON.parse(e.title),e.city_ids=e.title})),e.ruleForm.region=t}))},shippingFree:function(){var e=this;n["l"]({tempId:this.tempId}).then((function(t){t.forEach((function(e,t){e.title=JSON.parse(e.title),e.city_ids=e.title})),e.ruleForm.free=t}))},getCityList:function(){var e=this;n["c"]().then((function(t){sessionStorage.setItem("cityList",JSON.stringify(t));var r=JSON.parse(sessionStorage.getItem("cityList"));e.cityList=r})).catch((function(t){e.$message.error(t.message)}))},change:function(e){return e.map((function(e){var t=[];e.city_ids.map((function(e){e.splice(0,1),t.push(e[0])})),e.city_id=t})),e},changeOne:function(e){var t=[];return e.map((function(e){e.splice(0,1),t.push(e[0])})),t},onsubmit:function(e){var t=this;this.$refs[e].validate((function(e){if(!e)return!1;t.loading=!0;var r={appoint:t.ruleForm.appoint,name:t.ruleForm.name,sort:t.ruleForm.sort,type:t.ruleForm.type};t.ruleForm.region.forEach((function(e,t){e.title=e.city_ids.length>0?JSON.stringify(e.city_ids):JSON.stringify([[0,0]]);for(var r=0;r<e.city_ids.length;r++)e.city_ids[r].shift();e.cityId=e.city_ids.length>0?e.city_ids.join(","):"all"})),r.shippingTemplatesRegionRequestList=t.ruleForm.region,r.shippingTemplatesRegionRequestList.forEach((function(e,t){})),t.ruleForm.appoint&&(t.ruleForm.free.forEach((function(e,t){e.title=e.city_ids.length>0?JSON.stringify(e.city_ids):JSON.stringify([[0,0]]);for(var r=0;r<e.city_ids.length;r++)e.city_ids[r].shift();e.cityId=e.city_ids.length>0?e.city_ids.join(","):"all"})),r.shippingTemplatesFreeRequestList=t.ruleForm.free,r.shippingTemplatesFreeRequestList.forEach((function(e,t){}))),0===t.type?n["n"](r).then((function(e){t.$message.success("操作成功"),t.handleClose(),t.$nextTick((function(){t.dialogVisible=!1})),setTimeout((function(){t.$emit("getList")}),600),t.loading=!1})):n["p"](r,{id:t.tempId}).then((function(e){t.$message.success("操作成功"),setTimeout((function(){t.$emit("getList"),t.handleClose()}),600),t.$nextTick((function(){t.dialogVisible=!1})),t.loading=!1}))}))},clear:function(){this.ruleForm.name="",this.ruleForm.sort=0}}},d=m,f=(r("e16d"),r("2877")),p=Object(f["a"])(d,i,a,!1,null,"44a816e5",null);t["a"]=p.exports}}]);