package com.ylpz.admin.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.ylpz.core.common.page.CommonPage;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.core.common.request.StoreProductAddRequest;
import com.ylpz.core.common.request.StoreProductStockSearchRequest;
import com.ylpz.core.common.request.StoreSeckillSearchRequest;
import com.ylpz.core.common.response.CommonResult;
import com.ylpz.core.common.response.StoreProductInfoResponse;
import com.ylpz.core.common.response.StoreProductResponse;
import com.ylpz.core.common.response.StoreProductTabsHeader;
import com.ylpz.core.common.response.StoreSeckillResponse;
import com.ylpz.core.service.StoreProductService;
import com.ylpz.core.service.StoreSeckillService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("api/admin/store/product/stock")
@Api(tags = "商品 -- 库存管理") // 配合swagger使用
public class StoreProductStockController {

    @Autowired
    private StoreProductService storeProductService;

    @Autowired
    private StoreSeckillService storeSeckillService;

    /**
     * 分页显示商品表
     * 
     * @param request 搜索条件
     * @param pageParamRequest 分页参数
     */
    @PreAuthorize("hasAuthority('admin:product:list')")
    @ApiOperation(value = "分页列表") // 配合swagger使用
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public CommonResult<CommonPage<StoreProductResponse>> getList(@Validated StoreProductStockSearchRequest request,
        @Validated PageParamRequest pageParamRequest) {
        return CommonResult
            .success(CommonPage.restPage(storeProductService.getAdminStockList(request, pageParamRequest)));
    }

    /**
     * 分页显示商品秒杀产品表
     * 
     * @param request 搜索条件
     * @param pageParamRequest 分页参数
     */
    @PreAuthorize("hasAuthority('admin:product:list')")
    @ApiOperation(value = "秒杀分页列表") // 配合swagger使用
    @RequestMapping(value = "/seckillList", method = RequestMethod.GET)
    public CommonResult<CommonPage<StoreSeckillResponse>> getSeckillList(@Validated StoreSeckillSearchRequest request,
                                                                  @Validated PageParamRequest pageParamRequest) {
        CommonPage<StoreSeckillResponse> storeSeckillCommonPage =
            CommonPage.restPage(storeSeckillService.getList(request, pageParamRequest));
        return CommonResult.success(storeSeckillCommonPage);
    }

    /**
     * 商品修改
     * 
     * @param storeProductRequest 商品参数
     */
    @PreAuthorize("hasAuthority('admin:product:update')")
    @ApiOperation(value = "商品修改")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public CommonResult<String> update(@RequestBody @Validated StoreProductAddRequest storeProductRequest) {
        if (storeProductService.update(storeProductRequest)) {
            return CommonResult.success();
        } else {
            return CommonResult.failed();
        }
    }

    /**
     * 商品详情
     * 
     * @param id 商品id
     */
    @PreAuthorize("hasAuthority('admin:product:info')")
    @ApiOperation(value = "商品详情")
    @RequestMapping(value = "/info/{id}", method = RequestMethod.GET)
    public CommonResult<StoreProductInfoResponse> info(@PathVariable Integer id) {
        return CommonResult.success(storeProductService.getInfo(id));
    }

    /**
     * 商品库存tabs表头数据
     */
    @PreAuthorize("hasAuthority('admin:product:tabs:headers')")
    @ApiOperation(value = "商品库存表头数量")
    @RequestMapping(value = "/tabs/headers", method = RequestMethod.GET)
    public CommonResult<List<StoreProductTabsHeader>> getTabsHeader() {
        return CommonResult.success(storeProductService.getStockTabsHeader());
    }
}
