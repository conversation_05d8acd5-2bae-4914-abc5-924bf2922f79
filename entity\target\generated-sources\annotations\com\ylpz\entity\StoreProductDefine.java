package com.ylpz.entity;

public interface StoreProductDefine {
    String productAttrValues = "productAttrValues";
    String addTime = "addTime";
    String otPrice = "otPrice";
    String merUse = "merUse";
    String isBest = "isBest";
    String sales = "sales";
    String price = "price";
    String isBargain = "isBargain";
    String id = "id";
    String supportReturn7days = "supportReturn7days";
    String tag = "tag";
    String keyword = "keyword";
    String stock = "stock";
    String productAttrs = "productAttrs";
    String image = "image";
    String isGood = "isGood";
    String unitName = "unitName";
    String isBenefit = "isBenefit";
    String giveIntegral = "giveIntegral";
    String sort = "sort";
    String sliderImage = "sliderImage";
    String barCode = "barCode";
    String shareTitleType = "shareTitleType";
    String cateId = "cateId";
    String shareTitlePrefix = "shareTitlePrefix";
    String detailImages = "detailImages";
    String merId = "merId";
    String ficti = "ficti";
    String tempId = "tempId";
    String isHot = "isHot";
    String browse = "browse";
    String specType = "specType";
    String activity = "activity";
    String isPostage = "isPostage";
    String isSub = "isSub";
    String limitSpecificUsers = "limitSpecificUsers";
    String content = "content";
    String freightType = "freightType";
    String saleTime = "saleTime";
    String maxPurchaseCount = "maxPurchaseCount";
    String popularity = "popularity";
    String vipPrice = "vipPrice";
    String storeName = "storeName";
    String supportExchange = "supportExchange";
    String cost = "cost";
    String deliveryType = "deliveryType";
    String limitPurchaseCount = "limitPurchaseCount";
    String isNew = "isNew";
    String isShow = "isShow";
    String postage = "postage";
    String codePath = "codePath";
    String soureLink = "soureLink";
    String flatPattern = "flatPattern";
    String isRecycle = "isRecycle";
    String isSeckill = "isSeckill";
    String videoLink = "videoLink";
    String storeInfo = "storeInfo";
    String isDel = "isDel";
}
