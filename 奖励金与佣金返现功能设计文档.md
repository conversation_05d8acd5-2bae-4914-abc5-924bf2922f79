# 奖励金与佣金返现功能设计文档

## 1. 系统概述

本文档详细描述了电商商城系统中的奖励金与佣金返现功能的当前实现和改造方案。系统主要通过会员参数配置表（`system_member_param_config`）来管理奖励金和佣金返现的各项规则，用户佣金记录表（`user_brokerage_record`）来记录用户的奖励金和佣金记录。

## 2. 当前系统功能分析

### 2.1 佣金返现功能

#### 2.1.1 数据结构

佣金返现功能主要使用以下表：

1. **system_member_param_config** - 会员参数配置表
   - 存储佣金返现规则配置（config_type=3）
   - 按会员类型（withdraw_type）设置不同比例的佣金返现
   - 支持SVIP会员、VIP会员、普通会员三种类型的返现比例设置
   - 支持设置是否自动返现（auto_refund）

2. **user_brokerage_record** - 用户佣金记录表
   - 记录用户的佣金和奖励金变动
   - 记录关联的订单或提现ID
   - 跟踪佣金状态（创建、冻结期、完成、失效、提现申请）

#### 2.1.2 佣金返现规则

佣金返现是基于用户等级实现的不同比例返现：
- SVIP会员：默认返现比例10%
- VIP会员：默认返现比例6%
- 普通会员：默认返现比例3%

SVIP会员可以启用自动返现功能（auto_refund=1），订单支付后直接返还佣金给用户自己。

#### 2.1.3 佣金状态流转

1. 订单创建 -> 生成佣金记录（status=1）
2. 进入冻结期 -> 佣金记录状态更新（status=2）
3. 冻结期结束 -> 佣金解冻完成（status=3）
4. 如订单退款 -> 佣金失效（status=4）
5. 申请提现 -> 提现申请中（status=5）

### 2.2 奖励金功能

#### 2.2.1 数据结构

奖励金功能也使用会员参数配置表（`system_member_param_config`）来存储奖励金规则（config_type=4），并使用用户佣金记录表（`user_brokerage_record`）来记录奖励金发放情况。

#### 2.2.2 奖励金类型

当前系统支持的奖励金类型包括：
1. 推广普通会员升级为VIP：一次性奖励200元
2. 推广会员充值：按充值金额的10%奖励
3. 推广新用户首单购买：按订单金额的10%奖励
4. 销售排行榜奖励：
   - 周排行榜奖励
   - 月排行榜奖励
   - 季排行榜奖励

#### 2.2.3 奖励金发放规则

- 升级奖励：推广的普通会员升级为VIP后，给推广人一次性奖励固定金额
- 充值奖励：推广的会员充值时，按充值金额的一定比例给推广人奖励
- 首单奖励：推广的新用户首次下单，按订单金额的一定比例给推广人奖励
- 排行榜奖励：根据销售业绩排名，给予前几名的用户固定金额奖励

## 3. 改造需求

参考图片所示界面，需要对当前系统的奖励金和佣金返现功能进行以下改造：

### 3.1 奖励金功能改造

1. 开发奖励金专门管理界面，显示奖励金合计和明细记录
2. 增强奖励记录查询功能，支持按时间范围、奖励类型、人员等条件筛选
3. 优化奖励金展示，区分不同来源的奖励金
4. 细化奖励信息展示，包括奖励详情、关联人员、金额等

### 3.2 佣金返现功能改造

1. 确保佣金返现规则在会员参数配置中设置（不迁移到系统设置）
2. 增强佣金返现的业务处理逻辑，确保准确计算和及时发放
3. 改进佣金返现比例的配置界面，使其更加直观易用
4. 开发佣金返现专门管理界面，支持佣金返现记录查询和统计
5. 实现佣金返现记录的多维度筛选功能，如按会员类型、时间范围等

## 4. 技术方案设计

### 4.1 数据库表设计

#### 4.1.1 系统现有表结构

会员参数配置表（system_member_param_config）已包含奖励金和佣金返现相关配置，用户佣金记录表（user_brokerage_record）已可记录奖励金和佣金记录。无需新增表结构。

#### 4.1.2 查询优化

为提高奖励金和佣金返现记录查询性能，需要优化user_brokerage_record表的查询：
- 确保记录类型（type）、状态（status）、用户ID（uid）等字段已有索引
- 添加奖励类型和佣金返现类型相关的标记，便于区分不同记录类型
- 使用link_type字段区分不同业务类型的记录，如"commission"表示佣金返现记录

### 4.2 接口设计

#### 4.2.1 奖励金查询接口

```
GET /api/admin/member/bonus/list  // 奖励金记录列表查询
```

请求参数：
- 开始时间（startTime）：查询开始时间
- 结束时间（endTime）：查询结束时间
- 奖励类型（bonusType）：奖励金类型
- 人员手机号（mobile）：用户手机号
- 订单号（orderNo）：关联订单号
- 页码（page）：当前页码
- 每页条数（limit）：每页显示条数

响应数据：
- 奖励金合计（totalAmount）：符合条件的奖励金总额
- 奖励记录列表（records）：包含奖励人员、奖励金额、类型、时间、奖励信息等

#### 4.2.2 佣金返现查询接口

```
GET /api/admin/finance/commission/list  // 佣金返现记录列表查询
GET /api/admin/finance/commission/total  // 佣金返现合计金额查询
GET /api/admin/finance/commission/statistics/{uid}  // 用户佣金返现统计
```

请求参数：
- 用户ID（uid）：用户ID
- 用户手机号（mobile）：用户手机号
- 佣金返现类型（commissionType）：SVIP会员返现、VIP会员返现、普通会员返现
- 关联订单号（linkId）：关联订单号
- 状态（status）：1-已创建，2-冻结期，3-已完成，4-已失效
- 开始时间（startTime）：查询开始时间
- 结束时间（endTime）：查询结束时间
- 页码（page）：当前页码
- 每页条数（limit）：每页显示条数

响应数据：
- 佣金返现合计（total）：符合条件的佣金返现总额
- 佣金返现记录列表（records）：包含用户信息、返现金额、类型、时间、状态等
- 用户佣金返现统计：包含累计返现金额、记录数量、最近返现时间等

#### 4.2.3 奖励金配置接口

继续使用现有的会员参数配置接口：
```
GET /api/admin/member/param/getForm  // 获取会员参数配置
POST /api/admin/member/param/saveForm  // 保存会员参数配置
```

### 4.3 业务逻辑设计

#### 4.3.1 奖励金发放流程

1. 触发奖励金发放事件（如：用户升级、新用户首单等）
2. 查询对应的奖励金规则配置
3. 计算应发放的奖励金额
4. 创建奖励金记录
5. 更新用户佣金余额
6. 通知用户奖励金发放成功

#### 4.3.2 奖励金记录查询逻辑

1. 接收查询条件（时间范围、类型等）
2. 构建查询条件，过滤奖励金记录
3. 计算符合条件的奖励金总额
4. 分页返回奖励金记录列表

#### 4.3.3 佣金返现记录查询逻辑

1. 接收查询条件（用户ID、会员类型、时间范围等）
2. 构建查询条件，筛选佣金返现记录（link_type="commission"）
3. 根据佣金返现类型筛选对应的记录
4. 计算符合条件的佣金返现总额
5. 分页返回佣金返现记录列表

### 4.4 前端界面设计

参考图片中的界面设计，实现奖励金和佣金返现管理界面：
1. 顶部显示奖励金/佣金返现合计金额
2. 提供时间范围、查询条件等筛选功能
3. 列表展示记录详情，包含人员信息、金额、类型、时间、状态等字段
4. 针对不同类型的记录，显示不同的详细信息

## 5. 实现要点

### 5.1 奖励金记录查询

```java
/**
 * 查询奖励金记录列表
 * @param request 查询条件
 * @param pageParamRequest 分页参数
 * @return 奖励金记录分页数据
 */
public PageInfo<UserBrokerageRecord> getBonusList(BonusRecordRequest request, PageParamRequest pageParamRequest) {
    // 构建查询条件
    LambdaQueryWrapper<UserBrokerageRecord> lqw = new LambdaQueryWrapper<>();
    
    // 筛选奖励金相关记录（根据标题或者来源类型）
    lqw.and(wrapper -> {
        // 根据奖励类型筛选
        if (!StringUtils.isEmpty(request.getBonusType())) {
            // 不同的奖励类型对应不同的标题或者来源
            if ("upgrade".equals(request.getBonusType())) {
                wrapper.like(UserBrokerageRecord::getTitle, "升级为VIP");
            } else if ("firstOrder".equals(request.getBonusType())) {
                wrapper.like(UserBrokerageRecord::getTitle, "首单购买");
            } else if ("recharge".equals(request.getBonusType())) {
                wrapper.like(UserBrokerageRecord::getTitle, "会员充值");
            } else if ("rank".equals(request.getBonusType())) {
                wrapper.like(UserBrokerageRecord::getTitle, "排行榜");
            }
        }
    });
    
    // 时间范围筛选
    if (request.getStartTime() != null && request.getEndTime() != null) {
        lqw.between(UserBrokerageRecord::getCreateTime, request.getStartTime(), request.getEndTime());
    }
    
    // 用户筛选
    if (request.getUid() != null) {
        lqw.eq(UserBrokerageRecord::getUid, request.getUid());
    }
    
    // 关联订单筛选
    if (!StringUtils.isEmpty(request.getLinkId())) {
        lqw.eq(UserBrokerageRecord::getLinkId, request.getLinkId());
    }
    
    // 只查询增加类型的记录
    lqw.eq(UserBrokerageRecord::getType, BrokerageRecordConstants.BROKERAGE_RECORD_TYPE_ADD);
    
    // 按创建时间倒序排序
    lqw.orderByDesc(UserBrokerageRecord::getCreateTime);
    
    // 执行分页查询
    Page<UserBrokerageRecord> page = new Page<>(pageParamRequest.getPage(), pageParamRequest.getLimit());
    Page<UserBrokerageRecord> pageResult = dao.selectPage(page, lqw);
    
    // 查询用户信息并填充
    List<UserBrokerageRecord> records = pageResult.getRecords();
    if (!CollectionUtils.isEmpty(records)) {
        fillUserInfo(records);
    }
    
    // 计算符合条件的奖励金总额
    BigDecimal totalAmount = records.stream()
        .map(UserBrokerageRecord::getPrice)
        .reduce(BigDecimal.ZERO, BigDecimal::add);
    
    return PageUtils.copyPageInfo(pageResult, records, totalAmount);
}
```

### 5.2 佣金返现记录查询

```java
/**
 * 获取佣金返现记录列表
 * @param request 查询条件
 * @param pageParamRequest 分页参数
 * @return 分页数据
 */
public PageInfo<UserBrokerageRecord> getCommissionList(CommissionRecordRequest request, PageParamRequest pageParamRequest) {
    // 构建查询条件
    LambdaQueryWrapper<UserBrokerageRecord> lqw = new LambdaQueryWrapper<>();

    // 只查询佣金返现类型记录
    lqw.eq(UserBrokerageRecord::getLinkType, BrokerageRecordConstants.BROKERAGE_RECORD_LINK_TYPE_COMMISSION);

    // 用户筛选
    if (request.getUid() != null) {
        lqw.eq(UserBrokerageRecord::getUid, request.getUid());
    }

    // 通过手机号查询用户ID
    if (!StringUtils.isEmpty(request.getMobile())) {
        LambdaQueryWrapper<User> userQuery = new LambdaQueryWrapper<>();
        userQuery.eq(User::getPhone, request.getMobile());
        List<User> users = userDao.selectList(userQuery);
        if (!CollectionUtils.isEmpty(users)) {
            List<Integer> userIds = users.stream().map(User::getId).collect(Collectors.toList());
            lqw.in(UserBrokerageRecord::getUid, userIds);
        } else {
            // 如果没有找到用户，返回空结果
            return new PageInfo<>();
        }
    }

    // 按佣金返现类型筛选
    if (!StringUtils.isEmpty(request.getCommissionType())) {
        if ("svip".equals(request.getCommissionType())) {
            lqw.eq(UserBrokerageRecord::getTitle, BrokerageRecordConstants.BROKERAGE_RECORD_TITLE_COMMISSION_SVIP);
        } else if ("vip".equals(request.getCommissionType())) {
            lqw.eq(UserBrokerageRecord::getTitle, BrokerageRecordConstants.BROKERAGE_RECORD_TITLE_COMMISSION_VIP);
        } else if ("normal".equals(request.getCommissionType())) {
            lqw.eq(UserBrokerageRecord::getTitle, BrokerageRecordConstants.BROKERAGE_RECORD_TITLE_COMMISSION_NORMAL);
        }
    }

    // 关联订单筛选
    if (!StringUtils.isEmpty(request.getLinkId())) {
        lqw.eq(UserBrokerageRecord::getLinkId, request.getLinkId());
    }

    // 状态筛选
    if (request.getStatus() != null) {
        lqw.eq(UserBrokerageRecord::getStatus, request.getStatus());
    }

    // 时间范围筛选
    if (request.getStartTime() != null && request.getEndTime() != null) {
        lqw.between(UserBrokerageRecord::getCreateTime, request.getStartTime(), request.getEndTime());
    }

    // 只查询增加类型的记录
    lqw.eq(UserBrokerageRecord::getType, BrokerageRecordConstants.BROKERAGE_RECORD_TYPE_ADD);

    // 按创建时间倒序排序
    lqw.orderByDesc(UserBrokerageRecord::getCreateTime);

    // 执行分页查询
    Page<UserBrokerageRecord> page = new Page<>(pageParamRequest.getPage(), pageParamRequest.getLimit());
    Page<UserBrokerageRecord> pageResult = userBrokerageRecordDao.selectPage(page, lqw);

    // 查询用户信息并填充
    List<UserBrokerageRecord> records = pageResult.getRecords();
    if (!CollectionUtils.isEmpty(records)) {
        fillUserInfo(records);
    }

    // 创建分页信息
    PageInfo<UserBrokerageRecord> pageInfo = new PageInfo<>(records);
    pageInfo.setPages((int) pageResult.getPages());
    pageInfo.setPageNum((int) pageResult.getCurrent());
    pageInfo.setPageSize((int) pageResult.getSize());
    pageInfo.setTotal(pageResult.getTotal());
    
    return pageInfo;
}
```

### 5.3 奖励金发放

```java
/**
 * 发放排行榜奖励金
 * @param rankType 排行榜类型（周排行、月排行、季度排行）
 * @param rankDate 排行日期
 */
public void distributeRankBonus(String rankType, Date rankDate) {
    // 1. 查询对应排行榜奖励配置
    String sourceType = "";
    if ("week".equals(rankType)) {
        sourceType = MemberParamConstants.BONUS_SOURCE_WEEKLY_RANK;
    } else if ("month".equals(rankType)) {
        sourceType = MemberParamConstants.BONUS_SOURCE_MONTHLY_RANK;
    } else if ("quarter".equals(rankType)) {
        sourceType = MemberParamConstants.BONUS_SOURCE_QUARTERLY_RANK;
    }
    
    // 2. 获取排行榜配置
    List<MemberParamConfig> configs = memberParamConfigService.getListBySourceType(sourceType);
    if (CollectionUtils.isEmpty(configs)) {
        return;
    }
    
    // 3. 获取销售排行数据
    List<UserSalesRank> salesRanks = getSalesRankList(rankType, rankDate);
    if (CollectionUtils.isEmpty(salesRanks)) {
        return;
    }
    
    // 4. 发放奖励
    for (MemberParamConfig config : configs) {
        // 查找备注中的排名信息，如"周排行榜第一名奖励500元"
        String remark = config.getRemark();
        Integer rank = extractRankFromRemark(remark);
        if (rank == null || rank > salesRanks.size()) {
            continue;
        }
        
        // 获取对应排名的用户
        UserSalesRank userRank = salesRanks.get(rank - 1);
        
        // 创建并保存奖励记录
        UserBrokerageRecord record = new UserBrokerageRecord();
        record.setUid(userRank.getUid());
        record.setLinkId(rankType + "_" + DateUtil.format(rankDate, "yyyyMMdd"));
        record.setLinkType("rank");
        record.setType(BrokerageRecordConstants.BROKERAGE_RECORD_TYPE_ADD);
        record.setTitle(sourceType);
        record.setPrice(new BigDecimal(config.getNumber()));
        record.setBalance(getUserBrokerage(userRank.getUid()).add(record.getPrice()));
        record.setMark("获得" + sourceType + "第" + rank + "名，奖励" + config.getNumber() + config.getUnit());
        record.setStatus(BrokerageRecordConstants.BROKERAGE_RECORD_STATUS_COMPLETE);
        record.setCreateTime(new Date());
        record.setUpdateTime(new Date());
        
        // 保存记录并更新用户佣金余额
        userBrokerageRecordService.save(record);
        userService.updateBrokerage(userRank.getUid(), record.getPrice());
    }
}
```

## 6. 测试方案

### 6.1 单元测试

1. 奖励金和佣金返现规则配置测试
   - 测试不同类型奖励金和佣金返现规则的保存和查询
   - 测试规则参数验证逻辑

2. 奖励金和佣金返现计算测试
   - 测试不同类型奖励金和佣金返现的计算逻辑
   - 测试边界条件下的计算

3. 记录查询测试
   - 测试奖励金和佣金返现各种条件组合下的查询结果
   - 测试统计金额计算的准确性

### 6.2 集成测试

1. 发放流程测试
   - 测试各类奖励金和佣金返现发放流程
   - 测试排行榜奖励定时发放功能

2. 界面测试
   - 测试奖励金和佣金返现查询界面的各项功能
   - 测试配置界面的保存和加载

## 7. 部署和上线计划

1. 开发阶段：完成奖励金和佣金返现管理功能和查询接口开发
2. 测试阶段：进行单元测试和集成测试，确保功能正常
3. 预发布：在测试环境部署并进行最终验证
4. 正式上线：分批次上线功能，先上线查询功能，再上线配置功能

## 8. 风险评估和应对措施

1. 数据迁移风险
   - 风险：现有奖励金和佣金数据迁移可能导致数据丢失或不一致
   - 应对：提前做好数据备份，制定详细的数据迁移方案和回滚计划

2. 性能风险
   - 风险：记录查询可能存在性能问题
   - 应对：优化查询SQL，添加必要索引，实现结果缓存

3. 业务逻辑风险
   - 风险：复杂的规则可能导致计算错误
   - 应对：增加日志记录，完善异常处理，便于问题排查

## 9. 结论

通过本次改造，将完善电商商城系统的奖励金与佣金返现功能，实现更加灵活的奖励规则配置和更便捷的奖励金管理，提升用户体验和系统运营效率。改造方案充分利用现有系统架构和数据结构，降低开发风险，并为后续功能扩展预留了空间。

新增的佣金返现查询功能与奖励金查询功能相互补充，使系统能够全面支持财务人员对各类奖励和返现记录的管理和查询需求，提高了系统的完整性和易用性。 