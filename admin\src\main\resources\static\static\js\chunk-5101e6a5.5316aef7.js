(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5101e6a5"],{"0156":function(t,e,n){var i=n("6d8b");function r(t,e,n){n=n||{};var r=t.coordinateSystem,a=e.axis,o={},s=a.getAxesOnZeroOf()[0],l=a.position,u=s?"onZero":l,h=a.dim,c=r.getRect(),d=[c.x,c.x+c.width,c.y,c.y+c.height],f={left:0,right:1,top:0,bottom:1,onZero:2},p=e.get("offset")||0,g="x"===h?[d[2]-p,d[3]+p]:[d[0]-p,d[1]+p];if(s){var v=s.toGlobalCoord(s.dataToCoord(0));g[f.onZero]=Math.max(Math.min(v,g[1]),g[0])}o.position=["y"===h?g[f[u]]:d[0],"x"===h?g[f[u]]:d[3]],o.rotation=Math.PI/2*("x"===h?0:1);var m={top:-1,bottom:1,left:-1,right:1};o.labelDirection=o.tickDirection=o.nameDirection=m[l],o.labelOffset=s?g[f[l]]-g[f.onZero]:0,e.get("axisTick.inside")&&(o.tickDirection=-o.tickDirection),i.retrieve(n.labelInside,e.get("axisLabel.inside"))&&(o.labelDirection=-o.labelDirection);var y=e.get("axisLabel.rotate");return o.labelRotate="top"===u?-y:y,o.z2=1,o}e.layout=r},"01ed":function(t,e,n){var i=n("3eba"),r=n("6d8b"),a=n("2306");n("5aa9"),n("af24"),i.extendComponentView({type:"grid",render:function(t,e){this.group.removeAll(),t.get("show")&&this.group.add(new a.Rect({shape:t.coordinateSystem.getRect(),style:r.defaults({fill:t.get("backgroundColor")},t.getItemStyle()),silent:!0,z2:-1}))}}),i.registerPreprocessor((function(t){t.xAxis&&t.yAxis&&!t.grid&&(t.grid={})}))},"0352":function(t,e,n){var i=n("6cb7"),r=n("b12f"),a=n("0f99"),o=a.detectSourceFormat,s=n("93d0"),l=s.SERIES_LAYOUT_BY_COLUMN;i.extend({type:"dataset",defaultOption:{seriesLayoutBy:l,sourceHeader:null,dimensions:null,source:null},optionUpdated:function(){o(this)}}),r.extend({type:"dataset"})},"04f6":function(t,e){var n=32,i=7;function r(t){var e=0;while(t>=n)e|=1&t,t>>=1;return t+e}function a(t,e,n,i){var r=e+1;if(r===n)return 1;if(i(t[r++],t[e])<0){while(r<n&&i(t[r],t[r-1])<0)r++;o(t,e,r)}else while(r<n&&i(t[r],t[r-1])>=0)r++;return r-e}function o(t,e,n){n--;while(e<n){var i=t[e];t[e++]=t[n],t[n--]=i}}function s(t,e,n,i,r){for(i===e&&i++;i<n;i++){var a,o=t[i],s=e,l=i;while(s<l)a=s+l>>>1,r(o,t[a])<0?l=a:s=a+1;var u=i-s;switch(u){case 3:t[s+3]=t[s+2];case 2:t[s+2]=t[s+1];case 1:t[s+1]=t[s];break;default:while(u>0)t[s+u]=t[s+u-1],u--}t[s]=o}}function l(t,e,n,i,r,a){var o=0,s=0,l=1;if(a(t,e[n+r])>0){s=i-r;while(l<s&&a(t,e[n+r+l])>0)o=l,l=1+(l<<1),l<=0&&(l=s);l>s&&(l=s),o+=r,l+=r}else{s=r+1;while(l<s&&a(t,e[n+r-l])<=0)o=l,l=1+(l<<1),l<=0&&(l=s);l>s&&(l=s);var u=o;o=r-l,l=r-u}o++;while(o<l){var h=o+(l-o>>>1);a(t,e[n+h])>0?o=h+1:l=h}return l}function u(t,e,n,i,r,a){var o=0,s=0,l=1;if(a(t,e[n+r])<0){s=r+1;while(l<s&&a(t,e[n+r-l])<0)o=l,l=1+(l<<1),l<=0&&(l=s);l>s&&(l=s);var u=o;o=r-l,l=r-u}else{s=i-r;while(l<s&&a(t,e[n+r+l])>=0)o=l,l=1+(l<<1),l<=0&&(l=s);l>s&&(l=s),o+=r,l+=r}o++;while(o<l){var h=o+(l-o>>>1);a(t,e[n+h])<0?l=h:o=h+1}return l}function h(t,e){var n,r,a=i,o=0,s=0;o=t.length;var h=[];function c(t,e){n[s]=t,r[s]=e,s+=1}function d(){while(s>1){var t=s-2;if(t>=1&&r[t-1]<=r[t]+r[t+1]||t>=2&&r[t-2]<=r[t]+r[t-1])r[t-1]<r[t+1]&&t--;else if(r[t]>r[t+1])break;p(t)}}function f(){while(s>1){var t=s-2;t>0&&r[t-1]<r[t+1]&&t--,p(t)}}function p(i){var a=n[i],o=r[i],h=n[i+1],c=r[i+1];r[i]=o+c,i===s-3&&(n[i+1]=n[i+2],r[i+1]=r[i+2]),s--;var d=u(t[h],t,a,o,0,e);a+=d,o-=d,0!==o&&(c=l(t[a+o-1],t,h,c,c-1,e),0!==c&&(o<=c?g(a,o,h,c):v(a,o,h,c)))}function g(n,r,o,s){var c=0;for(c=0;c<r;c++)h[c]=t[n+c];var d=0,f=o,p=n;if(t[p++]=t[f++],0!==--s)if(1!==r){var g,v,m,y=a;while(1){g=0,v=0,m=!1;do{if(e(t[f],h[d])<0){if(t[p++]=t[f++],v++,g=0,0===--s){m=!0;break}}else if(t[p++]=h[d++],g++,v=0,1===--r){m=!0;break}}while((g|v)<y);if(m)break;do{if(g=u(t[f],h,d,r,0,e),0!==g){for(c=0;c<g;c++)t[p+c]=h[d+c];if(p+=g,d+=g,r-=g,r<=1){m=!0;break}}if(t[p++]=t[f++],0===--s){m=!0;break}if(v=l(h[d],t,f,s,0,e),0!==v){for(c=0;c<v;c++)t[p+c]=t[f+c];if(p+=v,f+=v,s-=v,0===s){m=!0;break}}if(t[p++]=h[d++],1===--r){m=!0;break}y--}while(g>=i||v>=i);if(m)break;y<0&&(y=0),y+=2}if(a=y,a<1&&(a=1),1===r){for(c=0;c<s;c++)t[p+c]=t[f+c];t[p+s]=h[d]}else{if(0===r)throw new Error;for(c=0;c<r;c++)t[p+c]=h[d+c]}}else{for(c=0;c<s;c++)t[p+c]=t[f+c];t[p+s]=h[d]}else for(c=0;c<r;c++)t[p+c]=h[d+c]}function v(n,r,o,s){var c=0;for(c=0;c<s;c++)h[c]=t[o+c];var d=n+r-1,f=s-1,p=o+s-1,g=0,v=0;if(t[p--]=t[d--],0!==--r)if(1!==s){var m=a;while(1){var y=0,x=0,_=!1;do{if(e(h[f],t[d])<0){if(t[p--]=t[d--],y++,x=0,0===--r){_=!0;break}}else if(t[p--]=h[f--],x++,y=0,1===--s){_=!0;break}}while((y|x)<m);if(_)break;do{if(y=r-u(h[f],t,n,r,r-1,e),0!==y){for(p-=y,d-=y,r-=y,v=p+1,g=d+1,c=y-1;c>=0;c--)t[v+c]=t[g+c];if(0===r){_=!0;break}}if(t[p--]=h[f--],1===--s){_=!0;break}if(x=s-l(t[d],h,0,s,s-1,e),0!==x){for(p-=x,f-=x,s-=x,v=p+1,g=f+1,c=0;c<x;c++)t[v+c]=h[g+c];if(s<=1){_=!0;break}}if(t[p--]=t[d--],0===--r){_=!0;break}m--}while(y>=i||x>=i);if(_)break;m<0&&(m=0),m+=2}if(a=m,a<1&&(a=1),1===s){for(p-=r,d-=r,v=p+1,g=d+1,c=r-1;c>=0;c--)t[v+c]=t[g+c];t[p]=h[f]}else{if(0===s)throw new Error;for(g=p-(s-1),c=0;c<s;c++)t[g+c]=h[c]}}else{for(p-=r,d-=r,v=p+1,g=d+1,c=r-1;c>=0;c--)t[v+c]=t[g+c];t[p]=h[f]}else for(g=p-(s-1),c=0;c<s;c++)t[g+c]=h[c]}n=[],r=[],this.mergeRuns=d,this.forceMergeRuns=f,this.pushRun=c}function c(t,e,i,o){i||(i=0),o||(o=t.length);var l=o-i;if(!(l<2)){var u=0;if(l<n)return u=a(t,i,o,e),void s(t,i,o,i+u,e);var c=new h(t,e),d=r(l);do{if(u=a(t,i,o,e),u<d){var f=l;f>d&&(f=d),s(t,i,i+f,i+u,e),u=f}c.pushRun(i,u),c.mergeRuns(),l-=u,i+=u}while(0!==l);c.forceMergeRuns()}}t.exports=c},"0655":function(t,e,n){var i=n("8728"),r=1e-8;function a(t,e){return Math.abs(t-e)<r}function o(t,e,n){var r=0,o=t[0];if(!o)return!1;for(var s=1;s<t.length;s++){var l=t[s];r+=i(o[0],o[1],l[0],l[1],e,n),o=l}var u=t[0];return a(o[0],u[0])&&a(o[1],u[1])||(r+=i(o[0],o[1],u[0],u[1],e,n)),0!==r}e.contain=o},"06ad":function(t,e,n){var i=n("4436"),r=n("41ef"),a=n("6d8b"),o=a.isArrayLike,s=Array.prototype.slice;function l(t,e){return t[e]}function u(t,e,n){t[e]=n}function h(t,e,n){return(e-t)*n+t}function c(t,e,n){return n>.5?e:t}function d(t,e,n,i,r){var a=t.length;if(1===r)for(var o=0;o<a;o++)i[o]=h(t[o],e[o],n);else{var s=a&&t[0].length;for(o=0;o<a;o++)for(var l=0;l<s;l++)i[o][l]=h(t[o][l],e[o][l],n)}}function f(t,e,n){var i=t.length,r=e.length;if(i!==r){var a=i>r;if(a)t.length=r;else for(var o=i;o<r;o++)t.push(1===n?e[o]:s.call(e[o]))}var l=t[0]&&t[0].length;for(o=0;o<t.length;o++)if(1===n)isNaN(t[o])&&(t[o]=e[o]);else for(var u=0;u<l;u++)isNaN(t[o][u])&&(t[o][u]=e[o][u])}function p(t,e,n){if(t===e)return!0;var i=t.length;if(i!==e.length)return!1;if(1===n){for(var r=0;r<i;r++)if(t[r]!==e[r])return!1}else{var a=t[0].length;for(r=0;r<i;r++)for(var o=0;o<a;o++)if(t[r][o]!==e[r][o])return!1}return!0}function g(t,e,n,i,r,a,o,s,l){var u=t.length;if(1===l)for(var h=0;h<u;h++)s[h]=v(t[h],e[h],n[h],i[h],r,a,o);else{var c=t[0].length;for(h=0;h<u;h++)for(var d=0;d<c;d++)s[h][d]=v(t[h][d],e[h][d],n[h][d],i[h][d],r,a,o)}}function v(t,e,n,i,r,a,o){var s=.5*(n-t),l=.5*(i-e);return(2*(e-n)+s+l)*o+(-3*(e-n)-2*s-l)*a+s*r+e}function m(t){if(o(t)){var e=t.length;if(o(t[0])){for(var n=[],i=0;i<e;i++)n.push(s.call(t[i]));return n}return s.call(t)}return t}function y(t){return t[0]=Math.floor(t[0]),t[1]=Math.floor(t[1]),t[2]=Math.floor(t[2]),"rgba("+t.join(",")+")"}function x(t){var e=t[t.length-1].value;return o(e&&e[0])?2:1}function _(t,e,n,a,s,l){var u=t._getter,m=t._setter,_="spline"===e,b=a.length;if(b){var w,S=a[0].value,M=o(S),T=!1,A=!1,C=M?x(a):0;a.sort((function(t,e){return t.time-e.time})),w=a[b-1].time;for(var I=[],k=[],D=a[0].value,P=!0,O=0;O<b;O++){I.push(a[O].time/w);var L=a[O].value;if(M&&p(L,D,C)||!M&&L===D||(P=!1),D=L,"string"===typeof L){var E=r.parse(L);E?(L=E,T=!0):A=!0}k.push(L)}if(l||!P){var R=k[b-1];for(O=0;O<b-1;O++)M?f(k[O],R,C):!isNaN(k[O])||isNaN(R)||A||T||(k[O]=R);M&&f(u(t._target,s),R,C);var B,N,z,F,V,H,W=0,G=0;if(T)var U=[0,0,0,0];var q=function(t,e){var n;if(e<0)n=0;else if(e<G){for(B=Math.min(W+1,b-1),n=B;n>=0;n--)if(I[n]<=e)break;n=Math.min(n,b-2)}else{for(n=W;n<b;n++)if(I[n]>e)break;n=Math.min(n-1,b-2)}W=n,G=e;var i=I[n+1]-I[n];if(0!==i)if(N=(e-I[n])/i,_)if(F=k[n],z=k[0===n?n:n-1],V=k[n>b-2?b-1:n+1],H=k[n>b-3?b-1:n+2],M)g(z,F,V,H,N,N*N,N*N*N,u(t,s),C);else{if(T)r=g(z,F,V,H,N,N*N,N*N*N,U,1),r=y(U);else{if(A)return c(F,V,N);r=v(z,F,V,H,N,N*N,N*N*N)}m(t,s,r)}else if(M)d(k[n],k[n+1],N,u(t,s),C);else{var r;if(T)d(k[n],k[n+1],N,U,1),r=y(U);else{if(A)return c(k[n],k[n+1],N);r=h(k[n],k[n+1],N)}m(t,s,r)}},Y=new i({target:t._target,life:w,loop:t._loop,delay:t._delay,onframe:q,ondestroy:n});return e&&"spline"!==e&&(Y.easing=e),Y}}}var b=function(t,e,n,i){this._tracks={},this._target=t,this._loop=e||!1,this._getter=n||l,this._setter=i||u,this._clipCount=0,this._delay=0,this._doneList=[],this._onframeList=[],this._clipList=[]};b.prototype={when:function(t,e){var n=this._tracks;for(var i in e)if(e.hasOwnProperty(i)){if(!n[i]){n[i]=[];var r=this._getter(this._target,i);if(null==r)continue;0!==t&&n[i].push({time:0,value:m(r)})}n[i].push({time:t,value:e[i]})}return this},during:function(t){return this._onframeList.push(t),this},pause:function(){for(var t=0;t<this._clipList.length;t++)this._clipList[t].pause();this._paused=!0},resume:function(){for(var t=0;t<this._clipList.length;t++)this._clipList[t].resume();this._paused=!1},isPaused:function(){return!!this._paused},_doneCallback:function(){this._tracks={},this._clipList.length=0;for(var t=this._doneList,e=t.length,n=0;n<e;n++)t[n].call(this)},start:function(t,e){var n,i=this,r=0,a=function(){r--,r||i._doneCallback()};for(var o in this._tracks)if(this._tracks.hasOwnProperty(o)){var s=_(this,t,a,this._tracks[o],o,e);s&&(this._clipList.push(s),r++,this.animation&&this.animation.addClip(s),n=s)}if(n){var l=n.onframe;n.onframe=function(t,e){l(t,e);for(var n=0;n<i._onframeList.length;n++)i._onframeList[n](t,e)}}return r||this._doneCallback(),this},stop:function(t){for(var e=this._clipList,n=this.animation,i=0;i<e.length;i++){var r=e[i];t&&r.onframe(this._target,1),n&&n.removeClip(r)}e.length=0},delay:function(t){return this._delay=t,this},done:function(t){return t&&this._doneList.push(t),this},getClips:function(){return this._clipList}};var w=b;t.exports=w},"0b44":function(t,e,n){var i=n("607d"),r=function(){this._track=[]};function a(t){var e=t[1][0]-t[0][0],n=t[1][1]-t[0][1];return Math.sqrt(e*e+n*n)}function o(t){return[(t[0][0]+t[1][0])/2,(t[0][1]+t[1][1])/2]}r.prototype={constructor:r,recognize:function(t,e,n){return this._doTrack(t,e,n),this._recognize(t)},clear:function(){return this._track.length=0,this},_doTrack:function(t,e,n){var r=t.touches;if(r){for(var a={points:[],touches:[],target:e,event:t},o=0,s=r.length;o<s;o++){var l=r[o],u=i.clientToLocal(n,l,{});a.points.push([u.zrX,u.zrY]),a.touches.push(l)}this._track.push(a)}},_recognize:function(t){for(var e in s)if(s.hasOwnProperty(e)){var n=s[e](this._track,t);if(n)return n}}};var s={pinch:function(t,e){var n=t.length;if(n){var i=(t[n-1]||{}).points,r=(t[n-2]||{}).points||i;if(r&&r.length>1&&i&&i.length>1){var s=a(i)/a(r);!isFinite(s)&&(s=1),e.pinchScale=s;var l=o(i);return e.pinchX=l[0],e.pinchY=l[1],{type:"pinch",target:t[0].target,event:e}}}}},l=r;t.exports=l},"0cde":function(t,e,n){var i=n("1687"),r=n("401b"),a=i.identity,o=5e-5;function s(t){return t>o||t<-o}var l=function(t){t=t||{},t.position||(this.position=[0,0]),null==t.rotation&&(this.rotation=0),t.scale||(this.scale=[1,1]),this.origin=this.origin||null},u=l.prototype;u.transform=null,u.needLocalTransform=function(){return s(this.rotation)||s(this.position[0])||s(this.position[1])||s(this.scale[0]-1)||s(this.scale[1]-1)};var h=[];u.updateTransform=function(){var t=this.parent,e=t&&t.transform,n=this.needLocalTransform(),r=this.transform;if(n||e){r=r||i.create(),n?this.getLocalTransform(r):a(r),e&&(n?i.mul(r,t.transform,r):i.copy(r,t.transform)),this.transform=r;var o=this.globalScaleRatio;if(null!=o&&1!==o){this.getGlobalScale(h);var s=h[0]<0?-1:1,l=h[1]<0?-1:1,u=((h[0]-s)*o+s)/h[0]||0,c=((h[1]-l)*o+l)/h[1]||0;r[0]*=u,r[1]*=u,r[2]*=c,r[3]*=c}this.invTransform=this.invTransform||i.create(),i.invert(this.invTransform,r)}else r&&a(r)},u.getLocalTransform=function(t){return l.getLocalTransform(this,t)},u.setTransform=function(t){var e=this.transform,n=t.dpr||1;e?t.setTransform(n*e[0],n*e[1],n*e[2],n*e[3],n*e[4],n*e[5]):t.setTransform(n,0,0,n,0,0)},u.restoreTransform=function(t){var e=t.dpr||1;t.setTransform(e,0,0,e,0,0)};var c=[],d=i.create();u.setLocalTransform=function(t){if(t){var e=t[0]*t[0]+t[1]*t[1],n=t[2]*t[2]+t[3]*t[3],i=this.position,r=this.scale;s(e-1)&&(e=Math.sqrt(e)),s(n-1)&&(n=Math.sqrt(n)),t[0]<0&&(e=-e),t[3]<0&&(n=-n),i[0]=t[4],i[1]=t[5],r[0]=e,r[1]=n,this.rotation=Math.atan2(-t[1]/n,t[0]/e)}},u.decomposeTransform=function(){if(this.transform){var t=this.parent,e=this.transform;t&&t.transform&&(i.mul(c,t.invTransform,e),e=c);var n=this.origin;n&&(n[0]||n[1])&&(d[4]=n[0],d[5]=n[1],i.mul(c,e,d),c[4]-=n[0],c[5]-=n[1],e=c),this.setLocalTransform(e)}},u.getGlobalScale=function(t){var e=this.transform;return t=t||[],e?(t[0]=Math.sqrt(e[0]*e[0]+e[1]*e[1]),t[1]=Math.sqrt(e[2]*e[2]+e[3]*e[3]),e[0]<0&&(t[0]=-t[0]),e[3]<0&&(t[1]=-t[1]),t):(t[0]=1,t[1]=1,t)},u.transformCoordToLocal=function(t,e){var n=[t,e],i=this.invTransform;return i&&r.applyTransform(n,n,i),n},u.transformCoordToGlobal=function(t,e){var n=[t,e],i=this.transform;return i&&r.applyTransform(n,n,i),n},l.getLocalTransform=function(t,e){e=e||[],a(e);var n=t.origin,r=t.scale||[1,1],o=t.rotation||0,s=t.position||[0,0];return n&&(e[4]-=n[0],e[5]-=n[1]),i.scale(e,e,r),o&&i.rotate(e,e,o),n&&(e[4]+=n[0],e[5]+=n[1]),e[4]+=s[0],e[5]+=s[1],e};var f=l;t.exports=f},"0da8":function(t,e,n){var i=n("19eb"),r=n("9850"),a=n("6d8b"),o=n("5e76");function s(t){i.call(this,t)}s.prototype={constructor:s,type:"image",brush:function(t,e){var n=this.style,i=n.image;n.bind(t,this,e);var r=this._image=o.createOrUpdateImage(i,this._image,this,this.onload);if(r&&o.isImageReady(r)){var a=n.x||0,s=n.y||0,l=n.width,u=n.height,h=r.width/r.height;if(null==l&&null!=u?l=u*h:null==u&&null!=l?u=l/h:null==l&&null==u&&(l=r.width,u=r.height),this.setTransform(t),n.sWidth&&n.sHeight){var c=n.sx||0,d=n.sy||0;t.drawImage(r,c,d,n.sWidth,n.sHeight,a,s,l,u)}else if(n.sx&&n.sy){c=n.sx,d=n.sy;var f=l-c,p=u-d;t.drawImage(r,c,d,f,p,a,s,l,u)}else t.drawImage(r,a,s,l,u);null!=n.text&&(this.restoreTransform(t),this.drawRectText(t,this.getBoundingRect()))}},getBoundingRect:function(){var t=this.style;return this._rect||(this._rect=new r(t.x||0,t.y||0,t.width||0,t.height||0)),this._rect}},a.inherits(s,i);var l=s;t.exports=l},"0f99":function(t,e,n){var i=n("4e08"),r=(i.__DEV__,n("e0d3")),a=r.makeInner,o=r.getDataItemValue,s=n("8b7f"),l=s.getCoordSysDefineBySeries,u=n("6d8b"),h=u.createHashMap,c=u.each,d=u.map,f=u.isArray,p=u.isString,g=u.isObject,v=u.isTypedArray,m=u.isArrayLike,y=u.extend,x=(u.assert,n("ec6f")),_=n("93d0"),b=_.SOURCE_FORMAT_ORIGINAL,w=_.SOURCE_FORMAT_ARRAY_ROWS,S=_.SOURCE_FORMAT_OBJECT_ROWS,M=_.SOURCE_FORMAT_KEYED_COLUMNS,T=_.SOURCE_FORMAT_UNKNOWN,A=_.SOURCE_FORMAT_TYPED_ARRAY,C=_.SERIES_LAYOUT_BY_ROW,I=a();function k(t){var e=t.option.source,n=T;if(v(e))n=A;else if(f(e)){0===e.length&&(n=w);for(var i=0,r=e.length;i<r;i++){var a=e[i];if(null!=a){if(f(a)){n=w;break}if(g(a)){n=S;break}}}}else if(g(e)){for(var o in e)if(e.hasOwnProperty(o)&&m(e[o])){n=M;break}}else if(null!=e)throw new Error("Invalid data");I(t).sourceFormat=n}function D(t){return I(t).source}function P(t){I(t).datasetMap=h()}function O(t){var e=t.option,n=e.data,i=v(n)?A:b,r=!1,a=e.seriesLayoutBy,o=e.sourceHeader,s=e.dimensions,l=z(t);if(l){var u=l.option;n=u.source,i=I(l).sourceFormat,r=!0,a=a||u.seriesLayoutBy,null==o&&(o=u.sourceHeader),s=s||u.dimensions}var h=L(n,i,a,o,s),c=e.encode;!c&&l&&(c=N(t,l,n,i,a,h)),I(t).source=new x({data:n,fromDataset:r,seriesLayoutBy:a,sourceFormat:i,dimensionsDefine:h.dimensionsDefine,startIndex:h.startIndex,dimensionsDetectCount:h.dimensionsDetectCount,encodeDefine:c})}function L(t,e,n,i,r){if(!t)return{dimensionsDefine:E(r)};var a,s,l,u;if(e===w)"auto"===i||null==i?R((function(t){null!=t&&"-"!==t&&(p(t)?null==s&&(s=1):s=0)}),n,t,10):s=i?1:0,r||1!==s||(r=[],R((function(t,e){r[e]=null!=t?t:""}),n,t)),a=r?r.length:n===C?t.length:t[0]?t[0].length:null;else if(e===S)r||(r=B(t),l=!0);else if(e===M)r||(r=[],l=!0,c(t,(function(t,e){r.push(e)})));else if(e===b){var h=o(t[0]);a=f(h)&&h.length||1}return l&&c(r,(function(t,e){"name"===(g(t)?t.name:t)&&(u=e)})),{startIndex:s,dimensionsDefine:E(r),dimensionsDetectCount:a,potentialNameDimIndex:u}}function E(t){if(t){var e=h();return d(t,(function(t,n){if(t=y({},g(t)?t:{name:t}),null==t.name)return t;t.name+="",null==t.displayName&&(t.displayName=t.name);var i=e.get(t.name);return i?t.name+="-"+i.count++:e.set(t.name,{count:1}),t}))}}function R(t,e,n,i){if(null==i&&(i=1/0),e===C)for(var r=0;r<n.length&&r<i;r++)t(n[r]?n[r][0]:null,r);else{var a=n[0]||[];for(r=0;r<a.length&&r<i;r++)t(a[r],r)}}function B(t){var e,n=0;while(n<t.length&&!(e=t[n++]));if(e){var i=[];return c(e,(function(t,e){i.push(e)})),i}}function N(t,e,n,i,r,a){var o=l(t),s={},u=[],d=[],f=t.subType,p=h(["pie","map","funnel"]),g=h(["line","bar","pictorialBar","scatter","effectScatter","candlestick","boxplot"]);if(o&&null!=g.get(f)){var v=t.ecModel,m=I(v).datasetMap,y=e.uid+"_"+r,x=m.get(y)||m.set(y,{categoryWayDim:1,valueWayDim:0});c(o.coordSysDims,(function(t){if(null==o.firstCategoryDimIndex){var e=x.valueWayDim++;s[t]=e,d.push(e)}else if(o.categoryAxisMap.get(t))s[t]=0,u.push(0);else{e=x.categoryWayDim++;s[t]=e,d.push(e)}}))}else if(null!=p.get(f)){for(var _,b=0;b<5&&null==_;b++)V(n,i,r,a.dimensionsDefine,a.startIndex,b)||(_=b);if(null!=_){s.value=_;var w=a.potentialNameDimIndex||Math.max(_-1,0);d.push(w),u.push(w)}}return u.length&&(s.itemName=u),d.length&&(s.seriesName=d),s}function z(t){var e=t.option,n=e.data;if(!n)return t.ecModel.getComponent("dataset",e.datasetIndex||0)}function F(t,e){return V(t.data,t.sourceFormat,t.seriesLayoutBy,t.dimensionsDefine,t.startIndex,e)}function V(t,e,n,i,r,a){var s,l,u=5;if(v(t))return!1;if(i&&(l=i[a],l=g(l)?l.name:l),e===w)if(n===C){for(var h=t[a],c=0;c<(h||[]).length&&c<u;c++)if(null!=(s=x(h[r+c])))return s}else for(c=0;c<t.length&&c<u;c++){var d=t[r+c];if(d&&null!=(s=x(d[a])))return s}else if(e===S){if(!l)return;for(c=0;c<t.length&&c<u;c++){var m=t[c];if(m&&null!=(s=x(m[l])))return s}}else if(e===M){if(!l)return;h=t[l];if(!h||v(h))return!1;for(c=0;c<h.length&&c<u;c++)if(null!=(s=x(h[c])))return s}else if(e===b)for(c=0;c<t.length&&c<u;c++){m=t[c];var y=o(m);if(!f(y))return!1;if(null!=(s=x(y[a])))return s}function x(t){return(null==t||!isFinite(t)||""===t)&&(!(!p(t)||"-"===t)||void 0)}return!1}e.detectSourceFormat=k,e.getSource=D,e.resetSourceDefaulter=P,e.prepareSource=O,e.guessOrdinal=F},"133d":function(t,e,n){var i=n("6d8b"),r=n("e0d3");function a(t,e){var n,a=[],o=t.seriesIndex;if(null==o||!(n=e.getSeriesByIndex(o)))return{point:[]};var s=n.getData(),l=r.queryDataIndex(s,t);if(null==l||l<0||i.isArray(l))return{point:[]};var u=s.getItemGraphicEl(l),h=n.coordinateSystem;if(n.getTooltipPosition)a=n.getTooltipPosition(l)||[];else if(h&&h.dataToPoint)a=h.dataToPoint(s.getValues(i.map(h.dimensions,(function(t){return s.mapDimension(t)})),l,!0))||[];else if(u){var c=u.getBoundingRect().clone();c.applyTransform(u.transform),a=[c.x+c.width/2,c.y+c.height/2]}return{point:a,el:u}}t.exports=a},1418:function(t,e,n){var i=n("6d8b"),r=n("a15a"),a=r.createSymbol,o=n("2306"),s=n("3842"),l=s.parsePercent,u=n("c775"),h=u.getDefaultLabel;function c(t,e,n){o.Group.call(this),this.updateData(t,e,n)}var d=c.prototype,f=c.getSymbolSize=function(t,e){var n=t.getItemVisual(e,"symbolSize");return n instanceof Array?n.slice():[+n,+n]};function p(t){return[t[0]/2,t[1]/2]}function g(t,e){this.parent.drift(t,e)}d._createSymbol=function(t,e,n,i,r){this.removeAll();var o=e.getItemVisual(n,"color"),s=a(t,-1,-1,2,2,o,r);s.attr({z2:100,culling:!0,scale:p(i)}),s.drift=g,this._symbolType=t,this.add(s)},d.stopSymbolAnimation=function(t){this.childAt(0).stopAnimation(t)},d.getSymbolPath=function(){return this.childAt(0)},d.getScale=function(){return this.childAt(0).scale},d.highlight=function(){this.childAt(0).trigger("emphasis")},d.downplay=function(){this.childAt(0).trigger("normal")},d.setZ=function(t,e){var n=this.childAt(0);n.zlevel=t,n.z=e},d.setDraggable=function(t){var e=this.childAt(0);e.draggable=t,e.cursor=t?"move":"pointer"},d.updateData=function(t,e,n){this.silent=!1;var i=t.getItemVisual(e,"symbol")||"circle",r=t.hostModel,a=f(t,e),s=i!==this._symbolType;if(s){var l=t.getItemVisual(e,"symbolKeepAspect");this._createSymbol(i,t,e,a,l)}else{var u=this.childAt(0);u.silent=!1,o.updateProps(u,{scale:p(a)},r,e)}if(this._updateCommon(t,e,a,n),s){u=this.childAt(0);var h=n&&n.fadeIn,c={scale:u.scale.slice()};h&&(c.style={opacity:u.style.opacity}),u.scale=[0,0],h&&(u.style.opacity=0),o.initProps(u,c,r,e)}this._seriesModel=r};var v=["itemStyle"],m=["emphasis","itemStyle"],y=["label"],x=["emphasis","label"];function _(){!o.isInEmphasis(this)&&w.call(this)}function b(){!o.isInEmphasis(this)&&S.call(this)}function w(){if(!this.incremental&&!this.useHoverLayer){var t=this.__symbolOriginalScale,e=t[1]/t[0];this.animateTo({scale:[Math.max(1.1*t[0],t[0]+3),Math.max(1.1*t[1],t[1]+3*e)]},400,"elasticOut")}}function S(){this.incremental||this.useHoverLayer||this.animateTo({scale:this.__symbolOriginalScale},400,"elasticOut")}d._updateCommon=function(t,e,n,r){var a=this.childAt(0),s=t.hostModel,u=t.getItemVisual(e,"color");"image"!==a.type&&a.useStyle({strokeNoScale:!0});var c=r&&r.itemStyle,d=r&&r.hoverItemStyle,f=r&&r.symbolRotate,g=r&&r.symbolOffset,M=r&&r.labelModel,T=r&&r.hoverLabelModel,A=r&&r.hoverAnimation,C=r&&r.cursorStyle;if(!r||t.hasItemOption){var I=r&&r.itemModel?r.itemModel:t.getItemModel(e);c=I.getModel(v).getItemStyle(["color"]),d=I.getModel(m).getItemStyle(),f=I.getShallow("symbolRotate"),g=I.getShallow("symbolOffset"),M=I.getModel(y),T=I.getModel(x),A=I.getShallow("hoverAnimation"),C=I.getShallow("cursor")}else d=i.extend({},d);var k=a.style;a.attr("rotation",(f||0)*Math.PI/180||0),g&&a.attr("position",[l(g[0],n[0]),l(g[1],n[1])]),C&&a.attr("cursor",C),a.setColor(u,r&&r.symbolInnerColor),a.setStyle(c);var D=t.getItemVisual(e,"opacity");null!=D&&(k.opacity=D);var P=t.getItemVisual(e,"liftZ"),O=a.__z2Origin;null!=P?null==O&&(a.__z2Origin=a.z2,a.z2+=P):null!=O&&(a.z2=O,a.__z2Origin=null);var L=r&&r.useNameLabel;function E(e,n){return L?t.getName(e):h(t,e)}o.setLabelStyle(k,d,M,T,{labelFetcher:s,labelDataIndex:e,defaultText:E,isRectText:!0,autoColor:u}),a.off("mouseover").off("mouseout").off("emphasis").off("normal"),a.hoverStyle=d,o.setHoverStyle(a),a.__symbolOriginalScale=p(n),A&&s.isAnimationEnabled()&&a.on("mouseover",_).on("mouseout",b).on("emphasis",w).on("normal",S)},d.fadeOut=function(t,e){var n=this.childAt(0);this.silent=n.silent=!0,(!e||!e.keepLabel)&&(n.style.text=null),o.updateProps(n,{style:{opacity:0},scale:[0,0]},this._seriesModel,this.dataIndex,t)},i.inherits(c,o.Group);var M=c;t.exports=M},"14d3":function(t,e,n){var i=n("6d8b"),r=n("2306"),a=n("fab22"),o=n("6679"),s=["axisLine","axisTickLabel","axisName"],l=["splitLine","splitArea"],u=o.extend({type:"radiusAxis",axisPointerClass:"PolarAxisPointer",render:function(t,e){if(this.group.removeAll(),t.get("show")){var n=t.axis,r=n.polar,o=r.getAngleAxis(),u=n.getTicksCoords(),c=o.getExtent()[0],d=n.getExtent(),f=h(r,t,c),p=new a(t,f);i.each(s,p.add,p),this.group.add(p.getGroup()),i.each(l,(function(e){t.get(e+".show")&&!n.scale.isBlank()&&this["_"+e](t,r,c,d,u)}),this)}},_splitLine:function(t,e,n,a,o){var s=t.getModel("splitLine"),l=s.getModel("lineStyle"),u=l.get("color"),h=0;u=u instanceof Array?u:[u];for(var c=[],d=0;d<o.length;d++){var f=h++%u.length;c[f]=c[f]||[],c[f].push(new r.Circle({shape:{cx:e.cx,cy:e.cy,r:o[d].coord},silent:!0}))}for(d=0;d<c.length;d++)this.group.add(r.mergePath(c[d],{style:i.defaults({stroke:u[d%u.length],fill:null},l.getLineStyle()),silent:!0}))},_splitArea:function(t,e,n,a,o){if(o.length){var s=t.getModel("splitArea"),l=s.getModel("areaStyle"),u=l.get("color"),h=0;u=u instanceof Array?u:[u];for(var c=[],d=o[0].coord,f=1;f<o.length;f++){var p=h++%u.length;c[p]=c[p]||[],c[p].push(new r.Sector({shape:{cx:e.cx,cy:e.cy,r0:d,r:o[f].coord,startAngle:0,endAngle:2*Math.PI},silent:!0})),d=o[f].coord}for(f=0;f<c.length;f++)this.group.add(r.mergePath(c[f],{style:i.defaults({fill:u[f%u.length]},l.getAreaStyle()),silent:!0}))}}});function h(t,e,n){return{position:[t.cx,t.cy],rotation:n/180*Math.PI,labelDirection:-1,tickDirection:-1,nameDirection:1,labelRotate:e.getModel("axisLabel").get("rotate"),z2:1}}t.exports=u},1548:function(t,e,n){var i=n("6d8b"),r=n("3301"),a=n("697e"),o=n("2023"),s=n("4319"),l=n("f934");l.getLayoutRect;e.getLayoutRect=l.getLayoutRect;var u=n("ee1a"),h=u.enableDataStack,c=u.isDimensionStacked,d=u.getStackedDimension,f=n("862d");e.completeDimensions=f;var p=n("b1d4");e.createDimensions=p;var g=n("a15a");function v(t){return r(t.getSource(),t)}e.createSymbol=g.createSymbol;var m={isDimensionStacked:c,enableDataStack:h,getStackedDimension:d};function y(t,e){var n=e;s.isInstance(e)||(n=new s(e),i.mixin(n,o));var r=a.createScaleByModel(n);return r.setExtent(t[0],t[1]),a.niceScaleExtent(r,n),r}function x(t){i.mixin(t,o)}e.createList=v,e.dataStack=m,e.createScale=y,e.mixinAxisModelCommonMethods=x},1687:function(t,e){var n="undefined"===typeof Float32Array?Array:Float32Array;function i(){var t=new n(6);return r(t),t}function r(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t}function a(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t}function o(t,e,n){var i=e[0]*n[0]+e[2]*n[1],r=e[1]*n[0]+e[3]*n[1],a=e[0]*n[2]+e[2]*n[3],o=e[1]*n[2]+e[3]*n[3],s=e[0]*n[4]+e[2]*n[5]+e[4],l=e[1]*n[4]+e[3]*n[5]+e[5];return t[0]=i,t[1]=r,t[2]=a,t[3]=o,t[4]=s,t[5]=l,t}function s(t,e,n){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4]+n[0],t[5]=e[5]+n[1],t}function l(t,e,n){var i=e[0],r=e[2],a=e[4],o=e[1],s=e[3],l=e[5],u=Math.sin(n),h=Math.cos(n);return t[0]=i*h+o*u,t[1]=-i*u+o*h,t[2]=r*h+s*u,t[3]=-r*u+h*s,t[4]=h*a+u*l,t[5]=h*l-u*a,t}function u(t,e,n){var i=n[0],r=n[1];return t[0]=e[0]*i,t[1]=e[1]*r,t[2]=e[2]*i,t[3]=e[3]*r,t[4]=e[4]*i,t[5]=e[5]*r,t}function h(t,e){var n=e[0],i=e[2],r=e[4],a=e[1],o=e[3],s=e[5],l=n*o-a*i;return l?(l=1/l,t[0]=o*l,t[1]=-a*l,t[2]=-i*l,t[3]=n*l,t[4]=(i*s-o*r)*l,t[5]=(a*r-n*s)*l,t):null}function c(t){var e=i();return a(e,t),e}e.create=i,e.identity=r,e.copy=a,e.mul=o,e.translate=s,e.rotate=l,e.scale=u,e.invert=h,e.clone=c},"17d6":function(t,e,n){var i=n("6d8b"),r=n("22d1"),a=n("e0d3"),o=a.makeInner,s=o(),l=i.each;function u(t,e,n){if(!r.node){var i=e.getZr();s(i).records||(s(i).records={}),h(i,e);var a=s(i).records[t]||(s(i).records[t]={});a.handler=n}}function h(t,e){function n(n,i){t.on(n,(function(n){var r=p(e);l(s(t).records,(function(t){t&&i(t,n,r.dispatchAction)})),c(r.pendings,e)}))}s(t).initialized||(s(t).initialized=!0,n("click",i.curry(f,"click")),n("mousemove",i.curry(f,"mousemove")),n("globalout",d))}function c(t,e){var n,i=t.showTip.length,r=t.hideTip.length;i?n=t.showTip[i-1]:r&&(n=t.hideTip[r-1]),n&&(n.dispatchAction=null,e.dispatchAction(n))}function d(t,e,n){t.handler("leave",null,n)}function f(t,e,n,i){e.handler(t,n,i)}function p(t){var e={showTip:[],hideTip:[]},n=function(i){var r=e[i.type];r?r.push(i):(i.dispatchAction=n,t.dispatchAction(i))};return{dispatchAction:n,pendings:e}}function g(t,e){if(!r.node){var n=e.getZr(),i=(s(n).records||{})[t];i&&(s(n).records[t]=null)}}e.register=u,e.unregister=g},"18c0":function(t,e,n){var i=n("6d8b"),r=n("e0d8"),a=n("8e43"),o=r.prototype,s=r.extend({type:"ordinal",init:function(t,e){t&&!i.isArray(t)||(t=new a({categories:t})),this._ordinalMeta=t,this._extent=e||[0,t.categories.length-1]},parse:function(t){return"string"===typeof t?this._ordinalMeta.getOrdinal(t):Math.round(t)},contain:function(t){return t=this.parse(t),o.contain.call(this,t)&&null!=this._ordinalMeta.categories[t]},normalize:function(t){return o.normalize.call(this,this.parse(t))},scale:function(t){return Math.round(o.scale.call(this,t))},getTicks:function(){var t=[],e=this._extent,n=e[0];while(n<=e[1])t.push(n),n++;return t},getLabel:function(t){if(!this.isBlank())return this._ordinalMeta.categories[t]},count:function(){return this._extent[1]-this._extent[0]+1},unionExtentFromData:function(t,e){this.unionExtent(t.getApproximateExtent(e))},getOrdinalMeta:function(){return this._ordinalMeta},niceTicks:i.noop,niceExtent:i.noop});s.create=function(){return new s};var l=s;t.exports=l},"19eb":function(t,e,n){var i=n("6d8b"),r=n("2b61"),a=n("d5b7"),o=n("9e2e");function s(t){for(var e in t=t||{},a.call(this,t),t)t.hasOwnProperty(e)&&"style"!==e&&(this[e]=t[e]);this.style=new r(t.style,this),this._rect=null,this.__clipPaths=[]}s.prototype={constructor:s,type:"displayable",__dirty:!0,invisible:!1,z:0,z2:0,zlevel:0,draggable:!1,dragging:!1,silent:!1,culling:!1,cursor:"pointer",rectHover:!1,progressive:!1,incremental:!1,globalScaleRatio:1,beforeBrush:function(t){},afterBrush:function(t){},brush:function(t,e){},getBoundingRect:function(){},contain:function(t,e){return this.rectContain(t,e)},traverse:function(t,e){t.call(e,this)},rectContain:function(t,e){var n=this.transformCoordToLocal(t,e),i=this.getBoundingRect();return i.contain(n[0],n[1])},dirty:function(){this.__dirty=this.__dirtyText=!0,this._rect=null,this.__zr&&this.__zr.refresh()},animateStyle:function(t){return this.animate("style",t)},attrKV:function(t,e){"style"!==t?a.prototype.attrKV.call(this,t,e):this.style.set(e)},setStyle:function(t,e){return this.style.set(t,e),this.dirty(!1),this},useStyle:function(t){return this.style=new r(t,this),this.dirty(!1),this}},i.inherits(s,a),i.mixin(s,o);var l=s;t.exports=l},"1ccf":function(t,e,n){var i=n("4e08"),r=(i.__DEV__,n("6d8b")),a=n("fd27"),o=n("3842"),s=o.parsePercent,l=n("697e"),u=l.createScaleByModel,h=l.niceScaleExtent,c=n("2039"),d=n("ee1a"),f=d.getStackedDimension;function p(t,e,n){var i=e.get("center"),r=n.getWidth(),a=n.getHeight();t.cx=s(i[0],r),t.cy=s(i[1],a);var o=t.getRadiusAxis(),l=Math.min(r,a)/2,u=s(e.get("radius"),l);o.inverse?o.setExtent(u,0):o.setExtent(0,u)}function g(t,e){var n=this,i=n.getAngleAxis(),a=n.getRadiusAxis();if(i.scale.setExtent(1/0,-1/0),a.scale.setExtent(1/0,-1/0),t.eachSeries((function(t){if(t.coordinateSystem===n){var e=t.getData();r.each(e.mapDimension("radius",!0),(function(t){a.scale.unionExtentFromData(e,f(e,t))})),r.each(e.mapDimension("angle",!0),(function(t){i.scale.unionExtentFromData(e,f(e,t))}))}})),h(i.scale,i.model),h(a.scale,a.model),"category"===i.type&&!i.onBand){var o=i.getExtent(),s=360/i.scale.count();i.inverse?o[1]+=s:o[1]-=s,i.setExtent(o[0],o[1])}}function v(t,e){if(t.type=e.get("type"),t.scale=u(e),t.onBand=e.get("boundaryGap")&&"category"===t.type,t.inverse=e.get("inverse"),"angleAxis"===e.mainType){t.inverse^=e.get("clockwise");var n=e.get("startAngle");t.setExtent(n,n+(t.inverse?-360:360))}e.axis=t,t.model=e}n("78f0");var m={dimensions:a.prototype.dimensions,create:function(t,e){var n=[];return t.eachComponent("polar",(function(t,i){var r=new a(i);r.update=g;var o=r.getRadiusAxis(),s=r.getAngleAxis(),l=t.findAxisModel("radiusAxis"),u=t.findAxisModel("angleAxis");v(o,l),v(s,u),p(r,t,e),n.push(r),t.coordinateSystem=r,r.model=t})),t.eachSeries((function(e){if("polar"===e.get("coordinateSystem")){var n=t.queryComponents({mainType:"polar",index:e.get("polarIndex"),id:e.get("polarId")})[0];e.coordinateSystem=n.coordinateSystem}})),n}};c.register("polar",m)},"1e32":function(t,e,n){var i=n("6d8b"),r=n("3842"),a=r.parsePercent,o=n("ee1a"),s=o.isDimensionStacked;function l(t){return t.get("stack")||"__ec_stack_"+t.seriesIndex}function u(t){return t.dim}function h(t,e,n){n.getWidth(),n.getHeight();var r={},a=c(i.filter(e.getSeriesByType(t),(function(t){return!e.isSeriesFiltered(t)&&t.coordinateSystem&&"polar"===t.coordinateSystem.type})));e.eachSeriesByType(t,(function(t){if("polar"===t.coordinateSystem.type){var e=t.getData(),n=t.coordinateSystem,i=n.getBaseAxis(),o=l(t),h=a[u(i)][o],c=h.offset,d=h.width,f=n.getOtherAxis(i),p=t.coordinateSystem.cx,g=t.coordinateSystem.cy,v=t.get("barMinHeight")||0,m=t.get("barMinAngle")||0;r[o]=r[o]||[];for(var y=e.mapDimension(f.dim),x=e.mapDimension(i.dim),_=s(e,y),b=f.getExtent()[0],w=0,S=e.count();w<S;w++){var M=e.get(y,w),T=e.get(x,w);if(!isNaN(M)){var A,C,I,k,D=M>=0?"p":"n",P=b;if(_&&(r[o][T]||(r[o][T]={p:b,n:b}),P=r[o][T][D]),"radius"===f.dim){var O=f.dataToRadius(M)-b,L=i.dataToAngle(T);Math.abs(O)<v&&(O=(O<0?-1:1)*v),A=P,C=P+O,I=L-c,k=I-d,_&&(r[o][T][D]=C)}else{var E=f.dataToAngle(M,!0)-b,R=i.dataToRadius(T);Math.abs(E)<m&&(E=(E<0?-1:1)*m),A=R+c,C=A+d,I=P,k=P+E,_&&(r[o][T][D]=k)}e.setItemLayout(w,{cx:p,cy:g,r0:A,r:C,startAngle:-I*Math.PI/180,endAngle:-k*Math.PI/180})}}}}),this)}function c(t,e){var n={};i.each(t,(function(t,e){var i=t.getData(),r=t.coordinateSystem,o=r.getBaseAxis(),s=o.getExtent(),h="category"===o.type?o.getBandWidth():Math.abs(s[1]-s[0])/i.count(),c=n[u(o)]||{bandWidth:h,remainedWidth:h,autoWidthCount:0,categoryGap:"20%",gap:"30%",stacks:{}},d=c.stacks;n[u(o)]=c;var f=l(t);d[f]||c.autoWidthCount++,d[f]=d[f]||{width:0,maxWidth:0};var p=a(t.get("barWidth"),h),g=a(t.get("barMaxWidth"),h),v=t.get("barGap"),m=t.get("barCategoryGap");p&&!d[f].width&&(p=Math.min(c.remainedWidth,p),d[f].width=p,c.remainedWidth-=p),g&&(d[f].maxWidth=g),null!=v&&(c.gap=v),null!=m&&(c.categoryGap=m)}));var r={};return i.each(n,(function(t,e){r[e]={};var n=t.stacks,o=t.bandWidth,s=a(t.categoryGap,o),l=a(t.gap,1),u=t.remainedWidth,h=t.autoWidthCount,c=(u-s)/(h+(h-1)*l);c=Math.max(c,0),i.each(n,(function(t,e){var n=t.maxWidth;n&&n<c&&(n=Math.min(n,u),t.width&&(n=Math.min(n,t.width)),u-=n,t.width=n,h--)})),c=(u-s)/(h+(h-1)*l),c=Math.max(c,0);var d,f=0;i.each(n,(function(t,e){t.width||(t.width=c),d=t,f+=t.width*(1+l)})),d&&(f-=d.width*l);var p=-f/2;i.each(n,(function(t,n){r[e][n]=r[e][n]||{offset:p,width:t.width},p+=t.width*(1+l)}))})),r}var d=h;t.exports=d},"1fab":function(t,e){var n=Array.prototype.slice,i=function(t){this._$handlers={},this._$eventProcessor=t};function r(t,e){var n=t._$eventProcessor;return null!=e&&n&&n.normalizeQuery&&(e=n.normalizeQuery(e)),e}function a(t,e,n,i,a,o){var s=t._$handlers;if("function"===typeof n&&(a=i,i=n,n=null),!i||!e)return t;n=r(t,n),s[e]||(s[e]=[]);for(var l=0;l<s[e].length;l++)if(s[e][l].h===i)return t;var u={h:i,one:o,query:n,ctx:a||t,callAtLast:i.zrEventfulCallAtLast},h=s[e].length-1,c=s[e][h];return c&&c.callAtLast?s[e].splice(h,0,u):s[e].push(u),t}i.prototype={constructor:i,one:function(t,e,n,i){return a(this,t,e,n,i,!0)},on:function(t,e,n,i){return a(this,t,e,n,i,!1)},isSilent:function(t){var e=this._$handlers;return!e[t]||!e[t].length},off:function(t,e){var n=this._$handlers;if(!t)return this._$handlers={},this;if(e){if(n[t]){for(var i=[],r=0,a=n[t].length;r<a;r++)n[t][r].h!==e&&i.push(n[t][r]);n[t]=i}n[t]&&0===n[t].length&&delete n[t]}else delete n[t];return this},trigger:function(t){var e=this._$handlers[t],i=this._$eventProcessor;if(e){var r=arguments,a=r.length;a>3&&(r=n.call(r,1));for(var o=e.length,s=0;s<o;){var l=e[s];if(i&&i.filter&&null!=l.query&&!i.filter(t,l.query))s++;else{switch(a){case 1:l.h.call(l.ctx);break;case 2:l.h.call(l.ctx,r[1]);break;case 3:l.h.call(l.ctx,r[1],r[2]);break;default:l.h.apply(l.ctx,r);break}l.one?(e.splice(s,1),o--):s++}}}return i&&i.afterTrigger&&i.afterTrigger(t),this},triggerWithContext:function(t){var e=this._$handlers[t],i=this._$eventProcessor;if(e){var r=arguments,a=r.length;a>4&&(r=n.call(r,1,r.length-1));for(var o=r[r.length-1],s=e.length,l=0;l<s;){var u=e[l];if(i&&i.filter&&null!=u.query&&!i.filter(t,u.query))l++;else{switch(a){case 1:u.h.call(o);break;case 2:u.h.call(o,r[1]);break;case 3:u.h.call(o,r[1],r[2]);break;default:u.h.apply(o,r);break}u.one?(e.splice(l,1),s--):l++}}}return i&&i.afterTrigger&&i.afterTrigger(t),this}};var o=i;t.exports=o},2023:function(t,e,n){var i=n("6d8b"),r={getMin:function(t){var e=this.option,n=t||null==e.rangeStart?e.min:e.rangeStart;return this.axis&&null!=n&&"dataMin"!==n&&"function"!==typeof n&&!i.eqNaN(n)&&(n=this.axis.scale.parse(n)),n},getMax:function(t){var e=this.option,n=t||null==e.rangeEnd?e.max:e.rangeEnd;return this.axis&&null!=n&&"dataMax"!==n&&"function"!==typeof n&&!i.eqNaN(n)&&(n=this.axis.scale.parse(n)),n},getNeedCrossZero:function(){var t=this.option;return null==t.rangeStart&&null==t.rangeEnd&&!t.scale},getCoordSysModel:i.noop,setRange:function(t,e){this.option.rangeStart=t,this.option.rangeEnd=e},resetRange:function(){this.option.rangeStart=this.option.rangeEnd=null}};t.exports=r},2039:function(t,e,n){var i=n("6d8b"),r={};function a(){this._coordinateSystems=[]}a.prototype={constructor:a,create:function(t,e){var n=[];i.each(r,(function(i,r){var a=i.create(t,e);n=n.concat(a||[])})),this._coordinateSystems=n},update:function(t,e){i.each(this._coordinateSystems,(function(n){n.update&&n.update(t,e)}))},getCoordinateSystems:function(){return this._coordinateSystems.slice()}},a.register=function(t,e){r[t]=e},a.get=function(t){return r[t]};var o=a;t.exports=o},"20c8":function(t,e,n){var i=n("4a3f"),r=n("401b"),a=n("e263"),o=n("9850"),s=n("2cf4"),l=s.devicePixelRatio,u={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},h=[],c=[],d=[],f=[],p=Math.min,g=Math.max,v=Math.cos,m=Math.sin,y=Math.sqrt,x=Math.abs,_="undefined"!==typeof Float32Array,b=function(t){this._saveData=!t,this._saveData&&(this.data=[]),this._ctx=null};b.prototype={constructor:b,_xi:0,_yi:0,_x0:0,_y0:0,_ux:0,_uy:0,_len:0,_lineDash:null,_dashOffset:0,_dashIdx:0,_dashSum:0,setScale:function(t,e){this._ux=x(1/l/t)||0,this._uy=x(1/l/e)||0},getContext:function(){return this._ctx},beginPath:function(t){return this._ctx=t,t&&t.beginPath(),t&&(this.dpr=t.dpr),this._saveData&&(this._len=0),this._lineDash&&(this._lineDash=null,this._dashOffset=0),this},moveTo:function(t,e){return this.addData(u.M,t,e),this._ctx&&this._ctx.moveTo(t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},lineTo:function(t,e){var n=x(t-this._xi)>this._ux||x(e-this._yi)>this._uy||this._len<5;return this.addData(u.L,t,e),this._ctx&&n&&(this._needsDash()?this._dashedLineTo(t,e):this._ctx.lineTo(t,e)),n&&(this._xi=t,this._yi=e),this},bezierCurveTo:function(t,e,n,i,r,a){return this.addData(u.C,t,e,n,i,r,a),this._ctx&&(this._needsDash()?this._dashedBezierTo(t,e,n,i,r,a):this._ctx.bezierCurveTo(t,e,n,i,r,a)),this._xi=r,this._yi=a,this},quadraticCurveTo:function(t,e,n,i){return this.addData(u.Q,t,e,n,i),this._ctx&&(this._needsDash()?this._dashedQuadraticTo(t,e,n,i):this._ctx.quadraticCurveTo(t,e,n,i)),this._xi=n,this._yi=i,this},arc:function(t,e,n,i,r,a){return this.addData(u.A,t,e,n,n,i,r-i,0,a?0:1),this._ctx&&this._ctx.arc(t,e,n,i,r,a),this._xi=v(r)*n+t,this._yi=m(r)*n+e,this},arcTo:function(t,e,n,i,r){return this._ctx&&this._ctx.arcTo(t,e,n,i,r),this},rect:function(t,e,n,i){return this._ctx&&this._ctx.rect(t,e,n,i),this.addData(u.R,t,e,n,i),this},closePath:function(){this.addData(u.Z);var t=this._ctx,e=this._x0,n=this._y0;return t&&(this._needsDash()&&this._dashedLineTo(e,n),t.closePath()),this._xi=e,this._yi=n,this},fill:function(t){t&&t.fill(),this.toStatic()},stroke:function(t){t&&t.stroke(),this.toStatic()},setLineDash:function(t){if(t instanceof Array){this._lineDash=t,this._dashIdx=0;for(var e=0,n=0;n<t.length;n++)e+=t[n];this._dashSum=e}return this},setLineDashOffset:function(t){return this._dashOffset=t,this},len:function(){return this._len},setData:function(t){var e=t.length;this.data&&this.data.length===e||!_||(this.data=new Float32Array(e));for(var n=0;n<e;n++)this.data[n]=t[n];this._len=e},appendPath:function(t){t instanceof Array||(t=[t]);for(var e=t.length,n=0,i=this._len,r=0;r<e;r++)n+=t[r].len();_&&this.data instanceof Float32Array&&(this.data=new Float32Array(i+n));for(r=0;r<e;r++)for(var a=t[r].data,o=0;o<a.length;o++)this.data[i++]=a[o];this._len=i},addData:function(t){if(this._saveData){var e=this.data;this._len+arguments.length>e.length&&(this._expandData(),e=this.data);for(var n=0;n<arguments.length;n++)e[this._len++]=arguments[n];this._prevCmd=t}},_expandData:function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},_needsDash:function(){return this._lineDash},_dashedLineTo:function(t,e){var n,i,r=this._dashSum,a=this._dashOffset,o=this._lineDash,s=this._ctx,l=this._xi,u=this._yi,h=t-l,c=e-u,d=y(h*h+c*c),f=l,v=u,m=o.length;h/=d,c/=d,a<0&&(a=r+a),a%=r,f-=a*h,v-=a*c;while(h>0&&f<=t||h<0&&f>=t||0===h&&(c>0&&v<=e||c<0&&v>=e))i=this._dashIdx,n=o[i],f+=h*n,v+=c*n,this._dashIdx=(i+1)%m,h>0&&f<l||h<0&&f>l||c>0&&v<u||c<0&&v>u||s[i%2?"moveTo":"lineTo"](h>=0?p(f,t):g(f,t),c>=0?p(v,e):g(v,e));h=f-t,c=v-e,this._dashOffset=-y(h*h+c*c)},_dashedBezierTo:function(t,e,n,r,a,o){var s,l,u,h,c,d=this._dashSum,f=this._dashOffset,p=this._lineDash,g=this._ctx,v=this._xi,m=this._yi,x=i.cubicAt,_=0,b=this._dashIdx,w=p.length,S=0;for(f<0&&(f=d+f),f%=d,s=0;s<1;s+=.1)l=x(v,t,n,a,s+.1)-x(v,t,n,a,s),u=x(m,e,r,o,s+.1)-x(m,e,r,o,s),_+=y(l*l+u*u);for(;b<w;b++)if(S+=p[b],S>f)break;s=(S-f)/_;while(s<=1)h=x(v,t,n,a,s),c=x(m,e,r,o,s),b%2?g.moveTo(h,c):g.lineTo(h,c),s+=p[b]/_,b=(b+1)%w;b%2!==0&&g.lineTo(a,o),l=a-h,u=o-c,this._dashOffset=-y(l*l+u*u)},_dashedQuadraticTo:function(t,e,n,i){var r=n,a=i;n=(n+2*t)/3,i=(i+2*e)/3,t=(this._xi+2*t)/3,e=(this._yi+2*e)/3,this._dashedBezierTo(t,e,n,i,r,a)},toStatic:function(){var t=this.data;t instanceof Array&&(t.length=this._len,_&&(this.data=new Float32Array(t)))},getBoundingRect:function(){h[0]=h[1]=d[0]=d[1]=Number.MAX_VALUE,c[0]=c[1]=f[0]=f[1]=-Number.MAX_VALUE;for(var t=this.data,e=0,n=0,i=0,s=0,l=0;l<t.length;){var p=t[l++];switch(1===l&&(e=t[l],n=t[l+1],i=e,s=n),p){case u.M:i=t[l++],s=t[l++],e=i,n=s,d[0]=i,d[1]=s,f[0]=i,f[1]=s;break;case u.L:a.fromLine(e,n,t[l],t[l+1],d,f),e=t[l++],n=t[l++];break;case u.C:a.fromCubic(e,n,t[l++],t[l++],t[l++],t[l++],t[l],t[l+1],d,f),e=t[l++],n=t[l++];break;case u.Q:a.fromQuadratic(e,n,t[l++],t[l++],t[l],t[l+1],d,f),e=t[l++],n=t[l++];break;case u.A:var g=t[l++],y=t[l++],x=t[l++],_=t[l++],b=t[l++],w=t[l++]+b;l+=1;var S=1-t[l++];1===l&&(i=v(b)*x+g,s=m(b)*_+y),a.fromArc(g,y,x,_,b,w,S,d,f),e=v(w)*x+g,n=m(w)*_+y;break;case u.R:i=e=t[l++],s=n=t[l++];var M=t[l++],T=t[l++];a.fromLine(i,s,i+M,s+T,d,f);break;case u.Z:e=i,n=s;break}r.min(h,h,d),r.max(c,c,f)}return 0===l&&(h[0]=h[1]=c[0]=c[1]=0),new o(h[0],h[1],c[0]-h[0],c[1]-h[1])},rebuildPath:function(t){for(var e,n,i,r,a,o,s=this.data,l=this._ux,h=this._uy,c=this._len,d=0;d<c;){var f=s[d++];switch(1===d&&(i=s[d],r=s[d+1],e=i,n=r),f){case u.M:e=i=s[d++],n=r=s[d++],t.moveTo(i,r);break;case u.L:a=s[d++],o=s[d++],(x(a-i)>l||x(o-r)>h||d===c-1)&&(t.lineTo(a,o),i=a,r=o);break;case u.C:t.bezierCurveTo(s[d++],s[d++],s[d++],s[d++],s[d++],s[d++]),i=s[d-2],r=s[d-1];break;case u.Q:t.quadraticCurveTo(s[d++],s[d++],s[d++],s[d++]),i=s[d-2],r=s[d-1];break;case u.A:var p=s[d++],g=s[d++],y=s[d++],_=s[d++],b=s[d++],w=s[d++],S=s[d++],M=s[d++],T=y>_?y:_,A=y>_?1:y/_,C=y>_?_/y:1,I=Math.abs(y-_)>.001,k=b+w;I?(t.translate(p,g),t.rotate(S),t.scale(A,C),t.arc(0,0,T,b,k,1-M),t.scale(1/A,1/C),t.rotate(-S),t.translate(-p,-g)):t.arc(p,g,T,b,k,1-M),1===d&&(e=v(b)*y+p,n=m(b)*_+g),i=v(k)*y+p,r=m(k)*_+g;break;case u.R:e=i=s[d],n=r=s[d+1],t.rect(s[d++],s[d++],s[d++],s[d++]);break;case u.Z:t.closePath(),i=e,r=n}}}},b.CMD=u;var w=b;t.exports=w},"216a":function(t,e,n){var i=n("6d8b"),r=n("3842"),a=n("eda2"),o=n("944e"),s=n("89e3"),l=s.prototype,u=Math.ceil,h=Math.floor,c=1e3,d=60*c,f=60*d,p=24*f,g=function(t,e,n,i){while(n<i){var r=n+i>>>1;t[r][1]<e?n=r+1:i=r}return n},v=s.extend({type:"time",getLabel:function(t){var e=this._stepLvl,n=new Date(t);return a.formatTime(e[0],n,this.getSetting("useUTC"))},niceExtent:function(t){var e=this._extent;if(e[0]===e[1]&&(e[0]-=p,e[1]+=p),e[1]===-1/0&&e[0]===1/0){var n=new Date;e[1]=+new Date(n.getFullYear(),n.getMonth(),n.getDate()),e[0]=e[1]-p}this.niceTicks(t.splitNumber,t.minInterval,t.maxInterval);var i=this._interval;t.fixMin||(e[0]=r.round(h(e[0]/i)*i)),t.fixMax||(e[1]=r.round(u(e[1]/i)*i))},niceTicks:function(t,e,n){t=t||10;var i=this._extent,a=i[1]-i[0],s=a/t;null!=e&&s<e&&(s=e),null!=n&&s>n&&(s=n);var l=m.length,c=g(m,s,0,l),d=m[Math.min(c,l-1)],f=d[1];if("year"===d[0]){var p=a/f,v=r.nice(p/t,!0);f*=v}var y=this.getSetting("useUTC")?0:60*new Date(+i[0]||+i[1]).getTimezoneOffset()*1e3,x=[Math.round(u((i[0]-y)/f)*f+y),Math.round(h((i[1]-y)/f)*f+y)];o.fixExtent(x,i),this._stepLvl=d,this._interval=f,this._niceExtent=x},parse:function(t){return+r.parseDate(t)}});i.each(["contain","normalize"],(function(t){v.prototype[t]=function(e){return l[t].call(this,this.parse(e))}}));var m=[["hh:mm:ss",c],["hh:mm:ss",5*c],["hh:mm:ss",10*c],["hh:mm:ss",15*c],["hh:mm:ss",30*c],["hh:mm\nMM-dd",d],["hh:mm\nMM-dd",5*d],["hh:mm\nMM-dd",10*d],["hh:mm\nMM-dd",15*d],["hh:mm\nMM-dd",30*d],["hh:mm\nMM-dd",f],["hh:mm\nMM-dd",2*f],["hh:mm\nMM-dd",6*f],["hh:mm\nMM-dd",12*f],["MM-dd\nyyyy",p],["MM-dd\nyyyy",2*p],["MM-dd\nyyyy",3*p],["MM-dd\nyyyy",4*p],["MM-dd\nyyyy",5*p],["MM-dd\nyyyy",6*p],["week",7*p],["MM-dd\nyyyy",10*p],["week",14*p],["week",21*p],["month",31*p],["week",42*p],["month",62*p],["week",70*p],["quarter",95*p],["month",31*p*4],["month",31*p*5],["half-year",380*p/2],["month",31*p*8],["month",31*p*10],["year",380*p]];v.create=function(t){return new v({useUTC:t.ecModel.get("useUTC")})};var y=v;t.exports=y},"217b8":function(t,e,n){var i=n("4e08"),r=(i.__DEV__,n("3301")),a=n("4f85"),o=a.extend({type:"series.line",dependencies:["grid","polar"],getInitialData:function(t,e){return r(this.getSource(),this)},defaultOption:{zlevel:0,z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,hoverAnimation:!0,clipOverflow:!0,label:{position:"top"},lineStyle:{width:2,type:"solid"},step:!1,smooth:!1,smoothMonotone:null,symbol:"emptyCircle",symbolSize:4,symbolRotate:null,showSymbol:!0,showAllSymbol:"auto",connectNulls:!1,sampling:"none",animationEasing:"linear",progressive:0,hoverLayerThreshold:1/0}});t.exports=o},"22d1":function(t,e){var n={};n="object"===typeof wx&&"function"===typeof wx.getSystemInfoSync?{browser:{},os:{},node:!1,wxa:!0,canvasSupported:!0,svgSupported:!1,touchEventsSupported:!0,domSupported:!1}:"undefined"===typeof document&&"undefined"!==typeof self?{browser:{},os:{},node:!1,worker:!0,canvasSupported:!0,domSupported:!1}:"undefined"===typeof navigator?{browser:{},os:{},node:!0,worker:!1,canvasSupported:!0,svgSupported:!0,domSupported:!1}:r(navigator.userAgent);var i=n;function r(t){var e={},n={},i=t.match(/Firefox\/([\d.]+)/),r=t.match(/MSIE\s([\d.]+)/)||t.match(/Trident\/.+?rv:(([\d.]+))/),a=t.match(/Edge\/([\d.]+)/),o=/micromessenger/i.test(t);return i&&(n.firefox=!0,n.version=i[1]),r&&(n.ie=!0,n.version=r[1]),a&&(n.edge=!0,n.version=a[1]),o&&(n.weChat=!0),{browser:n,os:e,node:!1,canvasSupported:!!document.createElement("canvas").getContext,svgSupported:"undefined"!==typeof SVGRect,touchEventsSupported:"ontouchstart"in window&&!n.ie&&!n.edge,pointerEventsSupported:"onpointerdown"in window&&(n.edge||n.ie&&n.version>=11),domSupported:"undefined"!==typeof document}}t.exports=i},2306:function(t,e,n){var i=n("6d8b"),r=n("342d"),a=n("41ef"),o=n("1687"),s=n("401b"),l=n("cbe5"),u=n("0cde"),h=n("0da8");e.Image=h;var c=n("e1fc");e.Group=c;var d=n("76a5");e.Text=d;var f=n("d9fc");e.Circle=f;var p=n("4aa2");e.Sector=p;var g=n("4573");e.Ring=g;var v=n("87b1");e.Polygon=v;var m=n("d498");e.Polyline=m;var y=n("c7a2");e.Rect=y;var x=n("cb11");e.Line=x;var _=n("ac0f");e.BezierCurve=_;var b=n("8d32");e.Arc=b;var w=n("d4c6");e.CompoundPath=w;var S=n("48a9");e.LinearGradient=S;var M=n("dded");e.RadialGradient=M;var T=n("9850");e.BoundingRect=T;var A=n("392f");e.IncrementalDisplayable=A;var C=Math.round,I=Math.max,k=Math.min,D={},P=1;function O(t){return l.extend(t)}function L(t,e){return r.extendFromString(t,e)}function E(t,e,n,i){var a=r.createFromString(t,e);return n&&("center"===i&&(n=B(n,a.getBoundingRect())),z(a,n)),a}function R(t,e,n){var i=new h({style:{image:t,x:e.x,y:e.y,width:e.width,height:e.height},onload:function(t){if("center"===n){var r={width:t.width,height:t.height};i.setStyle(B(e,r))}}});return i}function B(t,e){var n,i=e.width/e.height,r=t.height*i;r<=t.width?n=t.height:(r=t.width,n=r/i);var a=t.x+t.width/2,o=t.y+t.height/2;return{x:a-r/2,y:o-n/2,width:r,height:n}}var N=r.mergePath;function z(t,e){if(t.applyTransform){var n=t.getBoundingRect(),i=n.calculateTransform(e);t.applyTransform(i)}}function F(t){var e=t.shape,n=t.style.lineWidth;return C(2*e.x1)===C(2*e.x2)&&(e.x1=e.x2=H(e.x1,n,!0)),C(2*e.y1)===C(2*e.y2)&&(e.y1=e.y2=H(e.y1,n,!0)),t}function V(t){var e=t.shape,n=t.style.lineWidth,i=e.x,r=e.y,a=e.width,o=e.height;return e.x=H(e.x,n,!0),e.y=H(e.y,n,!0),e.width=Math.max(H(i+a,n,!1)-e.x,0===a?0:1),e.height=Math.max(H(r+o,n,!1)-e.y,0===o?0:1),t}function H(t,e,n){var i=C(2*t);return(i+C(e))%2===0?i/2:(i+(n?1:-1))/2}function W(t){return null!=t&&"none"!==t}var G=i.createHashMap(),U=0;function q(t){if("string"!==typeof t)return t;var e=G.get(t);return e||(e=a.lift(t,-.1),U<1e4&&(G.set(t,e),U++)),e}function Y(t){if(t.__hoverStlDirty){t.__hoverStlDirty=!1;var e=t.__hoverStl;if(e){var n=t.__cachedNormalStl={};t.__cachedNormalZ2=t.z2;var i=t.style;for(var r in e)null!=e[r]&&(n[r]=i[r]);n.fill=i.fill,n.stroke=i.stroke}else t.__cachedNormalStl=t.__cachedNormalZ2=null}}function X(t){var e=t.__hoverStl;if(e&&!t.__highlighted){var n=t.useHoverLayer;t.__highlighted=n?"layer":"plain";var i=t.__zr;if(i||!n){var r=t,a=t.style;n&&(r=i.addHover(t),a=r.style),ft(a),n||Y(r),a.extendFrom(e),j(a,e,"fill"),j(a,e,"stroke"),dt(a),n||(t.dirty(!1),t.z2+=P)}}}function j(t,e,n){!W(e[n])&&W(t[n])&&(t[n]=q(t[n]))}function Z(t){var e=t.__highlighted;if(e)if(t.__highlighted=!1,"layer"===e)t.__zr&&t.__zr.removeHover(t);else if(e){var n=t.style,i=t.__cachedNormalStl;i&&(ft(n),t.setStyle(i),dt(n));var r=t.__cachedNormalZ2;null!=r&&t.z2-r===P&&(t.z2=r)}}function K(t,e){t.isGroup?t.traverse((function(t){!t.isGroup&&e(t)})):e(t)}function $(t,e){e=t.__hoverStl=!1!==e&&(e||{}),t.__hoverStlDirty=!0,t.__highlighted&&(t.__cachedNormalStl=null,Z(t),X(t))}function Q(t){return t&&t.__isEmphasisEntered}function J(t){this.__hoverSilentOnTouch&&t.zrByTouch||!this.__isEmphasisEntered&&K(this,X)}function tt(t){this.__hoverSilentOnTouch&&t.zrByTouch||!this.__isEmphasisEntered&&K(this,Z)}function et(){this.__isEmphasisEntered=!0,K(this,X)}function nt(){this.__isEmphasisEntered=!1,K(this,Z)}function it(t,e,n){t.isGroup?t.traverse((function(t){!t.isGroup&&$(t,t.hoverStyle||e)})):$(t,t.hoverStyle||e),rt(t,n)}function rt(t,e){var n=!1===e;if(t.__hoverSilentOnTouch=null!=e&&e.hoverSilentOnTouch,!n||t.__hoverStyleTrigger){var i=n?"off":"on";t[i]("mouseover",J)[i]("mouseout",tt),t[i]("emphasis",et)[i]("normal",nt),t.__hoverStyleTrigger=!n}}function at(t,e,n,r,a,o,s){a=a||D;var l,u=a.labelFetcher,h=a.labelDataIndex,c=a.labelDimIndex,d=n.getShallow("show"),f=r.getShallow("show");(d||f)&&(u&&(l=u.getFormattedLabel(h,"normal",null,c)),null==l&&(l=i.isFunction(a.defaultText)?a.defaultText(h,a):a.defaultText));var p=d?l:null,g=f?i.retrieve2(u?u.getFormattedLabel(h,"emphasis",null,c):null,l):null;null==p&&null==g||(ot(t,n,o,a),ot(e,r,s,a,!0)),t.text=p,e.text=g}function ot(t,e,n,r,a){return lt(t,e,r,a),n&&i.extend(t,n),t}function st(t,e,n){var i,r={isRectText:!0};!1===n?i=!0:r.autoColor=n,lt(t,e,r,i)}function lt(t,e,n,r){if(n=n||D,n.isRectText){var a=e.getShallow("position")||(r?null:"inside");"outside"===a&&(a="top"),t.textPosition=a,t.textOffset=e.getShallow("offset");var o=e.getShallow("rotate");null!=o&&(o*=Math.PI/180),t.textRotation=o,t.textDistance=i.retrieve2(e.getShallow("distance"),r?null:5)}var s,l=e.ecModel,u=l&&l.option.textStyle,h=ut(e);if(h)for(var c in s={},h)if(h.hasOwnProperty(c)){var d=e.getModel(["rich",c]);ht(s[c]={},d,u,n,r)}return t.rich=s,ht(t,e,u,n,r,!0),n.forceRich&&!n.textStyle&&(n.textStyle={}),t}function ut(t){var e;while(t&&t!==t.ecModel){var n=(t.option||D).rich;if(n)for(var i in e=e||{},n)n.hasOwnProperty(i)&&(e[i]=1);t=t.parentModel}return e}function ht(t,e,n,r,a,o){n=!a&&n||D,t.textFill=ct(e.getShallow("color"),r)||n.color,t.textStroke=ct(e.getShallow("textBorderColor"),r)||n.textBorderColor,t.textStrokeWidth=i.retrieve2(e.getShallow("textBorderWidth"),n.textBorderWidth),t.insideRawTextPosition=t.textPosition,a||(o&&(t.insideRollbackOpt=r,dt(t)),null==t.textFill&&(t.textFill=r.autoColor)),t.fontStyle=e.getShallow("fontStyle")||n.fontStyle,t.fontWeight=e.getShallow("fontWeight")||n.fontWeight,t.fontSize=e.getShallow("fontSize")||n.fontSize,t.fontFamily=e.getShallow("fontFamily")||n.fontFamily,t.textAlign=e.getShallow("align"),t.textVerticalAlign=e.getShallow("verticalAlign")||e.getShallow("baseline"),t.textLineHeight=e.getShallow("lineHeight"),t.textWidth=e.getShallow("width"),t.textHeight=e.getShallow("height"),t.textTag=e.getShallow("tag"),o&&r.disableBox||(t.textBackgroundColor=ct(e.getShallow("backgroundColor"),r),t.textPadding=e.getShallow("padding"),t.textBorderColor=ct(e.getShallow("borderColor"),r),t.textBorderWidth=e.getShallow("borderWidth"),t.textBorderRadius=e.getShallow("borderRadius"),t.textBoxShadowColor=e.getShallow("shadowColor"),t.textBoxShadowBlur=e.getShallow("shadowBlur"),t.textBoxShadowOffsetX=e.getShallow("shadowOffsetX"),t.textBoxShadowOffsetY=e.getShallow("shadowOffsetY")),t.textShadowColor=e.getShallow("textShadowColor")||n.textShadowColor,t.textShadowBlur=e.getShallow("textShadowBlur")||n.textShadowBlur,t.textShadowOffsetX=e.getShallow("textShadowOffsetX")||n.textShadowOffsetX,t.textShadowOffsetY=e.getShallow("textShadowOffsetY")||n.textShadowOffsetY}function ct(t,e){return"auto"!==t?t:e&&e.autoColor?e.autoColor:null}function dt(t){var e=t.insideRollbackOpt;if(e&&null==t.textFill){var n,i=e.useInsideStyle,r=t.insideRawTextPosition,a=e.autoColor;!1!==i&&(!0===i||e.isRectText&&r&&"string"===typeof r&&r.indexOf("inside")>=0)?(n={textFill:null,textStroke:t.textStroke,textStrokeWidth:t.textStrokeWidth},t.textFill="#fff",null==t.textStroke&&(t.textStroke=a,null==t.textStrokeWidth&&(t.textStrokeWidth=2))):null!=a&&(n={textFill:null},t.textFill=a),n&&(t.insideRollback=n)}}function ft(t){var e=t.insideRollback;e&&(t.textFill=e.textFill,t.textStroke=e.textStroke,t.textStrokeWidth=e.textStrokeWidth,t.insideRollback=null)}function pt(t,e){var n=e||e.getModel("textStyle");return i.trim([t.fontStyle||n&&n.getShallow("fontStyle")||"",t.fontWeight||n&&n.getShallow("fontWeight")||"",(t.fontSize||n&&n.getShallow("fontSize")||12)+"px",t.fontFamily||n&&n.getShallow("fontFamily")||"sans-serif"].join(" "))}function gt(t,e,n,i,r,a){"function"===typeof r&&(a=r,r=null);var o=i&&i.isAnimationEnabled();if(o){var s=t?"Update":"",l=i.getShallow("animationDuration"+s),u=i.getShallow("animationEasing"+s),h=i.getShallow("animationDelay"+s);"function"===typeof h&&(h=h(r,i.getAnimationDelayParams?i.getAnimationDelayParams(e,r):null)),"function"===typeof l&&(l=l(r)),l>0?e.animateTo(n,l,h||0,u,a,!!a):(e.stopAnimation(),e.attr(n),a&&a())}else e.stopAnimation(),e.attr(n),a&&a()}function vt(t,e,n,i,r){gt(!0,t,e,n,i,r)}function mt(t,e,n,i,r){gt(!1,t,e,n,i,r)}function yt(t,e){var n=o.identity([]);while(t&&t!==e)o.mul(n,t.getLocalTransform(),n),t=t.parent;return n}function xt(t,e,n){return e&&!i.isArrayLike(e)&&(e=u.getLocalTransform(e)),n&&(e=o.invert([],e)),s.applyTransform([],t,e)}function _t(t,e,n){var i=0===e[4]||0===e[5]||0===e[0]?1:Math.abs(2*e[4]/e[0]),r=0===e[4]||0===e[5]||0===e[2]?1:Math.abs(2*e[4]/e[2]),a=["left"===t?-i:"right"===t?i:0,"top"===t?-r:"bottom"===t?r:0];return a=xt(a,e,n),Math.abs(a[0])>Math.abs(a[1])?a[0]>0?"right":"left":a[1]>0?"bottom":"top"}function bt(t,e,n,r){if(t&&e){var a=o(t);e.traverse((function(t){if(!t.isGroup&&t.anid){var e=a[t.anid];if(e){var i=l(t);t.attr(l(e)),vt(t,i,n,t.dataIndex)}}}))}function o(t){var e={};return t.traverse((function(t){!t.isGroup&&t.anid&&(e[t.anid]=t)})),e}function l(t){var e={position:s.clone(t.position),rotation:t.rotation};return t.shape&&(e.shape=i.extend({},t.shape)),e}}function wt(t,e){return i.map(t,(function(t){var n=t[0];n=I(n,e.x),n=k(n,e.x+e.width);var i=t[1];return i=I(i,e.y),i=k(i,e.y+e.height),[n,i]}))}function St(t,e){var n=I(t.x,e.x),i=k(t.x+t.width,e.x+e.width),r=I(t.y,e.y),a=k(t.y+t.height,e.y+e.height);if(i>=n&&a>=r)return{x:n,y:r,width:i-n,height:a-r}}function Mt(t,e,n){e=i.extend({rectHover:!0},e);var r=e.style={strokeNoScale:!0};if(n=n||{x:-1,y:-1,width:2,height:2},t)return 0===t.indexOf("image://")?(r.image=t.slice(8),i.defaults(r,n),new h(e)):E(t.replace("path://",""),e,n,"center")}e.Z2_EMPHASIS_LIFT=P,e.extendShape=O,e.extendPath=L,e.makePath=E,e.makeImage=R,e.mergePath=N,e.resizePath=z,e.subPixelOptimizeLine=F,e.subPixelOptimizeRect=V,e.subPixelOptimize=H,e.setElementHoverStyle=$,e.isInEmphasis=Q,e.setHoverStyle=it,e.setAsHoverStyleTrigger=rt,e.setLabelStyle=at,e.setTextStyle=ot,e.setText=st,e.getFont=pt,e.updateProps=vt,e.initProps=mt,e.getTransform=yt,e.applyTransform=xt,e.transformDirection=_t,e.groupTransition=bt,e.clipPointsByRect=wt,e.clipRectByRect=St,e.createIcon=Mt},"26e1":function(t,e,n){var i=n("6d8b"),r=n("e0d3"),a=i.each,o=i.isObject,s=["areaStyle","lineStyle","nodeStyle","linkStyle","chordStyle","label","labelLine"];function l(t){var e=t&&t.itemStyle;if(e)for(var n=0,r=s.length;n<r;n++){var a=s[n],o=e.normal,l=e.emphasis;o&&o[a]&&(t[a]=t[a]||{},t[a].normal?i.merge(t[a].normal,o[a]):t[a].normal=o[a],o[a]=null),l&&l[a]&&(t[a]=t[a]||{},t[a].emphasis?i.merge(t[a].emphasis,l[a]):t[a].emphasis=l[a],l[a]=null)}}function u(t,e,n){if(t&&t[e]&&(t[e].normal||t[e].emphasis)){var r=t[e].normal,a=t[e].emphasis;r&&(n?(t[e].normal=t[e].emphasis=null,i.defaults(t[e],r)):t[e]=r),a&&(t.emphasis=t.emphasis||{},t.emphasis[e]=a)}}function h(t){u(t,"itemStyle"),u(t,"lineStyle"),u(t,"areaStyle"),u(t,"label"),u(t,"labelLine"),u(t,"upperLabel"),u(t,"edgeLabel")}function c(t,e){var n=o(t)&&t[e],i=o(n)&&n.textStyle;if(i)for(var a=0,s=r.TEXT_STYLE_OPTIONS.length;a<s;a++){e=r.TEXT_STYLE_OPTIONS[a];i.hasOwnProperty(e)&&(n[e]=i[e])}}function d(t){t&&(h(t),c(t,"label"),t.emphasis&&c(t.emphasis,"label"))}function f(t){if(o(t)){l(t),h(t),c(t,"label"),c(t,"upperLabel"),c(t,"edgeLabel"),t.emphasis&&(c(t.emphasis,"label"),c(t.emphasis,"upperLabel"),c(t.emphasis,"edgeLabel"));var e=t.markPoint;e&&(l(e),d(e));var n=t.markLine;n&&(l(n),d(n));var r=t.markArea;r&&d(r);var a=t.data;if("graph"===t.type){a=a||t.nodes;var s=t.links||t.edges;if(s&&!i.isTypedArray(s))for(var f=0;f<s.length;f++)d(s[f]);i.each(t.categories,(function(t){h(t)}))}if(a&&!i.isTypedArray(a))for(f=0;f<a.length;f++)d(a[f]);e=t.markPoint;if(e&&e.data){var p=e.data;for(f=0;f<p.length;f++)d(p[f])}n=t.markLine;if(n&&n.data){var g=n.data;for(f=0;f<g.length;f++)i.isArray(g[f])?(d(g[f][0]),d(g[f][1])):d(g[f])}"gauge"===t.type?(c(t,"axisLabel"),c(t,"title"),c(t,"detail")):"treemap"===t.type?(u(t.breadcrumb,"itemStyle"),i.each(t.levels,(function(t){h(t)}))):"tree"===t.type&&h(t.leaves)}}function p(t){return i.isArray(t)?t:t?[t]:[]}function g(t){return(i.isArray(t)?t[0]:t)||{}}function v(t,e){a(p(t.series),(function(t){o(t)&&f(t)}));var n=["xAxis","yAxis","radiusAxis","angleAxis","singleAxis","parallelAxis","radar"];e&&n.push("valueAxis","categoryAxis","logAxis","timeAxis"),a(n,(function(e){a(p(t[e]),(function(t){t&&(c(t,"axisLabel"),c(t.axisPointer,"label"))}))})),a(p(t.parallel),(function(t){var e=t&&t.parallelAxisDefault;c(e,"axisLabel"),c(e&&e.axisPointer,"label")})),a(p(t.calendar),(function(t){u(t,"itemStyle"),c(t,"dayLabel"),c(t,"monthLabel"),c(t,"yearLabel")})),a(p(t.radar),(function(t){c(t,"name")})),a(p(t.geo),(function(t){o(t)&&(d(t),a(p(t.regions),(function(t){d(t)})))})),a(p(t.timeline),(function(t){d(t),u(t,"label"),u(t,"itemStyle"),u(t,"controlStyle",!0);var e=t.data;i.isArray(e)&&i.each(e,(function(t){i.isObject(t)&&(u(t,"label"),u(t,"itemStyle"))}))})),a(p(t.toolbox),(function(t){u(t,"iconStyle"),a(t.feature,(function(t){u(t,"iconStyle")}))})),c(g(t.axisPointer),"label"),c(g(t.tooltip).axisPointer,"label")}t.exports=v},"282b":function(t,e,n){var i=n("6d8b");function r(t){for(var e=0;e<t.length;e++)t[e][1]||(t[e][1]=t[e][0]);return function(e,n,r){for(var a={},o=0;o<t.length;o++){var s=t[o][1];if(!(n&&i.indexOf(n,s)>=0||r&&i.indexOf(r,s)<0)){var l=e.getShallow(s);null!=l&&(a[t[o][0]]=l)}}return a}}t.exports=r},"29a8":function(t,e){var n={toolbox:{brush:{title:{rect:"矩形选择",polygon:"圈选",lineX:"横向选择",lineY:"纵向选择",keep:"保持选择",clear:"清除选择"}},dataView:{title:"数据视图",lang:["数据视图","关闭","刷新"]},dataZoom:{title:{zoom:"区域缩放",back:"区域缩放还原"}},magicType:{title:{line:"切换为折线图",bar:"切换为柱状图",stack:"切换为堆叠",tiled:"切换为平铺"}},restore:{title:"还原"},saveAsImage:{title:"保存为图片",lang:["右键另存为图片"]}},series:{typeNames:{pie:"饼图",bar:"柱状图",line:"折线图",scatter:"散点图",effectScatter:"涟漪散点图",radar:"雷达图",tree:"树图",treemap:"矩形树图",boxplot:"箱型图",candlestick:"K线图",k:"K线图",heatmap:"热力图",map:"地图",parallel:"平行坐标图",lines:"线图",graph:"关系图",sankey:"桑基图",funnel:"漏斗图",gauge:"仪表盘图",pictorialBar:"象形柱图",themeRiver:"主题河流图",sunburst:"旭日图"}},aria:{general:{withTitle:"这是一个关于“{title}”的图表。",withoutTitle:"这是一个图表，"},series:{single:{prefix:"",withName:"图表类型是{seriesType}，表示{seriesName}。",withoutName:"图表类型是{seriesType}。"},multiple:{prefix:"它由{seriesCount}个图表系列组成。",withName:"第{seriesId}个系列是一个表示{seriesName}的{seriesType}，",withoutName:"第{seriesId}个系列是一个{seriesType}，",separator:{middle:"；",end:"。"}}},data:{allData:"其数据是——",partialData:"其中，前{displayCnt}项是——",withName:"{name}的数据是{value}",withoutName:"{value}",separator:{middle:"，",end:""}}}};t.exports=n},"2b17":function(t,e,n){var i=n("4e08"),r=(i.__DEV__,n("6d8b")),a=(r.isTypedArray,r.extend),o=(r.assert,r.each),s=r.isObject,l=n("e0d3"),u=l.getDataItemValue,h=l.isDataItemOption,c=n("3842"),d=c.parseDate,f=n("ec6f"),p=n("93d0"),g=p.SOURCE_FORMAT_TYPED_ARRAY,v=p.SOURCE_FORMAT_ARRAY_ROWS,m=p.SOURCE_FORMAT_ORIGINAL,y=p.SOURCE_FORMAT_OBJECT_ROWS;function x(t,e){f.isInstance(t)||(t=f.seriesDataToSource(t)),this._source=t;var n=this._data=t.data,i=t.sourceFormat;i===g&&(this._offset=0,this._dimSize=e,this._data=n);var r=b[i===v?i+"_"+t.seriesLayoutBy:i];a(this,r)}var _=x.prototype;_.pure=!1,_.persistent=!0,_.getSource=function(){return this._source};var b={arrayRows_column:{pure:!0,count:function(){return Math.max(0,this._data.length-this._source.startIndex)},getItem:function(t){return this._data[t+this._source.startIndex]},appendData:M},arrayRows_row:{pure:!0,count:function(){var t=this._data[0];return t?Math.max(0,t.length-this._source.startIndex):0},getItem:function(t){t+=this._source.startIndex;for(var e=[],n=this._data,i=0;i<n.length;i++){var r=n[i];e.push(r?r[t]:null)}return e},appendData:function(){throw new Error('Do not support appendData when set seriesLayoutBy: "row".')}},objectRows:{pure:!0,count:w,getItem:S,appendData:M},keyedColumns:{pure:!0,count:function(){var t=this._source.dimensionsDefine[0].name,e=this._data[t];return e?e.length:0},getItem:function(t){for(var e=[],n=this._source.dimensionsDefine,i=0;i<n.length;i++){var r=this._data[n[i].name];e.push(r?r[t]:null)}return e},appendData:function(t){var e=this._data;o(t,(function(t,n){for(var i=e[n]||(e[n]=[]),r=0;r<(t||[]).length;r++)i.push(t[r])}))}},original:{count:w,getItem:S,appendData:M},typedArray:{persistent:!1,pure:!0,count:function(){return this._data?this._data.length/this._dimSize:0},getItem:function(t,e){t-=this._offset,e=e||[];for(var n=this._dimSize*t,i=0;i<this._dimSize;i++)e[i]=this._data[n+i];return e},appendData:function(t){this._data=t},clean:function(){this._offset+=this.count(),this._data=null}}};function w(){return this._data.length}function S(t){return this._data[t]}function M(t){for(var e=0;e<t.length;e++)this._data.push(t[e])}var T={arrayRows:A,objectRows:function(t,e,n,i){return null!=n?t[i]:t},keyedColumns:A,original:function(t,e,n,i){var r=u(t);return null!=n&&r instanceof Array?r[n]:r},typedArray:A};function A(t,e,n,i){return null!=n?t[n]:t}var C={arrayRows:I,objectRows:function(t,e,n,i){return k(t[e],this._dimensionInfos[e])},keyedColumns:I,original:function(t,e,n,i){var r=t&&(null==t.value?t:t.value);return!this._rawData.pure&&h(t)&&(this.hasItemOption=!0),k(r instanceof Array?r[i]:r,this._dimensionInfos[e])},typedArray:function(t,e,n,i){return t[i]}};function I(t,e,n,i){return k(t[i],this._dimensionInfos[e])}function k(t,e){var n=e&&e.type;if("ordinal"===n){var i=e&&e.ordinalMeta;return i?i.parseAndCollect(t):t}return"time"===n&&"number"!==typeof t&&null!=t&&"-"!==t&&(t=+d(t)),null==t||""===t?NaN:+t}function D(t,e,n){if(t){var i=t.getRawDataItem(e);if(null!=i){var r,a,o=t.getProvider().getSource().sourceFormat,s=t.getDimensionInfo(n);return s&&(r=s.name,a=s.index),T[o](i,e,a,r)}}}function P(t,e,n){if(t){var i=t.getProvider().getSource().sourceFormat;if(i===m||i===y){var r=t.getRawDataItem(e);return i!==m||s(r)||(r=null),r?r[n]:void 0}}}e.DefaultDataProvider=x,e.defaultDimValueGetters=C,e.retrieveRawValue=D,e.retrieveRawAttr=P},"2b61":function(t,e,n){var i=n("7d6d"),r=n("82eb"),a=r.ContextCachedBy,o=[["shadowBlur",0],["shadowOffsetX",0],["shadowOffsetY",0],["shadowColor","#000"],["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]],s=function(t){this.extendFrom(t,!1)};function l(t,e,n){var i=null==e.x?0:e.x,r=null==e.x2?1:e.x2,a=null==e.y?0:e.y,o=null==e.y2?0:e.y2;e.global||(i=i*n.width+n.x,r=r*n.width+n.x,a=a*n.height+n.y,o=o*n.height+n.y),i=isNaN(i)?0:i,r=isNaN(r)?1:r,a=isNaN(a)?0:a,o=isNaN(o)?0:o;var s=t.createLinearGradient(i,a,r,o);return s}function u(t,e,n){var i=n.width,r=n.height,a=Math.min(i,r),o=null==e.x?.5:e.x,s=null==e.y?.5:e.y,l=null==e.r?.5:e.r;e.global||(o=o*i+n.x,s=s*r+n.y,l*=a);var u=t.createRadialGradient(o,s,0,o,s,l);return u}s.prototype={constructor:s,fill:"#000",stroke:null,opacity:1,fillOpacity:null,strokeOpacity:null,lineDash:null,lineDashOffset:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,lineWidth:1,strokeNoScale:!1,text:null,font:null,textFont:null,fontStyle:null,fontWeight:null,fontSize:null,fontFamily:null,textTag:null,textFill:"#000",textStroke:null,textWidth:null,textHeight:null,textStrokeWidth:0,textLineHeight:null,textPosition:"inside",textRect:null,textOffset:null,textAlign:null,textVerticalAlign:null,textDistance:5,textShadowColor:"transparent",textShadowBlur:0,textShadowOffsetX:0,textShadowOffsetY:0,textBoxShadowColor:"transparent",textBoxShadowBlur:0,textBoxShadowOffsetX:0,textBoxShadowOffsetY:0,transformText:!1,textRotation:0,textOrigin:null,textBackgroundColor:null,textBorderColor:null,textBorderWidth:0,textBorderRadius:0,textPadding:null,rich:null,truncate:null,blend:null,bind:function(t,e,n){var r=this,s=n&&n.style,l=!s||t.__attrCachedBy!==a.STYLE_BIND;t.__attrCachedBy=a.STYLE_BIND;for(var u=0;u<o.length;u++){var h=o[u],c=h[0];(l||r[c]!==s[c])&&(t[c]=i(t,c,r[c]||h[1]))}if((l||r.fill!==s.fill)&&(t.fillStyle=r.fill),(l||r.stroke!==s.stroke)&&(t.strokeStyle=r.stroke),(l||r.opacity!==s.opacity)&&(t.globalAlpha=null==r.opacity?1:r.opacity),(l||r.blend!==s.blend)&&(t.globalCompositeOperation=r.blend||"source-over"),this.hasStroke()){var d=r.lineWidth;t.lineWidth=d/(this.strokeNoScale&&e&&e.getLineScale?e.getLineScale():1)}},hasFill:function(){var t=this.fill;return null!=t&&"none"!==t},hasStroke:function(){var t=this.stroke;return null!=t&&"none"!==t&&this.lineWidth>0},extendFrom:function(t,e){if(t)for(var n in t)!t.hasOwnProperty(n)||!0!==e&&(!1===e?this.hasOwnProperty(n):null==t[n])||(this[n]=t[n])},set:function(t,e){"string"===typeof t?this[t]=e:this.extendFrom(t,!0)},clone:function(){var t=new this.constructor;return t.extendFrom(this,!0),t},getGradient:function(t,e,n){for(var i="radial"===e.type?u:l,r=i(t,e,n),a=e.colorStops,o=0;o<a.length;o++)r.addColorStop(a[o].offset,a[o].color);return r}};for(var h=s.prototype,c=0;c<o.length;c++){var d=o[c];d[0]in h||(h[d[0]]=d[1])}s.getGradient=h.getGradient;var f=s;t.exports=f},"2cf4":function(t,e){var n=1;"undefined"!==typeof window&&(n=Math.max(window.devicePixelRatio||1,1));var i=0,r=n;e.debugMode=i,e.devicePixelRatio=r},"2f45":function(t,e,n){var i=n("6d8b"),r=i.each,a=i.createHashMap,o=(i.assert,n("4e08")),s=(o.__DEV__,a(["tooltip","label","itemName","itemId","seriesName"]));function l(t){var e={},n=e.encode={},i=a(),o=[],l=[];r(t.dimensions,(function(e){var r=t.getDimensionInfo(e),a=r.coordDim;if(a){var u=n[a];n.hasOwnProperty(a)||(u=n[a]=[]),u[r.coordDimIndex]=e,r.isExtraCoord||(i.set(a,1),h(r.type)&&(o[0]=e)),r.defaultTooltip&&l.push(e)}s.each((function(t,e){var i=n[e];n.hasOwnProperty(e)||(i=n[e]=[]);var a=r.otherDims[e];null!=a&&!1!==a&&(i[a]=r.name)}))}));var u=[],c={};i.each((function(t,e){var i=n[e];c[e]=i[0],u=u.concat(i)})),e.dataDimsOnCoord=u,e.encodeFirstDimNotExtra=c;var d=n.label;d&&d.length&&(o=d.slice());var f=n.tooltip;return f&&f.length?l=f.slice():l.length||(l=o.slice()),n.defaultedLabel=o,n.defaultedTooltip=l,e}function u(t){return"category"===t?"ordinal":"time"===t?"time":"float"}function h(t){return!("ordinal"===t||"time"===t)}e.OTHER_DIMENSIONS=s,e.summarizeDimensions=l,e.getDimensionTypeByAxis=u},"2f73":function(t,e,n){var i=n("3eba"),r=n("6d8b"),a=n("1e32");n("1ccf"),n("f5e6"),n("792e"),n("cb8f"),n("6acf"),i.registerLayout(r.curry(a,"bar")),i.extendComponentView({type:"polar"})},3041:function(t,e,n){var i=n("e1fc"),r=n("0da8"),a=n("76a5"),o=n("d9fc"),s=n("c7a2"),l=n("ae69"),u=n("cb11"),h=n("cbe5"),c=n("87b1"),d=n("d498"),f=n("48a9"),p=n("2b61"),g=n("1687"),v=n("342d"),m=v.createFromString,y=n("6d8b"),x=y.isString,_=y.extend,b=y.defaults,w=y.trim,S=y.each,M=/[\s,]+/;function T(t){if(x(t)){var e=new DOMParser;t=e.parseFromString(t,"text/xml")}9===t.nodeType&&(t=t.firstChild);while("svg"!==t.nodeName.toLowerCase()||1!==t.nodeType)t=t.nextSibling;return t}function A(){this._defs={},this._root=null,this._isDefine=!1,this._isText=!1}A.prototype.parse=function(t,e){e=e||{};var n=T(t);if(!n)throw new Error("Illegal svg");var r=new i;this._root=r;var a=n.getAttribute("viewBox")||"",o=parseFloat(n.getAttribute("width")||e.width),l=parseFloat(n.getAttribute("height")||e.height);isNaN(o)&&(o=null),isNaN(l)&&(l=null),L(n,r,null,!0);var u,h,c=n.firstChild;while(c)this._parseNode(c,r),c=c.nextSibling;if(a){var d=w(a).split(M);d.length>=4&&(u={x:parseFloat(d[0]||0),y:parseFloat(d[1]||0),width:parseFloat(d[2]),height:parseFloat(d[3])})}if(u&&null!=o&&null!=l&&(h=V(u,o,l),!e.ignoreViewBox)){var f=r;r=new i,r.add(f),f.scale=h.scale.slice(),f.position=h.position.slice()}return e.ignoreRootClip||null==o||null==l||r.setClipPath(new s({shape:{x:0,y:0,width:o,height:l}})),{root:r,width:o,height:l,viewBoxRect:u,viewBoxTransform:h}},A.prototype._parseNode=function(t,e){var n,i=t.nodeName.toLowerCase();if("defs"===i?this._isDefine=!0:"text"===i&&(this._isText=!0),this._isDefine){var r=I[i];if(r){var a=r.call(this,t),o=t.getAttribute("id");o&&(this._defs[o]=a)}}else{r=C[i];r&&(n=r.call(this,t,e),e.add(n))}var s=t.firstChild;while(s)1===s.nodeType&&this._parseNode(s,n),3===s.nodeType&&this._isText&&this._parseText(s,n),s=s.nextSibling;"defs"===i?this._isDefine=!1:"text"===i&&(this._isText=!1)},A.prototype._parseText=function(t,e){if(1===t.nodeType){var n=t.getAttribute("dx")||0,i=t.getAttribute("dy")||0;this._textX+=parseFloat(n),this._textY+=parseFloat(i)}var r=new a({style:{text:t.textContent,transformText:!0},position:[this._textX||0,this._textY||0]});D(e,r),L(t,r,this._defs);var o=r.style.fontSize;o&&o<9&&(r.style.fontSize=9,r.scale=r.scale||[1,1],r.scale[0]*=o/9,r.scale[1]*=o/9);var s=r.getBoundingRect();return this._textX+=s.width,e.add(r),r};var C={g:function(t,e){var n=new i;return D(e,n),L(t,n,this._defs),n},rect:function(t,e){var n=new s;return D(e,n),L(t,n,this._defs),n.setShape({x:parseFloat(t.getAttribute("x")||0),y:parseFloat(t.getAttribute("y")||0),width:parseFloat(t.getAttribute("width")||0),height:parseFloat(t.getAttribute("height")||0)}),n},circle:function(t,e){var n=new o;return D(e,n),L(t,n,this._defs),n.setShape({cx:parseFloat(t.getAttribute("cx")||0),cy:parseFloat(t.getAttribute("cy")||0),r:parseFloat(t.getAttribute("r")||0)}),n},line:function(t,e){var n=new u;return D(e,n),L(t,n,this._defs),n.setShape({x1:parseFloat(t.getAttribute("x1")||0),y1:parseFloat(t.getAttribute("y1")||0),x2:parseFloat(t.getAttribute("x2")||0),y2:parseFloat(t.getAttribute("y2")||0)}),n},ellipse:function(t,e){var n=new l;return D(e,n),L(t,n,this._defs),n.setShape({cx:parseFloat(t.getAttribute("cx")||0),cy:parseFloat(t.getAttribute("cy")||0),rx:parseFloat(t.getAttribute("rx")||0),ry:parseFloat(t.getAttribute("ry")||0)}),n},polygon:function(t,e){var n=t.getAttribute("points");n&&(n=P(n));var i=new c({shape:{points:n||[]}});return D(e,i),L(t,i,this._defs),i},polyline:function(t,e){var n=new h;D(e,n),L(t,n,this._defs);var i=t.getAttribute("points");i&&(i=P(i));var r=new d({shape:{points:i||[]}});return r},image:function(t,e){var n=new r;return D(e,n),L(t,n,this._defs),n.setStyle({image:t.getAttribute("xlink:href"),x:t.getAttribute("x"),y:t.getAttribute("y"),width:t.getAttribute("width"),height:t.getAttribute("height")}),n},text:function(t,e){var n=t.getAttribute("x")||0,r=t.getAttribute("y")||0,a=t.getAttribute("dx")||0,o=t.getAttribute("dy")||0;this._textX=parseFloat(n)+parseFloat(a),this._textY=parseFloat(r)+parseFloat(o);var s=new i;return D(e,s),L(t,s,this._defs),s},tspan:function(t,e){var n=t.getAttribute("x"),r=t.getAttribute("y");null!=n&&(this._textX=parseFloat(n)),null!=r&&(this._textY=parseFloat(r));var a=t.getAttribute("dx")||0,o=t.getAttribute("dy")||0,s=new i;return D(e,s),L(t,s,this._defs),this._textX+=a,this._textY+=o,s},path:function(t,e){var n=t.getAttribute("d")||"",i=m(n);return D(e,i),L(t,i,this._defs),i}},I={lineargradient:function(t){var e=parseInt(t.getAttribute("x1")||0,10),n=parseInt(t.getAttribute("y1")||0,10),i=parseInt(t.getAttribute("x2")||10,10),r=parseInt(t.getAttribute("y2")||0,10),a=new f(e,n,i,r);return k(t,a),a},radialgradient:function(t){}};function k(t,e){var n=t.firstChild;while(n){if(1===n.nodeType){var i=n.getAttribute("offset");i=i.indexOf("%")>0?parseInt(i,10)/100:i?parseFloat(i):0;var r=n.getAttribute("stop-color")||"#000000";e.addColorStop(i,r)}n=n.nextSibling}}function D(t,e){t&&t.__inheritedStyle&&(e.__inheritedStyle||(e.__inheritedStyle={}),b(e.__inheritedStyle,t.__inheritedStyle))}function P(t){for(var e=w(t).split(M),n=[],i=0;i<e.length;i+=2){var r=parseFloat(e[i]),a=parseFloat(e[i+1]);n.push([r,a])}return n}var O={fill:"fill",stroke:"stroke","stroke-width":"lineWidth",opacity:"opacity","fill-opacity":"fillOpacity","stroke-opacity":"strokeOpacity","stroke-dasharray":"lineDash","stroke-dashoffset":"lineDashOffset","stroke-linecap":"lineCap","stroke-linejoin":"lineJoin","stroke-miterlimit":"miterLimit","font-family":"fontFamily","font-size":"fontSize","font-style":"fontStyle","font-weight":"fontWeight","text-align":"textAlign","alignment-baseline":"textBaseline"};function L(t,e,n,i){var r=e.__inheritedStyle||{},a="text"===e.type;if(1===t.nodeType&&(N(t,e),_(r,F(t)),!i))for(var o in O)if(O.hasOwnProperty(o)){var s=t.getAttribute(o);null!=s&&(r[O[o]]=s)}var l=a?"textFill":"fill",u=a?"textStroke":"stroke";e.style=e.style||new p;var h=e.style;null!=r.fill&&h.set(l,R(r.fill,n)),null!=r.stroke&&h.set(u,R(r.stroke,n)),S(["lineWidth","opacity","fillOpacity","strokeOpacity","miterLimit","fontSize"],(function(t){var e="lineWidth"===t&&a?"textStrokeWidth":t;null!=r[t]&&h.set(e,parseFloat(r[t]))})),r.textBaseline&&"auto"!==r.textBaseline||(r.textBaseline="alphabetic"),"alphabetic"===r.textBaseline&&(r.textBaseline="bottom"),"start"===r.textAlign&&(r.textAlign="left"),"end"===r.textAlign&&(r.textAlign="right"),S(["lineDashOffset","lineCap","lineJoin","fontWeight","fontFamily","fontStyle","textAlign","textBaseline"],(function(t){null!=r[t]&&h.set(t,r[t])})),r.lineDash&&(e.style.lineDash=w(r.lineDash).split(M)),h[u]&&"none"!==h[u]&&(e[u]=!0),e.__inheritedStyle=r}var E=/url\(\s*#(.*?)\)/;function R(t,e){var n=e&&t&&t.match(E);if(n){var i=w(n[1]),r=e[i];return r}return t}var B=/(translate|scale|rotate|skewX|skewY|matrix)\(([\-\s0-9\.e,]*)\)/g;function N(t,e){var n=t.getAttribute("transform");if(n){n=n.replace(/,/g," ");var i=null,r=[];n.replace(B,(function(t,e,n){r.push(e,n)}));for(var a=r.length-1;a>0;a-=2){var o=r[a],s=r[a-1];switch(i=i||g.create(),s){case"translate":o=w(o).split(M),g.translate(i,i,[parseFloat(o[0]),parseFloat(o[1]||0)]);break;case"scale":o=w(o).split(M),g.scale(i,i,[parseFloat(o[0]),parseFloat(o[1]||o[0])]);break;case"rotate":o=w(o).split(M),g.rotate(i,i,parseFloat(o[0]));break;case"skew":o=w(o).split(M),console.warn("Skew transform is not supported yet");break;case"matrix":o=w(o).split(M);i[0]=parseFloat(o[0]),i[1]=parseFloat(o[1]),i[2]=parseFloat(o[2]),i[3]=parseFloat(o[3]),i[4]=parseFloat(o[4]),i[5]=parseFloat(o[5]);break}}e.setLocalTransform(i)}}var z=/([^\s:;]+)\s*:\s*([^:;]+)/g;function F(t){var e=t.getAttribute("style"),n={};if(!e)return n;var i,r={};z.lastIndex=0;while(null!=(i=z.exec(e)))r[i[1]]=i[2];for(var a in O)O.hasOwnProperty(a)&&null!=r[a]&&(n[O[a]]=r[a]);return n}function V(t,e,n){var i=e/t.width,r=n/t.height,a=Math.min(i,r),o=[a,a],s=[-(t.x+t.width/2)*a+e/2,-(t.y+t.height/2)*a+n/2];return{scale:o,position:s}}function H(t,e){var n=new A;return n.parse(t,e)}e.parseXML=T,e.makeViewBoxTransform=V,e.parseSVG=H},"30a3":function(t,e,n){var i=n("6d8b"),r=n("607d"),a=r.Dispatcher,o=n("98b7"),s=n("06ad"),l=function(t){t=t||{},this.stage=t.stage||{},this.onframe=t.onframe||function(){},this._clips=[],this._running=!1,this._time,this._pausedTime,this._pauseStart,this._paused=!1,a.call(this)};l.prototype={constructor:l,addClip:function(t){this._clips.push(t)},addAnimator:function(t){t.animation=this;for(var e=t.getClips(),n=0;n<e.length;n++)this.addClip(e[n])},removeClip:function(t){var e=i.indexOf(this._clips,t);e>=0&&this._clips.splice(e,1)},removeAnimator:function(t){for(var e=t.getClips(),n=0;n<e.length;n++)this.removeClip(e[n]);t.animation=null},_update:function(){for(var t=(new Date).getTime()-this._pausedTime,e=t-this._time,n=this._clips,i=n.length,r=[],a=[],o=0;o<i;o++){var s=n[o],l=s.step(t,e);l&&(r.push(l),a.push(s))}for(o=0;o<i;)n[o]._needsRemove?(n[o]=n[i-1],n.pop(),i--):o++;i=r.length;for(o=0;o<i;o++)a[o].fire(r[o]);this._time=t,this.onframe(e),this.trigger("frame",e),this.stage.update&&this.stage.update()},_startLoop:function(){var t=this;function e(){t._running&&(o(e),!t._paused&&t._update())}this._running=!0,o(e)},start:function(){this._time=(new Date).getTime(),this._pausedTime=0,this._startLoop()},stop:function(){this._running=!1},pause:function(){this._paused||(this._pauseStart=(new Date).getTime(),this._paused=!0)},resume:function(){this._paused&&(this._pausedTime+=(new Date).getTime()-this._pauseStart,this._paused=!1)},clear:function(){this._clips=[]},isFinished:function(){return!this._clips.length},animate:function(t,e){e=e||{};var n=new s(t,e.loop,e.getter,e.setter);return this.addAnimator(n),n}},i.mixin(l,a);var u=l;t.exports=u},3301:function(t,e,n){var i=n("6d8b"),r=n("6179"),a=n("b1d4"),o=n("93d0"),s=o.SOURCE_FORMAT_ORIGINAL,l=n("2f45"),u=l.getDimensionTypeByAxis,h=n("e0d3"),c=h.getDataItemValue,d=n("2039"),f=n("8b7f"),p=f.getCoordSysDefineBySeries,g=n("ec6f"),v=n("ee1a"),m=v.enableDataStack;function y(t,e,n){n=n||{},g.isInstance(t)||(t=g.seriesDataToSource(t));var o,s=e.get("coordinateSystem"),l=d.get(s),h=p(e);h&&(o=i.map(h.coordSysDims,(function(t){var e={name:t},n=h.axisMap.get(t);if(n){var i=n.get("type");e.type=u(i)}return e}))),o||(o=l&&(l.getDimensionsInfo?l.getDimensionsInfo():l.dimensions.slice())||["x","y"]);var c,f,v=a(t,{coordDimensions:o,generateCoord:n.generateCoord});h&&i.each(v,(function(t,e){var n=t.coordDim,i=h.categoryAxisMap.get(n);i&&(null==c&&(c=e),t.ordinalMeta=i.getOrdinalMeta()),null!=t.otherDims.itemName&&(f=!0)})),f||null==c||(v[c].otherDims.itemName=0);var y=m(e,v),_=new r(v,e);_.setCalculationInfo(y);var b=null!=c&&x(t)?function(t,e,n,i){return i===c?n:this.defaultDimValueGetter(t,e,n,i)}:null;return _.hasItemOption=!1,_.initData(t,null,b),_}function x(t){if(t.sourceFormat===s){var e=_(t.data||[]);return null!=e&&!i.isArray(c(e))}}function _(t){var e=0;while(e<t.length&&null==t[e])e++;return t[e]}var b=y;t.exports=b},"342d":function(t,e,n){var i=n("cbe5"),r=n("20c8"),a=n("ee84"),o=Math.sqrt,s=Math.sin,l=Math.cos,u=Math.PI,h=function(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])},c=function(t,e){return(t[0]*e[0]+t[1]*e[1])/(h(t)*h(e))},d=function(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(c(t,e))};function f(t,e,n,i,r,a,h,f,p,g,v){var m=p*(u/180),y=l(m)*(t-n)/2+s(m)*(e-i)/2,x=-1*s(m)*(t-n)/2+l(m)*(e-i)/2,_=y*y/(h*h)+x*x/(f*f);_>1&&(h*=o(_),f*=o(_));var b=(r===a?-1:1)*o((h*h*(f*f)-h*h*(x*x)-f*f*(y*y))/(h*h*(x*x)+f*f*(y*y)))||0,w=b*h*x/f,S=b*-f*y/h,M=(t+n)/2+l(m)*w-s(m)*S,T=(e+i)/2+s(m)*w+l(m)*S,A=d([1,0],[(y-w)/h,(x-S)/f]),C=[(y-w)/h,(x-S)/f],I=[(-1*y-w)/h,(-1*x-S)/f],k=d(C,I);c(C,I)<=-1&&(k=u),c(C,I)>=1&&(k=0),0===a&&k>0&&(k-=2*u),1===a&&k<0&&(k+=2*u),v.addData(g,M,T,h,f,A,k,m,a)}var p=/([mlvhzcqtsa])([^mlvhzcqtsa]*)/gi,g=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;function v(t){if(!t)return new r;for(var e,n=0,i=0,a=n,o=i,s=new r,l=r.CMD,u=t.match(p),h=0;h<u.length;h++){for(var c,d=u[h],v=d.charAt(0),m=d.match(g)||[],y=m.length,x=0;x<y;x++)m[x]=parseFloat(m[x]);var _=0;while(_<y){var b,w,S,M,T,A,C,I=n,k=i;switch(v){case"l":n+=m[_++],i+=m[_++],c=l.L,s.addData(c,n,i);break;case"L":n=m[_++],i=m[_++],c=l.L,s.addData(c,n,i);break;case"m":n+=m[_++],i+=m[_++],c=l.M,s.addData(c,n,i),a=n,o=i,v="l";break;case"M":n=m[_++],i=m[_++],c=l.M,s.addData(c,n,i),a=n,o=i,v="L";break;case"h":n+=m[_++],c=l.L,s.addData(c,n,i);break;case"H":n=m[_++],c=l.L,s.addData(c,n,i);break;case"v":i+=m[_++],c=l.L,s.addData(c,n,i);break;case"V":i=m[_++],c=l.L,s.addData(c,n,i);break;case"C":c=l.C,s.addData(c,m[_++],m[_++],m[_++],m[_++],m[_++],m[_++]),n=m[_-2],i=m[_-1];break;case"c":c=l.C,s.addData(c,m[_++]+n,m[_++]+i,m[_++]+n,m[_++]+i,m[_++]+n,m[_++]+i),n+=m[_-2],i+=m[_-1];break;case"S":b=n,w=i;var D=s.len(),P=s.data;e===l.C&&(b+=n-P[D-4],w+=i-P[D-3]),c=l.C,I=m[_++],k=m[_++],n=m[_++],i=m[_++],s.addData(c,b,w,I,k,n,i);break;case"s":b=n,w=i;D=s.len(),P=s.data;e===l.C&&(b+=n-P[D-4],w+=i-P[D-3]),c=l.C,I=n+m[_++],k=i+m[_++],n+=m[_++],i+=m[_++],s.addData(c,b,w,I,k,n,i);break;case"Q":I=m[_++],k=m[_++],n=m[_++],i=m[_++],c=l.Q,s.addData(c,I,k,n,i);break;case"q":I=m[_++]+n,k=m[_++]+i,n+=m[_++],i+=m[_++],c=l.Q,s.addData(c,I,k,n,i);break;case"T":b=n,w=i;D=s.len(),P=s.data;e===l.Q&&(b+=n-P[D-4],w+=i-P[D-3]),n=m[_++],i=m[_++],c=l.Q,s.addData(c,b,w,n,i);break;case"t":b=n,w=i;D=s.len(),P=s.data;e===l.Q&&(b+=n-P[D-4],w+=i-P[D-3]),n+=m[_++],i+=m[_++],c=l.Q,s.addData(c,b,w,n,i);break;case"A":S=m[_++],M=m[_++],T=m[_++],A=m[_++],C=m[_++],I=n,k=i,n=m[_++],i=m[_++],c=l.A,f(I,k,n,i,A,C,S,M,T,c,s);break;case"a":S=m[_++],M=m[_++],T=m[_++],A=m[_++],C=m[_++],I=n,k=i,n+=m[_++],i+=m[_++],c=l.A,f(I,k,n,i,A,C,S,M,T,c,s);break}}"z"!==v&&"Z"!==v||(c=l.Z,s.addData(c),n=a,i=o),e=c}return s.toStatic(),s}function m(t,e){var n=v(t);return e=e||{},e.buildPath=function(t){if(t.setData){t.setData(n.data);var e=t.getContext();e&&t.rebuildPath(e)}else{e=t;n.rebuildPath(e)}},e.applyTransform=function(t){a(n,t),this.dirty(!0)},e}function y(t,e){return new i(m(t,e))}function x(t,e){return i.extend(m(t,e))}function _(t,e){for(var n=[],r=t.length,a=0;a<r;a++){var o=t[a];o.path||o.createPathProxy(),o.__dirtyPath&&o.buildPath(o.path,o.shape,!0),n.push(o.path)}var s=new i(e);return s.createPathProxy(),s.buildPath=function(t){t.appendPath(n);var e=t.getContext();e&&t.rebuildPath(e)},s}e.createFromString=y,e.extendFromString=x,e.mergePath=_},3842:function(t,e,n){var i=n("6d8b"),r=1e-4;function a(t){return t.replace(/^\s+/,"").replace(/\s+$/,"")}function o(t,e,n,i){var r=e[1]-e[0],a=n[1]-n[0];if(0===r)return 0===a?n[0]:(n[0]+n[1])/2;if(i)if(r>0){if(t<=e[0])return n[0];if(t>=e[1])return n[1]}else{if(t>=e[0])return n[0];if(t<=e[1])return n[1]}else{if(t===e[0])return n[0];if(t===e[1])return n[1]}return(t-e[0])/r*a+n[0]}function s(t,e){switch(t){case"center":case"middle":t="50%";break;case"left":case"top":t="0%";break;case"right":case"bottom":t="100%";break}return"string"===typeof t?a(t).match(/%$/)?parseFloat(t)/100*e:parseFloat(t):null==t?NaN:+t}function l(t,e,n){return null==e&&(e=10),e=Math.min(Math.max(0,e),20),t=(+t).toFixed(e),n?t:+t}function u(t){return t.sort((function(t,e){return t-e})),t}function h(t){if(t=+t,isNaN(t))return 0;var e=1,n=0;while(Math.round(t*e)/e!==t)e*=10,n++;return n}function c(t){var e=t.toString(),n=e.indexOf("e");if(n>0){var i=+e.slice(n+1);return i<0?-i:0}var r=e.indexOf(".");return r<0?0:e.length-1-r}function d(t,e){var n=Math.log,i=Math.LN10,r=Math.floor(n(t[1]-t[0])/i),a=Math.round(n(Math.abs(e[1]-e[0]))/i),o=Math.min(Math.max(-r+a,0),20);return isFinite(o)?o:20}function f(t,e,n){if(!t[e])return 0;var r=i.reduce(t,(function(t,e){return t+(isNaN(e)?0:e)}),0);if(0===r)return 0;var a=Math.pow(10,n),o=i.map(t,(function(t){return(isNaN(t)?0:t)/r*a*100})),s=100*a,l=i.map(o,(function(t){return Math.floor(t)})),u=i.reduce(l,(function(t,e){return t+e}),0),h=i.map(o,(function(t,e){return t-l[e]}));while(u<s){for(var c=Number.NEGATIVE_INFINITY,d=null,f=0,p=h.length;f<p;++f)h[f]>c&&(c=h[f],d=f);++l[d],h[d]=0,++u}return l[e]/a}var p=9007199254740991;function g(t){var e=2*Math.PI;return(t%e+e)%e}function v(t){return t>-r&&t<r}var m=/^(?:(\d{4})(?:[-\/](\d{1,2})(?:[-\/](\d{1,2})(?:[T ](\d{1,2})(?::(\d\d)(?::(\d\d)(?:[.,](\d+))?)?)?(Z|[\+\-]\d\d:?\d\d)?)?)?)?)?$/;function y(t){if(t instanceof Date)return t;if("string"===typeof t){var e=m.exec(t);if(!e)return new Date(NaN);if(e[8]){var n=+e[4]||0;return"Z"!==e[8].toUpperCase()&&(n-=e[8].slice(0,3)),new Date(Date.UTC(+e[1],+(e[2]||1)-1,+e[3]||1,n,+(e[5]||0),+e[6]||0,+e[7]||0))}return new Date(+e[1],+(e[2]||1)-1,+e[3]||1,+e[4]||0,+(e[5]||0),+e[6]||0,+e[7]||0)}return null==t?new Date(NaN):new Date(Math.round(t))}function x(t){return Math.pow(10,_(t))}function _(t){return Math.floor(Math.log(t)/Math.LN10)}function b(t,e){var n,i=_(t),r=Math.pow(10,i),a=t/r;return n=e?a<1.5?1:a<2.5?2:a<4?3:a<7?5:10:a<1?1:a<2?2:a<3?3:a<5?5:10,t=n*r,i>=-20?+t.toFixed(i<0?-i:0):t}function w(t,e){var n=(t.length-1)*e+1,i=Math.floor(n),r=+t[i-1],a=n-i;return a?r+a*(t[i]-r):r}function S(t){t.sort((function(t,e){return s(t,e,0)?-1:1}));for(var e=-1/0,n=1,i=0;i<t.length;){for(var r=t[i].interval,a=t[i].close,o=0;o<2;o++)r[o]<=e&&(r[o]=e,a[o]=o?1:1-n),e=r[o],n=a[o];r[0]===r[1]&&a[0]*a[1]!==1?t.splice(i,1):i++}return t;function s(t,e,n){return t.interval[n]<e.interval[n]||t.interval[n]===e.interval[n]&&(t.close[n]-e.close[n]===(n?-1:1)||!n&&s(t,e,1))}}function M(t){return t-parseFloat(t)>=0}e.linearMap=o,e.parsePercent=s,e.round=l,e.asc=u,e.getPrecision=h,e.getPrecisionSafe=c,e.getPixelPrecision=d,e.getPercentWithPrecision=f,e.MAX_SAFE_INTEGER=p,e.remRadian=g,e.isRadianAroundZero=v,e.parseDate=y,e.quantity=x,e.nice=b,e.quantile=w,e.reformIntervals=S,e.isNumeric=M},"38a2":function(t,e,n){var i=n("2b17"),r=i.retrieveRawValue,a=n("eda2"),o=a.getTooltipMarker,s=a.formatTpl,l=n("e0d3"),u=l.getTooltipRenderMode,h=/\{@(.+?)\}/g,c={getDataParams:function(t,e){var n=this.getData(e),i=this.getRawValue(t,e),r=n.getRawIndex(t),a=n.getName(t),s=n.getRawDataItem(t),l=n.getItemVisual(t,"color"),h=this.ecModel.getComponent("tooltip"),c=h&&h.get("renderMode"),d=u(c),f=this.mainType,p="series"===f;return{componentType:f,componentSubType:this.subType,componentIndex:this.componentIndex,seriesType:p?this.subType:null,seriesIndex:this.seriesIndex,seriesId:p?this.id:null,seriesName:p?this.name:null,name:a,dataIndex:r,data:s,dataType:e,value:i,color:l,marker:o({color:l,renderMode:d}),$vars:["seriesName","name","value"]}},getFormattedLabel:function(t,e,n,i,a){e=e||"normal";var o=this.getData(n),l=o.getItemModel(t),u=this.getDataParams(t,n);null!=i&&u.value instanceof Array&&(u.value=u.value[i]);var c=l.get("normal"===e?[a||"label","formatter"]:[e,a||"label","formatter"]);if("function"===typeof c)return u.status=e,c(u);if("string"===typeof c){var d=s(c,u);return d.replace(h,(function(e,n){var i=n.length;return"["===n.charAt(0)&&"]"===n.charAt(i-1)&&(n=+n.slice(1,i-1)),r(o,t,n)}))}},getRawValue:function(t,e){return r(this.getData(e),t)},formatTooltip:function(){}};t.exports=c},3901:function(t,e,n){var i=n("282b"),r=i([["lineWidth","width"],["stroke","color"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"]]),a={getLineStyle:function(t){var e=r(this,t),n=this.getLineDash(e.lineWidth);return n&&(e.lineDash=n),e},getLineDash:function(t){null==t&&(t=1);var e=this.get("type"),n=Math.max(t,2),i=4*t;return"solid"===e||null==e?null:"dashed"===e?[i,i]:[n,n]}};t.exports=a},"392f":function(t,e,n){var i=n("6d8b"),r=i.inherits,a=n("19eb"),o=n("9850");function s(t){a.call(this,t),this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.notClear=!0}s.prototype.incremental=!0,s.prototype.clearDisplaybles=function(){this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.dirty(),this.notClear=!1},s.prototype.addDisplayable=function(t,e){e?this._temporaryDisplayables.push(t):this._displayables.push(t),this.dirty()},s.prototype.addDisplayables=function(t,e){e=e||!1;for(var n=0;n<t.length;n++)this.addDisplayable(t[n],e)},s.prototype.eachPendingDisplayable=function(t){for(var e=this._cursor;e<this._displayables.length;e++)t&&t(this._displayables[e]);for(e=0;e<this._temporaryDisplayables.length;e++)t&&t(this._temporaryDisplayables[e])},s.prototype.update=function(){this.updateTransform();for(var t=this._cursor;t<this._displayables.length;t++){var e=this._displayables[t];e.parent=this,e.update(),e.parent=null}for(t=0;t<this._temporaryDisplayables.length;t++){e=this._temporaryDisplayables[t];e.parent=this,e.update(),e.parent=null}},s.prototype.brush=function(t,e){for(var n=this._cursor;n<this._displayables.length;n++){var i=this._displayables[n];i.beforeBrush&&i.beforeBrush(t),i.brush(t,n===this._cursor?null:this._displayables[n-1]),i.afterBrush&&i.afterBrush(t)}this._cursor=n;for(n=0;n<this._temporaryDisplayables.length;n++){i=this._temporaryDisplayables[n];i.beforeBrush&&i.beforeBrush(t),i.brush(t,0===n?null:this._temporaryDisplayables[n-1]),i.afterBrush&&i.afterBrush(t)}this._temporaryDisplayables=[],this.notClear=!0};var l=[];s.prototype.getBoundingRect=function(){if(!this._rect){for(var t=new o(1/0,1/0,-1/0,-1/0),e=0;e<this._displayables.length;e++){var n=this._displayables[e],i=n.getBoundingRect().clone();n.needLocalTransform()&&i.applyTransform(n.getLocalTransform(l)),t.union(i)}this._rect=t}return this._rect},s.prototype.contain=function(t,e){var n=this.transformCoordToLocal(t,e),i=this.getBoundingRect();if(i.contain(n[0],n[1]))for(var r=0;r<this._displayables.length;r++){var a=this._displayables[r];if(a.contain(t,e))return!0}return!1},r(s,a);var u=s;t.exports=u},"3eba":function(t,e,n){var i=n("4e08"),r=(i.__DEV__,n("697e7")),a=n("6d8b"),o=n("41ef"),s=n("22d1"),l=n("04f6"),u=n("1fab"),h=n("7e63"),c=n("843e"),d=n("2039"),f=n("ca98"),p=n("fb05"),g=n("d15d"),v=n("6cb7"),m=n("4f85"),y=n("b12f"),x=n("e887"),_=n("2306"),b=n("e0d3"),w=n("88b3"),S=w.throttle,M=n("fd63"),T=n("b809"),A=n("998c"),C=n("69ff"),I=n("c533"),k=n("f219");n("0352");var D=n("ec34"),P=a.assert,O=a.each,L=a.isFunction,E=a.isObject,R=v.parseClassType,B="4.2.1",N={zrender:"4.0.6"},z=1,F=1e3,V=5e3,H=1e3,W=2e3,G=3e3,U=4e3,q=5e3,Y={PROCESSOR:{FILTER:F,STATISTIC:V},VISUAL:{LAYOUT:H,GLOBAL:W,CHART:G,COMPONENT:U,BRUSH:q}},X="__flagInMainProcess",j="__optionUpdated",Z=/^[a-zA-Z0-9_]+$/;function K(t){return function(e,n,i){e=e&&e.toLowerCase(),u.prototype[t].call(this,e,n,i)}}function $(){u.call(this)}function Q(t,e,n){n=n||{},"string"===typeof e&&(e=Ct[e]),this.id,this.group,this._dom=t;var i="canvas",o=this._zr=r.init(t,{renderer:n.renderer||i,devicePixelRatio:n.devicePixelRatio,width:n.width,height:n.height});this._throttledZrFlush=S(a.bind(o.flush,o),17);e=a.clone(e);e&&p(e,!0),this._theme=e,this._chartsViews=[],this._chartsMap={},this._componentsViews=[],this._componentsMap={},this._coordSysMgr=new d;var s=this._api=xt(this);function h(t,e){return t.__prio-e.__prio}l(At,h),l(St,h),this._scheduler=new C(this,s,St,At),u.call(this,this._ecEventProcessor=new _t),this._messageCenter=new $,this._initEvents(),this.resize=a.bind(this.resize,this),this._pendingActions=[],o.animation.on("frame",this._onframe,this),lt(o,this),a.setAsPrimitive(this)}$.prototype.on=K("on"),$.prototype.off=K("off"),$.prototype.one=K("one"),a.mixin($,u);var J=Q.prototype;function tt(t,e,n){var i,r=this._model,a=this._coordSysMgr.getCoordinateSystems();e=b.parseFinder(r,e);for(var o=0;o<a.length;o++){var s=a[o];if(s[t]&&null!=(i=s[t](r,e,n)))return i}}J._onframe=function(){if(!this._disposed){var t=this._scheduler;if(this[j]){var e=this[j].silent;this[X]=!0,nt(this),et.update.call(this),this[X]=!1,this[j]=!1,ot.call(this,e),st.call(this,e)}else if(t.unfinished){var n=z,i=this._model,r=this._api;t.unfinished=!1;do{var a=+new Date;t.performSeriesTasks(i),t.performDataProcessorTasks(i),rt(this,i),t.performVisualTasks(i),ft(this,this._model,r,"remain"),n-=+new Date-a}while(n>0&&t.unfinished);t.unfinished||this._zr.flush()}}},J.getDom=function(){return this._dom},J.getZr=function(){return this._zr},J.setOption=function(t,e,n){var i;if(E(e)&&(n=e.lazyUpdate,i=e.silent,e=e.notMerge),this[X]=!0,!this._model||e){var r=new f(this._api),a=this._theme,o=this._model=new h(null,null,a,r);o.scheduler=this._scheduler,o.init(null,null,a,r)}this._model.setOption(t,Mt),n?(this[j]={silent:i},this[X]=!1):(nt(this),et.update.call(this),this._zr.flush(),this[j]=!1,this[X]=!1,ot.call(this,i),st.call(this,i))},J.setTheme=function(){console.error("ECharts#setTheme() is DEPRECATED in ECharts 3.0")},J.getModel=function(){return this._model},J.getOption=function(){return this._model&&this._model.getOption()},J.getWidth=function(){return this._zr.getWidth()},J.getHeight=function(){return this._zr.getHeight()},J.getDevicePixelRatio=function(){return this._zr.painter.dpr||window.devicePixelRatio||1},J.getRenderedCanvas=function(t){if(s.canvasSupported){t=t||{},t.pixelRatio=t.pixelRatio||1,t.backgroundColor=t.backgroundColor||this._model.get("backgroundColor");var e=this._zr;return e.painter.getRenderedCanvas(t)}},J.getSvgDataUrl=function(){if(s.svgSupported){var t=this._zr,e=t.storage.getDisplayList();return a.each(e,(function(t){t.stopAnimation(!0)})),t.painter.pathToDataUrl()}},J.getDataURL=function(t){t=t||{};var e=t.excludeComponents,n=this._model,i=[],r=this;O(e,(function(t){n.eachComponent({mainType:t},(function(t){var e=r._componentsMap[t.__viewId];e.group.ignore||(i.push(e),e.group.ignore=!0)}))}));var a="svg"===this._zr.painter.getType()?this.getSvgDataUrl():this.getRenderedCanvas(t).toDataURL("image/"+(t&&t.type||"png"));return O(i,(function(t){t.group.ignore=!1})),a},J.getConnectedDataURL=function(t){if(s.canvasSupported){var e=this.group,n=Math.min,i=Math.max,o=1/0;if(Dt[e]){var l=o,u=o,h=-o,c=-o,d=[],f=t&&t.pixelRatio||1;a.each(kt,(function(r,o){if(r.group===e){var s=r.getRenderedCanvas(a.clone(t)),f=r.getDom().getBoundingClientRect();l=n(f.left,l),u=n(f.top,u),h=i(f.right,h),c=i(f.bottom,c),d.push({dom:s,left:f.left,top:f.top})}})),l*=f,u*=f,h*=f,c*=f;var p=h-l,g=c-u,v=a.createCanvas();v.width=p,v.height=g;var m=r.init(v);return O(d,(function(t){var e=new _.Image({style:{x:t.left*f-l,y:t.top*f-u,image:t.dom}});m.add(e)})),m.refreshImmediately(),v.toDataURL("image/"+(t&&t.type||"png"))}return this.getDataURL(t)}},J.convertToPixel=a.curry(tt,"convertToPixel"),J.convertFromPixel=a.curry(tt,"convertFromPixel"),J.containPixel=function(t,e){var n,i=this._model;return t=b.parseFinder(i,t),a.each(t,(function(t,i){i.indexOf("Models")>=0&&a.each(t,(function(t){var r=t.coordinateSystem;if(r&&r.containPoint)n|=!!r.containPoint(e);else if("seriesModels"===i){var a=this._chartsMap[t.__viewId];a&&a.containPoint&&(n|=a.containPoint(e,t))}}),this)}),this),!!n},J.getVisual=function(t,e){var n=this._model;t=b.parseFinder(n,t,{defaultMainType:"series"});var i=t.seriesModel,r=i.getData(),a=t.hasOwnProperty("dataIndexInside")?t.dataIndexInside:t.hasOwnProperty("dataIndex")?r.indexOfRawIndex(t.dataIndex):null;return null!=a?r.getItemVisual(a,e):r.getVisual(e)},J.getViewOfComponentModel=function(t){return this._componentsMap[t.__viewId]},J.getViewOfSeriesModel=function(t){return this._chartsMap[t.__viewId]};var et={prepareAndUpdate:function(t){nt(this),et.update.call(this,t)},update:function(t){var e=this._model,n=this._api,i=this._zr,r=this._coordSysMgr,a=this._scheduler;if(e){a.restoreData(e,t),a.performSeriesTasks(e),r.create(e,n),a.performDataProcessorTasks(e,t),rt(this,e),r.update(e,n),ht(e),a.performVisualTasks(e,t),ct(this,e,n,t);var l=e.get("backgroundColor")||"transparent";if(s.canvasSupported)i.setBackgroundColor(l);else{var u=o.parse(l);l=o.stringify(u,"rgb"),0===u[3]&&(l="transparent")}pt(e,n)}},updateTransform:function(t){var e=this._model,n=this,i=this._api;if(e){var r=[];e.eachComponent((function(a,o){var s=n.getViewOfComponentModel(o);if(s&&s.__alive)if(s.updateTransform){var l=s.updateTransform(o,e,i,t);l&&l.update&&r.push(s)}else r.push(s)}));var o=a.createHashMap();e.eachSeries((function(r){var a=n._chartsMap[r.__viewId];if(a.updateTransform){var s=a.updateTransform(r,e,i,t);s&&s.update&&o.set(r.uid,1)}else o.set(r.uid,1)})),ht(e),this._scheduler.performVisualTasks(e,t,{setDirty:!0,dirtyMap:o}),ft(n,e,i,t,o),pt(e,this._api)}},updateView:function(t){var e=this._model;e&&(x.markUpdateMethod(t,"updateView"),ht(e),this._scheduler.performVisualTasks(e,t,{setDirty:!0}),ct(this,this._model,this._api,t),pt(e,this._api))},updateVisual:function(t){et.update.call(this,t)},updateLayout:function(t){et.update.call(this,t)}};function nt(t){var e=t._model,n=t._scheduler;n.restorePipelines(e),n.prepareStageTasks(),ut(t,"component",e,n),ut(t,"chart",e,n),n.plan()}function it(t,e,n,i,r){var o=t._model;if(i){var s={};s[i+"Id"]=n[i+"Id"],s[i+"Index"]=n[i+"Index"],s[i+"Name"]=n[i+"Name"];var l={mainType:i,query:s};r&&(l.subType=r);var u=n.excludeSeriesId;null!=u&&(u=a.createHashMap(b.normalizeToArray(u))),o&&o.eachComponent(l,(function(e){u&&null!=u.get(e.id)||h(t["series"===i?"_chartsMap":"_componentsMap"][e.__viewId])}),t)}else O(t._componentsViews.concat(t._chartsViews),h);function h(i){i&&i.__alive&&i[e]&&i[e](i.__model,o,t._api,n)}}function rt(t,e){var n=t._chartsMap,i=t._scheduler;e.eachSeries((function(t){i.updateStreamModes(t,n[t.__viewId])}))}function at(t,e){var n=t.type,i=t.escapeConnect,r=bt[n],o=r.actionInfo,s=(o.update||"update").split(":"),l=s.pop();s=null!=s[0]&&R(s[0]),this[X]=!0;var u=[t],h=!1;t.batch&&(h=!0,u=a.map(t.batch,(function(e){return e=a.defaults(a.extend({},e),t),e.batch=null,e})));var c,d=[],f="highlight"===n||"downplay"===n;O(u,(function(t){c=r.action(t,this._model,this._api),c=c||a.extend({},t),c.type=o.event||c.type,d.push(c),f?it(this,l,t,"series"):s&&it(this,l,t,s.main,s.sub)}),this),"none"===l||f||s||(this[j]?(nt(this),et.update.call(this,t),this[j]=!1):et[l].call(this,t)),c=h?{type:o.event||n,escapeConnect:i,batch:d}:d[0],this[X]=!1,!e&&this._messageCenter.trigger(c.type,c)}function ot(t){var e=this._pendingActions;while(e.length){var n=e.shift();at.call(this,n,t)}}function st(t){!t&&this.trigger("updated")}function lt(t,e){t.on("rendered",(function(){e.trigger("rendered"),!t.animation.isFinished()||e[j]||e._scheduler.unfinished||e._pendingActions.length||e.trigger("finished")}))}function ut(t,e,n,i){for(var r="component"===e,a=r?t._componentsViews:t._chartsViews,o=r?t._componentsMap:t._chartsMap,s=t._zr,l=t._api,u=0;u<a.length;u++)a[u].__alive=!1;function h(t){var e="_ec_"+t.id+"_"+t.type,u=o[e];if(!u){var h=R(t.type),c=r?y.getClass(h.main,h.sub):x.getClass(h.sub);u=new c,u.init(n,l),o[e]=u,a.push(u),s.add(u.group)}t.__viewId=u.__id=e,u.__alive=!0,u.__model=t,u.group.__ecComponentInfo={mainType:t.mainType,index:t.componentIndex},!r&&i.prepareView(u,t,n,l)}r?n.eachComponent((function(t,e){"series"!==t&&h(e)})):n.eachSeries(h);for(u=0;u<a.length;){var c=a[u];c.__alive?u++:(!r&&c.renderTask.dispose(),s.remove(c.group),c.dispose(n,l),a.splice(u,1),delete o[c.__id],c.__id=c.group.__ecComponentInfo=null)}}function ht(t){t.clearColorPalette(),t.eachSeries((function(t){t.clearColorPalette()}))}function ct(t,e,n,i){dt(t,e,n,i),O(t._chartsViews,(function(t){t.__alive=!1})),ft(t,e,n,i),O(t._chartsViews,(function(t){t.__alive||t.remove(e,n)}))}function dt(t,e,n,i,r){O(r||t._componentsViews,(function(t){var r=t.__model;t.render(r,e,n,i),yt(r,t)}))}function ft(t,e,n,i,r){var a,o=t._scheduler;e.eachSeries((function(e){var n=t._chartsMap[e.__viewId];n.__alive=!0;var s=n.renderTask;o.updatePayload(s,i),r&&r.get(e.uid)&&s.dirty(),a|=s.perform(o.getPerformArgs(s)),n.group.silent=!!e.get("silent"),yt(e,n),mt(e,n)})),o.unfinished|=a,vt(t._zr,e),T(t._zr.dom,e)}function pt(t,e){O(Tt,(function(n){n(t,e)}))}J.resize=function(t){this._zr.resize(t);var e=this._model;if(this._loadingFX&&this._loadingFX.resize(),e){var n=e.resetOption("media"),i=t&&t.silent;this[X]=!0,n&&nt(this),et.update.call(this),this[X]=!1,ot.call(this,i),st.call(this,i)}},J.showLoading=function(t,e){if(E(t)&&(e=t,t=""),t=t||"default",this.hideLoading(),It[t]){var n=It[t](this._api,e),i=this._zr;this._loadingFX=n,i.add(n)}},J.hideLoading=function(){this._loadingFX&&this._zr.remove(this._loadingFX),this._loadingFX=null},J.makeActionFromEvent=function(t){var e=a.extend({},t);return e.type=wt[t.type],e},J.dispatchAction=function(t,e){E(e)||(e={silent:!!e}),bt[t.type]&&this._model&&(this[X]?this._pendingActions.push(t):(at.call(this,t,e.silent),e.flush?this._zr.flush(!0):!1!==e.flush&&s.browser.weChat&&this._throttledZrFlush(),ot.call(this,e.silent),st.call(this,e.silent)))},J.appendData=function(t){var e=t.seriesIndex,n=this.getModel(),i=n.getSeriesByIndex(e);i.appendData(t),this._scheduler.unfinished=!0},J.on=K("on"),J.off=K("off"),J.one=K("one");var gt=["click","dblclick","mouseover","mouseout","mousemove","mousedown","mouseup","globalout","contextmenu"];function vt(t,e){var n=t.storage,i=0;n.traverse((function(t){t.isGroup||i++})),i>e.get("hoverLayerThreshold")&&!s.node&&n.traverse((function(t){t.isGroup||(t.useHoverLayer=!0)}))}function mt(t,e){var n=t.get("blendMode")||null;e.group.traverse((function(t){t.isGroup||t.style.blend!==n&&t.setStyle("blend",n),t.eachPendingDisplayable&&t.eachPendingDisplayable((function(t){t.setStyle("blend",n)}))}))}function yt(t,e){var n=t.get("z"),i=t.get("zlevel");e.group.traverse((function(t){"group"!==t.type&&(null!=n&&(t.z=n),null!=i&&(t.zlevel=i))}))}function xt(t){var e=t._coordSysMgr;return a.extend(new c(t),{getCoordinateSystems:a.bind(e.getCoordinateSystems,e),getComponentByElement:function(e){while(e){var n=e.__ecComponentInfo;if(null!=n)return t._model.getComponent(n.mainType,n.index);e=e.parent}}})}function _t(){this.eventInfo}J._initEvents=function(){O(gt,(function(t){var e=function(e){var n,i=this.getModel(),r=e.target,o="globalout"===t;if(o)n={};else if(r&&null!=r.dataIndex){var s=r.dataModel||i.getSeriesByIndex(r.seriesIndex);n=s&&s.getDataParams(r.dataIndex,r.dataType,r)||{}}else r&&r.eventData&&(n=a.extend({},r.eventData));if(n){var l=n.componentType,u=n.componentIndex;"markLine"!==l&&"markPoint"!==l&&"markArea"!==l||(l="series",u=n.seriesIndex);var h=l&&null!=u&&i.getComponent(l,u),c=h&&this["series"===h.mainType?"_chartsMap":"_componentsMap"][h.__viewId];n.event=e,n.type=t,this._ecEventProcessor.eventInfo={targetEl:r,packedEvent:n,model:h,view:c},this.trigger(t,n)}};e.zrEventfulCallAtLast=!0,this._zr.on(t,e,this)}),this),O(wt,(function(t,e){this._messageCenter.on(e,(function(t){this.trigger(e,t)}),this)}),this)},J.isDisposed=function(){return this._disposed},J.clear=function(){this.setOption({series:[]},!0)},J.dispose=function(){if(!this._disposed){this._disposed=!0,b.setAttribute(this.getDom(),Lt,"");var t=this._api,e=this._model;O(this._componentsViews,(function(n){n.dispose(e,t)})),O(this._chartsViews,(function(n){n.dispose(e,t)})),this._zr.dispose(),delete kt[this.id]}},a.mixin(Q,u),_t.prototype={constructor:_t,normalizeQuery:function(t){var e={},n={},i={};if(a.isString(t)){var r=R(t);e.mainType=r.main||null,e.subType=r.sub||null}else{var o=["Index","Name","Id"],s={name:1,dataIndex:1,dataType:1};a.each(t,(function(t,r){for(var a=!1,l=0;l<o.length;l++){var u=o[l],h=r.lastIndexOf(u);if(h>0&&h===r.length-u.length){var c=r.slice(0,h);"data"!==c&&(e.mainType=c,e[u.toLowerCase()]=t,a=!0)}}s.hasOwnProperty(r)&&(n[r]=t,a=!0),a||(i[r]=t)}))}return{cptQuery:e,dataQuery:n,otherQuery:i}},filter:function(t,e,n){var i=this.eventInfo;if(!i)return!0;var r=i.targetEl,a=i.packedEvent,o=i.model,s=i.view;if(!o||!s)return!0;var l=e.cptQuery,u=e.dataQuery;return h(l,o,"mainType")&&h(l,o,"subType")&&h(l,o,"index","componentIndex")&&h(l,o,"name")&&h(l,o,"id")&&h(u,a,"name")&&h(u,a,"dataIndex")&&h(u,a,"dataType")&&(!s.filterForExposedEvent||s.filterForExposedEvent(t,e.otherQuery,r,a));function h(t,e,n,i){return null==t[n]||e[i||n]===t[n]}},afterTrigger:function(){this.eventInfo=null}};var bt={},wt={},St=[],Mt=[],Tt=[],At=[],Ct={},It={},kt={},Dt={},Pt=new Date-0,Ot=new Date-0,Lt="_echarts_instance_";function Et(t){var e=0,n=1,i=2,r="__connectUpdateStatus";function a(t,e){for(var n=0;n<t.length;n++){var i=t[n];i[r]=e}}O(wt,(function(o,s){t._messageCenter.on(s,(function(o){if(Dt[t.group]&&t[r]!==e){if(o&&o.escapeConnect)return;var s=t.makeActionFromEvent(o),l=[];O(kt,(function(e){e!==t&&e.group===t.group&&l.push(e)})),a(l,e),O(l,(function(t){t[r]!==n&&t.dispatchAction(s)})),a(l,i)}}))}))}function Rt(t,e,n){var i=Vt(t);if(i)return i;var r=new Q(t,e,n);return r.id="ec_"+Pt++,kt[r.id]=r,b.setAttribute(t,Lt,r.id),Et(r),r}function Bt(t){if(a.isArray(t)){var e=t;t=null,O(e,(function(e){null!=e.group&&(t=e.group)})),t=t||"g_"+Ot++,O(e,(function(e){e.group=t}))}return Dt[t]=!0,t}function Nt(t){Dt[t]=!1}var zt=Nt;function Ft(t){"string"===typeof t?t=kt[t]:t instanceof Q||(t=Vt(t)),t instanceof Q&&!t.isDisposed()&&t.dispose()}function Vt(t){return kt[b.getAttribute(t,Lt)]}function Ht(t){return kt[t]}function Wt(t,e){Ct[t]=e}function Gt(t){Mt.push(t)}function Ut(t,e){$t(St,t,e,F)}function qt(t){Tt.push(t)}function Yt(t,e,n){"function"===typeof e&&(n=e,e="");var i=E(t)?t.type:[t,t={event:e}][0];t.event=(t.event||i).toLowerCase(),e=t.event,P(Z.test(i)&&Z.test(e)),bt[i]||(bt[i]={action:n,actionInfo:t}),wt[e]=i}function Xt(t,e){d.register(t,e)}function jt(t){var e=d.get(t);if(e)return e.getDimensionsInfo?e.getDimensionsInfo():e.dimensions.slice()}function Zt(t,e){$t(At,t,e,H,"layout")}function Kt(t,e){$t(At,t,e,G,"visual")}function $t(t,e,n,i,r){(L(e)||E(e))&&(n=e,e=i);var a=C.wrapStageHandler(n,r);return a.__prio=e,a.__raw=n,t.push(a),a}function Qt(t,e){It[t]=e}function Jt(t){return v.extend(t)}function te(t){return y.extend(t)}function ee(t){return m.extend(t)}function ne(t){return x.extend(t)}function ie(t){a.$override("createCanvas",t)}function re(t,e,n){D.registerMap(t,e,n)}function ae(t){var e=D.retrieveMap(t);return e&&e[0]&&{geoJson:e[0].geoJSON,specialAreas:e[0].specialAreas}}Kt(W,M),Gt(p),Ut(V,g),Qt("default",A),Yt({type:"highlight",event:"highlight",update:"highlight"},a.noop),Yt({type:"downplay",event:"downplay",update:"downplay"},a.noop),Wt("light",I),Wt("dark",k);var oe={};e.version=B,e.dependencies=N,e.PRIORITY=Y,e.init=Rt,e.connect=Bt,e.disConnect=Nt,e.disconnect=zt,e.dispose=Ft,e.getInstanceByDom=Vt,e.getInstanceById=Ht,e.registerTheme=Wt,e.registerPreprocessor=Gt,e.registerProcessor=Ut,e.registerPostUpdate=qt,e.registerAction=Yt,e.registerCoordinateSystem=Xt,e.getCoordinateSystemDimensions=jt,e.registerLayout=Zt,e.registerVisual=Kt,e.registerLoading=Qt,e.extendComponentModel=Jt,e.extendComponentView=te,e.extendSeriesModel=ee,e.extendChartView=ne,e.setCanvasCreator=ie,e.registerMap=re,e.getMap=ae,e.dataTool=oe;var se=n("b719");(function(){for(var t in se)se.hasOwnProperty(t)&&(e[t]=se[t])})()},"401b":function(t,e){var n="undefined"===typeof Float32Array?Array:Float32Array;function i(t,e){var i=new n(2);return null==t&&(t=0),null==e&&(e=0),i[0]=t,i[1]=e,i}function r(t,e){return t[0]=e[0],t[1]=e[1],t}function a(t){var e=new n(2);return e[0]=t[0],e[1]=t[1],e}function o(t,e,n){return t[0]=e,t[1]=n,t}function s(t,e,n){return t[0]=e[0]+n[0],t[1]=e[1]+n[1],t}function l(t,e,n,i){return t[0]=e[0]+n[0]*i,t[1]=e[1]+n[1]*i,t}function u(t,e,n){return t[0]=e[0]-n[0],t[1]=e[1]-n[1],t}function h(t){return Math.sqrt(d(t))}var c=h;function d(t){return t[0]*t[0]+t[1]*t[1]}var f=d;function p(t,e,n){return t[0]=e[0]*n[0],t[1]=e[1]*n[1],t}function g(t,e,n){return t[0]=e[0]/n[0],t[1]=e[1]/n[1],t}function v(t,e){return t[0]*e[0]+t[1]*e[1]}function m(t,e,n){return t[0]=e[0]*n,t[1]=e[1]*n,t}function y(t,e){var n=h(e);return 0===n?(t[0]=0,t[1]=0):(t[0]=e[0]/n,t[1]=e[1]/n),t}function x(t,e){return Math.sqrt((t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1]))}var _=x;function b(t,e){return(t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1])}var w=b;function S(t,e){return t[0]=-e[0],t[1]=-e[1],t}function M(t,e,n,i){return t[0]=e[0]+i*(n[0]-e[0]),t[1]=e[1]+i*(n[1]-e[1]),t}function T(t,e,n){var i=e[0],r=e[1];return t[0]=n[0]*i+n[2]*r+n[4],t[1]=n[1]*i+n[3]*r+n[5],t}function A(t,e,n){return t[0]=Math.min(e[0],n[0]),t[1]=Math.min(e[1],n[1]),t}function C(t,e,n){return t[0]=Math.max(e[0],n[0]),t[1]=Math.max(e[1],n[1]),t}e.create=i,e.copy=r,e.clone=a,e.set=o,e.add=s,e.scaleAndAdd=l,e.sub=u,e.len=h,e.length=c,e.lenSquare=d,e.lengthSquare=f,e.mul=p,e.div=g,e.dot=v,e.scale=m,e.normalize=y,e.distance=x,e.dist=_,e.distanceSquare=b,e.distSquare=w,e.negate=S,e.lerp=M,e.applyTransform=T,e.min=A,e.max=C},"41ef":function(t,e,n){var i=n("d51b"),r={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]};function a(t){return t=Math.round(t),t<0?0:t>255?255:t}function o(t){return t=Math.round(t),t<0?0:t>360?360:t}function s(t){return t<0?0:t>1?1:t}function l(t){return t.length&&"%"===t.charAt(t.length-1)?a(parseFloat(t)/100*255):a(parseInt(t,10))}function u(t){return t.length&&"%"===t.charAt(t.length-1)?s(parseFloat(t)/100):s(parseFloat(t))}function h(t,e,n){return n<0?n+=1:n>1&&(n-=1),6*n<1?t+(e-t)*n*6:2*n<1?e:3*n<2?t+(e-t)*(2/3-n)*6:t}function c(t,e,n){return t+(e-t)*n}function d(t,e,n,i,r){return t[0]=e,t[1]=n,t[2]=i,t[3]=r,t}function f(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t}var p=new i(20),g=null;function v(t,e){g&&f(g,e),g=p.put(t,g||e.slice())}function m(t,e){if(t){e=e||[];var n=p.get(t);if(n)return f(e,n);t+="";var i=t.replace(/ /g,"").toLowerCase();if(i in r)return f(e,r[i]),v(t,e),e;if("#"!==i.charAt(0)){var a=i.indexOf("("),o=i.indexOf(")");if(-1!==a&&o+1===i.length){var s=i.substr(0,a),h=i.substr(a+1,o-(a+1)).split(","),c=1;switch(s){case"rgba":if(4!==h.length)return void d(e,0,0,0,1);c=u(h.pop());case"rgb":return 3!==h.length?void d(e,0,0,0,1):(d(e,l(h[0]),l(h[1]),l(h[2]),c),v(t,e),e);case"hsla":return 4!==h.length?void d(e,0,0,0,1):(h[3]=u(h[3]),y(h,e),v(t,e),e);case"hsl":return 3!==h.length?void d(e,0,0,0,1):(y(h,e),v(t,e),e);default:return}}d(e,0,0,0,1)}else{if(4===i.length){var g=parseInt(i.substr(1),16);return g>=0&&g<=4095?(d(e,(3840&g)>>4|(3840&g)>>8,240&g|(240&g)>>4,15&g|(15&g)<<4,1),v(t,e),e):void d(e,0,0,0,1)}if(7===i.length){g=parseInt(i.substr(1),16);return g>=0&&g<=16777215?(d(e,(16711680&g)>>16,(65280&g)>>8,255&g,1),v(t,e),e):void d(e,0,0,0,1)}}}}function y(t,e){var n=(parseFloat(t[0])%360+360)%360/360,i=u(t[1]),r=u(t[2]),o=r<=.5?r*(i+1):r+i-r*i,s=2*r-o;return e=e||[],d(e,a(255*h(s,o,n+1/3)),a(255*h(s,o,n)),a(255*h(s,o,n-1/3)),1),4===t.length&&(e[3]=t[3]),e}function x(t){if(t){var e,n,i=t[0]/255,r=t[1]/255,a=t[2]/255,o=Math.min(i,r,a),s=Math.max(i,r,a),l=s-o,u=(s+o)/2;if(0===l)e=0,n=0;else{n=u<.5?l/(s+o):l/(2-s-o);var h=((s-i)/6+l/2)/l,c=((s-r)/6+l/2)/l,d=((s-a)/6+l/2)/l;i===s?e=d-c:r===s?e=1/3+h-d:a===s&&(e=2/3+c-h),e<0&&(e+=1),e>1&&(e-=1)}var f=[360*e,n,u];return null!=t[3]&&f.push(t[3]),f}}function _(t,e){var n=m(t);if(n){for(var i=0;i<3;i++)n[i]=e<0?n[i]*(1-e)|0:(255-n[i])*e+n[i]|0,n[i]>255?n[i]=255:t[i]<0&&(n[i]=0);return I(n,4===n.length?"rgba":"rgb")}}function b(t){var e=m(t);if(e)return((1<<24)+(e[0]<<16)+(e[1]<<8)+ +e[2]).toString(16).slice(1)}function w(t,e,n){if(e&&e.length&&t>=0&&t<=1){n=n||[];var i=t*(e.length-1),r=Math.floor(i),o=Math.ceil(i),l=e[r],u=e[o],h=i-r;return n[0]=a(c(l[0],u[0],h)),n[1]=a(c(l[1],u[1],h)),n[2]=a(c(l[2],u[2],h)),n[3]=s(c(l[3],u[3],h)),n}}var S=w;function M(t,e,n){if(e&&e.length&&t>=0&&t<=1){var i=t*(e.length-1),r=Math.floor(i),o=Math.ceil(i),l=m(e[r]),u=m(e[o]),h=i-r,d=I([a(c(l[0],u[0],h)),a(c(l[1],u[1],h)),a(c(l[2],u[2],h)),s(c(l[3],u[3],h))],"rgba");return n?{color:d,leftIndex:r,rightIndex:o,value:i}:d}}var T=M;function A(t,e,n,i){if(t=m(t),t)return t=x(t),null!=e&&(t[0]=o(e)),null!=n&&(t[1]=u(n)),null!=i&&(t[2]=u(i)),I(y(t),"rgba")}function C(t,e){if(t=m(t),t&&null!=e)return t[3]=s(e),I(t,"rgba")}function I(t,e){if(t&&t.length){var n=t[0]+","+t[1]+","+t[2];return"rgba"!==e&&"hsva"!==e&&"hsla"!==e||(n+=","+t[3]),e+"("+n+")"}}e.parse=m,e.lift=_,e.toHex=b,e.fastLerp=w,e.fastMapToColor=S,e.lerp=M,e.mapToColor=T,e.modifyHSL=A,e.modifyAlpha=C,e.stringify=I},"42e5":function(t,e){var n=function(t){this.colorStops=t||[]};n.prototype={constructor:n,addColorStop:function(t,e){this.colorStops.push({offset:t,color:e})}};var i=n;t.exports=i},4319:function(t,e,n){var i=n("6d8b"),r=n("22d1"),a=n("e0d3"),o=a.makeInner,s=n("625e"),l=s.enableClassExtend,u=s.enableClassCheck,h=n("3901"),c=n("9bdb"),d=n("fe21"),f=n("551f"),p=i.mixin,g=o();function v(t,e,n){this.parentModel=e,this.ecModel=n,this.option=t}function m(t,e,n){for(var i=0;i<e.length;i++)if(e[i]&&(t=t&&"object"===typeof t?t[e[i]]:null,null==t))break;return null==t&&n&&(t=n.get(e)),t}function y(t,e){var n=g(t).getParent;return n?n.call(t,e):t.parentModel}v.prototype={constructor:v,init:null,mergeOption:function(t){i.merge(this.option,t,!0)},get:function(t,e){return null==t?this.option:m(this.option,this.parsePath(t),!e&&y(this,t))},getShallow:function(t,e){var n=this.option,i=null==n?n:n[t],r=!e&&y(this,t);return null==i&&r&&(i=r.getShallow(t)),i},getModel:function(t,e){var n,i=null==t?this.option:m(this.option,t=this.parsePath(t));return e=e||(n=y(this,t))&&n.getModel(t),new v(i,e,this.ecModel)},isEmpty:function(){return null==this.option},restoreData:function(){},clone:function(){var t=this.constructor;return new t(i.clone(this.option))},setReadOnly:function(t){},parsePath:function(t){return"string"===typeof t&&(t=t.split(".")),t},customizeGetParent:function(t){g(this).getParent=t},isAnimationEnabled:function(){if(!r.node){if(null!=this.option.animation)return!!this.option.animation;if(this.parentModel)return this.parentModel.isAnimationEnabled()}}},l(v),u(v),p(v,h),p(v,c),p(v,d),p(v,f);var x=v;t.exports=x},4436:function(t,e,n){var i=n("74cb");function r(t){this._target=t.target,this._life=t.life||1e3,this._delay=t.delay||0,this._initialized=!1,this.loop=null!=t.loop&&t.loop,this.gap=t.gap||0,this.easing=t.easing||"Linear",this.onframe=t.onframe,this.ondestroy=t.ondestroy,this.onrestart=t.onrestart,this._pausedTime=0,this._paused=!1}r.prototype={constructor:r,step:function(t,e){if(this._initialized||(this._startTime=t+this._delay,this._initialized=!0),this._paused)this._pausedTime+=e;else{var n=(t-this._startTime-this._pausedTime)/this._life;if(!(n<0)){n=Math.min(n,1);var r=this.easing,a="string"===typeof r?i[r]:r,o="function"===typeof a?a(n):n;return this.fire("frame",o),1===n?this.loop?(this.restart(t),"restart"):(this._needsRemove=!0,"destroy"):null}}},restart:function(t){var e=(t-this._startTime-this._pausedTime)%this._life;this._startTime=t-e+this.gap,this._pausedTime=0,this._needsRemove=!1},fire:function(t,e){t="on"+t,this[t]&&this[t](this._target,e)},pause:function(){this._paused=!0},resume:function(){this._paused=!1}};var a=r;t.exports=a},4573:function(t,e,n){var i=n("cbe5"),r=i.extend({type:"ring",shape:{cx:0,cy:0,r:0,r0:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=2*Math.PI;t.moveTo(n+e.r,i),t.arc(n,i,e.r,0,r,!1),t.moveTo(n+e.r0,i),t.arc(n,i,e.r0,0,r,!0)}});t.exports=r},"48a9":function(t,e,n){var i=n("6d8b"),r=n("42e5"),a=function(t,e,n,i,a,o){this.x=null==t?0:t,this.y=null==e?0:e,this.x2=null==n?1:n,this.y2=null==i?0:i,this.type="linear",this.global=o||!1,r.call(this,a)};a.prototype={constructor:a},i.inherits(a,r);var o=a;t.exports=o},"48ac":function(t,e,n){var i=n("3eba"),r=i.extendComponentModel({type:"axisPointer",coordSysAxesInfo:null,defaultOption:{show:"auto",triggerOn:null,zlevel:0,z:50,type:"line",snap:!1,triggerTooltip:!0,value:null,status:null,link:[],animation:null,animationDurationUpdate:200,lineStyle:{color:"#aaa",width:1,type:"solid"},shadowStyle:{color:"rgba(150,150,150,0.3)"},label:{show:!0,formatter:null,precision:"auto",margin:3,color:"#fff",padding:[5,7,5,7],backgroundColor:"auto",borderColor:null,borderWidth:0,shadowBlur:3,shadowColor:"#aaa"},handle:{show:!1,icon:"M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7v-1.2h6.6z M13.3,22H6.7v-1.2h6.6z M13.3,19.6H6.7v-1.2h6.6z",size:45,margin:50,color:"#333",shadowBlur:3,shadowColor:"#aaa",shadowOffsetX:0,shadowOffsetY:2,throttle:40}}}),a=r;t.exports=a},"48c7":function(t,e,n){var i=n("6d8b"),r=n("6cb7"),a=n("9e47"),o=n("2023"),s=r.extend({type:"cartesian2dAxis",axis:null,init:function(){s.superApply(this,"init",arguments),this.resetRange()},mergeOption:function(){s.superApply(this,"mergeOption",arguments),this.resetRange()},restoreData:function(){s.superApply(this,"restoreData",arguments),this.resetRange()},getCoordSysModel:function(){return this.ecModel.queryComponents({mainType:"grid",index:this.option.gridIndex,id:this.option.gridId})[0]}});function l(t,e){return e.type||(e.data?"category":"value")}i.merge(s.prototype,o);var u={offset:0};a("x",s,l,u),a("y",s,l,u);var h=s;t.exports=h},4942:function(t,e,n){var i=n("2cf4"),r=i.debugMode,a=function(){};1===r?a=function(){for(var t in arguments)throw new Error(arguments[t])}:r>1&&(a=function(){for(var t in arguments)console.log(arguments[t])});var o=a;t.exports=o},"4a3f":function(t,e,n){var i=n("401b"),r=i.create,a=i.distSquare,o=Math.pow,s=Math.sqrt,l=1e-8,u=1e-4,h=s(3),c=1/3,d=r(),f=r(),p=r();function g(t){return t>-l&&t<l}function v(t){return t>l||t<-l}function m(t,e,n,i,r){var a=1-r;return a*a*(a*t+3*r*e)+r*r*(r*i+3*a*n)}function y(t,e,n,i,r){var a=1-r;return 3*(((e-t)*a+2*(n-e)*r)*a+(i-n)*r*r)}function x(t,e,n,i,r,a){var l=i+3*(e-n)-t,u=3*(n-2*e+t),d=3*(e-t),f=t-r,p=u*u-3*l*d,v=u*d-9*l*f,m=d*d-3*u*f,y=0;if(g(p)&&g(v))if(g(u))a[0]=0;else{var x=-d/u;x>=0&&x<=1&&(a[y++]=x)}else{var _=v*v-4*p*m;if(g(_)){var b=v/p,w=(x=-u/l+b,-b/2);x>=0&&x<=1&&(a[y++]=x),w>=0&&w<=1&&(a[y++]=w)}else if(_>0){var S=s(_),M=p*u+1.5*l*(-v+S),T=p*u+1.5*l*(-v-S);M=M<0?-o(-M,c):o(M,c),T=T<0?-o(-T,c):o(T,c);x=(-u-(M+T))/(3*l);x>=0&&x<=1&&(a[y++]=x)}else{var A=(2*p*u-3*l*v)/(2*s(p*p*p)),C=Math.acos(A)/3,I=s(p),k=Math.cos(C),D=(x=(-u-2*I*k)/(3*l),w=(-u+I*(k+h*Math.sin(C)))/(3*l),(-u+I*(k-h*Math.sin(C)))/(3*l));x>=0&&x<=1&&(a[y++]=x),w>=0&&w<=1&&(a[y++]=w),D>=0&&D<=1&&(a[y++]=D)}}return y}function _(t,e,n,i,r){var a=6*n-12*e+6*t,o=9*e+3*i-3*t-9*n,l=3*e-3*t,u=0;if(g(o)){if(v(a)){var h=-l/a;h>=0&&h<=1&&(r[u++]=h)}}else{var c=a*a-4*o*l;if(g(c))r[0]=-a/(2*o);else if(c>0){var d=s(c),f=(h=(-a+d)/(2*o),(-a-d)/(2*o));h>=0&&h<=1&&(r[u++]=h),f>=0&&f<=1&&(r[u++]=f)}}return u}function b(t,e,n,i,r,a){var o=(e-t)*r+t,s=(n-e)*r+e,l=(i-n)*r+n,u=(s-o)*r+o,h=(l-s)*r+s,c=(h-u)*r+u;a[0]=t,a[1]=o,a[2]=u,a[3]=c,a[4]=c,a[5]=h,a[6]=l,a[7]=i}function w(t,e,n,i,r,o,l,h,c,g,v){var y,x,_,b,w,S=.005,M=1/0;d[0]=c,d[1]=g;for(var T=0;T<1;T+=.05)f[0]=m(t,n,r,l,T),f[1]=m(e,i,o,h,T),b=a(d,f),b<M&&(y=T,M=b);M=1/0;for(var A=0;A<32;A++){if(S<u)break;x=y-S,_=y+S,f[0]=m(t,n,r,l,x),f[1]=m(e,i,o,h,x),b=a(f,d),x>=0&&b<M?(y=x,M=b):(p[0]=m(t,n,r,l,_),p[1]=m(e,i,o,h,_),w=a(p,d),_<=1&&w<M?(y=_,M=w):S*=.5)}return v&&(v[0]=m(t,n,r,l,y),v[1]=m(e,i,o,h,y)),s(M)}function S(t,e,n,i){var r=1-i;return r*(r*t+2*i*e)+i*i*n}function M(t,e,n,i){return 2*((1-i)*(e-t)+i*(n-e))}function T(t,e,n,i,r){var a=t-2*e+n,o=2*(e-t),l=t-i,u=0;if(g(a)){if(v(o)){var h=-l/o;h>=0&&h<=1&&(r[u++]=h)}}else{var c=o*o-4*a*l;if(g(c)){h=-o/(2*a);h>=0&&h<=1&&(r[u++]=h)}else if(c>0){var d=s(c),f=(h=(-o+d)/(2*a),(-o-d)/(2*a));h>=0&&h<=1&&(r[u++]=h),f>=0&&f<=1&&(r[u++]=f)}}return u}function A(t,e,n){var i=t+n-2*e;return 0===i?.5:(t-e)/i}function C(t,e,n,i,r){var a=(e-t)*i+t,o=(n-e)*i+e,s=(o-a)*i+a;r[0]=t,r[1]=a,r[2]=s,r[3]=s,r[4]=o,r[5]=n}function I(t,e,n,i,r,o,l,h,c){var g,v=.005,m=1/0;d[0]=l,d[1]=h;for(var y=0;y<1;y+=.05){f[0]=S(t,n,r,y),f[1]=S(e,i,o,y);var x=a(d,f);x<m&&(g=y,m=x)}m=1/0;for(var _=0;_<32;_++){if(v<u)break;var b=g-v,w=g+v;f[0]=S(t,n,r,b),f[1]=S(e,i,o,b);x=a(f,d);if(b>=0&&x<m)g=b,m=x;else{p[0]=S(t,n,r,w),p[1]=S(e,i,o,w);var M=a(p,d);w<=1&&M<m?(g=w,m=M):v*=.5}}return c&&(c[0]=S(t,n,r,g),c[1]=S(e,i,o,g)),s(m)}e.cubicAt=m,e.cubicDerivativeAt=y,e.cubicRootAt=x,e.cubicExtrema=_,e.cubicSubdivide=b,e.cubicProjectPoint=w,e.quadraticAt=S,e.quadraticDerivativeAt=M,e.quadraticRootAt=T,e.quadraticExtremum=A,e.quadraticSubdivide=C,e.quadraticProjectPoint=I},"4a9d":function(t,e,n){var i=n("2306"),r=n("dcb3"),a=n("ff2e"),o=n("0156"),s=n("6679"),l=r.extend({makeElOption:function(t,e,n,i,r){var s=n.axis,l=s.grid,c=i.get("type"),d=u(l,s).getOtherAxis(s).getGlobalExtent(),f=s.toGlobalCoord(s.dataToCoord(e,!0));if(c&&"none"!==c){var p=a.buildElStyle(i),g=h[c](s,f,d,p);g.style=p,t.graphicKey=g.type,t.pointer=g}var v=o.layout(l.model,n);a.buildCartesianSingleLabelElOption(e,t,v,n,i,r)},getHandleTransform:function(t,e,n){var i=o.layout(e.axis.grid.model,e,{labelInside:!1});return i.labelMargin=n.get("handle.margin"),{position:a.getTransformedPosition(e.axis,t,i),rotation:i.rotation+(i.labelDirection<0?Math.PI:0)}},updateHandleTransform:function(t,e,n,i){var r=n.axis,a=r.grid,o=r.getGlobalExtent(!0),s=u(a,r).getOtherAxis(r).getGlobalExtent(),l="x"===r.dim?0:1,h=t.position;h[l]+=e[l],h[l]=Math.min(o[1],h[l]),h[l]=Math.max(o[0],h[l]);var c=(s[1]+s[0])/2,d=[c,c];d[l]=h[l];var f=[{verticalAlign:"middle"},{align:"center"}];return{position:h,rotation:t.rotation,cursorPoint:d,tooltipOption:f[l]}}});function u(t,e){var n={};return n[e.dim+"AxisIndex"]=e.index,t.getCartesian(n)}var h={line:function(t,e,n,r){var o=a.makeLineShape([e,n[0]],[e,n[1]],c(t));return i.subPixelOptimizeLine({shape:o,style:r}),{type:"Line",shape:o}},shadow:function(t,e,n,i){var r=Math.max(1,t.getBandWidth()),o=n[1]-n[0];return{type:"Rect",shape:a.makeRectShape([e-r/2,n[0]],[r,o],c(t))}}};function c(t){return"x"===t.dim?0:1}s.registerAxisPointerClass("CartesianAxisPointer",l);var d=l;t.exports=d},"4aa2":function(t,e,n){var i=n("cbe5"),r=n("897a"),a=i.extend({type:"sector",shape:{cx:0,cy:0,r0:0,r:0,startAngle:0,endAngle:2*Math.PI,clockwise:!0},brush:r(i.prototype.brush),buildPath:function(t,e){var n=e.cx,i=e.cy,r=Math.max(e.r0||0,0),a=Math.max(e.r,0),o=e.startAngle,s=e.endAngle,l=e.clockwise,u=Math.cos(o),h=Math.sin(o);t.moveTo(u*r+n,h*r+i),t.lineTo(u*a+n,h*a+i),t.arc(n,i,a,o,s,!l),t.lineTo(Math.cos(s)*r+n,Math.sin(s)*r+i),0!==r&&t.arc(n,i,r,s,o,l),t.closePath()}});t.exports=a},"4e08":function(t,e,n){(function(t){var n;"undefined"!==typeof window?n=window.__DEV__:"undefined"!==typeof t&&(n=t.__DEV__),"undefined"===typeof n&&(n=!0);var i=n;e.__DEV__=i}).call(this,n("c8ba"))},"4f85":function(t,e,n){var i=n("4e08"),r=(i.__DEV__,n("6d8b")),a=n("22d1"),o=n("eda2"),s=o.formatTime,l=o.encodeHTML,u=o.addCommas,h=o.getTooltipMarker,c=n("e0d3"),d=n("6cb7"),f=n("e47b"),p=n("38a2"),g=n("f934"),v=g.getLayoutParams,m=g.mergeLayoutParam,y=n("f47d"),x=y.createTask,_=n("0f99"),b=_.prepareSource,w=_.getSource,S=n("2b17"),M=S.retrieveRawValue,T=c.makeInner(),A=d.extend({type:"series.__base__",seriesIndex:0,coordinateSystem:null,defaultOption:null,legendDataProvider:null,visualColorAccessPath:"itemStyle.color",layoutMode:null,init:function(t,e,n,i){this.seriesIndex=this.componentIndex,this.dataTask=x({count:k,reset:D}),this.dataTask.context={model:this},this.mergeDefaultAndTheme(t,n),b(this);var r=this.getInitialData(t,n);O(r,this),this.dataTask.context.data=r,T(this).dataBeforeProcessed=r,C(this)},mergeDefaultAndTheme:function(t,e){var n=this.layoutMode,i=n?v(t):{},a=this.subType;d.hasClass(a)&&(a+="Series"),r.merge(t,e.getTheme().get(this.subType)),r.merge(t,this.getDefaultOption()),c.defaultEmphasis(t,"label",["show"]),this.fillDataTextStyle(t.data),n&&m(t,i,n)},mergeOption:function(t,e){t=r.merge(this.option,t,!0),this.fillDataTextStyle(t.data);var n=this.layoutMode;n&&m(this.option,t,n),b(this);var i=this.getInitialData(t,e);O(i,this),this.dataTask.dirty(),this.dataTask.context.data=i,T(this).dataBeforeProcessed=i,C(this)},fillDataTextStyle:function(t){if(t&&!r.isTypedArray(t))for(var e=["show"],n=0;n<t.length;n++)t[n]&&t[n].label&&c.defaultEmphasis(t[n],"label",e)},getInitialData:function(){},appendData:function(t){var e=this.getRawData();e.appendData(t.data)},getData:function(t){var e=E(this);if(e){var n=e.context.data;return null==t?n:n.getLinkedData(t)}return T(this).data},setData:function(t){var e=E(this);if(e){var n=e.context;n.data!==t&&e.modifyOutputEnd&&e.setOutputEnd(t.count()),n.outputData=t,e!==this.dataTask&&(n.data=t)}T(this).data=t},getSource:function(){return w(this)},getRawData:function(){return T(this).dataBeforeProcessed},getBaseAxis:function(){var t=this.coordinateSystem;return t&&t.getBaseAxis&&t.getBaseAxis()},formatTooltip:function(t,e,n,i){var a=this;i=i||"html";var o="html"===i?"<br/>":"\n",d="richText"===i,f={},p=0;function g(n){var o=r.reduce(n,(function(t,e,n){var i=m.getDimensionInfo(n);return t|(i&&!1!==i.tooltip&&null!=i.displayName)}),0),c=[];function g(t,n){var r=m.getDimensionInfo(n);if(r&&!1!==r.otherDims.tooltip){var g=r.type,v="sub"+a.seriesIndex+"at"+p,y=h({color:w,type:"subItem",renderMode:i,markerId:v}),x="string"===typeof y?y:y.content,_=(o?x+l(r.displayName||"-")+": ":"")+l("ordinal"===g?t+"":"time"===g?e?"":s("yyyy/MM/dd hh:mm:ss",t):u(t));_&&c.push(_),d&&(f[v]=w,++p)}}y.length?r.each(y,(function(e){g(M(m,t,e),e)})):r.each(n,g);var v=o?d?"\n":"<br/>":"",x=v+c.join(v||", ");return{renderMode:i,content:x,style:f}}function v(t){return{renderMode:i,content:l(u(t)),style:f}}var m=this.getData(),y=m.mapDimension("defaultedTooltip",!0),x=y.length,_=this.getRawValue(t),b=r.isArray(_),w=m.getItemVisual(t,"color");r.isObject(w)&&w.colorStops&&(w=(w.colorStops[0]||{}).color),w=w||"transparent";var S=x>1||b&&!x?g(_):v(x?M(m,t,y[0]):b?_[0]:_),T=S.content,A=a.seriesIndex+"at"+p,C=h({color:w,type:"item",renderMode:i,markerId:A});f[A]=w,++p;var I=m.getName(t),k=this.name;c.isNameSpecified(this)||(k=""),k=k?l(k)+(e?": ":o):"";var D="string"===typeof C?C:C.content,P=e?D+k+T:k+D+(I?l(I)+": "+T:T);return{html:P,markers:f}},isAnimationEnabled:function(){if(a.node)return!1;var t=this.getShallow("animation");return t&&this.getData().count()>this.getShallow("animationThreshold")&&(t=!1),t},restoreData:function(){this.dataTask.dirty()},getColorFromPalette:function(t,e,n){var i=this.ecModel,r=f.getColorFromPalette.call(this,t,e,n);return r||(r=i.getColorFromPalette(t,e,n)),r},coordDimToDataDim:function(t){return this.getRawData().mapDimension(t,!0)},getProgressive:function(){return this.get("progressive")},getProgressiveThreshold:function(){return this.get("progressiveThreshold")},getAxisTooltipData:null,getTooltipPosition:null,pipeTask:null,preventIncremental:null,pipelineContext:null});function C(t){var e=t.name;c.isNameSpecified(t)||(t.name=I(t)||e)}function I(t){var e=t.getRawData(),n=e.mapDimension("seriesName",!0),i=[];return r.each(n,(function(t){var n=e.getDimensionInfo(t);n.displayName&&i.push(n.displayName)})),i.join(" ")}function k(t){return t.model.getRawData().count()}function D(t){var e=t.model;return e.setData(e.getRawData().cloneShallow()),P}function P(t,e){t.end>e.outputData.count()&&e.model.getRawData().cloneShallow(e.outputData)}function O(t,e){r.each(t.CHANGABLE_METHODS,(function(n){t.wrapMethod(n,r.curry(L,e))}))}function L(t){var e=E(t);e&&e.setOutputEnd(this.count())}function E(t){var e=(t.ecModel||{}).scheduler,n=e&&e.getPipeline(t.uid);if(n){var i=n.currentTask;if(i){var r=i.agentStubMap;r&&(i=r.get(t.uid))}return i}}r.mixin(A,p),r.mixin(A,f);var R=A;t.exports=R},"4fac":function(t,e,n){var i=n("620b"),r=n("9c2c");function a(t,e,n){var a=e.points,o=e.smooth;if(a&&a.length>=2){if(o&&"spline"!==o){var s=r(a,o,n,e.smoothConstraint);t.moveTo(a[0][0],a[0][1]);for(var l=a.length,u=0;u<(n?l:l-1);u++){var h=s[2*u],c=s[2*u+1],d=a[(u+1)%l];t.bezierCurveTo(h[0],h[1],c[0],c[1],d[0],d[1])}}else{"spline"===o&&(a=i(a,n)),t.moveTo(a[0][0],a[0][1]);u=1;for(var f=a.length;u<f;u++)t.lineTo(a[u][0],a[u][1])}n&&t.closePath()}}e.buildPath=a},"551f":function(t,e,n){var i=n("282b"),r=i([["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["textPosition"],["textAlign"]]),a={getItemStyle:function(t,e){var n=r(this,t,e),i=this.getBorderLineDash();return i&&(n.lineDash=i),n},getBorderLineDash:function(){var t=this.get("borderType");return"solid"===t||null==t?null:"dashed"===t?[5,5]:[1,1]}};t.exports=a},5693:function(t,e){function n(t,e){var n,i,r,a,o,s=e.x,l=e.y,u=e.width,h=e.height,c=e.r;u<0&&(s+=u,u=-u),h<0&&(l+=h,h=-h),"number"===typeof c?n=i=r=a=c:c instanceof Array?1===c.length?n=i=r=a=c[0]:2===c.length?(n=r=c[0],i=a=c[1]):3===c.length?(n=c[0],i=a=c[1],r=c[2]):(n=c[0],i=c[1],r=c[2],a=c[3]):n=i=r=a=0,n+i>u&&(o=n+i,n*=u/o,i*=u/o),r+a>u&&(o=r+a,r*=u/o,a*=u/o),i+r>h&&(o=i+r,i*=h/o,r*=h/o),n+a>h&&(o=n+a,n*=h/o,a*=h/o),t.moveTo(s+n,l),t.lineTo(s+u-i,l),0!==i&&t.arc(s+u-i,l+i,i,-Math.PI/2,0),t.lineTo(s+u,l+h-r),0!==r&&t.arc(s+u-r,l+h-r,r,0,Math.PI/2),t.lineTo(s+a,l+h),0!==a&&t.arc(s+a,l+h-a,a,Math.PI/2,Math.PI),t.lineTo(s,l+n),0!==n&&t.arc(s+n,l+n,n,Math.PI,1.5*Math.PI)}e.buildPath=n},"5aa9":function(t,e,n){var i=n("4e08"),r=(i.__DEV__,n("6d8b")),a=r.isObject,o=r.each,s=r.map,l=r.indexOf,u=(r.retrieve,n("f934")),h=u.getLayoutRect,c=n("697e"),d=c.createScaleByModel,f=c.ifAxisCrossZero,p=c.niceScaleExtent,g=c.estimateLabelUnionRect,v=n("cbe9"),m=n("ec02"),y=n("2039"),x=n("ee1a"),_=x.getStackedDimension;function b(t,e,n){return t.getCoordSysModel()===e}function w(t,e,n){this._coordsMap={},this._coordsList=[],this._axesMap={},this._axesList=[],this._initCartesian(t,e,n),this.model=t}n("8ed2");var S=w.prototype;function M(t,e,n,i){n.getAxesOnZeroOf=function(){return r?[r]:[]};var r,a=t[e],o=n.model,s=o.get("axisLine.onZero"),l=o.get("axisLine.onZeroAxisIndex");if(s){if(null!=l)T(a[l])&&(r=a[l]);else for(var u in a)if(a.hasOwnProperty(u)&&T(a[u])&&!i[h(a[u])]){r=a[u];break}r&&(i[h(r)]=!0)}function h(t){return t.dim+"_"+t.index}}function T(t){return t&&"category"!==t.type&&"time"!==t.type&&f(t)}function A(t,e){var n=t.getExtent(),i=n[0]+n[1];t.toGlobalCoord="x"===t.dim?function(t){return t+e}:function(t){return i-t+e},t.toLocalCoord="x"===t.dim?function(t){return t-e}:function(t){return i-t+e}}S.type="grid",S.axisPointerEnabled=!0,S.getRect=function(){return this._rect},S.update=function(t,e){var n=this._axesMap;this._updateScale(t,this.model),o(n.x,(function(t){p(t.scale,t.model)})),o(n.y,(function(t){p(t.scale,t.model)}));var i={};o(n.x,(function(t){M(n,"y",t,i)})),o(n.y,(function(t){M(n,"x",t,i)})),this.resize(this.model,e)},S.resize=function(t,e,n){var i=h(t.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()});this._rect=i;var r=this._axesList;function a(){o(r,(function(t){var e=t.isHorizontal(),n=e?[0,i.width]:[0,i.height],r=t.inverse?1:0;t.setExtent(n[r],n[1-r]),A(t,e?i.x:i.y)}))}a(),!n&&t.get("containLabel")&&(o(r,(function(t){if(!t.model.get("axisLabel.inside")){var e=g(t);if(e){var n=t.isHorizontal()?"height":"width",r=t.model.get("axisLabel.margin");i[n]-=e[n]+r,"top"===t.position?i.y+=e.height+r:"left"===t.position&&(i.x+=e.width+r)}}})),a())},S.getAxis=function(t,e){var n=this._axesMap[t];if(null!=n){if(null==e)for(var i in n)if(n.hasOwnProperty(i))return n[i];return n[e]}},S.getAxes=function(){return this._axesList.slice()},S.getCartesian=function(t,e){if(null!=t&&null!=e){var n="x"+t+"y"+e;return this._coordsMap[n]}a(t)&&(e=t.yAxisIndex,t=t.xAxisIndex);for(var i=0,r=this._coordsList;i<r.length;i++)if(r[i].getAxis("x").index===t||r[i].getAxis("y").index===e)return r[i]},S.getCartesians=function(){return this._coordsList.slice()},S.convertToPixel=function(t,e,n){var i=this._findConvertTarget(t,e);return i.cartesian?i.cartesian.dataToPoint(n):i.axis?i.axis.toGlobalCoord(i.axis.dataToCoord(n)):null},S.convertFromPixel=function(t,e,n){var i=this._findConvertTarget(t,e);return i.cartesian?i.cartesian.pointToData(n):i.axis?i.axis.coordToData(i.axis.toLocalCoord(n)):null},S._findConvertTarget=function(t,e){var n,i,r=e.seriesModel,a=e.xAxisModel||r&&r.getReferringComponents("xAxis")[0],o=e.yAxisModel||r&&r.getReferringComponents("yAxis")[0],s=e.gridModel,u=this._coordsList;if(r)n=r.coordinateSystem,l(u,n)<0&&(n=null);else if(a&&o)n=this.getCartesian(a.componentIndex,o.componentIndex);else if(a)i=this.getAxis("x",a.componentIndex);else if(o)i=this.getAxis("y",o.componentIndex);else if(s){var h=s.coordinateSystem;h===this&&(n=this._coordsList[0])}return{cartesian:n,axis:i}},S.containPoint=function(t){var e=this._coordsList[0];if(e)return e.containPoint(t)},S._initCartesian=function(t,e,n){var i={left:!1,right:!1,top:!1,bottom:!1},r={x:{},y:{}},a={x:0,y:0};if(e.eachComponent("xAxis",s("x"),this),e.eachComponent("yAxis",s("y"),this),!a.x||!a.y)return this._axesMap={},void(this._axesList=[]);function s(n){return function(o,s){if(b(o,t,e)){var l=o.get("position");"x"===n?"top"!==l&&"bottom"!==l&&(l="bottom",i[l]&&(l="top"===l?"bottom":"top")):"left"!==l&&"right"!==l&&(l="left",i[l]&&(l="left"===l?"right":"left")),i[l]=!0;var u=new m(n,d(o),[0,0],o.get("type"),l),h="category"===u.type;u.onBand=h&&o.get("boundaryGap"),u.inverse=o.get("inverse"),o.axis=u,u.model=o,u.grid=this,u.index=s,this._axesList.push(u),r[n][s]=u,a[n]++}}}this._axesMap=r,o(r.x,(function(e,n){o(r.y,(function(i,r){var a="x"+n+"y"+r,o=new v(a);o.grid=this,o.model=t,this._coordsMap[a]=o,this._coordsList.push(o),o.addAxis(e),o.addAxis(i)}),this)}),this)},S._updateScale=function(t,e){function n(t,e,n){o(t.mapDimension(e.dim,!0),(function(n){e.scale.unionExtentFromData(t,_(t,n))}))}o(this._axesList,(function(t){t.scale.setExtent(1/0,-1/0)})),t.eachSeries((function(i){if(k(i)){var r=I(i,t),a=r[0],o=r[1];if(!b(a,e,t)||!b(o,e,t))return;var s=this.getCartesian(a.componentIndex,o.componentIndex),l=i.getData(),u=s.getAxis("x"),h=s.getAxis("y");"list"===l.type&&(n(l,u,i),n(l,h,i))}}),this)},S.getTooltipAxes=function(t){var e=[],n=[];return o(this.getCartesians(),(function(i){var r=null!=t&&"auto"!==t?i.getAxis(t):i.getBaseAxis(),a=i.getOtherAxis(r);l(e,r)<0&&e.push(r),l(n,a)<0&&n.push(a)})),{baseAxes:e,otherAxes:n}};var C=["xAxis","yAxis"];function I(t,e){return s(C,(function(e){var n=t.getReferringComponents(e)[0];return n}))}function k(t){return"cartesian2d"===t.get("coordinateSystem")}w.create=function(t,e){var n=[];return t.eachComponent("grid",(function(i,r){var a=new w(i,t,e);a.name="grid_"+r,a.resize(i,e,!0),i.coordinateSystem=a,n.push(a)})),t.eachSeries((function(e){if(k(e)){var n=I(e,t),i=n[0],r=n[1],a=i.getCoordSysModel(),o=a.coordinateSystem;e.coordinateSystem=o.getCartesian(i.componentIndex,r.componentIndex)}})),n},w.dimensions=w.prototype.dimensions=v.prototype.dimensions,y.register("cartesian2d",w);var D=w;t.exports=D},"5e68":function(t,e,n){var i=n("6d8b"),r=n("2cf4"),a=r.devicePixelRatio,o=n("2b61"),s=n("dc2f");function l(){return!1}function u(t,e,n){var r=i.createCanvas(),a=e.getWidth(),o=e.getHeight(),s=r.style;return s&&(s.position="absolute",s.left=0,s.top=0,s.width=a+"px",s.height=o+"px",r.setAttribute("data-zr-dom-id",t)),r.width=a*n,r.height=o*n,r}var h=function(t,e,n){var r;n=n||a,"string"===typeof t?r=u(t,e,n):i.isObject(t)&&(r=t,t=r.id),this.id=t,this.dom=r;var o=r.style;o&&(r.onselectstart=l,o["-webkit-user-select"]="none",o["user-select"]="none",o["-webkit-touch-callout"]="none",o["-webkit-tap-highlight-color"]="rgba(0,0,0,0)",o["padding"]=0,o["margin"]=0,o["border-width"]=0),this.domBack=null,this.ctxBack=null,this.painter=e,this.config=null,this.clearColor=0,this.motionBlur=!1,this.lastFrameAlpha=.7,this.dpr=n};h.prototype={constructor:h,__dirty:!0,__used:!1,__drawIndex:0,__startIndex:0,__endIndex:0,incremental:!1,getElementCount:function(){return this.__endIndex-this.__startIndex},initContext:function(){this.ctx=this.dom.getContext("2d"),this.ctx.dpr=this.dpr},createBackBuffer:function(){var t=this.dpr;this.domBack=u("back-"+this.id,this.painter,t),this.ctxBack=this.domBack.getContext("2d"),1!==t&&this.ctxBack.scale(t,t)},resize:function(t,e){var n=this.dpr,i=this.dom,r=i.style,a=this.domBack;r&&(r.width=t+"px",r.height=e+"px"),i.width=t*n,i.height=e*n,a&&(a.width=t*n,a.height=e*n,1!==n&&this.ctxBack.scale(n,n))},clear:function(t,e){var n,i=this.dom,r=this.ctx,a=i.width,l=i.height,u=(e=e||this.clearColor,this.motionBlur&&!t),h=this.lastFrameAlpha,c=this.dpr;(u&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(i,0,0,a/c,l/c)),r.clearRect(0,0,a,l),e&&"transparent"!==e)&&(e.colorStops?(n=e.__canvasGradient||o.getGradient(r,e,{x:0,y:0,width:a,height:l}),e.__canvasGradient=n):e.image&&(n=s.prototype.getCanvasPattern.call(e,r)),r.save(),r.fillStyle=n||e,r.fillRect(0,0,a,l),r.restore());if(u){var d=this.domBack;r.save(),r.globalAlpha=h,r.drawImage(d,0,0,a,l),r.restore()}}};var c=h;t.exports=c},"5e76":function(t,e,n){var i=n("d51b"),r=new i(50);function a(t){if("string"===typeof t){var e=r.get(t);return e&&e.image}return t}function o(t,e,n,i,a){if(t){if("string"===typeof t){if(e&&e.__zrImageSrc===t||!n)return e;var o=r.get(t),u={hostEl:n,cb:i,cbPayload:a};return o?(e=o.image,!l(e)&&o.pending.push(u)):(e=new Image,e.onload=e.onerror=s,r.put(t,e.__cachedImgObj={image:e,pending:[u]}),e.src=e.__zrImageSrc=t),e}return t}return e}function s(){var t=this.__cachedImgObj;this.onload=this.onerror=this.__cachedImgObj=null;for(var e=0;e<t.pending.length;e++){var n=t.pending[e],i=n.cb;i&&i(this,n.cbPayload),n.hostEl.dirty()}t.pending.length=0}function l(t){return t&&t.width&&t.height}e.findExistImage=a,e.createOrUpdateImage=o,e.isImageReady=l},"607d":function(t,e,n){var i=n("1fab");e.Dispatcher=i;var r=n("22d1"),a="undefined"!==typeof window&&!!window.addEventListener,o=/^(?:mouse|pointer|contextmenu|drag|drop)|click/;function s(t){return t.getBoundingClientRect?t.getBoundingClientRect():{left:0,top:0}}function l(t,e,n,i){return n=n||{},i||!r.canvasSupported?u(t,e,n):r.browser.firefox&&null!=e.layerX&&e.layerX!==e.offsetX?(n.zrX=e.layerX,n.zrY=e.layerY):null!=e.offsetX?(n.zrX=e.offsetX,n.zrY=e.offsetY):u(t,e,n),n}function u(t,e,n){var i=s(t);n.zrX=e.clientX-i.left,n.zrY=e.clientY-i.top}function h(t,e,n){if(e=e||window.event,null!=e.zrX)return e;var i=e.type,r=i&&i.indexOf("touch")>=0;if(r){var a="touchend"!==i?e.targetTouches[0]:e.changedTouches[0];a&&l(t,a,e,n)}else l(t,e,e,n),e.zrDelta=e.wheelDelta?e.wheelDelta/120:-(e.detail||0)/3;var s=e.button;return null==e.which&&void 0!==s&&o.test(e.type)&&(e.which=1&s?1:2&s?3:4&s?2:0),e}function c(t,e,n){a?t.addEventListener(e,n):t.attachEvent("on"+e,n)}function d(t,e,n){a?t.removeEventListener(e,n):t.detachEvent("on"+e,n)}var f=a?function(t){t.preventDefault(),t.stopPropagation(),t.cancelBubble=!0}:function(t){t.returnValue=!1,t.cancelBubble=!0};function p(t){return 2===t.which||3===t.which}function g(t){return t.which>1}e.clientToLocal=l,e.normalizeEvent=h,e.addEventListener=c,e.removeEventListener=d,e.stop=f,e.isMiddleOrRightButtonOnMouseUpDown=p,e.notLeftMouse=g},6179:function(t,e,n){var i=n("4e08"),r=(i.__DEV__,n("6d8b")),a=n("4319"),o=n("80f0"),s=n("ec6f"),l=n("2b17"),u=l.defaultDimValueGetters,h=l.DefaultDataProvider,c=n("2f45"),d=c.summarizeDimensions,f=r.isObject,p="undefined",g=-1,v="e\0\0",m={float:typeof Float64Array===p?Array:Float64Array,int:typeof Int32Array===p?Array:Int32Array,ordinal:Array,number:Array,time:Array},y=typeof Uint32Array===p?Array:Uint32Array,x=typeof Int32Array===p?Array:Int32Array,_=typeof Uint16Array===p?Array:Uint16Array;function b(t){return t._rawCount>65535?y:_}function w(t){var e=t.constructor;return e===Array?t.slice():new e(t)}var S=["hasItemOption","_nameList","_idList","_invertedIndicesMap","_rawData","_chunkSize","_chunkCount","_dimValueGetter","_count","_rawCount","_nameDimIdx","_idDimIdx"],M=["_extent","_approximateExtent","_rawExtent"];function T(t,e){r.each(S.concat(e.__wrappedMethods||[]),(function(n){e.hasOwnProperty(n)&&(t[n]=e[n])})),t.__wrappedMethods=e.__wrappedMethods,r.each(M,(function(n){t[n]=r.clone(e[n])})),t._calculationInfo=r.extend(e._calculationInfo)}var A=function(t,e){t=t||["x","y"];for(var n={},i=[],a={},o=0;o<t.length;o++){var s=t[o];r.isString(s)&&(s={name:s});var l=s.name;s.type=s.type||"float",s.coordDim||(s.coordDim=l,s.coordDimIndex=0),s.otherDims=s.otherDims||{},i.push(l),n[l]=s,s.index=o,s.createInvertedIndices&&(a[l]=[])}this.dimensions=i,this._dimensionInfos=n,this.hostModel=e,this.dataType,this._indices=null,this._count=0,this._rawCount=0,this._storage={},this._nameList=[],this._idList=[],this._optionModels=[],this._visual={},this._layout={},this._itemVisuals=[],this.hasItemVisual={},this._itemLayouts=[],this._graphicEls=[],this._chunkSize=1e5,this._chunkCount=0,this._rawData,this._rawExtent={},this._extent={},this._approximateExtent={},this._dimensionsSummary=d(this),this._invertedIndicesMap=a,this._calculationInfo={}},C=A.prototype;function I(t,e,n,i,r){var a=m[e.type],o=i-1,s=e.name,l=t[s][o];if(l&&l.length<n){for(var u=new a(Math.min(r-o*n,n)),h=0;h<l.length;h++)u[h]=l[h];t[s][o]=u}for(var c=i*n;c<r;c+=n)t[s].push(new a(Math.min(r-c,n)))}function k(t){var e=t._invertedIndicesMap;r.each(e,(function(n,i){var r=t._dimensionInfos[i],a=r.ordinalMeta;if(a){n=e[i]=new x(a.categories.length);for(var o=0;o<n.length;o++)n[o]=g;for(o=0;o<t._count;o++)n[t.get(i,o)]=o}}))}function D(t,e,n){var i;if(null!=e){var r=t._chunkSize,a=Math.floor(n/r),o=n%r,s=t.dimensions[e],l=t._storage[s][a];if(l){i=l[o];var u=t._dimensionInfos[s].ordinalMeta;u&&u.categories.length&&(i=u.categories[i])}}return i}function P(t){return t}function O(t){return t<this._count&&t>=0?this._indices[t]:-1}function L(t,e){var n=t._idList[e];return null==n&&(n=D(t,t._idDimIdx,e)),null==n&&(n=v+e),n}function E(t){return r.isArray(t)||(t=[t]),t}function R(t,e){var n=t.dimensions,i=new A(r.map(n,t.getDimensionInfo,t),t.hostModel);T(i,t);for(var a=i._storage={},o=t._storage,s=0;s<n.length;s++){var l=n[s];o[l]&&(r.indexOf(e,l)>=0?(a[l]=B(o[l]),i._rawExtent[l]=N(),i._extent[l]=null):a[l]=o[l])}return i}function B(t){for(var e=new Array(t.length),n=0;n<t.length;n++)e[n]=w(t[n]);return e}function N(){return[1/0,-1/0]}C.type="list",C.hasItemOption=!0,C.getDimension=function(t){return isNaN(t)||(t=this.dimensions[t]||t),t},C.getDimensionInfo=function(t){return this._dimensionInfos[this.getDimension(t)]},C.getDimensionsOnCoord=function(){return this._dimensionsSummary.dataDimsOnCoord.slice()},C.mapDimension=function(t,e){var n=this._dimensionsSummary;if(null==e)return n.encodeFirstDimNotExtra[t];var i=n.encode[t];return!0===e?(i||[]).slice():i&&i[e]},C.initData=function(t,e,n){var i=s.isInstance(t)||r.isArrayLike(t);i&&(t=new h(t,this.dimensions.length)),this._rawData=t,this._storage={},this._indices=null,this._nameList=e||[],this._idList=[],this._nameRepeatCount={},n||(this.hasItemOption=!1),this.defaultDimValueGetter=u[this._rawData.getSource().sourceFormat],this._dimValueGetter=n=n||this.defaultDimValueGetter,this._dimValueGetterArrayRows=u.arrayRows,this._rawExtent={},this._initDataFromProvider(0,t.count()),t.pure&&(this.hasItemOption=!1)},C.getProvider=function(){return this._rawData},C.appendData=function(t){var e=this._rawData,n=this.count();e.appendData(t);var i=e.count();e.persistent||(i+=n),this._initDataFromProvider(n,i)},C.appendValues=function(t,e){for(var n=this._chunkSize,i=this._storage,r=this.dimensions,a=r.length,o=this._rawExtent,s=this.count(),l=s+Math.max(t.length,e?e.length:0),u=this._chunkCount,h=0;h<a;h++){var c=r[h];o[c]||(o[c]=N()),i[c]||(i[c]=[]),I(i,this._dimensionInfos[c],n,u,l),this._chunkCount=i[c].length}for(var d=new Array(a),f=s;f<l;f++){for(var p=f-s,g=Math.floor(f/n),v=f%n,m=0;m<a;m++){c=r[m];var y=this._dimValueGetterArrayRows(t[p]||d,c,p,m);i[c][g][v]=y;var x=o[c];y<x[0]&&(x[0]=y),y>x[1]&&(x[1]=y)}e&&(this._nameList[f]=e[p])}this._rawCount=this._count=l,this._extent={},k(this)},C._initDataFromProvider=function(t,e){if(!(t>=e)){for(var n,i=this._chunkSize,r=this._rawData,a=this._storage,o=this.dimensions,s=o.length,l=this._dimensionInfos,u=this._nameList,h=this._idList,c=this._rawExtent,d=this._nameRepeatCount={},f=this._chunkCount,p=0;p<s;p++){var g=o[p];c[g]||(c[g]=N());var v=l[g];0===v.otherDims.itemName&&(n=this._nameDimIdx=p),0===v.otherDims.itemId&&(this._idDimIdx=p),a[g]||(a[g]=[]),I(a,v,i,f,e),this._chunkCount=a[g].length}for(var m=new Array(s),y=t;y<e;y++){m=r.getItem(y,m);for(var x=Math.floor(y/i),_=y%i,b=0;b<s;b++){g=o[b];var w=a[g][x],S=this._dimValueGetter(m,g,y,b);w[_]=S;var M=c[g];S<M[0]&&(M[0]=S),S>M[1]&&(M[1]=S)}if(!r.pure){var T=u[y];if(m&&null==T)if(null!=m.name)u[y]=T=m.name;else if(null!=n){var A=o[n],C=a[A][x];if(C){T=C[_];var D=l[A].ordinalMeta;D&&D.categories.length&&(T=D.categories[T])}}var P=null==m?null:m.id;null==P&&null!=T&&(d[T]=d[T]||0,P=T,d[T]>0&&(P+="__ec__"+d[T]),d[T]++),null!=P&&(h[y]=P)}}!r.persistent&&r.clean&&r.clean(),this._rawCount=this._count=e,this._extent={},k(this)}},C.count=function(){return this._count},C.getIndices=function(){var t=this._indices;if(t){var e=t.constructor,n=this._count;if(e===Array){r=new e(n);for(var i=0;i<n;i++)r[i]=t[i]}else r=new e(t.buffer,0,n)}else{e=b(this);var r=new e(this.count());for(i=0;i<r.length;i++)r[i]=i}return r},C.get=function(t,e){if(!(e>=0&&e<this._count))return NaN;var n=this._storage;if(!n[t])return NaN;e=this.getRawIndex(e);var i=Math.floor(e/this._chunkSize),r=e%this._chunkSize,a=n[t][i],o=a[r];return o},C.getByRawIndex=function(t,e){if(!(e>=0&&e<this._rawCount))return NaN;var n=this._storage[t];if(!n)return NaN;var i=Math.floor(e/this._chunkSize),r=e%this._chunkSize,a=n[i];return a[r]},C._getFast=function(t,e){var n=Math.floor(e/this._chunkSize),i=e%this._chunkSize,r=this._storage[t][n];return r[i]},C.getValues=function(t,e){var n=[];r.isArray(t)||(e=t,t=this.dimensions);for(var i=0,a=t.length;i<a;i++)n.push(this.get(t[i],e));return n},C.hasValue=function(t){for(var e=this._dimensionsSummary.dataDimsOnCoord,n=this._dimensionInfos,i=0,r=e.length;i<r;i++)if("ordinal"!==n[e[i]].type&&isNaN(this.get(e[i],t)))return!1;return!0},C.getDataExtent=function(t){t=this.getDimension(t);var e=this._storage[t],n=N();if(!e)return n;var i,r=this.count(),a=!this._indices;if(a)return this._rawExtent[t].slice();if(i=this._extent[t],i)return i.slice();i=n;for(var o=i[0],s=i[1],l=0;l<r;l++){var u=this._getFast(t,this.getRawIndex(l));u<o&&(o=u),u>s&&(s=u)}return i=[o,s],this._extent[t]=i,i},C.getApproximateExtent=function(t){return t=this.getDimension(t),this._approximateExtent[t]||this.getDataExtent(t)},C.setApproximateExtent=function(t,e){e=this.getDimension(e),this._approximateExtent[e]=t.slice()},C.getCalculationInfo=function(t){return this._calculationInfo[t]},C.setCalculationInfo=function(t,e){f(t)?r.extend(this._calculationInfo,t):this._calculationInfo[t]=e},C.getSum=function(t){var e=this._storage[t],n=0;if(e)for(var i=0,r=this.count();i<r;i++){var a=this.get(t,i);isNaN(a)||(n+=a)}return n},C.getMedian=function(t){var e=[];this.each(t,(function(t,n){isNaN(t)||e.push(t)}));var n=[].concat(e).sort((function(t,e){return t-e})),i=this.count();return 0===i?0:i%2===1?n[(i-1)/2]:(n[i/2]+n[i/2-1])/2},C.rawIndexOf=function(t,e){var n=t&&this._invertedIndicesMap[t],i=n[e];return null==i||isNaN(i)?g:i},C.indexOfName=function(t){for(var e=0,n=this.count();e<n;e++)if(this.getName(e)===t)return e;return-1},C.indexOfRawIndex=function(t){if(!this._indices)return t;if(t>=this._rawCount||t<0)return-1;var e=this._indices,n=e[t];if(null!=n&&n<this._count&&n===t)return t;var i=0,r=this._count-1;while(i<=r){var a=(i+r)/2|0;if(e[a]<t)i=a+1;else{if(!(e[a]>t))return a;r=a-1}}return-1},C.indicesOfNearest=function(t,e,n){var i=this._storage,r=i[t],a=[];if(!r)return a;null==n&&(n=1/0);for(var o=Number.MAX_VALUE,s=-1,l=0,u=this.count();l<u;l++){var h=e-this.get(t,l),c=Math.abs(h);h<=n&&c<=o&&((c<o||h>=0&&s<0)&&(o=c,s=h,a.length=0),a.push(l))}return a},C.getRawIndex=P,C.getRawDataItem=function(t){if(this._rawData.persistent)return this._rawData.getItem(this.getRawIndex(t));for(var e=[],n=0;n<this.dimensions.length;n++){var i=this.dimensions[n];e.push(this.get(i,t))}return e},C.getName=function(t){var e=this.getRawIndex(t);return this._nameList[e]||D(this,this._nameDimIdx,e)||""},C.getId=function(t){return L(this,this.getRawIndex(t))},C.each=function(t,e,n,i){"use strict";if(this._count){"function"===typeof t&&(i=n,n=e,e=t,t=[]),n=n||i||this,t=r.map(E(t),this.getDimension,this);for(var a=t.length,o=0;o<this.count();o++)switch(a){case 0:e.call(n,o);break;case 1:e.call(n,this.get(t[0],o),o);break;case 2:e.call(n,this.get(t[0],o),this.get(t[1],o),o);break;default:for(var s=0,l=[];s<a;s++)l[s]=this.get(t[s],o);l[s]=o,e.apply(n,l)}}},C.filterSelf=function(t,e,n,i){"use strict";if(this._count){"function"===typeof t&&(i=n,n=e,e=t,t=[]),n=n||i||this,t=r.map(E(t),this.getDimension,this);for(var a=this.count(),o=b(this),s=new o(a),l=[],u=t.length,h=0,c=t[0],d=0;d<a;d++){var f,p=this.getRawIndex(d);if(0===u)f=e.call(n,d);else if(1===u){var g=this._getFast(c,p);f=e.call(n,g,d)}else{for(var v=0;v<u;v++)l[v]=this._getFast(c,p);l[v]=d,f=e.apply(n,l)}f&&(s[h++]=p)}return h<a&&(this._indices=s),this._count=h,this._extent={},this.getRawIndex=this._indices?O:P,this}},C.selectRange=function(t){"use strict";if(this._count){var e=[];for(var n in t)t.hasOwnProperty(n)&&e.push(n);var i=e.length;if(i){var r=this.count(),a=b(this),o=new a(r),s=0,l=e[0],u=t[l][0],h=t[l][1],c=!1;if(!this._indices){var d=0;if(1===i){for(var f=this._storage[e[0]],p=0;p<this._chunkCount;p++)for(var g=f[p],v=Math.min(this._count-p*this._chunkSize,this._chunkSize),m=0;m<v;m++){var y=g[m];(y>=u&&y<=h||isNaN(y))&&(o[s++]=d),d++}c=!0}else if(2===i){f=this._storage[l];var x=this._storage[e[1]],_=t[e[1]][0],w=t[e[1]][1];for(p=0;p<this._chunkCount;p++){g=f[p];var S=x[p];for(v=Math.min(this._count-p*this._chunkSize,this._chunkSize),m=0;m<v;m++){y=g[m];var M=S[m];(y>=u&&y<=h||isNaN(y))&&(M>=_&&M<=w||isNaN(M))&&(o[s++]=d),d++}}c=!0}}if(!c)if(1===i)for(m=0;m<r;m++){var T=this.getRawIndex(m);y=this._getFast(l,T);(y>=u&&y<=h||isNaN(y))&&(o[s++]=T)}else for(m=0;m<r;m++){var A=!0;for(T=this.getRawIndex(m),p=0;p<i;p++){var C=e[p];y=this._getFast(n,T);(y<t[C][0]||y>t[C][1])&&(A=!1)}A&&(o[s++]=this.getRawIndex(m))}return s<r&&(this._indices=o),this._count=s,this._extent={},this.getRawIndex=this._indices?O:P,this}}},C.mapArray=function(t,e,n,i){"use strict";"function"===typeof t&&(i=n,n=e,e=t,t=[]),n=n||i||this;var r=[];return this.each(t,(function(){r.push(e&&e.apply(this,arguments))}),n),r},C.map=function(t,e,n,i){"use strict";n=n||i||this,t=r.map(E(t),this.getDimension,this);var a=R(this,t);a._indices=this._indices,a.getRawIndex=a._indices?O:P;for(var o=a._storage,s=[],l=this._chunkSize,u=t.length,h=this.count(),c=[],d=a._rawExtent,f=0;f<h;f++){for(var p=0;p<u;p++)c[p]=this.get(t[p],f);c[u]=f;var g=e&&e.apply(n,c);if(null!=g){"object"!==typeof g&&(s[0]=g,g=s);for(var v=this.getRawIndex(f),m=Math.floor(v/l),y=v%l,x=0;x<g.length;x++){var _=t[x],b=g[x],w=d[_],S=o[_];S&&(S[m][y]=b),b<w[0]&&(w[0]=b),b>w[1]&&(w[1]=b)}}}return a},C.downSample=function(t,e,n,i){for(var r=R(this,[t]),a=r._storage,o=[],s=Math.floor(1/e),l=a[t],u=this.count(),h=this._chunkSize,c=r._rawExtent[t],d=new(b(this))(u),f=0,p=0;p<u;p+=s){s>u-p&&(s=u-p,o.length=s);for(var g=0;g<s;g++){var v=this.getRawIndex(p+g),m=Math.floor(v/h),y=v%h;o[g]=l[m][y]}var x=n(o),_=this.getRawIndex(Math.min(p+i(o,x)||0,u-1)),w=Math.floor(_/h),S=_%h;l[w][S]=x,x<c[0]&&(c[0]=x),x>c[1]&&(c[1]=x),d[f++]=_}return r._count=f,r._indices=d,r.getRawIndex=O,r},C.getItemModel=function(t){var e=this.hostModel;return new a(this.getRawDataItem(t),e,e&&e.ecModel)},C.diff=function(t){var e=this;return new o(t?t.getIndices():[],this.getIndices(),(function(e){return L(t,e)}),(function(t){return L(e,t)}))},C.getVisual=function(t){var e=this._visual;return e&&e[t]},C.setVisual=function(t,e){if(f(t))for(var n in t)t.hasOwnProperty(n)&&this.setVisual(n,t[n]);else this._visual=this._visual||{},this._visual[t]=e},C.setLayout=function(t,e){if(f(t))for(var n in t)t.hasOwnProperty(n)&&this.setLayout(n,t[n]);else this._layout[t]=e},C.getLayout=function(t){return this._layout[t]},C.getItemLayout=function(t){return this._itemLayouts[t]},C.setItemLayout=function(t,e,n){this._itemLayouts[t]=n?r.extend(this._itemLayouts[t]||{},e):e},C.clearItemLayouts=function(){this._itemLayouts.length=0},C.getItemVisual=function(t,e,n){var i=this._itemVisuals[t],r=i&&i[e];return null!=r||n?r:this.getVisual(e)},C.setItemVisual=function(t,e,n){var i=this._itemVisuals[t]||{},r=this.hasItemVisual;if(this._itemVisuals[t]=i,f(e))for(var a in e)e.hasOwnProperty(a)&&(i[a]=e[a],r[a]=!0);else i[e]=n,r[e]=!0},C.clearAllVisual=function(){this._visual={},this._itemVisuals=[],this.hasItemVisual={}};var z=function(t){t.seriesIndex=this.seriesIndex,t.dataIndex=this.dataIndex,t.dataType=this.dataType};C.setItemGraphicEl=function(t,e){var n=this.hostModel;e&&(e.dataIndex=t,e.dataType=this.dataType,e.seriesIndex=n&&n.seriesIndex,"group"===e.type&&e.traverse(z,e)),this._graphicEls[t]=e},C.getItemGraphicEl=function(t){return this._graphicEls[t]},C.eachItemGraphicEl=function(t,e){r.each(this._graphicEls,(function(n,i){n&&t&&t.call(e,n,i)}))},C.cloneShallow=function(t){if(!t){var e=r.map(this.dimensions,this.getDimensionInfo,this);t=new A(e,this.hostModel)}if(t._storage=this._storage,T(t,this),this._indices){var n=this._indices.constructor;t._indices=new n(this._indices)}else t._indices=null;return t.getRawIndex=t._indices?O:P,t},C.wrapMethod=function(t,e){var n=this[t];"function"===typeof n&&(this.__wrappedMethods=this.__wrappedMethods||[],this.__wrappedMethods.push(t),this[t]=function(){var t=n.apply(this,arguments);return e.apply(this,[t].concat(r.slice(arguments)))})},C.TRANSFERABLE_METHODS=["cloneShallow","downSample","map"],C.CHANGABLE_METHODS=["filterSelf","selectRange"];var F=A;t.exports=F},"620b":function(t,e,n){var i=n("401b"),r=i.distance;function a(t,e,n,i,r,a,o){var s=.5*(n-t),l=.5*(i-e);return(2*(e-n)+s+l)*o+(-3*(e-n)-2*s-l)*a+s*r+e}function o(t,e){for(var n=t.length,i=[],o=0,s=1;s<n;s++)o+=r(t[s-1],t[s]);var l=o/2;l=l<n?n:l;for(s=0;s<l;s++){var u,h,c,d=s/(l-1)*(e?n:n-1),f=Math.floor(d),p=d-f,g=t[f%n];e?(u=t[(f-1+n)%n],h=t[(f+1)%n],c=t[(f+2)%n]):(u=t[0===f?f:f-1],h=t[f>n-2?n-1:f+1],c=t[f>n-3?n-1:f+2]);var v=p*p,m=p*v;i.push([a(u[0],g[0],h[0],c[0],p,v,m),a(u[1],g[1],h[1],c[1],p,v,m)])}return i}t.exports=o},"625e":function(t,e,n){var i=n("4e08"),r=(i.__DEV__,n("6d8b")),a=".",o="___EC__COMPONENT__CONTAINER___";function s(t){var e={main:"",sub:""};return t&&(t=t.split(a),e.main=t[0]||"",e.sub=t[1]||""),e}function l(t){r.assert(/^[a-zA-Z0-9_]+([.][a-zA-Z0-9_]+)?$/.test(t),'componentType "'+t+'" illegal')}function u(t,e){t.$constructor=t,t.extend=function(t){var e=this,n=function(){t.$constructor?t.$constructor.apply(this,arguments):e.apply(this,arguments)};return r.extend(n.prototype,t),n.extend=this.extend,n.superCall=d,n.superApply=f,r.inherits(n,this),n.superClass=e,n}}var h=0;function c(t){var e=["__\0is_clz",h++,Math.random().toFixed(3)].join("_");t.prototype[e]=!0,t.isInstance=function(t){return!(!t||!t[e])}}function d(t,e){var n=r.slice(arguments,2);return this.superClass.prototype[e].apply(t,n)}function f(t,e,n){return this.superClass.prototype[e].apply(t,n)}function p(t,e){e=e||{};var n={};function i(t){var e=n[t.main];return e&&e[o]||(e=n[t.main]={},e[o]=!0),e}if(t.registerClass=function(t,e){if(e)if(l(e),e=s(e),e.sub){if(e.sub!==o){var r=i(e);r[e.sub]=t}}else n[e.main]=t;return t},t.getClass=function(t,e,i){var r=n[t];if(r&&r[o]&&(r=e?r[e]:null),i&&!r)throw new Error(e?"Component "+t+"."+(e||"")+" not exists. Load it first.":t+".type should be specified.");return r},t.getClassesByMainType=function(t){t=s(t);var e=[],i=n[t.main];return i&&i[o]?r.each(i,(function(t,n){n!==o&&e.push(t)})):e.push(i),e},t.hasClass=function(t){return t=s(t),!!n[t.main]},t.getAllClassMainTypes=function(){var t=[];return r.each(n,(function(e,n){t.push(n)})),t},t.hasSubTypes=function(t){t=s(t);var e=n[t.main];return e&&e[o]},t.parseClassType=s,e.registerWhenExtend){var a=t.extend;a&&(t.extend=function(e){var n=a.call(this,e);return t.registerClass(n,e.type)})}return t}function g(t,e){}e.parseClassType=s,e.enableClassExtend=u,e.enableClassCheck=c,e.enableClassManagement=p,e.setReadOnly=g},6679:function(t,e,n){var i=n("4e08"),r=(i.__DEV__,n("3eba")),a=n("cd33"),o=r.extendComponentView({type:"axis",_axisPointer:null,axisPointerClass:null,render:function(t,e,n,i){this.axisPointerClass&&a.fixValue(t),o.superApply(this,"render",arguments),s(this,t,e,n,i,!0)},updateAxisPointer:function(t,e,n,i,r){s(this,t,e,n,i,!1)},remove:function(t,e){var n=this._axisPointer;n&&n.remove(e),o.superApply(this,"remove",arguments)},dispose:function(t,e){l(this,e),o.superApply(this,"dispose",arguments)}});function s(t,e,n,i,r,s){var u=o.getAxisPointerClass(t.axisPointerClass);if(u){var h=a.getAxisPointerModel(e);h?(t._axisPointer||(t._axisPointer=new u)).render(e,h,i,s):l(t,i)}}function l(t,e,n){var i=t._axisPointer;i&&i.dispose(e,n),t._axisPointer=null}var u=[];o.registerAxisPointerClass=function(t,e){u[t]=e},o.getAxisPointerClass=function(t){return t&&u[t]};var h=o;t.exports=h},"68ab":function(t,e,n){var i=n("4a3f"),r=i.quadraticProjectPoint;function a(t,e,n,i,a,o,s,l,u){if(0===s)return!1;var h=s;if(u>e+h&&u>i+h&&u>o+h||u<e-h&&u<i-h&&u<o-h||l>t+h&&l>n+h&&l>a+h||l<t-h&&l<n-h&&l<a-h)return!1;var c=r(t,e,n,i,a,o,l,u,null);return c<=h/2}e.containStroke=a},"697e":function(t,e,n){var i=n("4e08"),r=(i.__DEV__,n("6d8b")),a=n("18c0"),o=n("89e3"),s=n("e0d8"),l=n("3842"),u=n("9d57"),h=u.prepareLayoutBarSeries,c=u.makeColumnLayout,d=u.retrieveColumnLayout,f=n("9850");function p(t,e){var n,i,a,o=t.type,s=e.getMin(),u=e.getMax(),d=null!=s,f=null!=u,p=t.getExtent();"ordinal"===o?n=e.getCategories().length:(i=e.get("boundaryGap"),r.isArray(i)||(i=[i||0,i||0]),"boolean"===typeof i[0]&&(i=[0,0]),i[0]=l.parsePercent(i[0],1),i[1]=l.parsePercent(i[1],1),a=p[1]-p[0]||Math.abs(p[0])),null==s&&(s="ordinal"===o?n?0:NaN:p[0]-i[0]*a),null==u&&(u="ordinal"===o?n?n-1:NaN:p[1]+i[1]*a),"dataMin"===s?s=p[0]:"function"===typeof s&&(s=s({min:p[0],max:p[1]})),"dataMax"===u?u=p[1]:"function"===typeof u&&(u=u({min:p[0],max:p[1]})),(null==s||!isFinite(s))&&(s=NaN),(null==u||!isFinite(u))&&(u=NaN),t.setBlank(r.eqNaN(s)||r.eqNaN(u)||"ordinal"===o&&!t.getOrdinalMeta().categories.length),e.getNeedCrossZero()&&(s>0&&u>0&&!d&&(s=0),s<0&&u<0&&!f&&(u=0));var v=e.ecModel;if(v&&"time"===o){var m,y=h("bar",v);if(r.each(y,(function(t){m|=t.getBaseAxis()===e.axis})),m){var x=c(y),_=g(s,u,e,x);s=_.min,u=_.max}}return[s,u]}function g(t,e,n,i){var a=n.axis.getExtent(),o=a[1]-a[0],s=d(i,n.axis);if(void 0===s)return{min:t,max:e};var l=1/0;r.each(s,(function(t){l=Math.min(t.offset,l)}));var u=-1/0;r.each(s,(function(t){u=Math.max(t.offset+t.width,u)})),l=Math.abs(l),u=Math.abs(u);var h=l+u,c=e-t,f=1-(l+u)/o,p=c/f-c;return e+=p*(u/h),t-=p*(l/h),{min:t,max:e}}function v(t,e){var n=p(t,e),i=null!=e.getMin(),r=null!=e.getMax(),a=e.get("splitNumber");"log"===t.type&&(t.base=e.get("logBase"));var o=t.type;t.setExtent(n[0],n[1]),t.niceExtent({splitNumber:a,fixMin:i,fixMax:r,minInterval:"interval"===o||"time"===o?e.get("minInterval"):null,maxInterval:"interval"===o||"time"===o?e.get("maxInterval"):null});var s=e.get("interval");null!=s&&t.setInterval&&t.setInterval(s)}function m(t,e){if(e=e||t.get("type"),e)switch(e){case"category":return new a(t.getOrdinalMeta?t.getOrdinalMeta():t.getCategories(),[1/0,-1/0]);case"value":return new o;default:return(s.getClass(e)||o).create(t)}}function y(t){var e=t.scale.getExtent(),n=e[0],i=e[1];return!(n>0&&i>0||n<0&&i<0)}function x(t){var e=t.getLabelModel().get("formatter"),n="category"===t.type?t.scale.getExtent()[0]:null;return"string"===typeof e?(e=function(e){return function(n){return n=t.scale.getLabel(n),e.replace("{value}",null!=n?n:"")}}(e),e):"function"===typeof e?function(i,r){return null!=n&&(r=i-n),e(_(t,i),r)}:function(e){return t.scale.getLabel(e)}}function _(t,e){return"category"===t.type?t.scale.getLabel(e):e}function b(t){var e=t.model,n=t.scale;if(e.get("axisLabel.show")&&!n.isBlank()){var i,r,a="category"===t.type,o=n.getExtent();a?r=n.count():(i=n.getTicks(),r=i.length);var s,l=t.getLabelModel(),u=x(t),h=1;r>40&&(h=Math.ceil(r/40));for(var c=0;c<r;c+=h){var d=i?i[c]:o[0]+c,f=u(d),p=l.getTextRect(f),g=w(p,l.get("rotate")||0);s?s.union(g):s=g}return s}}function w(t,e){var n=e*Math.PI/180,i=t.plain(),r=i.width,a=i.height,o=r*Math.cos(n)+a*Math.sin(n),s=r*Math.sin(n)+a*Math.cos(n),l=new f(i.x,i.y,o,s);return l}function S(t){var e=t.get("interval");return null==e?"auto":e}function M(t){return"category"===t.type&&0===S(t.getLabelModel())}n("216a"),n("8c2a"),e.getScaleExtent=p,e.niceScaleExtent=v,e.createScaleByModel=m,e.ifAxisCrossZero=y,e.makeLabelFormatter=x,e.getAxisRawValue=_,e.estimateLabelUnionRect=b,e.getOptionCategoryInterval=S,e.shouldShowAllLabels=M},"697e7":function(t,e,n){var i=n("de00"),r=n("22d1"),a=n("6d8b"),o=n("d2cf"),s=n("afa0"),l=n("ed21"),u=n("30a3"),h=n("cdaa"),c=!r.canvasSupported,d={canvas:l},f={},p="4.0.7";function g(t,e){var n=new _(i(),t,e);return f[n.id]=n,n}function v(t){if(t)t.dispose();else{for(var e in f)f.hasOwnProperty(e)&&f[e].dispose();f={}}return this}function m(t){return f[t]}function y(t,e){d[t]=e}function x(t){delete f[t]}var _=function(t,e,n){n=n||{},this.dom=e,this.id=t;var i=this,l=new s,f=n.renderer;if(c){if(!d.vml)throw new Error("You need to require 'zrender/vml/vml' to support IE8");f="vml"}else f&&d[f]||(f="canvas");var p=new d[f](e,l,n,t);this.storage=l,this.painter=p;var g=r.node||r.worker?null:new h(p.getViewportRoot());this.handler=new o(l,p,g,p.root),this.animation=new u({stage:{update:a.bind(this.flush,this)}}),this.animation.start(),this._needsRefresh;var v=l.delFromStorage,m=l.addToStorage;l.delFromStorage=function(t){v.call(l,t),t&&t.removeSelfFromZr(i)},l.addToStorage=function(t){m.call(l,t),t.addSelfToZr(i)}};_.prototype={constructor:_,getId:function(){return this.id},add:function(t){this.storage.addRoot(t),this._needsRefresh=!0},remove:function(t){this.storage.delRoot(t),this._needsRefresh=!0},configLayer:function(t,e){this.painter.configLayer&&this.painter.configLayer(t,e),this._needsRefresh=!0},setBackgroundColor:function(t){this.painter.setBackgroundColor&&this.painter.setBackgroundColor(t),this._needsRefresh=!0},refreshImmediately:function(){this._needsRefresh=!1,this.painter.refresh(),this._needsRefresh=!1},refresh:function(){this._needsRefresh=!0},flush:function(){var t;this._needsRefresh&&(t=!0,this.refreshImmediately()),this._needsRefreshHover&&(t=!0,this.refreshHoverImmediately()),t&&this.trigger("rendered")},addHover:function(t,e){if(this.painter.addHover){var n=this.painter.addHover(t,e);return this.refreshHover(),n}},removeHover:function(t){this.painter.removeHover&&(this.painter.removeHover(t),this.refreshHover())},clearHover:function(){this.painter.clearHover&&(this.painter.clearHover(),this.refreshHover())},refreshHover:function(){this._needsRefreshHover=!0},refreshHoverImmediately:function(){this._needsRefreshHover=!1,this.painter.refreshHover&&this.painter.refreshHover()},resize:function(t){t=t||{},this.painter.resize(t.width,t.height),this.handler.resize()},clearAnimation:function(){this.animation.clear()},getWidth:function(){return this.painter.getWidth()},getHeight:function(){return this.painter.getHeight()},pathToImage:function(t,e){return this.painter.pathToImage(t,e)},setCursorStyle:function(t){this.handler.setCursorStyle(t)},findHover:function(t,e){return this.handler.findHover(t,e)},on:function(t,e,n){this.handler.on(t,e,n)},off:function(t,e){this.handler.off(t,e)},trigger:function(t,e){this.handler.trigger(t,e)},clear:function(){this.storage.delRoot(),this.painter.clear()},dispose:function(){this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null,x(this.id)}},e.version=p,e.init=g,e.dispose=v,e.getInstance=m,e.registerPainter=y},"69ff":function(t,e,n){var i=n("6d8b"),r=i.each,a=i.map,o=i.isFunction,s=i.createHashMap,l=i.noop,u=n("f47d"),h=u.createTask,c=n("8918"),d=c.getUID,f=n("7e63"),p=n("843e"),g=n("e0d3"),v=g.normalizeToArray;function m(t,e,n,i){this.ecInstance=t,this.api=e,this.unfinished;n=this._dataProcessorHandlers=n.slice(),i=this._visualHandlers=i.slice();this._allHandlers=n.concat(i),this._stageTaskMap=s()}var y=m.prototype;function x(t,e,n,i,a){var o;function s(t,e){return t.setDirty&&(!t.dirtyMap||t.dirtyMap.get(e.__pipeline.id))}a=a||{},r(e,(function(e,r){if(!a.visualType||a.visualType===e.visualType){var l=t._stageTaskMap.get(e.uid),u=l.seriesTaskMap,h=l.overallTask;if(h){var c,d=h.agentStubMap;d.each((function(t){s(a,t)&&(t.dirty(),c=!0)})),c&&h.dirty(),_(h,i);var f=t.getPerformArgs(h,a.block);d.each((function(t){t.perform(f)})),o|=h.perform(f)}else u&&u.each((function(r,l){s(a,r)&&r.dirty();var u=t.getPerformArgs(r,a.block);u.skip=!e.performRawSeries&&n.isSeriesFiltered(r.context.model),_(r,i),o|=r.perform(u)}))}})),t.unfinished|=o}y.restoreData=function(t,e){t.restoreData(e),this._stageTaskMap.each((function(t){var e=t.overallTask;e&&e.dirty()}))},y.getPerformArgs=function(t,e){if(t.__pipeline){var n=this._pipelineMap.get(t.__pipeline.id),i=n.context,r=!e&&n.progressiveEnabled&&(!i||i.progressiveRender)&&t.__idxInPipeline>n.blockIndex,a=r?n.step:null,o=i&&i.modDataCount,s=null!=o?Math.ceil(o/a):null;return{step:a,modBy:s,modDataCount:o}}},y.getPipeline=function(t){return this._pipelineMap.get(t)},y.updateStreamModes=function(t,e){var n=this._pipelineMap.get(t.uid),i=t.getData(),r=i.count(),a=n.progressiveEnabled&&e.incrementalPrepareRender&&r>=n.threshold,o=t.get("large")&&r>=t.get("largeThreshold"),s="mod"===t.get("progressiveChunkMode")?r:null;t.pipelineContext=n.context={progressiveRender:a,modDataCount:s,large:o}},y.restorePipelines=function(t){var e=this,n=e._pipelineMap=s();t.eachSeries((function(t){var i=t.getProgressive(),r=t.uid;n.set(r,{id:r,head:null,tail:null,threshold:t.getProgressiveThreshold(),progressiveEnabled:i&&!(t.preventIncremental&&t.preventIncremental()),blockIndex:-1,step:Math.round(i||700),count:0}),O(e,t,t.dataTask)}))},y.prepareStageTasks=function(){var t=this._stageTaskMap,e=this.ecInstance.getModel(),n=this.api;r(this._allHandlers,(function(i){var r=t.get(i.uid)||t.set(i.uid,[]);i.reset&&b(this,i,r,e,n),i.overallReset&&w(this,i,r,e,n)}),this)},y.prepareView=function(t,e,n,i){var r=t.renderTask,a=r.context;a.model=e,a.ecModel=n,a.api=i,r.__block=!t.incrementalPrepareRender,O(this,e,r)},y.performDataProcessorTasks=function(t,e){x(this,this._dataProcessorHandlers,t,e,{block:!0})},y.performVisualTasks=function(t,e,n){x(this,this._visualHandlers,t,e,n)},y.performSeriesTasks=function(t){var e;t.eachSeries((function(t){e|=t.dataTask.perform()})),this.unfinished|=e},y.plan=function(){this._pipelineMap.each((function(t){var e=t.tail;do{if(e.__block){t.blockIndex=e.__idxInPipeline;break}e=e.getUpstream()}while(e)}))};var _=y.updatePayload=function(t,e){"remain"!==e&&(t.context.payload=e)};function b(t,e,n,i,r){var a=n.seriesTaskMap||(n.seriesTaskMap=s()),o=e.seriesType,l=e.getTargetSeries;function u(n){var o=n.uid,s=a.get(o)||a.set(o,h({plan:C,reset:I,count:P}));s.context={model:n,ecModel:i,api:r,useClearVisual:e.isVisual&&!e.isLayout,plan:e.plan,reset:e.reset,scheduler:t},O(t,n,s)}e.createOnAllSeries?i.eachRawSeries(u):o?i.eachRawSeriesByType(o,u):l&&l(i,r).each(u);var c=t._pipelineMap;a.each((function(t,e){c.get(e)||(t.dispose(),a.removeKey(e))}))}function w(t,e,n,i,a){var o=n.overallTask=n.overallTask||h({reset:S});o.context={ecModel:i,api:a,overallReset:e.overallReset,scheduler:t};var l=o.agentStubMap=o.agentStubMap||s(),u=e.seriesType,c=e.getTargetSeries,d=!0,f=e.modifyOutputEnd;function p(e){var n=e.uid,i=l.get(n);i||(i=l.set(n,h({reset:M,onDirty:A})),o.dirty()),i.context={model:e,overallProgress:d,modifyOutputEnd:f},i.agent=o,i.__block=d,O(t,e,i)}u?i.eachRawSeriesByType(u,p):c?c(i,a).each(p):(d=!1,r(i.getSeries(),p));var g=t._pipelineMap;l.each((function(t,e){g.get(e)||(t.dispose(),o.dirty(),l.removeKey(e))}))}function S(t){t.overallReset(t.ecModel,t.api,t.payload)}function M(t,e){return t.overallProgress&&T}function T(){this.agent.dirty(),this.getDownstream().dirty()}function A(){this.agent&&this.agent.dirty()}function C(t){return t.plan&&t.plan(t.model,t.ecModel,t.api,t.payload)}function I(t){t.useClearVisual&&t.data.clearAllVisual();var e=t.resetDefines=v(t.reset(t.model,t.ecModel,t.api,t.payload));return e.length>1?a(e,(function(t,e){return D(e)})):k}var k=D(0);function D(t){return function(e,n){var i=n.data,r=n.resetDefines[t];if(r&&r.dataEach)for(var a=e.start;a<e.end;a++)r.dataEach(i,a);else r&&r.progress&&r.progress(e,i)}}function P(t){return t.data.count()}function O(t,e,n){var i=e.uid,r=t._pipelineMap.get(i);!r.head&&(r.head=n),r.tail&&r.tail.pipe(n),r.tail=n,n.__idxInPipeline=r.count++,n.__pipeline=r}function L(t){E=null;try{t(R,B)}catch(e){}return E}m.wrapStageHandler=function(t,e){return o(t)&&(t={overallReset:t,seriesType:L(t)}),t.uid=d("stageHandler"),e&&(t.visualType=e),t};var E,R={},B={};function N(t,e){for(var n in e.prototype)t[n]=l}N(R,f),N(B,p),R.eachSeriesByType=R.eachRawSeriesByType=function(t){E=t},R.eachComponent=function(t){"series"===t.mainType&&t.subType&&(E=t.subType)};var z=m;t.exports=z},"6acf":function(t,e,n){var i=n("eda2"),r=n("dcb3"),a=n("2306"),o=n("ff2e"),s=n("1687"),l=n("fab22"),u=n("6679"),h=r.extend({makeElOption:function(t,e,n,r,a){var s=n.axis;"angle"===s.dim&&(this.animationThreshold=Math.PI/18);var l,u=s.polar,h=u.getOtherAxis(s),f=h.getExtent();l=s["dataTo"+i.capitalFirst(s.dim)](e);var p=r.get("type");if(p&&"none"!==p){var g=o.buildElStyle(r),v=d[p](s,u,l,f,g);v.style=g,t.graphicKey=v.type,t.pointer=v}var m=r.get("label.margin"),y=c(e,n,r,u,m);o.buildLabelElOption(t,n,r,a,y)}});function c(t,e,n,i,r){var o=e.axis,u=o.dataToCoord(t),h=i.getAngleAxis().getExtent()[0];h=h/180*Math.PI;var c,d,f,p=i.getRadiusAxis().getExtent();if("radius"===o.dim){var g=s.create();s.rotate(g,g,h),s.translate(g,g,[i.cx,i.cy]),c=a.applyTransform([u,-r],g);var v=e.getModel("axisLabel").get("rotate")||0,m=l.innerTextLayout(h,v*Math.PI/180,-1);d=m.textAlign,f=m.textVerticalAlign}else{var y=p[1];c=i.coordToPoint([y+r,u]);var x=i.cx,_=i.cy;d=Math.abs(c[0]-x)/y<.3?"center":c[0]>x?"left":"right",f=Math.abs(c[1]-_)/y<.3?"middle":c[1]>_?"top":"bottom"}return{position:c,align:d,verticalAlign:f}}var d={line:function(t,e,n,i,r){return"angle"===t.dim?{type:"Line",shape:o.makeLineShape(e.coordToPoint([i[0],n]),e.coordToPoint([i[1],n]))}:{type:"Circle",shape:{cx:e.cx,cy:e.cy,r:n}}},shadow:function(t,e,n,i,r){var a=Math.max(1,t.getBandWidth()),s=Math.PI/180;return"angle"===t.dim?{type:"Sector",shape:o.makeSectorShape(e.cx,e.cy,i[0],i[1],(-n-a/2)*s,(a/2-n)*s)}:{type:"Sector",shape:o.makeSectorShape(e.cx,e.cy,n-a/2,n+a/2,0,2*Math.PI)}}};u.registerAxisPointerClass("PolarAxisPointer",h);var f=h;t.exports=f},"6cb7":function(t,e,n){var i=n("6d8b"),r=n("4319"),a=n("8918"),o=n("625e"),s=o.enableClassManagement,l=o.parseClassType,u=n("e0d3"),h=u.makeInner,c=n("f934"),d=n("de1c"),f=h(),p=r.extend({type:"component",id:"",name:"",mainType:"",subType:"",componentIndex:0,defaultOption:null,ecModel:null,dependentModels:[],uid:null,layoutMode:null,$constructor:function(t,e,n,i){r.call(this,t,e,n,i),this.uid=a.getUID("ec_cpt_model")},init:function(t,e,n,i){this.mergeDefaultAndTheme(t,n)},mergeDefaultAndTheme:function(t,e){var n=this.layoutMode,r=n?c.getLayoutParams(t):{},a=e.getTheme();i.merge(t,a.get(this.mainType)),i.merge(t,this.getDefaultOption()),n&&c.mergeLayoutParam(t,r,n)},mergeOption:function(t,e){i.merge(this.option,t,!0);var n=this.layoutMode;n&&c.mergeLayoutParam(this.option,t,n)},optionUpdated:function(t,e){},getDefaultOption:function(){var t=f(this);if(!t.defaultOption){var e=[],n=this.constructor;while(n){var r=n.prototype.defaultOption;r&&e.push(r),n=n.superClass}for(var a={},o=e.length-1;o>=0;o--)a=i.merge(a,e[o],!0);t.defaultOption=a}return t.defaultOption},getReferringComponents:function(t){return this.ecModel.queryComponents({mainType:t,index:this.get(t+"Index",!0),id:this.get(t+"Id",!0)})}});function g(t){var e=[];return i.each(p.getClassesByMainType(t),(function(t){e=e.concat(t.prototype.dependencies||[])})),e=i.map(e,(function(t){return l(t).main})),"dataset"!==t&&i.indexOf(e,"dataset")<=0&&e.unshift("dataset"),e}s(p,{registerWhenExtend:!0}),a.enableSubTypeDefaulter(p),a.enableTopologicalTravel(p,g),i.mixin(p,d);var v=p;t.exports=v},"6d8b":function(t,e){var n={"[object Function]":1,"[object RegExp]":1,"[object Date]":1,"[object Error]":1,"[object CanvasGradient]":1,"[object CanvasPattern]":1,"[object Image]":1,"[object Canvas]":1},i={"[object Int8Array]":1,"[object Uint8Array]":1,"[object Uint8ClampedArray]":1,"[object Int16Array]":1,"[object Uint16Array]":1,"[object Int32Array]":1,"[object Uint32Array]":1,"[object Float32Array]":1,"[object Float64Array]":1},r=Object.prototype.toString,a=Array.prototype,o=a.forEach,s=a.filter,l=a.slice,u=a.map,h=a.reduce,c={};function d(t,e){"createCanvas"===t&&(y=null),c[t]=e}function f(t){if(null==t||"object"!==typeof t)return t;var e=t,a=r.call(t);if("[object Array]"===a){if(!Z(t)){e=[];for(var o=0,s=t.length;o<s;o++)e[o]=f(t[o])}}else if(i[a]){if(!Z(t)){var l=t.constructor;if(t.constructor.from)e=l.from(t);else{e=new l(t.length);for(o=0,s=t.length;o<s;o++)e[o]=f(t[o])}}}else if(!n[a]&&!Z(t)&&!z(t))for(var u in e={},t)t.hasOwnProperty(u)&&(e[u]=f(t[u]));return e}function p(t,e,n){if(!R(e)||!R(t))return n?f(e):t;for(var i in e)if(e.hasOwnProperty(i)){var r=t[i],a=e[i];!R(a)||!R(r)||O(a)||O(r)||z(a)||z(r)||B(a)||B(r)||Z(a)||Z(r)?!n&&i in t||(t[i]=f(e[i],!0)):p(r,a,n)}return t}function g(t,e){for(var n=t[0],i=1,r=t.length;i<r;i++)n=p(n,t[i],e);return n}function v(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n]);return t}function m(t,e,n){for(var i in e)e.hasOwnProperty(i)&&(n?null!=e[i]:null==t[i])&&(t[i]=e[i]);return t}var y,x=function(){return c.createCanvas()};function _(){return y||(y=x().getContext("2d")),y}function b(t,e){if(t){if(t.indexOf)return t.indexOf(e);for(var n=0,i=t.length;n<i;n++)if(t[n]===e)return n}return-1}function w(t,e){var n=t.prototype;function i(){}for(var r in i.prototype=e.prototype,t.prototype=new i,n)t.prototype[r]=n[r];t.prototype.constructor=t,t.superClass=e}function S(t,e,n){t="prototype"in t?t.prototype:t,e="prototype"in e?e.prototype:e,m(t,e,n)}function M(t){if(t)return"string"!==typeof t&&"number"===typeof t.length}function T(t,e,n){if(t&&e)if(t.forEach&&t.forEach===o)t.forEach(e,n);else if(t.length===+t.length)for(var i=0,r=t.length;i<r;i++)e.call(n,t[i],i,t);else for(var a in t)t.hasOwnProperty(a)&&e.call(n,t[a],a,t)}function A(t,e,n){if(t&&e){if(t.map&&t.map===u)return t.map(e,n);for(var i=[],r=0,a=t.length;r<a;r++)i.push(e.call(n,t[r],r,t));return i}}function C(t,e,n,i){if(t&&e){if(t.reduce&&t.reduce===h)return t.reduce(e,n,i);for(var r=0,a=t.length;r<a;r++)n=e.call(i,n,t[r],r,t);return n}}function I(t,e,n){if(t&&e){if(t.filter&&t.filter===s)return t.filter(e,n);for(var i=[],r=0,a=t.length;r<a;r++)e.call(n,t[r],r,t)&&i.push(t[r]);return i}}function k(t,e,n){if(t&&e)for(var i=0,r=t.length;i<r;i++)if(e.call(n,t[i],i,t))return t[i]}function D(t,e){var n=l.call(arguments,2);return function(){return t.apply(e,n.concat(l.call(arguments)))}}function P(t){var e=l.call(arguments,1);return function(){return t.apply(this,e.concat(l.call(arguments)))}}function O(t){return"[object Array]"===r.call(t)}function L(t){return"function"===typeof t}function E(t){return"[object String]"===r.call(t)}function R(t){var e=typeof t;return"function"===e||!!t&&"object"===e}function B(t){return!!n[r.call(t)]}function N(t){return!!i[r.call(t)]}function z(t){return"object"===typeof t&&"number"===typeof t.nodeType&&"object"===typeof t.ownerDocument}function F(t){return t!==t}function V(t){for(var e=0,n=arguments.length;e<n;e++)if(null!=arguments[e])return arguments[e]}function H(t,e){return null!=t?t:e}function W(t,e,n){return null!=t?t:null!=e?e:n}function G(){return Function.call.apply(l,arguments)}function U(t){if("number"===typeof t)return[t,t,t,t];var e=t.length;return 2===e?[t[0],t[1],t[0],t[1]]:3===e?[t[0],t[1],t[2],t[1]]:t}function q(t,e){if(!t)throw new Error(e)}function Y(t){return null==t?null:"function"===typeof t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}c.createCanvas=function(){return document.createElement("canvas")};var X="__ec_primitive__";function j(t){t[X]=!0}function Z(t){return t[X]}function K(t){var e=O(t);this.data={};var n=this;function i(t,i){e?n.set(t,i):n.set(i,t)}t instanceof K?t.each(i):t&&T(t,i)}function $(t){return new K(t)}function Q(t,e){for(var n=new t.constructor(t.length+e.length),i=0;i<t.length;i++)n[i]=t[i];var r=t.length;for(i=0;i<e.length;i++)n[i+r]=e[i];return n}function J(){}K.prototype={constructor:K,get:function(t){return this.data.hasOwnProperty(t)?this.data[t]:null},set:function(t,e){return this.data[t]=e},each:function(t,e){for(var n in void 0!==e&&(t=D(t,e)),this.data)this.data.hasOwnProperty(n)&&t(this.data[n],n)},removeKey:function(t){delete this.data[t]}},e.$override=d,e.clone=f,e.merge=p,e.mergeAll=g,e.extend=v,e.defaults=m,e.createCanvas=x,e.getContext=_,e.indexOf=b,e.inherits=w,e.mixin=S,e.isArrayLike=M,e.each=T,e.map=A,e.reduce=C,e.filter=I,e.find=k,e.bind=D,e.curry=P,e.isArray=O,e.isFunction=L,e.isString=E,e.isObject=R,e.isBuiltInObject=B,e.isTypedArray=N,e.isDom=z,e.eqNaN=F,e.retrieve=V,e.retrieve2=H,e.retrieve3=W,e.slice=G,e.normalizeCssArray=U,e.assert=q,e.trim=Y,e.setAsPrimitive=j,e.isPrimitive=Z,e.createHashMap=$,e.concatArray=Q,e.noop=J},"71ad":function(t,e,n){var i=n("6d8b"),r={show:!0,zlevel:0,z:0,inverse:!1,name:"",nameLocation:"end",nameRotate:null,nameTruncate:{maxWidth:null,ellipsis:"...",placeholder:"."},nameTextStyle:{},nameGap:15,silent:!1,triggerEvent:!1,tooltip:{show:!1},axisPointer:{},axisLine:{show:!0,onZero:!0,onZeroAxisIndex:null,lineStyle:{color:"#333",width:1,type:"solid"},symbol:["none","none"],symbolSize:[10,15]},axisTick:{show:!0,inside:!1,length:5,lineStyle:{width:1}},axisLabel:{show:!0,inside:!1,rotate:0,showMinLabel:null,showMaxLabel:null,margin:8,fontSize:12},splitLine:{show:!0,lineStyle:{color:["#ccc"],width:1,type:"solid"}},splitArea:{show:!1,areaStyle:{color:["rgba(250,250,250,0.3)","rgba(200,200,200,0.3)"]}}},a={};a.categoryAxis=i.merge({boundaryGap:!0,deduplication:null,splitLine:{show:!1},axisTick:{alignWithLabel:!1,interval:"auto"},axisLabel:{interval:"auto"}},r),a.valueAxis=i.merge({boundaryGap:[0,0],splitNumber:5},r),a.timeAxis=i.defaults({scale:!0,min:"dataMin",max:"dataMax"},a.valueAxis),a.logAxis=i.defaults({scale:!0,logBase:10},a.valueAxis);var o=a;t.exports=o},"74cb":function(t,e){var n={linear:function(t){return t},quadraticIn:function(t){return t*t},quadraticOut:function(t){return t*(2-t)},quadraticInOut:function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)},cubicIn:function(t){return t*t*t},cubicOut:function(t){return--t*t*t+1},cubicInOut:function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)},quarticIn:function(t){return t*t*t*t},quarticOut:function(t){return 1- --t*t*t*t},quarticInOut:function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)},quinticIn:function(t){return t*t*t*t*t},quinticOut:function(t){return--t*t*t*t*t+1},quinticInOut:function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)},sinusoidalIn:function(t){return 1-Math.cos(t*Math.PI/2)},sinusoidalOut:function(t){return Math.sin(t*Math.PI/2)},sinusoidalInOut:function(t){return.5*(1-Math.cos(Math.PI*t))},exponentialIn:function(t){return 0===t?0:Math.pow(1024,t-1)},exponentialOut:function(t){return 1===t?1:1-Math.pow(2,-10*t)},exponentialInOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?.5*Math.pow(1024,t-1):.5*(2-Math.pow(2,-10*(t-1)))},circularIn:function(t){return 1-Math.sqrt(1-t*t)},circularOut:function(t){return Math.sqrt(1- --t*t)},circularInOut:function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)},elasticIn:function(t){var e,n=.1,i=.4;return 0===t?0:1===t?1:(!n||n<1?(n=1,e=i/4):e=i*Math.asin(1/n)/(2*Math.PI),-n*Math.pow(2,10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/i))},elasticOut:function(t){var e,n=.1,i=.4;return 0===t?0:1===t?1:(!n||n<1?(n=1,e=i/4):e=i*Math.asin(1/n)/(2*Math.PI),n*Math.pow(2,-10*t)*Math.sin((t-e)*(2*Math.PI)/i)+1)},elasticInOut:function(t){var e,n=.1,i=.4;return 0===t?0:1===t?1:(!n||n<1?(n=1,e=i/4):e=i*Math.asin(1/n)/(2*Math.PI),(t*=2)<1?n*Math.pow(2,10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/i)*-.5:n*Math.pow(2,-10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/i)*.5+1)},backIn:function(t){var e=1.70158;return t*t*((e+1)*t-e)},backOut:function(t){var e=1.70158;return--t*t*((e+1)*t+e)+1},backInOut:function(t){var e=2.5949095;return(t*=2)<1?t*t*((e+1)*t-e)*.5:.5*((t-=2)*t*((e+1)*t+e)+2)},bounceIn:function(t){return 1-n.bounceOut(1-t)},bounceOut:function(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},bounceInOut:function(t){return t<.5?.5*n.bounceIn(2*t):.5*n.bounceOut(2*t-1)+.5}},i=n;t.exports=i},"76a5":function(t,e,n){var i=n("19eb"),r=n("6d8b"),a=n("e86a"),o=n("a73c"),s=n("82eb"),l=s.ContextCachedBy,u=function(t){i.call(this,t)};u.prototype={constructor:u,type:"text",brush:function(t,e){var n=this.style;this.__dirty&&o.normalizeTextStyle(n,!0),n.fill=n.stroke=n.shadowBlur=n.shadowColor=n.shadowOffsetX=n.shadowOffsetY=null;var i=n.text;null!=i&&(i+=""),o.needDrawText(i,n)?(this.setTransform(t),o.renderText(this,t,i,n,null,e),this.restoreTransform(t)):t.__attrCachedBy=l.NONE},getBoundingRect:function(){var t=this.style;if(this.__dirty&&o.normalizeTextStyle(t,!0),!this._rect){var e=t.text;null!=e?e+="":e="";var n=a.getBoundingRect(t.text+"",t.font,t.textAlign,t.textVerticalAlign,t.textPadding,t.textLineHeight,t.rich);if(n.x+=t.x||0,n.y+=t.y||0,o.getStroke(t.textStroke,t.textStrokeWidth)){var i=t.textStrokeWidth;n.x-=i/2,n.y-=i/2,n.width+=i,n.height+=i}this._rect=n}return this._rect}},r.inherits(u,i);var h=u;t.exports=h},"78f0":function(t,e,n){var i=n("3eba");n("d9f1");var r=i.extendComponentModel({type:"polar",dependencies:["polarAxis","angleAxis"],coordinateSystem:null,findAxisModel:function(t){var e,n=this.ecModel;return n.eachComponent(t,(function(t){t.getCoordSysModel()===this&&(e=t)}),this),e},defaultOption:{zlevel:0,z:0,center:["50%","50%"],radius:"80%"}});t.exports=r},"792e":function(t,e,n){n("1ccf"),n("14d3")},"7d6d":function(t,e){var n={shadowBlur:1,shadowOffsetX:1,shadowOffsetY:1,textShadowBlur:1,textShadowOffsetX:1,textShadowOffsetY:1,textBoxShadowBlur:1,textBoxShadowOffsetX:1,textBoxShadowOffsetY:1};function i(t,e,i){return n.hasOwnProperty(e)?i*t.dpr:i}t.exports=i},"7e63":function(t,e,n){var i=n("4e08"),r=(i.__DEV__,n("6d8b")),a=r.each,o=r.filter,s=r.map,l=r.isArray,u=r.indexOf,h=r.isObject,c=r.isString,d=r.createHashMap,f=r.assert,p=r.clone,g=r.merge,v=r.extend,m=r.mixin,y=n("e0d3"),x=n("4319"),_=n("6cb7"),b=n("8971"),w=n("e47b"),S=n("0f99"),M=S.resetSourceDefaulter,T="\0_ec_inner",A=x.extend({init:function(t,e,n,i){n=n||{},this.option=null,this._theme=new x(n),this._optionManager=i},setOption:function(t,e){f(!(T in t),"please use chart.getOption()"),this._optionManager.setOption(t,e),this.resetOption(null)},resetOption:function(t){var e=!1,n=this._optionManager;if(!t||"recreate"===t){var i=n.mountOption("recreate"===t);this.option&&"recreate"!==t?(this.restoreData(),this.mergeOption(i)):k.call(this,i),e=!0}if("timeline"!==t&&"media"!==t||this.restoreData(),!t||"recreate"===t||"timeline"===t){var r=n.getTimelineOption(this);r&&(this.mergeOption(r),e=!0)}if(!t||"recreate"===t||"media"===t){var o=n.getMediaOption(this,this._api);o.length&&a(o,(function(t){this.mergeOption(t,e=!0)}),this)}return e},mergeOption:function(t){var e=this.option,n=this._componentsMap,i=[];function r(i,r){var o=y.normalizeToArray(t[i]),s=y.mappingToExists(n.get(i),o);y.makeIdAndName(s),a(s,(function(t,e){var n=t.option;h(n)&&(t.keyInfo.mainType=i,t.keyInfo.subType=P(i,n,t.exist))}));var l=D(n,r);e[i]=[],n.set(i,[]),a(s,(function(t,r){var a=t.exist,o=t.option;if(f(h(o)||a,"Empty component definition"),o){var s=_.getClass(i,t.keyInfo.subType,!0);if(a&&a instanceof s)a.name=t.keyInfo.name,a.mergeOption(o,this),a.optionUpdated(o,!1);else{var u=v({dependentModels:l,componentIndex:r},t.keyInfo);a=new s(o,this,this,u),v(a,u),a.init(o,this,this,u),a.optionUpdated(null,!0)}}else a.mergeOption({},this),a.optionUpdated({},!1);n.get(i)[r]=a,e[i][r]=a.option}),this),"series"===i&&O(this,n.get("series"))}M(this),a(t,(function(t,n){null!=t&&(_.hasClass(n)?n&&i.push(n):e[n]=null==e[n]?p(t):g(e[n],t,!0))})),_.topologicalTravel(i,_.getAllClassMainTypes(),r,this),this._seriesIndicesMap=d(this._seriesIndices=this._seriesIndices||[])},getOption:function(){var t=p(this.option);return a(t,(function(e,n){if(_.hasClass(n)){e=y.normalizeToArray(e);for(var i=e.length-1;i>=0;i--)y.isIdInner(e[i])&&e.splice(i,1);t[n]=e}})),delete t[T],t},getTheme:function(){return this._theme},getComponent:function(t,e){var n=this._componentsMap.get(t);if(n)return n[e||0]},queryComponents:function(t){var e=t.mainType;if(!e)return[];var n,i=t.index,r=t.id,a=t.name,h=this._componentsMap.get(e);if(!h||!h.length)return[];if(null!=i)l(i)||(i=[i]),n=o(s(i,(function(t){return h[t]})),(function(t){return!!t}));else if(null!=r){var c=l(r);n=o(h,(function(t){return c&&u(r,t.id)>=0||!c&&t.id===r}))}else if(null!=a){var d=l(a);n=o(h,(function(t){return d&&u(a,t.name)>=0||!d&&t.name===a}))}else n=h.slice();return L(n,t)},findComponents:function(t){var e=t.query,n=t.mainType,i=a(e),r=i?this.queryComponents(i):this._componentsMap.get(n);return s(L(r,t));function a(t){var e=n+"Index",i=n+"Id",r=n+"Name";return!t||null==t[e]&&null==t[i]&&null==t[r]?null:{mainType:n,index:t[e],id:t[i],name:t[r]}}function s(e){return t.filter?o(e,t.filter):e}},eachComponent:function(t,e,n){var i=this._componentsMap;if("function"===typeof t)n=e,e=t,i.each((function(t,i){a(t,(function(t,r){e.call(n,i,t,r)}))}));else if(c(t))a(i.get(t),e,n);else if(h(t)){var r=this.findComponents(t);a(r,e,n)}},getSeriesByName:function(t){var e=this._componentsMap.get("series");return o(e,(function(e){return e.name===t}))},getSeriesByIndex:function(t){return this._componentsMap.get("series")[t]},getSeriesByType:function(t){var e=this._componentsMap.get("series");return o(e,(function(e){return e.subType===t}))},getSeries:function(){return this._componentsMap.get("series").slice()},getSeriesCount:function(){return this._componentsMap.get("series").length},eachSeries:function(t,e){E(this),a(this._seriesIndices,(function(n){var i=this._componentsMap.get("series")[n];t.call(e,i,n)}),this)},eachRawSeries:function(t,e){a(this._componentsMap.get("series"),t,e)},eachSeriesByType:function(t,e,n){E(this),a(this._seriesIndices,(function(i){var r=this._componentsMap.get("series")[i];r.subType===t&&e.call(n,r,i)}),this)},eachRawSeriesByType:function(t,e,n){return a(this.getSeriesByType(t),e,n)},isSeriesFiltered:function(t){return E(this),null==this._seriesIndicesMap.get(t.componentIndex)},getCurrentSeriesIndices:function(){return(this._seriesIndices||[]).slice()},filterSeries:function(t,e){E(this);var n=o(this._componentsMap.get("series"),t,e);O(this,n)},restoreData:function(t){var e=this._componentsMap;O(this,e.get("series"));var n=[];e.each((function(t,e){n.push(e)})),_.topologicalTravel(n,_.getAllClassMainTypes(),(function(n,i){a(e.get(n),(function(e){("series"!==n||!C(e,t))&&e.restoreData()}))}))}});function C(t,e){if(e){var n=e.seiresIndex,i=e.seriesId,r=e.seriesName;return null!=n&&t.componentIndex!==n||null!=i&&t.id!==i||null!=r&&t.name!==r}}function I(t,e){var n=t.color&&!t.colorLayer;a(e,(function(e,i){"colorLayer"===i&&n||_.hasClass(i)||("object"===typeof e?t[i]=t[i]?g(t[i],e,!1):p(e):null==t[i]&&(t[i]=e))}))}function k(t){t=t,this.option={},this.option[T]=1,this._componentsMap=d({series:[]}),this._seriesIndices,this._seriesIndicesMap,I(t,this._theme.option),g(t,b,!1),this.mergeOption(t)}function D(t,e){l(e)||(e=e?[e]:[]);var n={};return a(e,(function(e){n[e]=(t.get(e)||[]).slice()})),n}function P(t,e,n){var i=e.type?e.type:n?n.subType:_.determineSubType(t,e);return i}function O(t,e){t._seriesIndicesMap=d(t._seriesIndices=s(e,(function(t){return t.componentIndex}))||[])}function L(t,e){return e.hasOwnProperty("subType")?o(t,(function(t){return t.subType===e.subType})):t}function E(t){}m(A,w);var R=A;t.exports=R},"7f96":function(t,e){function n(t,e,n){return{seriesType:t,performRawSeries:!0,reset:function(t,i,r){var a=t.getData(),o=t.get("symbol")||e,s=t.get("symbolSize"),l=t.get("symbolKeepAspect");if(a.setVisual({legendSymbol:n||o,symbol:o,symbolSize:s,symbolKeepAspect:l}),!i.isSeriesFiltered(t)){var u="function"===typeof s;return{dataEach:a.hasItemOption||u?h:null}}function h(e,n){if("function"===typeof s){var i=t.getRawValue(n),r=t.getDataParams(n);e.setItemVisual(n,"symbolSize",s(i,r))}if(e.hasItemOption){var a=e.getItemModel(n),o=a.getShallow("symbol",!0),l=a.getShallow("symbolSize",!0),u=a.getShallow("symbolKeepAspect",!0);null!=o&&e.setItemVisual(n,"symbol",o),null!=l&&e.setItemVisual(n,"symbolSize",l),null!=u&&e.setItemVisual(n,"symbolKeepAspect",u)}}}}}t.exports=n},"80f0":function(t,e){function n(t){return t}function i(t,e,i,r,a){this._old=t,this._new=e,this._oldKeyGetter=i||n,this._newKeyGetter=r||n,this.context=a}function r(t,e,n,i,r){for(var a=0;a<t.length;a++){var o="_ec_"+r[i](t[a],a),s=e[o];null==s?(n.push(o),e[o]=a):(s.length||(e[o]=s=[s]),s.push(a))}}i.prototype={constructor:i,add:function(t){return this._add=t,this},update:function(t){return this._update=t,this},remove:function(t){return this._remove=t,this},execute:function(){var t=this._old,e=this._new,n={},i={},a=[],o=[];for(r(t,n,a,"_oldKeyGetter",this),r(e,i,o,"_newKeyGetter",this),h=0;h<t.length;h++){var s=a[h],l=i[s];if(null!=l){var u=l.length;u?(1===u&&(i[s]=null),l=l.unshift()):i[s]=null,this._update&&this._update(l,h)}else this._remove&&this._remove(h)}for(var h=0;h<o.length;h++){s=o[h];if(i.hasOwnProperty(s)){l=i[s];if(null==l)continue;if(l.length){var c=0;for(u=l.length;c<u;c++)this._add&&this._add(l[c])}else this._add&&this._add(l)}}}};var a=i;t.exports=a},"82eb":function(t,e){var n={NONE:0,STYLE_BIND:1,PLAIN_TEXT:2},i=9;e.ContextCachedBy=n,e.WILL_BE_RESTORED=i},"843e":function(t,e,n){var i=n("6d8b"),r=["getDom","getZr","getWidth","getHeight","getDevicePixelRatio","dispatchAction","isDisposed","on","off","getDataURL","getConnectedDataURL","getModel","getOption","getViewOfComponentModel","getViewOfSeriesModel"];function a(t){i.each(r,(function(e){this[e]=i.bind(t[e],t)}),this)}var o=a;t.exports=o},"84ce":function(t,e,n){var i=n("6d8b"),r=i.each,a=i.map,o=n("3842"),s=o.linearMap,l=o.getPixelPrecision,u=n("e073"),h=u.createAxisTicks,c=u.createAxisLabels,d=u.calculateCategoryInterval,f=[0,1],p=function(t,e,n){this.dim=t,this.scale=e,this._extent=n||[0,0],this.inverse=!1,this.onBand=!1};function g(t,e){var n=t[1]-t[0],i=e,r=n/i/2;t[0]+=r,t[1]-=r}function v(t,e,n,i,a){var o=e.length;if(t.onBand&&!i&&o){var s,l=t.getExtent();if(1===o)e[0].coord=l[0],s=e[1]={coord:l[0]};else{var u=e[1].coord-e[0].coord;r(e,(function(t){t.coord-=u/2;var e=e||0;e%2>0&&(t.coord-=u/(2*(e+1)))})),s={coord:e[o-1].coord+u},e.push(s)}var h=l[0]>l[1];c(e[0].coord,l[0])&&(a?e[0].coord=l[0]:e.shift()),a&&c(l[0],e[0].coord)&&e.unshift({coord:l[0]}),c(l[1],s.coord)&&(a?s.coord=l[1]:e.pop()),a&&c(s.coord,l[1])&&e.push({coord:l[1]})}function c(t,e){return h?t>e:t<e}}p.prototype={constructor:p,contain:function(t){var e=this._extent,n=Math.min(e[0],e[1]),i=Math.max(e[0],e[1]);return t>=n&&t<=i},containData:function(t){return this.contain(this.dataToCoord(t))},getExtent:function(){return this._extent.slice()},getPixelPrecision:function(t){return l(t||this.scale.getExtent(),this._extent)},setExtent:function(t,e){var n=this._extent;n[0]=t,n[1]=e},dataToCoord:function(t,e){var n=this._extent,i=this.scale;return t=i.normalize(t),this.onBand&&"ordinal"===i.type&&(n=n.slice(),g(n,i.count())),s(t,f,n,e)},coordToData:function(t,e){var n=this._extent,i=this.scale;this.onBand&&"ordinal"===i.type&&(n=n.slice(),g(n,i.count()));var r=s(t,n,f,e);return this.scale.scale(r)},pointToData:function(t,e){},getTicksCoords:function(t){t=t||{};var e=t.tickModel||this.getTickModel(),n=h(this,e),i=n.ticks,r=a(i,(function(t){return{coord:this.dataToCoord(t),tickValue:t}}),this),o=e.get("alignWithLabel");return v(this,r,n.tickCategoryInterval,o,t.clamp),r},getViewLabels:function(){return c(this).labels},getLabelModel:function(){return this.model.getModel("axisLabel")},getTickModel:function(){return this.model.getModel("axisTick")},getBandWidth:function(){var t=this._extent,e=this.scale.getExtent(),n=e[1]-e[0]+(this.onBand?1:0);0===n&&(n=1);var i=Math.abs(t[1]-t[0]);return Math.abs(i)/n},isHorizontal:null,getRotate:null,calculateCategoryInterval:function(){return d(this)}};var m=p;t.exports=m},"857d":function(t,e){var n=2*Math.PI;function i(t){return t%=n,t<0&&(t+=n),t}e.normalizeRadian=i},"862d":function(t,e,n){var i=n("6d8b"),r=i.createHashMap,a=i.each,o=i.isString,s=i.defaults,l=i.extend,u=i.isObject,h=i.clone,c=n("e0d3"),d=c.normalizeToArray,f=n("0f99"),p=f.guessOrdinal,g=n("ec6f"),v=n("2f45"),m=v.OTHER_DIMENSIONS;function y(t,e,n){g.isInstance(e)||(e=g.seriesDataToSource(e)),n=n||{},t=(t||[]).slice();for(var i=(n.dimsDef||[]).slice(),c=r(n.encodeDef),f=r(),v=r(),y=[],b=x(e,t,i,n.dimCount),w=0;w<b;w++){var S=i[w]=l({},u(i[w])?i[w]:{name:i[w]}),M=S.name,T=y[w]={otherDims:{}};null!=M&&null==f.get(M)&&(T.name=T.displayName=M,f.set(M,w)),null!=S.type&&(T.type=S.type),null!=S.displayName&&(T.displayName=S.displayName)}c.each((function(t,e){if(t=d(t).slice(),1===t.length&&t[0]<0)c.set(e,!1);else{var n=c.set(e,[]);a(t,(function(t,i){o(t)&&(t=f.get(t)),null!=t&&t<b&&(n[i]=t,C(y[t],e,i))}))}}));var A=0;function C(t,e,n){null!=m.get(e)?t.otherDims[e]=n:(t.coordDim=e,t.coordDimIndex=n,v.set(e,!0))}a(t,(function(t,e){var n,i,r;if(o(t))n=t,t={};else{n=t.name;var l=t.ordinalMeta;t.ordinalMeta=null,t=h(t),t.ordinalMeta=l,i=t.dimsDef,r=t.otherDims,t.name=t.coordDim=t.coordDimIndex=t.dimsDef=t.otherDims=null}var f=c.get(n);if(!1!==f){f=d(f);if(!f.length)for(var p=0;p<(i&&i.length||1);p++){while(A<y.length&&null!=y[A].coordDim)A++;A<y.length&&f.push(A++)}a(f,(function(e,a){var o=y[e];if(C(s(o,t),n,a),null==o.name&&i){var l=i[a];!u(l)&&(l={name:l}),o.name=o.displayName=l.name,o.defaultTooltip=l.defaultTooltip}r&&s(o.otherDims,r)}))}}));var I=n.generateCoord,k=n.generateCoordCount,D=null!=k;k=I?k||1:0;for(var P=I||"value",O=0;O<b;O++){T=y[O]=y[O]||{};var L=T.coordDim;null==L&&(T.coordDim=_(P,v,D),T.coordDimIndex=0,(!I||k<=0)&&(T.isExtraCoord=!0),k--),null==T.name&&(T.name=_(T.coordDim,f)),null==T.type&&p(e,O,T.name)&&(T.type="ordinal")}return y}function x(t,e,n,i){var r=Math.max(t.dimensionsDetectCount||1,e.length,n.length,i||0);return a(e,(function(t){var e=t.dimsDef;e&&(r=Math.max(r,e.length))})),r}function _(t,e,n){if(n||null!=e.get(t)){var i=0;while(null!=e.get(t+i))i++;t+=i}return e.set(t,!0),t}var b=y;t.exports=b},8728:function(t,e){function n(t,e,n,i,r,a){if(a>e&&a>i||a<e&&a<i)return 0;if(i===e)return 0;var o=i<e?1:-1,s=(a-e)/(i-e);1!==s&&0!==s||(o=i<e?.5:-.5);var l=s*(n-t)+t;return l===r?1/0:l>r?o:0}t.exports=n},"87b1":function(t,e,n){var i=n("cbe5"),r=n("4fac"),a=i.extend({type:"polygon",shape:{points:null,smooth:!1,smoothConstraint:null},buildPath:function(t,e){r.buildPath(t,e,!0)}});t.exports=a},"87c3":function(t,e,n){var i=n("6d8b"),r=i.map,a=n("cccd"),o=n("ee1a"),s=o.isDimensionStacked;function l(t){return{seriesType:t,plan:a(),reset:function(t){var e=t.getData(),n=t.coordinateSystem,i=t.pipelineContext,a=i.large;if(n){var o=r(n.dimensions,(function(t){return e.mapDimension(t)})).slice(0,2),l=o.length,u=e.getCalculationInfo("stackResultDimension");return s(e,o[0])&&(o[0]=u),s(e,o[1])&&(o[1]=u),l&&{progress:h}}function h(t,e){for(var i=t.end-t.start,r=a&&new Float32Array(i*l),s=t.start,u=0,h=[],c=[];s<t.end;s++){var d;if(1===l){var f=e.get(o[0],s);d=!isNaN(f)&&n.dataToPoint(f,null,c)}else{f=h[0]=e.get(o[0],s);var p=h[1]=e.get(o[1],s);d=!isNaN(f)&&!isNaN(p)&&n.dataToPoint(h,null,c)}a?(r[u++]=d?d[0]:NaN,r[u++]=d?d[1]:NaN):e.setItemLayout(s,d&&d.slice()||[NaN,NaN])}a&&e.setLayout("symbolPoints",r)}}}}t.exports=l},"88b3":function(t,e){var n="\0__throttleOriginMethod",i="\0__throttleRate",r="\0__throttleType";function a(t,e,n){var i,r,a,o,s,l=0,u=0,h=null;function c(){u=(new Date).getTime(),h=null,t.apply(a,o||[])}e=e||0;var d=function(){i=(new Date).getTime(),a=this,o=arguments;var t=s||e,d=s||n;s=null,r=i-(d?l:u)-t,clearTimeout(h),d?h=setTimeout(c,t):r>=0?c():h=setTimeout(c,-r),l=i};return d.clear=function(){h&&(clearTimeout(h),h=null)},d.debounceNextCall=function(t){s=t},d}function o(t,e,o,s){var l=t[e];if(l){var u=l[n]||l,h=l[r],c=l[i];if(c!==o||h!==s){if(null==o||!s)return t[e]=u;l=t[e]=a(u,o,"debounce"===s),l[n]=u,l[r]=s,l[i]=o}return l}}function s(t,e){var i=t[e];i&&i[n]&&(t[e]=i[n])}e.throttle=a,e.createOrUpdate=o,e.clear=s},8918:function(t,e,n){var i=n("6d8b"),r=n("625e"),a=r.parseClassType,o=0;function s(t){return[t||"",o++,Math.random().toFixed(5)].join("_")}function l(t){var e={};return t.registerSubTypeDefaulter=function(t,n){t=a(t),e[t.main]=n},t.determineSubType=function(n,i){var r=i.type;if(!r){var o=a(n).main;t.hasSubTypes(n)&&e[o]&&(r=e[o](i))}return r},t}function u(t,e){function n(t){var n={},o=[];return i.each(t,(function(s){var l=r(n,s),u=l.originalDeps=e(s),h=a(u,t);l.entryCount=h.length,0===l.entryCount&&o.push(s),i.each(h,(function(t){i.indexOf(l.predecessor,t)<0&&l.predecessor.push(t);var e=r(n,t);i.indexOf(e.successor,t)<0&&e.successor.push(s)}))})),{graph:n,noEntryList:o}}function r(t,e){return t[e]||(t[e]={predecessor:[],successor:[]}),t[e]}function a(t,e){var n=[];return i.each(t,(function(t){i.indexOf(e,t)>=0&&n.push(t)})),n}t.topologicalTravel=function(t,e,r,a){if(t.length){var o=n(e),s=o.graph,l=o.noEntryList,u={};i.each(t,(function(t){u[t]=!0}));while(l.length){var h=l.pop(),c=s[h],d=!!u[h];d&&(r.call(a,h,c.originalDeps.slice()),delete u[h]),i.each(c.successor,d?p:f)}i.each(u,(function(){throw new Error("Circle dependency may exists")}))}function f(t){s[t].entryCount--,0===s[t].entryCount&&l.push(t)}function p(t){u[t]=!0,f(t)}}}e.getUID=s,e.enableSubTypeDefaulter=l,e.enableTopologicalTravel=u},8971:function(t,e){var n="";"undefined"!==typeof navigator&&(n=navigator.platform||"");var i={color:["#c23531","#2f4554","#61a0a8","#d48265","#91c7ae","#749f83","#ca8622","#bda29a","#6e7074","#546570","#c4ccd3"],gradientColor:["#f6efa6","#d88273","#bf444c"],textStyle:{fontFamily:n.match(/^Win/)?"Microsoft YaHei":"sans-serif",fontSize:12,fontStyle:"normal",fontWeight:"normal"},blendMode:null,animation:"auto",animationDuration:1e3,animationDurationUpdate:300,animationEasing:"exponentialOut",animationEasingUpdate:"cubicOut",animationThreshold:2e3,progressiveThreshold:3e3,progressive:400,hoverLayerThreshold:3e3,useUTC:!1};t.exports=i},"897a":function(t,e,n){var i=n("22d1"),r=[["shadowBlur",0],["shadowColor","#000"],["shadowOffsetX",0],["shadowOffsetY",0]];function a(t){return i.browser.ie&&i.browser.version>=11?function(){var e,n=this.__clipPaths,i=this.style;if(n)for(var a=0;a<n.length;a++){var o=n[a],s=o&&o.shape,l=o&&o.type;if(s&&("sector"===l&&s.startAngle===s.endAngle||"rect"===l&&(!s.width||!s.height))){for(var u=0;u<r.length;u++)r[u][2]=i[r[u][0]],i[r[u][0]]=r[u][1];e=!0;break}}if(t.apply(this,arguments),e)for(u=0;u<r.length;u++)i[r[u][0]]=r[u][2]}:t}t.exports=a},"89e3":function(t,e,n){var i=n("3842"),r=n("eda2"),a=n("e0d8"),o=n("944e"),s=i.round,l=a.extend({type:"interval",_interval:0,_intervalPrecision:2,setExtent:function(t,e){var n=this._extent;isNaN(t)||(n[0]=parseFloat(t)),isNaN(e)||(n[1]=parseFloat(e))},unionExtent:function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1]),l.prototype.setExtent.call(this,e[0],e[1])},getInterval:function(){return this._interval},setInterval:function(t){this._interval=t,this._niceExtent=this._extent.slice(),this._intervalPrecision=o.getIntervalPrecision(t)},getTicks:function(){return o.intervalScaleGetTicks(this._interval,this._extent,this._niceExtent,this._intervalPrecision)},getLabel:function(t,e){if(null==t)return"";var n=e&&e.precision;return null==n?n=i.getPrecisionSafe(t)||0:"auto"===n&&(n=this._intervalPrecision),t=s(t,n,!0),r.addCommas(t)},niceTicks:function(t,e,n){t=t||5;var i=this._extent,r=i[1]-i[0];if(isFinite(r)){r<0&&(r=-r,i.reverse());var a=o.intervalScaleNiceTicks(i,t,e,n);this._intervalPrecision=a.intervalPrecision,this._interval=a.interval,this._niceExtent=a.niceTickExtent}},niceExtent:function(t){var e=this._extent;if(e[0]===e[1])if(0!==e[0]){var n=e[0];t.fixMax||(e[1]+=n/2),e[0]-=n/2}else e[1]=1;var i=e[1]-e[0];isFinite(i)||(e[0]=0,e[1]=1),this.niceTicks(t.splitNumber,t.minInterval,t.maxInterval);var r=this._interval;t.fixMin||(e[0]=s(Math.floor(e[0]/r)*r)),t.fixMax||(e[1]=s(Math.ceil(e[1]/r)*r))}});l.create=function(){return new l};var u=l;t.exports=u},"8b7f":function(t,e,n){var i=n("4e08"),r=(i.__DEV__,n("6d8b")),a=r.createHashMap,o=(r.retrieve,r.each);function s(t){var e=t.get("coordinateSystem"),n={coordSysName:e,coordSysDims:[],axisMap:a(),categoryAxisMap:a()},i=l[e];if(i)return i(t,n,n.axisMap,n.categoryAxisMap),n}var l={cartesian2d:function(t,e,n,i){var r=t.getReferringComponents("xAxis")[0],a=t.getReferringComponents("yAxis")[0];e.coordSysDims=["x","y"],n.set("x",r),n.set("y",a),u(r)&&(i.set("x",r),e.firstCategoryDimIndex=0),u(a)&&(i.set("y",a),e.firstCategoryDimIndex=1)},singleAxis:function(t,e,n,i){var r=t.getReferringComponents("singleAxis")[0];e.coordSysDims=["single"],n.set("single",r),u(r)&&(i.set("single",r),e.firstCategoryDimIndex=0)},polar:function(t,e,n,i){var r=t.getReferringComponents("polar")[0],a=r.findAxisModel("radiusAxis"),o=r.findAxisModel("angleAxis");e.coordSysDims=["radius","angle"],n.set("radius",a),n.set("angle",o),u(a)&&(i.set("radius",a),e.firstCategoryDimIndex=0),u(o)&&(i.set("angle",o),e.firstCategoryDimIndex=1)},geo:function(t,e,n,i){e.coordSysDims=["lng","lat"]},parallel:function(t,e,n,i){var r=t.ecModel,a=r.getComponent("parallel",t.get("parallelIndex")),s=e.coordSysDims=a.dimensions.slice();o(a.parallelAxisIndex,(function(t,a){var o=r.getComponent("parallelAxis",t),l=s[a];n.set(l,o),u(o)&&null==e.firstCategoryDimIndex&&(i.set(l,o),e.firstCategoryDimIndex=a)}))}};function u(t){return"category"===t.get("type")}e.getCoordSysDefineBySeries=s},"8c2a":function(t,e,n){var i=n("6d8b"),r=n("e0d8"),a=n("3842"),o=n("89e3"),s=r.prototype,l=o.prototype,u=a.getPrecisionSafe,h=a.round,c=Math.floor,d=Math.ceil,f=Math.pow,p=Math.log,g=r.extend({type:"log",base:10,$constructor:function(){r.apply(this,arguments),this._originalScale=new o},getTicks:function(){var t=this._originalScale,e=this._extent,n=t.getExtent();return i.map(l.getTicks.call(this),(function(i){var r=a.round(f(this.base,i));return r=i===e[0]&&t.__fixMin?v(r,n[0]):r,r=i===e[1]&&t.__fixMax?v(r,n[1]):r,r}),this)},getLabel:l.getLabel,scale:function(t){return t=s.scale.call(this,t),f(this.base,t)},setExtent:function(t,e){var n=this.base;t=p(t)/p(n),e=p(e)/p(n),l.setExtent.call(this,t,e)},getExtent:function(){var t=this.base,e=s.getExtent.call(this);e[0]=f(t,e[0]),e[1]=f(t,e[1]);var n=this._originalScale,i=n.getExtent();return n.__fixMin&&(e[0]=v(e[0],i[0])),n.__fixMax&&(e[1]=v(e[1],i[1])),e},unionExtent:function(t){this._originalScale.unionExtent(t);var e=this.base;t[0]=p(t[0])/p(e),t[1]=p(t[1])/p(e),s.unionExtent.call(this,t)},unionExtentFromData:function(t,e){this.unionExtent(t.getApproximateExtent(e))},niceTicks:function(t){t=t||10;var e=this._extent,n=e[1]-e[0];if(!(n===1/0||n<=0)){var i=a.quantity(n),r=t/n*i;r<=.5&&(i*=10);while(!isNaN(i)&&Math.abs(i)<1&&Math.abs(i)>0)i*=10;var o=[a.round(d(e[0]/i)*i),a.round(c(e[1]/i)*i)];this._interval=i,this._niceExtent=o}},niceExtent:function(t){l.niceExtent.call(this,t);var e=this._originalScale;e.__fixMin=t.fixMin,e.__fixMax=t.fixMax}});function v(t,e){return h(t,u(e))}i.each(["contain","normalize"],(function(t){g.prototype[t]=function(e){return e=p(e)/p(this.base),s[t].call(this,e)}})),g.create=function(){return new g};var m=g;t.exports=m},"8d32":function(t,e,n){var i=n("cbe5"),r=i.extend({type:"arc",shape:{cx:0,cy:0,r:0,startAngle:0,endAngle:2*Math.PI,clockwise:!0},style:{stroke:"#000",fill:null},buildPath:function(t,e){var n=e.cx,i=e.cy,r=Math.max(e.r,0),a=e.startAngle,o=e.endAngle,s=e.clockwise,l=Math.cos(a),u=Math.sin(a);t.moveTo(l*r+n,u*r+i),t.arc(n,i,r,a,o,!s)}});t.exports=r},"8e43":function(t,e,n){var i=n("6d8b"),r=i.createHashMap,a=i.isObject,o=i.map;function s(t){this.categories=t.categories||[],this._needCollect=t.needCollect,this._deduplication=t.deduplication,this._map}s.createByAxisModel=function(t){var e=t.option,n=e.data,i=n&&o(n,h);return new s({categories:i,needCollect:!i,deduplication:!1!==e.dedplication})};var l=s.prototype;function u(t){return t._map||(t._map=r(t.categories))}function h(t){return a(t)&&null!=t.value?t.value:t+""}l.getOrdinal=function(t){return u(this).get(t)},l.parseAndCollect=function(t){var e,n=this._needCollect;if("string"!==typeof t&&!n)return t;if(n&&!this._deduplication)return e=this.categories.length,this.categories[e]=t,e;var i=u(this);return e=i.get(t),null==e&&(n?(e=this.categories.length,this.categories[e]=t,i.set(t,e)):e=NaN),e};var c=s;t.exports=c},"8ed2":function(t,e,n){n("48c7");var i=n("6cb7"),r=i.extend({type:"grid",dependencies:["xAxis","yAxis"],layoutMode:"box",coordinateSystem:null,defaultOption:{show:!1,zlevel:0,z:0,left:"10%",top:60,right:"10%",bottom:60,containLabel:!1,backgroundColor:"rgba(0,0,0,0)",borderWidth:1,borderColor:"#ccc"}});t.exports=r},9273:function(t,e,n){var i=n("6d8b"),r=n("84ce");function a(t,e){r.call(this,"radius",t,e),this.type="category"}a.prototype={constructor:a,pointToData:function(t,e){return this.polar.pointToData(t,e)["radius"===this.dim?0:1]},dataToRadius:r.prototype.dataToCoord,radiusToData:r.prototype.coordToData},i.inherits(a,r);var o=a;t.exports=o},"93d0":function(t,e){var n="original",i="arrayRows",r="objectRows",a="keyedColumns",o="unknown",s="typedArray",l="column",u="row";e.SOURCE_FORMAT_ORIGINAL=n,e.SOURCE_FORMAT_ARRAY_ROWS=i,e.SOURCE_FORMAT_OBJECT_ROWS=r,e.SOURCE_FORMAT_KEYED_COLUMNS=a,e.SOURCE_FORMAT_UNKNOWN=o,e.SOURCE_FORMAT_TYPED_ARRAY=s,e.SERIES_LAYOUT_BY_COLUMN=l,e.SERIES_LAYOUT_BY_ROW=u},"944e":function(t,e,n){var i=n("3842"),r=i.round;function a(t,e,n,a){var s={},u=t[1]-t[0],h=s.interval=i.nice(u/e,!0);null!=n&&h<n&&(h=s.interval=n),null!=a&&h>a&&(h=s.interval=a);var c=s.intervalPrecision=o(h),d=s.niceTickExtent=[r(Math.ceil(t[0]/h)*h,c),r(Math.floor(t[1]/h)*h,c)];return l(d,t),s}function o(t){return i.getPrecisionSafe(t)+2}function s(t,e,n){t[e]=Math.max(Math.min(t[e],n[1]),n[0])}function l(t,e){!isFinite(t[0])&&(t[0]=e[0]),!isFinite(t[1])&&(t[1]=e[1]),s(t,0,e),s(t,1,e),t[0]>t[1]&&(t[0]=t[1])}function u(t,e,n,i){var a=[];if(!t)return a;var o=1e4;e[0]<n[0]&&a.push(e[0]);var s=n[0];while(s<=n[1]){if(a.push(s),s=r(s+t,i),s===a[a.length-1])break;if(a.length>o)return[]}return e[1]>(a.length?a[a.length-1]:n[1])&&a.push(e[1]),a}e.intervalScaleNiceTicks=a,e.getIntervalPrecision=o,e.fixExtent=l,e.intervalScaleGetTicks=u},9680:function(t,e){function n(t,e,n,i,r,a,o){if(0===r)return!1;var s=r,l=0,u=t;if(o>e+s&&o>i+s||o<e-s&&o<i-s||a>t+s&&a>n+s||a<t-s&&a<n-s)return!1;if(t===n)return Math.abs(a-t)<=s/2;l=(e-i)/(t-n),u=(t*i-n*e)/(t-n);var h=l*a-o+u,c=h*h/(l*l+1);return c<=s/2*s/2}e.containStroke=n},9850:function(t,e,n){var i=n("401b"),r=n("1687"),a=i.applyTransform,o=Math.min,s=Math.max;function l(t,e,n,i){n<0&&(t+=n,n=-n),i<0&&(e+=i,i=-i),this.x=t,this.y=e,this.width=n,this.height=i}l.prototype={constructor:l,union:function(t){var e=o(t.x,this.x),n=o(t.y,this.y);this.width=s(t.x+t.width,this.x+this.width)-e,this.height=s(t.y+t.height,this.y+this.height)-n,this.x=e,this.y=n},applyTransform:function(){var t=[],e=[],n=[],i=[];return function(r){if(r){t[0]=n[0]=this.x,t[1]=i[1]=this.y,e[0]=i[0]=this.x+this.width,e[1]=n[1]=this.y+this.height,a(t,t,r),a(e,e,r),a(n,n,r),a(i,i,r),this.x=o(t[0],e[0],n[0],i[0]),this.y=o(t[1],e[1],n[1],i[1]);var l=s(t[0],e[0],n[0],i[0]),u=s(t[1],e[1],n[1],i[1]);this.width=l-this.x,this.height=u-this.y}}}(),calculateTransform:function(t){var e=this,n=t.width/e.width,i=t.height/e.height,a=r.create();return r.translate(a,a,[-e.x,-e.y]),r.scale(a,a,[n,i]),r.translate(a,a,[t.x,t.y]),a},intersect:function(t){if(!t)return!1;t instanceof l||(t=l.create(t));var e=this,n=e.x,i=e.x+e.width,r=e.y,a=e.y+e.height,o=t.x,s=t.x+t.width,u=t.y,h=t.y+t.height;return!(i<o||s<n||a<u||h<r)},contain:function(t,e){var n=this;return t>=n.x&&t<=n.x+n.width&&e>=n.y&&e<=n.y+n.height},clone:function(){return new l(this.x,this.y,this.width,this.height)},copy:function(t){this.x=t.x,this.y=t.y,this.width=t.width,this.height=t.height},plain:function(){return{x:this.x,y:this.y,width:this.width,height:this.height}}},l.create=function(t){return new l(t.x,t.y,t.width,t.height)};var u=l;t.exports=u},"98b7":function(t,e){var n="undefined"!==typeof window&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(t){setTimeout(t,16)};t.exports=n},"998c":function(t,e,n){var i=n("6d8b"),r=n("2306"),a=Math.PI;function o(t,e){e=e||{},i.defaults(e,{text:"loading",color:"#c23531",textColor:"#000",maskColor:"rgba(255, 255, 255, 0.8)",zlevel:0});var n=new r.Rect({style:{fill:e.maskColor},zlevel:e.zlevel,z:1e4}),o=new r.Arc({shape:{startAngle:-a/2,endAngle:-a/2+.1,r:10},style:{stroke:e.color,lineCap:"round",lineWidth:5},zlevel:e.zlevel,z:10001}),s=new r.Rect({style:{fill:"none",text:e.text,textPosition:"right",textDistance:10,textFill:e.textColor},zlevel:e.zlevel,z:10001});o.animateShape(!0).when(1e3,{endAngle:3*a/2}).start("circularInOut"),o.animateShape(!0).when(1e3,{startAngle:3*a/2}).delay(300).start("circularInOut");var l=new r.Group;return l.add(o),l.add(s),l.add(n),l.resize=function(){var e=t.getWidth()/2,i=t.getHeight()/2;o.setShape({cx:e,cy:i});var r=o.shape.r;s.setShape({x:e-r,y:i-r,width:2*r,height:2*r}),n.setShape({x:0,y:0,width:t.getWidth(),height:t.getHeight()})},l.resize(),l}t.exports=o},"9bdb":function(t,e,n){var i=n("282b"),r=i([["fill","color"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["opacity"],["shadowColor"]]),a={getAreaStyle:function(t,e){return r(this,t,e)}};t.exports=a},"9c2c":function(t,e,n){var i=n("401b"),r=i.min,a=i.max,o=i.scale,s=i.distance,l=i.add,u=i.clone,h=i.sub;function c(t,e,n,i){var c,d,f,p,g=[],v=[],m=[],y=[];if(i){f=[1/0,1/0],p=[-1/0,-1/0];for(var x=0,_=t.length;x<_;x++)r(f,f,t[x]),a(p,p,t[x]);r(f,f,i[0]),a(p,p,i[1])}for(x=0,_=t.length;x<_;x++){var b=t[x];if(n)c=t[x?x-1:_-1],d=t[(x+1)%_];else{if(0===x||x===_-1){g.push(u(t[x]));continue}c=t[x-1],d=t[x+1]}h(v,d,c),o(v,v,e);var w=s(b,c),S=s(b,d),M=w+S;0!==M&&(w/=M,S/=M),o(m,v,-w),o(y,v,S);var T=l([],b,m),A=l([],b,y);i&&(a(T,T,f),r(T,T,p),a(A,A,f),r(A,A,p)),g.push(T),g.push(A)}return n&&g.push(g.shift()),g}t.exports=c},"9cf9":function(t,e){var n=Math.round;function i(t,e,i){var r=i&&i.lineWidth;if(e&&r){var o=e.x1,s=e.x2,l=e.y1,u=e.y2;n(2*o)===n(2*s)?t.x1=t.x2=a(o,r,!0):(t.x1=o,t.x2=s),n(2*l)===n(2*u)?t.y1=t.y2=a(l,r,!0):(t.y1=l,t.y2=u)}}function r(t,e,n){var i=n&&n.lineWidth;if(e&&i){var r=e.x,o=e.y,s=e.width,l=e.height;t.x=a(r,i,!0),t.y=a(o,i,!0),t.width=Math.max(a(r+s,i,!1)-t.x,0===s?0:1),t.height=Math.max(a(o+l,i,!1)-t.y,0===l?0:1)}}function a(t,e,i){var r=n(2*t);return(r+n(e))%2===0?r/2:(r+(i?1:-1))/2}e.subPixelOptimizeLine=i,e.subPixelOptimizeRect=r,e.subPixelOptimize=a},"9d57":function(t,e,n){var i=n("6d8b"),r=n("3842"),a=r.parsePercent,o=n("ee1a"),s=o.isDimensionStacked,l=n("cccd"),u="__ec_stack_",h=.5,c="undefined"!==typeof Float32Array?Float32Array:Array;function d(t){return t.get("stack")||u+t.seriesIndex}function f(t){return t.dim+t.index}function p(t){var e=[],n=t.axis,r="axis0";if("category"===n.type){for(var a=n.getBandWidth(),o=0;o<t.count;o++)e.push(i.defaults({bandWidth:a,axisKey:r,stackId:u+o},t));var s=m(e),l=[];for(o=0;o<t.count;o++){var h=s[r][u+o];h.offsetCenter=h.offset+h.width/2,l.push(h)}return l}}function g(t,e){var n=[];return e.eachSeriesByType(t,(function(t){b(t)&&!w(t)&&n.push(t)})),n}function v(t){var e=[];return i.each(t,(function(t){var n=t.getData(),i=t.coordinateSystem,r=i.getBaseAxis(),o=r.getExtent(),s="category"===r.type?r.getBandWidth():Math.abs(o[1]-o[0])/n.count(),l=a(t.get("barWidth"),s),u=a(t.get("barMaxWidth"),s),h=t.get("barGap"),c=t.get("barCategoryGap");e.push({bandWidth:s,barWidth:l,barMaxWidth:u,barGap:h,barCategoryGap:c,axisKey:f(r),stackId:d(t)})})),m(e)}function m(t){var e={};i.each(t,(function(t,n){var i=t.axisKey,r=t.bandWidth,a=e[i]||{bandWidth:r,remainedWidth:r,autoWidthCount:0,categoryGap:"20%",gap:"30%",stacks:{}},o=a.stacks;e[i]=a;var s=t.stackId;o[s]||a.autoWidthCount++,o[s]=o[s]||{width:0,maxWidth:0};var l=t.barWidth;l&&!o[s].width&&(o[s].width=l,l=Math.min(a.remainedWidth,l),a.remainedWidth-=l);var u=t.barMaxWidth;u&&(o[s].maxWidth=u);var h=t.barGap;null!=h&&(a.gap=h);var c=t.barCategoryGap;null!=c&&(a.categoryGap=c)}));var n={};return i.each(e,(function(t,e){n[e]={};var r=t.stacks,o=t.bandWidth,s=a(t.categoryGap,o),l=a(t.gap,1),u=t.remainedWidth,h=t.autoWidthCount,c=(u-s)/(h+(h-1)*l);c=Math.max(c,0),i.each(r,(function(t,e){var n=t.maxWidth;n&&n<c&&(n=Math.min(n,u),t.width&&(n=Math.min(n,t.width)),u-=n,t.width=n,h--)})),c=(u-s)/(h+(h-1)*l),c=Math.max(c,0);var d,f=0;i.each(r,(function(t,e){t.width||(t.width=c),d=t,f+=t.width*(1+l)})),d&&(f-=d.width*l);var p=-f/2;i.each(r,(function(t,i){n[e][i]=n[e][i]||{offset:p,width:t.width},p+=t.width*(1+l)}))})),n}function y(t,e,n){if(t&&e){var i=t[f(e)];return null!=i&&null!=n&&(i=i[d(n)]),i}}function x(t,e){var n=g(t,e),r=v(n),a={},o={};i.each(n,(function(t){var e=t.getData(),n=t.coordinateSystem,i=n.getBaseAxis(),l=d(t),u=r[f(i)][l],h=u.offset,c=u.width,p=n.getOtherAxis(i),g=t.get("barMinHeight")||0;a[l]=a[l]||[],o[l]=o[l]||[],e.setLayout({offset:h,size:c});for(var v=e.mapDimension(p.dim),m=e.mapDimension(i.dim),y=s(e,v),x=p.isHorizontal(),_=S(i,p,y),b=0,w=e.count();b<w;b++){var M=e.get(v,b),T=e.get(m,b);if(!isNaN(M)){var A,C,I,k,D=M>=0?"p":"n",P=_;if(y&&(a[l][T]||(a[l][T]={p:_,n:_}),P=a[l][T][D]),x){var O=n.dataToPoint([M,T]);A=P,C=O[1]+h,I=O[0]-_,k=c,Math.abs(I)<g&&(I=(I<0?-1:1)*g),y&&(a[l][T][D]+=I)}else{O=n.dataToPoint([T,M]);A=O[0]+h,C=P,I=c,k=O[1]-_,Math.abs(k)<g&&(k=(k<=0?-1:1)*g),y&&(a[l][T][D]+=k)}e.setItemLayout(b,{x:A,y:C,width:I,height:k})}}}),this)}var _={seriesType:"bar",plan:l(),reset:function(t){if(b(t)&&w(t)){var e=t.getData(),n=t.coordinateSystem,i=n.getBaseAxis(),r=n.getOtherAxis(i),a=e.mapDimension(r.dim),o=e.mapDimension(i.dim),s=r.isHorizontal(),l=s?0:1,u=y(v([t]),i,t).width;return u>h||(u=h),{progress:d}}function d(t,e){var h,d=new c(2*t.count),f=[],p=[],g=0;while(null!=(h=t.next()))p[l]=e.get(a,h),p[1-l]=e.get(o,h),f=n.dataToPoint(p,null,f),d[g++]=f[0],d[g++]=f[1];e.setLayout({largePoints:d,barWidth:u,valueAxisStart:S(i,r,!1),valueAxisHorizontal:s})}}};function b(t){return t.coordinateSystem&&"cartesian2d"===t.coordinateSystem.type}function w(t){return t.pipelineContext&&t.pipelineContext.large}function S(t,e,n){var i,r,a=e.getGlobalExtent();a[0]>a[1]?(i=a[1],r=a[0]):(i=a[0],r=a[1]);var o=e.toGlobalCoord(e.dataToCoord(0));return o<i&&(o=i),o>r&&(o=r),o}e.getLayoutOnAxis=p,e.prepareLayoutBarSeries=g,e.makeColumnLayout=v,e.retrieveColumnLayout=y,e.layout=x,e.largeLayout=_},"9e2e":function(t,e,n){var i=n("a73c"),r=n("9850"),a=n("82eb"),o=a.WILL_BE_RESTORED,s=new r,l=function(){};l.prototype={constructor:l,drawRectText:function(t,e){var n=this.style;e=n.textRect||e,this.__dirty&&i.normalizeTextStyle(n,!0);var r=n.text;if(null!=r&&(r+=""),i.needDrawText(r,n)){t.save();var a=this.transform;n.transformText?this.setTransform(t):a&&(s.copy(e),s.applyTransform(a),e=s),i.renderText(this,t,r,n,e,o),t.restore()}}};var u=l;t.exports=u},"9e47":function(t,e,n){var i=n("6d8b"),r=n("71ad"),a=n("6cb7"),o=n("f934"),s=o.getLayoutParams,l=o.mergeLayoutParam,u=n("8e43"),h=["value","category","time","log"];function c(t,e,n,o){i.each(h,(function(a){e.extend({type:t+"Axis."+a,mergeDefaultAndTheme:function(e,r){var o=this.layoutMode,u=o?s(e):{},h=r.getTheme();i.merge(e,h.get(a+"Axis")),i.merge(e,this.getDefaultOption()),e.type=n(t,e),o&&l(e,u,o)},optionUpdated:function(){var t=this.option;"category"===t.type&&(this.__ordinalMeta=u.createByAxisModel(this))},getCategories:function(t){var e=this.option;if("category"===e.type)return t?e.data:this.__ordinalMeta.categories},getOrdinalMeta:function(){return this.__ordinalMeta},defaultOption:i.mergeAll([{},r[a+"Axis"],o],!0)})})),a.registerSubTypeDefaulter(t+"Axis",i.curry(n,t))}t.exports=c},"9f51":function(t,e,n){var i=n("857d"),r=i.normalizeRadian,a=2*Math.PI;function o(t,e,n,i,o,s,l,u,h){if(0===l)return!1;var c=l;u-=t,h-=e;var d=Math.sqrt(u*u+h*h);if(d-c>n||d+c<n)return!1;if(Math.abs(i-o)%a<1e-4)return!0;if(s){var f=i;i=r(o),o=r(f)}else i=r(i),o=r(o);i>o&&(o+=a);var p=Math.atan2(h,u);return p<0&&(p+=a),p>=i&&p<=o||p+a>=i&&p+a<=o}e.containStroke=o},"9f82":function(t,e,n){var i=n("ee1a"),r=i.isDimensionStacked,a=n("6d8b"),o=a.map;function s(t,e,n){var i,a=t.getBaseAxis(),s=t.getOtherAxis(a),u=l(s,n),h=a.dim,c=s.dim,d=e.mapDimension(c),f=e.mapDimension(h),p="x"===c||"radius"===c?1:0,g=o(t.dimensions,(function(t){return e.mapDimension(t)})),v=e.getCalculationInfo("stackResultDimension");return(i|=r(e,g[0]))&&(g[0]=v),(i|=r(e,g[1]))&&(g[1]=v),{dataDimsForPoint:g,valueStart:u,valueAxisDim:c,baseAxisDim:h,stacked:!!i,valueDim:d,baseDim:f,baseDataOffset:p,stackedOverDimension:e.getCalculationInfo("stackedOverDimension")}}function l(t,e){var n=0,i=t.scale.getExtent();return"start"===e?n=i[0]:"end"===e?n=i[1]:i[0]>0?n=i[0]:i[1]<0&&(n=i[1]),n}function u(t,e,n,i){var r=NaN;t.stacked&&(r=n.get(n.getCalculationInfo("stackedOverDimension"),i)),isNaN(r)&&(r=t.valueStart);var a=t.baseDataOffset,o=[];return o[a]=n.get(t.baseDim,i),o[1-a]=r,e.dataToPoint(o)}e.prepareDataCoordInfo=s,e.getStackedOnPoint=u},a15a:function(t,e,n){var i=n("6d8b"),r=n("2306"),a=n("9850"),o=r.extendShape({type:"triangle",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=e.width/2,a=e.height/2;t.moveTo(n,i-a),t.lineTo(n+r,i+a),t.lineTo(n-r,i+a),t.closePath()}}),s=r.extendShape({type:"diamond",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=e.width/2,a=e.height/2;t.moveTo(n,i-a),t.lineTo(n+r,i),t.lineTo(n,i+a),t.lineTo(n-r,i),t.closePath()}}),l=r.extendShape({type:"pin",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.x,i=e.y,r=e.width/5*3,a=Math.max(r,e.height),o=r/2,s=o*o/(a-o),l=i-a+o+s,u=Math.asin(s/o),h=Math.cos(u)*o,c=Math.sin(u),d=Math.cos(u),f=.6*o,p=.7*o;t.moveTo(n-h,l+s),t.arc(n,l,o,Math.PI-u,2*Math.PI+u),t.bezierCurveTo(n+h-c*f,l+s+d*f,n,i-p,n,i),t.bezierCurveTo(n,i-p,n-h+c*f,l+s+d*f,n-h,l+s),t.closePath()}}),u=r.extendShape({type:"arrow",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.height,i=e.width,r=e.x,a=e.y,o=i/3*2;t.moveTo(r,a),t.lineTo(r+o,a+n),t.lineTo(r,a+n/4*3),t.lineTo(r-o,a+n),t.lineTo(r,a),t.closePath()}}),h={line:r.Line,rect:r.Rect,roundRect:r.Rect,square:r.Rect,circle:r.Circle,diamond:s,pin:l,arrow:u,triangle:o},c={line:function(t,e,n,i,r){r.x1=t,r.y1=e+i/2,r.x2=t+n,r.y2=e+i/2},rect:function(t,e,n,i,r){r.x=t,r.y=e,r.width=n,r.height=i},roundRect:function(t,e,n,i,r){r.x=t,r.y=e,r.width=n,r.height=i,r.r=Math.min(n,i)/4},square:function(t,e,n,i,r){var a=Math.min(n,i);r.x=t,r.y=e,r.width=a,r.height=a},circle:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.r=Math.min(n,i)/2},diamond:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.width=n,r.height=i},pin:function(t,e,n,i,r){r.x=t+n/2,r.y=e+i/2,r.width=n,r.height=i},arrow:function(t,e,n,i,r){r.x=t+n/2,r.y=e+i/2,r.width=n,r.height=i},triangle:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.width=n,r.height=i}},d={};i.each(h,(function(t,e){d[e]=new t}));var f=r.extendShape({type:"symbol",shape:{symbolType:"",x:0,y:0,width:0,height:0},beforeBrush:function(){var t=this.style,e=this.shape;"pin"===e.symbolType&&"inside"===t.textPosition&&(t.textPosition=["50%","40%"],t.textAlign="center",t.textVerticalAlign="middle")},buildPath:function(t,e,n){var i=e.symbolType,r=d[i];"none"!==e.symbolType&&(r||(i="rect",r=d[i]),c[i](e.x,e.y,e.width,e.height,r.shape),r.buildPath(t,r.shape,n))}});function p(t,e){if("image"!==this.type){var n=this.style,i=this.shape;i&&"line"===i.symbolType?n.stroke=t:this.__isEmptyBrush?(n.stroke=t,n.fill=e||"#fff"):(n.fill&&(n.fill=t),n.stroke&&(n.stroke=t)),this.dirty(!1)}}function g(t,e,n,i,o,s,l){var u,h=0===t.indexOf("empty");return h&&(t=t.substr(5,1).toLowerCase()+t.substr(6)),u=0===t.indexOf("image://")?r.makeImage(t.slice(8),new a(e,n,i,o),l?"center":"cover"):0===t.indexOf("path://")?r.makePath(t.slice(7),{},new a(e,n,i,o),l?"center":"cover"):new f({shape:{symbolType:t,x:e,y:n,width:i,height:o}}),u.__isEmptyBrush=h,u.setColor=p,u.setColor(s),u}e.createSymbol=g},a73c:function(t,e,n){var i=n("6d8b"),r=i.retrieve2,a=i.retrieve3,o=i.each,s=i.normalizeCssArray,l=i.isString,u=i.isObject,h=n("e86a"),c=n("5693"),d=n("5e76"),f=n("7d6d"),p=n("82eb"),g=p.ContextCachedBy,v=p.WILL_BE_RESTORED,m=h.DEFAULT_FONT,y={left:1,right:1,center:1},x={top:1,bottom:1,middle:1},_=[["textShadowBlur","shadowBlur",0],["textShadowOffsetX","shadowOffsetX",0],["textShadowOffsetY","shadowOffsetY",0],["textShadowColor","shadowColor","transparent"]];function b(t){return w(t),o(t.rich,w),t}function w(t){if(t){t.font=h.makeFont(t);var e=t.textAlign;"middle"===e&&(e="center"),t.textAlign=null==e||y[e]?e:"left";var n=t.textVerticalAlign||t.textBaseline;"center"===n&&(n="middle"),t.textVerticalAlign=null==n||x[n]?n:"top";var i=t.textPadding;i&&(t.textPadding=s(t.textPadding))}}function S(t,e,n,i,r,a){i.rich?T(t,e,n,i,r,a):M(t,e,n,i,r,a)}function M(t,e,n,i,r,a){"use strict";var o,s=k(i),l=!1,u=e.__attrCachedBy===g.PLAIN_TEXT;a!==v?(a&&(o=a.style,l=!s&&u&&o),e.__attrCachedBy=s?g.NONE:g.PLAIN_TEXT):u&&(e.__attrCachedBy=g.NONE);var c=i.font||m;l&&c===(o.font||m)||(e.font=c);var d=t.__computedFont;t.__styleFont!==c&&(t.__styleFont=c,d=t.__computedFont=e.font);var p=i.textPadding,y=i.textLineHeight,x=t.__textCotentBlock;x&&!t.__dirtyText||(x=t.__textCotentBlock=h.parsePlainText(n,d,p,y,i.truncate));var b=x.outerHeight,w=x.lines,S=x.lineHeight,M=O(b,i,r),T=M.baseX,A=M.baseY,I=M.textAlign||"left",P=M.textVerticalAlign;C(e,i,r,T,A);var L=h.adjustTextY(A,b,P),B=T,z=L;if(s||p){var F=h.getWidth(n,d),V=F;p&&(V+=p[1]+p[3]);var H=h.adjustTextX(T,V,I);s&&D(t,e,i,H,L,V,b),p&&(B=N(T,I,p),z+=p[0])}e.textAlign=I,e.textBaseline="middle",e.globalAlpha=i.opacity||1;for(var W=0;W<_.length;W++){var G=_[W],U=G[0],q=G[1],Y=i[U];l&&Y===o[U]||(e[q]=f(e,q,Y||G[2]))}z+=S/2;var X=i.textStrokeWidth,j=l?o.textStrokeWidth:null,Z=!l||X!==j,K=!l||Z||i.textStroke!==o.textStroke,$=E(i.textStroke,X),Q=R(i.textFill);if($&&(Z&&(e.lineWidth=X),K&&(e.strokeStyle=$)),Q&&(l&&i.textFill===o.textFill||(e.fillStyle=Q)),1===w.length)$&&e.strokeText(w[0],B,z),Q&&e.fillText(w[0],B,z);else for(W=0;W<w.length;W++)$&&e.strokeText(w[W],B,z),Q&&e.fillText(w[W],B,z),z+=S}function T(t,e,n,i,r,a){a!==v&&(e.__attrCachedBy=g.NONE);var o=t.__textCotentBlock;o&&!t.__dirtyText||(o=t.__textCotentBlock=h.parseRichText(n,i)),A(t,e,o,i,r)}function A(t,e,n,i,r){var a=n.width,o=n.outerWidth,s=n.outerHeight,l=i.textPadding,u=O(s,i,r),c=u.baseX,d=u.baseY,f=u.textAlign,p=u.textVerticalAlign;C(e,i,r,c,d);var g=h.adjustTextX(c,o,f),v=h.adjustTextY(d,s,p),m=g,y=v;l&&(m+=l[3],y+=l[0]);var x=m+a;k(i)&&D(t,e,i,g,v,o,s);for(var _=0;_<n.lines.length;_++){var b,w=n.lines[_],S=w.tokens,M=S.length,T=w.lineHeight,A=w.width,P=0,L=m,E=x,R=M-1;while(P<M&&(b=S[P],!b.textAlign||"left"===b.textAlign))I(t,e,b,i,T,y,L,"left"),A-=b.width,L+=b.width,P++;while(R>=0&&(b=S[R],"right"===b.textAlign))I(t,e,b,i,T,y,E,"right"),A-=b.width,E-=b.width,R--;L+=(a-(L-m)-(x-E)-A)/2;while(P<=R)b=S[P],I(t,e,b,i,T,y,L+b.width/2,"center"),L+=b.width,P++;y+=T}}function C(t,e,n,i,r){if(n&&e.textRotation){var a=e.textOrigin;"center"===a?(i=n.width/2+n.x,r=n.height/2+n.y):a&&(i=a[0]+n.x,r=a[1]+n.y),t.translate(i,r),t.rotate(-e.textRotation),t.translate(-i,-r)}}function I(t,e,n,i,o,s,l,u){var h=i.rich[n.styleName]||{};h.text=n.text;var c=n.textVerticalAlign,d=s+o/2;"top"===c?d=s+n.height/2:"bottom"===c&&(d=s+o-n.height/2),!n.isLineHolder&&k(h)&&D(t,e,h,"right"===u?l-n.width:"center"===u?l-n.width/2:l,d-n.height/2,n.width,n.height);var f=n.textPadding;f&&(l=N(l,u,f),d-=n.height/2-f[2]-n.textHeight/2),L(e,"shadowBlur",a(h.textShadowBlur,i.textShadowBlur,0)),L(e,"shadowColor",h.textShadowColor||i.textShadowColor||"transparent"),L(e,"shadowOffsetX",a(h.textShadowOffsetX,i.textShadowOffsetX,0)),L(e,"shadowOffsetY",a(h.textShadowOffsetY,i.textShadowOffsetY,0)),L(e,"textAlign",u),L(e,"textBaseline","middle"),L(e,"font",n.font||m);var p=E(h.textStroke||i.textStroke,v),g=R(h.textFill||i.textFill),v=r(h.textStrokeWidth,i.textStrokeWidth);p&&(L(e,"lineWidth",v),L(e,"strokeStyle",p),e.strokeText(n.text,l,d)),g&&(L(e,"fillStyle",g),e.fillText(n.text,l,d))}function k(t){return!!(t.textBackgroundColor||t.textBorderWidth&&t.textBorderColor)}function D(t,e,n,i,r,a,o){var s=n.textBackgroundColor,h=n.textBorderWidth,f=n.textBorderColor,p=l(s);if(L(e,"shadowBlur",n.textBoxShadowBlur||0),L(e,"shadowColor",n.textBoxShadowColor||"transparent"),L(e,"shadowOffsetX",n.textBoxShadowOffsetX||0),L(e,"shadowOffsetY",n.textBoxShadowOffsetY||0),p||h&&f){e.beginPath();var g=n.textBorderRadius;g?c.buildPath(e,{x:i,y:r,width:a,height:o,r:g}):e.rect(i,r,a,o),e.closePath()}if(p)if(L(e,"fillStyle",s),null!=n.fillOpacity){var v=e.globalAlpha;e.globalAlpha=n.fillOpacity*n.opacity,e.fill(),e.globalAlpha=v}else e.fill();else if(u(s)){var m=s.image;m=d.createOrUpdateImage(m,null,t,P,s),m&&d.isImageReady(m)&&e.drawImage(m,i,r,a,o)}if(h&&f)if(L(e,"lineWidth",h),L(e,"strokeStyle",f),null!=n.strokeOpacity){v=e.globalAlpha;e.globalAlpha=n.strokeOpacity*n.opacity,e.stroke(),e.globalAlpha=v}else e.stroke()}function P(t,e){e.image=t}function O(t,e,n){var i=e.x||0,r=e.y||0,a=e.textAlign,o=e.textVerticalAlign;if(n){var s=e.textPosition;if(s instanceof Array)i=n.x+B(s[0],n.width),r=n.y+B(s[1],n.height);else{var l=h.adjustTextPositionOnRect(s,n,e.textDistance);i=l.x,r=l.y,a=a||l.textAlign,o=o||l.textVerticalAlign}var u=e.textOffset;u&&(i+=u[0],r+=u[1])}return{baseX:i,baseY:r,textAlign:a,textVerticalAlign:o}}function L(t,e,n){return t[e]=f(t,e,n),t[e]}function E(t,e){return null==t||e<=0||"transparent"===t||"none"===t?null:t.image||t.colorStops?"#000":t}function R(t){return null==t||"none"===t?null:t.image||t.colorStops?"#000":t}function B(t,e){return"string"===typeof t?t.lastIndexOf("%")>=0?parseFloat(t)/100*e:parseFloat(t):t}function N(t,e,n){return"right"===e?t-n[1]:"center"===e?t+n[3]/2-n[1]/2:t+n[3]}function z(t,e){return null!=t&&(t||e.textBackgroundColor||e.textBorderWidth&&e.textBorderColor||e.textPadding)}e.normalizeTextStyle=b,e.renderText=S,e.getStroke=E,e.getFill=R,e.needDrawText=z},a991:function(t,e,n){var i=n("6d8b"),r=n("e86a"),a=n("84ce"),o=n("e0d3"),s=o.makeInner,l=s();function u(t,e){e=e||[0,360],a.call(this,"angle",t,e),this.type="category"}u.prototype={constructor:u,pointToData:function(t,e){return this.polar.pointToData(t,e)["radius"===this.dim?0:1]},dataToAngle:a.prototype.dataToCoord,angleToData:a.prototype.coordToData,calculateCategoryInterval:function(){var t=this,e=t.getLabelModel(),n=t.scale,i=n.getExtent(),a=n.count();if(i[1]-i[0]<1)return 0;var o=i[0],s=t.dataToCoord(o+1)-t.dataToCoord(o),u=Math.abs(s),h=r.getBoundingRect(o,e.getFont(),"center","top"),c=Math.max(h.height,7),d=c/u;isNaN(d)&&(d=1/0);var f=Math.max(0,Math.floor(d)),p=l(t.model),g=p.lastAutoInterval,v=p.lastTickCount;return null!=g&&null!=v&&Math.abs(g-f)<=1&&Math.abs(v-a)<=1&&g>f?f=g:(p.lastTickCount=a,p.lastAutoInterval=f),f}},i.inherits(u,a);var h=u;t.exports=h},ac0f:function(t,e,n){var i=n("cbe5"),r=n("401b"),a=n("4a3f"),o=a.quadraticSubdivide,s=a.cubicSubdivide,l=a.quadraticAt,u=a.cubicAt,h=a.quadraticDerivativeAt,c=a.cubicDerivativeAt,d=[];function f(t,e,n){var i=t.cpx2,r=t.cpy2;return null===i||null===r?[(n?c:u)(t.x1,t.cpx1,t.cpx2,t.x2,e),(n?c:u)(t.y1,t.cpy1,t.cpy2,t.y2,e)]:[(n?h:l)(t.x1,t.cpx1,t.x2,e),(n?h:l)(t.y1,t.cpy1,t.y2,e)]}var p=i.extend({type:"bezier-curve",shape:{x1:0,y1:0,x2:0,y2:0,cpx1:0,cpy1:0,percent:1},style:{stroke:"#000",fill:null},buildPath:function(t,e){var n=e.x1,i=e.y1,r=e.x2,a=e.y2,l=e.cpx1,u=e.cpy1,h=e.cpx2,c=e.cpy2,f=e.percent;0!==f&&(t.moveTo(n,i),null==h||null==c?(f<1&&(o(n,l,r,f,d),l=d[1],r=d[2],o(i,u,a,f,d),u=d[1],a=d[2]),t.quadraticCurveTo(l,u,r,a)):(f<1&&(s(n,l,h,r,f,d),l=d[1],h=d[2],r=d[3],s(i,u,c,a,f,d),u=d[1],c=d[2],a=d[3]),t.bezierCurveTo(l,u,h,c,r,a)))},pointAt:function(t){return f(this.shape,t,!1)},tangentAt:function(t){var e=f(this.shape,t,!0);return r.normalize(e,e)}});t.exports=p},ae69:function(t,e,n){var i=n("cbe5"),r=i.extend({type:"ellipse",shape:{cx:0,cy:0,rx:0,ry:0},buildPath:function(t,e){var n=.5522848,i=e.cx,r=e.cy,a=e.rx,o=e.ry,s=a*n,l=o*n;t.moveTo(i-a,r),t.bezierCurveTo(i-a,r-l,i-s,r-o,i,r-o),t.bezierCurveTo(i+s,r-o,i+a,r-l,i+a,r),t.bezierCurveTo(i+a,r+l,i+s,r+o,i,r+o),t.bezierCurveTo(i-s,r+o,i-a,r+l,i-a,r),t.closePath()}});t.exports=r},af24:function(t,e,n){n("48c7"),n("f273")},afa0:function(t,e,n){var i=n("6d8b"),r=n("22d1"),a=n("e1fc"),o=n("04f6");function s(t,e){return t.zlevel===e.zlevel?t.z===e.z?t.z2-e.z2:t.z-e.z:t.zlevel-e.zlevel}var l=function(){this._roots=[],this._displayList=[],this._displayListLen=0};l.prototype={constructor:l,traverse:function(t,e){for(var n=0;n<this._roots.length;n++)this._roots[n].traverse(t,e)},getDisplayList:function(t,e){return e=e||!1,t&&this.updateDisplayList(e),this._displayList},updateDisplayList:function(t){this._displayListLen=0;for(var e=this._roots,n=this._displayList,i=0,a=e.length;i<a;i++)this._updateAndAddDisplayable(e[i],null,t);n.length=this._displayListLen,r.canvasSupported&&o(n,s)},_updateAndAddDisplayable:function(t,e,n){if(!t.ignore||n){t.beforeUpdate(),t.__dirty&&t.update(),t.afterUpdate();var i=t.clipPath;if(i){e=e?e.slice():[];var r=i,a=t;while(r)r.parent=a,r.updateTransform(),e.push(r),a=r,r=r.clipPath}if(t.isGroup){for(var o=t._children,s=0;s<o.length;s++){var l=o[s];t.__dirty&&(l.__dirty=!0),this._updateAndAddDisplayable(l,e,n)}t.__dirty=!1}else t.__clipPaths=e,this._displayList[this._displayListLen++]=t}},addRoot:function(t){t.__storage!==this&&(t instanceof a&&t.addChildrenToStorage(this),this.addToStorage(t),this._roots.push(t))},delRoot:function(t){if(null==t){for(var e=0;e<this._roots.length;e++){var n=this._roots[e];n instanceof a&&n.delChildrenFromStorage(this)}return this._roots=[],this._displayList=[],void(this._displayListLen=0)}if(t instanceof Array){e=0;for(var r=t.length;e<r;e++)this.delRoot(t[e])}else{var o=i.indexOf(this._roots,t);o>=0&&(this.delFromStorage(t),this._roots.splice(o,1),t instanceof a&&t.delChildrenFromStorage(this))}},addToStorage:function(t){return t&&(t.__storage=this,t.dirty(!1)),this},delFromStorage:function(t){return t&&(t.__storage=null),this},dispose:function(){this._renderList=this._roots=null},displayableSortFunc:s};var u=l;t.exports=u},b12f:function(t,e,n){var i=n("e1fc"),r=n("8918"),a=n("625e"),o=function(){this.group=new i,this.uid=r.getUID("viewComponent")};o.prototype={constructor:o,init:function(t,e){},render:function(t,e,n,i){},dispose:function(){},filterForExposedEvent:null};var s=o.prototype;s.updateView=s.updateLayout=s.updateVisual=function(t,e,n,i){},a.enableClassExtend(o),a.enableClassManagement(o,{registerWhenExtend:!0});var l=o;t.exports=l},b1d4:function(t,e,n){var i=n("862d");function r(t,e){return e=e||{},i(e.coordDimensions||[],t,{dimsDef:e.dimensionsDefine||t.dimensionsDefine,encodeDef:e.encodeDefine||t.encodeDefine,dimCount:e.dimensionsCount,generateCoord:e.generateCoord,generateCoordCount:e.generateCoordCount})}t.exports=r},b419:function(t,e,n){var i=n("6d8b"),r=n("2306"),a=n("4319"),o=n("6679"),s=["axisLine","axisLabel","axisTick","splitLine","splitArea"];function l(t,e,n){e[1]>e[0]&&(e=e.slice().reverse());var i=t.coordToPoint([e[0],n]),r=t.coordToPoint([e[1],n]);return{x1:i[0],y1:i[1],x2:r[0],y2:r[1]}}function u(t){var e=t.getRadiusAxis();return e.inverse?0:1}function h(t){var e=t[0],n=t[t.length-1];e&&n&&Math.abs(Math.abs(e.coord-n.coord)-360)<1e-4&&t.pop()}var c=o.extend({type:"angleAxis",axisPointerClass:"PolarAxisPointer",render:function(t,e){if(this.group.removeAll(),t.get("show")){var n=t.axis,r=n.polar,a=r.getRadiusAxis().getExtent(),o=n.getTicksCoords(),l=i.map(n.getViewLabels(),(function(t){t=i.clone(t);return t.coord=n.dataToCoord(t.tickValue),t}));h(l),h(o),i.each(s,(function(e){!t.get(e+".show")||n.scale.isBlank()&&"axisLine"!==e||this["_"+e](t,r,o,a,l)}),this)}},_axisLine:function(t,e,n,i){var a=t.getModel("axisLine.lineStyle"),o=new r.Circle({shape:{cx:e.cx,cy:e.cy,r:i[u(e)]},style:a.getLineStyle(),z2:1,silent:!0});o.style.fill=null,this.group.add(o)},_axisTick:function(t,e,n,a){var o=t.getModel("axisTick"),s=(o.get("inside")?-1:1)*o.get("length"),h=a[u(e)],c=i.map(n,(function(t){return new r.Line({shape:l(e,[h,h+s],t.coord)})}));this.group.add(r.mergePath(c,{style:i.defaults(o.getModel("lineStyle").getLineStyle(),{stroke:t.get("axisLine.lineStyle.color")})}))},_axisLabel:function(t,e,n,o,s){var l=t.getCategories(!0),h=t.getModel("axisLabel"),c=h.get("margin");i.each(s,(function(n,i){var s=h,d=n.tickValue,f=o[u(e)],p=e.coordToPoint([f+c,n.coord]),g=e.cx,v=e.cy,m=Math.abs(p[0]-g)/f<.3?"center":p[0]>g?"left":"right",y=Math.abs(p[1]-v)/f<.3?"middle":p[1]>v?"top":"bottom";l&&l[d]&&l[d].textStyle&&(s=new a(l[d].textStyle,h,h.ecModel));var x=new r.Text({silent:!0});this.group.add(x),r.setTextStyle(x.style,s,{x:p[0],y:p[1],textFill:s.getTextColor()||t.get("axisLine.lineStyle.color"),text:n.formattedLabel,textAlign:m,textVerticalAlign:y})}),this)},_splitLine:function(t,e,n,a){var o=t.getModel("splitLine"),s=o.getModel("lineStyle"),u=s.get("color"),h=0;u=u instanceof Array?u:[u];for(var c=[],d=0;d<n.length;d++){var f=h++%u.length;c[f]=c[f]||[],c[f].push(new r.Line({shape:l(e,a,n[d].coord)}))}for(d=0;d<c.length;d++)this.group.add(r.mergePath(c[d],{style:i.defaults({stroke:u[d%u.length]},s.getLineStyle()),silent:!0,z:t.get("z")}))},_splitArea:function(t,e,n,a){if(n.length){var o=t.getModel("splitArea"),s=o.getModel("areaStyle"),l=s.get("color"),u=0;l=l instanceof Array?l:[l];for(var h=[],c=Math.PI/180,d=-n[0].coord*c,f=Math.min(a[0],a[1]),p=Math.max(a[0],a[1]),g=t.get("clockwise"),v=1;v<n.length;v++){var m=u++%l.length;h[m]=h[m]||[],h[m].push(new r.Sector({shape:{cx:e.cx,cy:e.cy,r0:f,r:p,startAngle:d,endAngle:-n[v].coord*c,clockwise:g},silent:!0})),d=-n[v].coord*c}for(v=0;v<h.length;v++)this.group.add(r.mergePath(h[v],{style:i.defaults({fill:l[v%l.length]},s.getAreaStyle()),silent:!0}))}}});t.exports=c},b719:function(t,e,n){var i=n("697e7");e.zrender=i;var r=n("1687");e.matrix=r;var a=n("401b");e.vector=a;var o=n("6d8b"),s=n("41ef");e.color=s;var l=n("2306"),u=n("3842");e.number=u;var h=n("eda2");e.format=h;var c=n("88b3");c.throttle;e.throttle=c.throttle;var d=n("1548");e.helper=d;var f=n("bda7");e.parseGeoJSON=f;var p=n("6179");e.List=p;var g=n("4319");e.Model=g;var v=n("84ce");e.Axis=v;var m=n("22d1");e.env=m;var y=f,x={};o.each(["map","each","filter","indexOf","inherits","reduce","filter","bind","curry","isArray","isString","isObject","isFunction","extend","defaults","clone","merge"],(function(t){x[t]=o[t]}));var _={};o.each(["extendShape","extendPath","makePath","makeImage","mergePath","resizePath","createIcon","setHoverStyle","setLabelStyle","setTextStyle","setText","getFont","updateProps","initProps","getTransform","clipPointsByRect","clipRectByRect","Group","Image","Text","Circle","Sector","Ring","Polygon","Polyline","Rect","Line","BezierCurve","Arc","IncrementalDisplayable","CompoundPath","LinearGradient","RadialGradient","BoundingRect"],(function(t){_[t]=l[t]})),e.parseGeoJson=y,e.util=x,e.graphic=_},b809:function(t,e,n){var i=n("6d8b"),r=n("29a8"),a=n("2b17"),o=a.retrieveRawValue;function s(t,e){var n=e.getModel("aria");if(n.get("show"))if(n.get("description"))t.setAttribute("aria-label",n.get("description"));else{var a=0;e.eachSeries((function(t,e){++a}),this);var s,l=n.get("data.maxCount")||10,u=n.get("series.maxCount")||10,h=Math.min(a,u);if(!(a<1)){var c=v();s=c?p(g("general.withTitle"),{title:c}):g("general.withoutTitle");var d=[],f=a>1?"series.multiple.prefix":"series.single.prefix";s+=p(g(f),{seriesCount:a}),e.eachSeries((function(t,e){if(e<h){var n,i=t.get("name"),r="series."+(a>1?"multiple":"single")+".";n=g(i?r+"withName":r+"withoutName"),n=p(n,{seriesId:t.seriesIndex,seriesName:t.get("name"),seriesType:m(t.subType)});var s=t.getData();window.data=s,s.count()>l?n+=p(g("data.partialData"),{displayCnt:l}):n+=g("data.allData");for(var u=[],c=0;c<s.count();c++)if(c<l){var f=s.getName(c),v=o(s,c);u.push(p(g(f?"data.withName":"data.withoutName"),{name:f,value:v}))}n+=u.join(g("data.separator.middle"))+g("data.separator.end"),d.push(n)}})),s+=d.join(g("series.multiple.separator.middle"))+g("series.multiple.separator.end"),t.setAttribute("aria-label",s)}}function p(t,e){if("string"!==typeof t)return t;var n=t;return i.each(e,(function(t,e){n=n.replace(new RegExp("\\{\\s*"+e+"\\s*\\}","g"),t)})),n}function g(t){var e=n.get(t);if(null==e){for(var i=t.split("."),a=r.aria,o=0;o<i.length;++o)a=a[i[o]];return a}return e}function v(){var t=e.getModel("title").option;return t&&t.length&&(t=t[0]),t&&t.text}function m(t){return r.series.typeNames[t]||"自定义图"}}t.exports=s},bd6b:function(t,e,n){var i=n("06ad"),r=n("4942"),a=n("6d8b"),o=a.isString,s=a.isFunction,l=a.isObject,u=a.isArrayLike,h=a.indexOf,c=function(){this.animators=[]};function d(t,e,n,i,r,a,l,u){o(i)?(a=r,r=i,i=0):s(r)?(a=r,r="linear",i=0):s(i)?(a=i,i=0):s(n)?(a=n,n=500):n||(n=500),t.stopAnimation(),f(t,"",t,e,n,i,u);var h=t.animators.slice(),c=h.length;function d(){c--,c||a&&a()}c||a&&a();for(var p=0;p<h.length;p++)h[p].done(d).start(r,l)}function f(t,e,n,i,r,a,o){var s={},h=0;for(var c in i)i.hasOwnProperty(c)&&(null!=n[c]?l(i[c])&&!u(i[c])?f(t,e?e+"."+c:c,n[c],i[c],r,a,o):(o?(s[c]=n[c],p(t,e,c,i[c])):s[c]=i[c],h++):null==i[c]||o||p(t,e,c,i[c]));h>0&&t.animate(e,!1).when(null==r?500:r,s).delay(a||0)}function p(t,e,n,i){if(e){var r={};r[e]={},r[e][n]=i,t.attr(r)}else t.attr(n,i)}c.prototype={constructor:c,animate:function(t,e){var n,a=!1,o=this,s=this.__zr;if(t){var l=t.split("."),u=o;a="shape"===l[0];for(var c=0,d=l.length;c<d;c++)u&&(u=u[l[c]]);u&&(n=u)}else n=o;if(n){var f=o.animators,p=new i(n,e);return p.during((function(t){o.dirty(a)})).done((function(){f.splice(h(f,p),1)})),f.push(p),s&&s.animation.addAnimator(p),p}r('Property "'+t+'" is not existed in element '+o.id)},stopAnimation:function(t){for(var e=this.animators,n=e.length,i=0;i<n;i++)e[i].stop(t);return e.length=0,this},animateTo:function(t,e,n,i,r,a){d(this,t,e,n,i,r,a)},animateFrom:function(t,e,n,i,r,a){d(this,t,e,n,i,r,a,!0)}};var g=c;t.exports=g},bda7:function(t,e,n){var i=n("6d8b"),r=n("f279");function a(t){if(!t.UTF8Encoding)return t;var e=t.UTF8Scale;null==e&&(e=1024);for(var n=t.features,i=0;i<n.length;i++)for(var r=n[i],a=r.geometry,s=a.coordinates,l=a.encodeOffsets,u=0;u<s.length;u++){var h=s[u];if("Polygon"===a.type)s[u]=o(h,l[u],e);else if("MultiPolygon"===a.type)for(var c=0;c<h.length;c++){var d=h[c];h[c]=o(d,l[u][c],e)}}return t.UTF8Encoding=!1,t}function o(t,e,n){for(var i=[],r=e[0],a=e[1],o=0;o<t.length;o+=2){var s=t.charCodeAt(o)-64,l=t.charCodeAt(o+1)-64;s=s>>1^-(1&s),l=l>>1^-(1&l),s+=r,l+=a,r=s,a=l,i.push([s/n,l/n])}return i}function s(t){return a(t),i.map(i.filter(t.features,(function(t){return t.geometry&&t.properties&&t.geometry.coordinates.length>0})),(function(t){var e=t.properties,n=t.geometry,a=n.coordinates,o=[];"Polygon"===n.type&&o.push({type:"polygon",exterior:a[0],interiors:a.slice(1)}),"MultiPolygon"===n.type&&i.each(a,(function(t){t[0]&&o.push({type:"polygon",exterior:t[0],interiors:t.slice(1)})}));var s=new r(e.name,o,e.cp);return s.properties=e,s}))}t.exports=s},c533:function(t,e){var n=["#37A2DA","#32C5E9","#67E0E3","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#E062AE","#E690D1","#e7bcf3","#9d96f5","#8378EA","#96BFFF"],i={color:n,colorLayer:[["#37A2DA","#ffd85c","#fd7b5f"],["#37A2DA","#67E0E3","#FFDB5C","#ff9f7f","#E062AE","#9d96f5"],["#37A2DA","#32C5E9","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#e7bcf3","#8378EA","#96BFFF"],n]};t.exports=i},c775:function(t,e,n){var i=n("2b17"),r=i.retrieveRawValue;function a(t,e){var n=t.mapDimension("defaultedLabel",!0),i=n.length;if(1===i)return r(t,e,n[0]);if(i){for(var a=[],o=0;o<n.length;o++){var s=r(t,e,n[o]);a.push(s)}return a.join(" ")}}e.getDefaultLabel=a},c7a2:function(t,e,n){var i=n("cbe5"),r=n("5693"),a=n("9cf9"),o=a.subPixelOptimizeRect,s={},l=i.extend({type:"rect",shape:{r:0,x:0,y:0,width:0,height:0},buildPath:function(t,e){var n,i,a,l;this.subPixelOptimize?(o(s,e,this.style),n=s.x,i=s.y,a=s.width,l=s.height,s.r=e.r,e=s):(n=e.x,i=e.y,a=e.width,l=e.height),e.r?r.buildPath(t,e):t.rect(n,i,a,l),t.closePath()}});t.exports=l},ca98:function(t,e,n){var i=n("6d8b"),r=n("e0d3"),a=n("6cb7"),o=i.each,s=i.clone,l=i.map,u=i.merge,h=/^(min|max)?(.+)$/;function c(t){this._api=t,this._timelineOptions=[],this._mediaList=[],this._mediaDefault,this._currentMediaIndices=[],this._optionBackup,this._newBaseOption}function d(t,e,n){var r,a,s=[],l=[],u=t.timeline;if(t.baseOption&&(a=t.baseOption),(u||t.options)&&(a=a||{},s=(t.options||[]).slice()),t.media){a=a||{};var h=t.media;o(h,(function(t){t&&t.option&&(t.query?l.push(t):r||(r=t))}))}return a||(a=t),a.timeline||(a.timeline=u),o([a].concat(s).concat(i.map(l,(function(t){return t.option}))),(function(t){o(e,(function(e){e(t,n)}))})),{baseOption:a,timelineOptions:s,mediaDefault:r,mediaList:l}}function f(t,e,n){var r={width:e,height:n,aspectratio:e/n},a=!0;return i.each(t,(function(t,e){var n=e.match(h);if(n&&n[1]&&n[2]){var i=n[1],o=n[2].toLowerCase();p(r[o],t,i)||(a=!1)}})),a}function p(t,e,n){return"min"===n?t>=e:"max"===n?t<=e:t===e}function g(t,e){return t.join(",")===e.join(",")}function v(t,e){e=e||{},o(e,(function(e,n){if(null!=e){var i=t[n];if(a.hasClass(n)){e=r.normalizeToArray(e),i=r.normalizeToArray(i);var o=r.mappingToExists(i,e);t[n]=l(o,(function(t){return t.option&&t.exist?u(t.exist,t.option,!0):t.exist||t.option}))}else t[n]=u(i,e,!0)}}))}c.prototype={constructor:c,setOption:function(t,e){t&&i.each(r.normalizeToArray(t.series),(function(t){t&&t.data&&i.isTypedArray(t.data)&&i.setAsPrimitive(t.data)})),t=s(t,!0);var n=this._optionBackup,a=d.call(this,t,e,!n);this._newBaseOption=a.baseOption,n?(v(n.baseOption,a.baseOption),a.timelineOptions.length&&(n.timelineOptions=a.timelineOptions),a.mediaList.length&&(n.mediaList=a.mediaList),a.mediaDefault&&(n.mediaDefault=a.mediaDefault)):this._optionBackup=a},mountOption:function(t){var e=this._optionBackup;return this._timelineOptions=l(e.timelineOptions,s),this._mediaList=l(e.mediaList,s),this._mediaDefault=s(e.mediaDefault),this._currentMediaIndices=[],s(t?e.baseOption:this._newBaseOption)},getTimelineOption:function(t){var e,n=this._timelineOptions;if(n.length){var i=t.getComponent("timeline");i&&(e=s(n[i.getCurrentIndex()],!0))}return e},getMediaOption:function(t){var e=this._api.getWidth(),n=this._api.getHeight(),i=this._mediaList,r=this._mediaDefault,a=[],o=[];if(!i.length&&!r)return o;for(var u=0,h=i.length;u<h;u++)f(i[u].query,e,n)&&a.push(u);return!a.length&&r&&(a=[-1]),a.length&&!g(a,this._currentMediaIndices)&&(o=l(a,(function(t){return s(-1===t?r.option:i[t].option)}))),this._currentMediaIndices=a,o}};var m=c;t.exports=m},cb11:function(t,e,n){var i=n("cbe5"),r=n("9cf9"),a=r.subPixelOptimizeLine,o={},s=i.extend({type:"line",shape:{x1:0,y1:0,x2:0,y2:0,percent:1},style:{stroke:"#000",fill:null},buildPath:function(t,e){var n,i,r,s;this.subPixelOptimize?(a(o,e,this.style),n=o.x1,i=o.y1,r=o.x2,s=o.y2):(n=e.x1,i=e.y1,r=e.x2,s=e.y2);var l=e.percent;0!==l&&(t.moveTo(n,i),l<1&&(r=n*(1-l)+r*l,s=i*(1-l)+s*l),t.lineTo(r,s))},pointAt:function(t){var e=this.shape;return[e.x1*(1-t)+e.x2*t,e.y1*(1-t)+e.y2*t]}});t.exports=s},cb6d:function(t,e){function n(){this.on("mousedown",this._dragStart,this),this.on("mousemove",this._drag,this),this.on("mouseup",this._dragEnd,this),this.on("globalout",this._dragEnd,this)}function i(t,e){return{target:t,topTarget:e&&e.topTarget}}n.prototype={constructor:n,_dragStart:function(t){var e=t.target;e&&e.draggable&&(this._draggingTarget=e,e.dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.dispatchToElement(i(e,t),"dragstart",t.event))},_drag:function(t){var e=this._draggingTarget;if(e){var n=t.offsetX,r=t.offsetY,a=n-this._x,o=r-this._y;this._x=n,this._y=r,e.drift(a,o,t),this.dispatchToElement(i(e,t),"drag",t.event);var s=this.findHover(n,r,e).target,l=this._dropTarget;this._dropTarget=s,e!==s&&(l&&s!==l&&this.dispatchToElement(i(l,t),"dragleave",t.event),s&&s!==l&&this.dispatchToElement(i(s,t),"dragenter",t.event))}},_dragEnd:function(t){var e=this._draggingTarget;e&&(e.dragging=!1),this.dispatchToElement(i(e,t),"dragend",t.event),this._dropTarget&&this.dispatchToElement(i(this._dropTarget,t),"drop",t.event),this._draggingTarget=null,this._dropTarget=null}};var r=n;t.exports=r},cb8f:function(t,e,n){var i=n("3eba"),r=n("6d8b"),a=n("cd33"),o=n("eb6b");n("48ac"),n("d4b1"),n("4a9d"),i.registerPreprocessor((function(t){if(t){(!t.axisPointer||0===t.axisPointer.length)&&(t.axisPointer={});var e=t.axisPointer.link;e&&!r.isArray(e)&&(t.axisPointer.link=[e])}})),i.registerProcessor(i.PRIORITY.PROCESSOR.STATISTIC,(function(t,e){t.getComponent("axisPointer").coordSysAxesInfo=a.collect(t,e)})),i.registerAction({type:"updateAxisPointer",event:"updateAxisPointer",update:":updateAxisPointer"},o)},cbe5:function(t,e,n){var i=n("19eb"),r=n("6d8b"),a=n("20c8"),o=n("d833"),s=n("dc2f"),l=s.prototype.getCanvasPattern,u=Math.abs,h=new a(!0);function c(t){i.call(this,t),this.path=null}c.prototype={constructor:c,type:"path",__dirtyPath:!0,strokeContainThreshold:5,subPixelOptimize:!1,brush:function(t,e){var n,i=this.style,r=this.path||h,a=i.hasStroke(),o=i.hasFill(),s=i.fill,u=i.stroke,c=o&&!!s.colorStops,d=a&&!!u.colorStops,f=o&&!!s.image,p=a&&!!u.image;(i.bind(t,this,e),this.setTransform(t),this.__dirty)&&(c&&(n=n||this.getBoundingRect(),this._fillGradient=i.getGradient(t,s,n)),d&&(n=n||this.getBoundingRect(),this._strokeGradient=i.getGradient(t,u,n)));c?t.fillStyle=this._fillGradient:f&&(t.fillStyle=l.call(s,t)),d?t.strokeStyle=this._strokeGradient:p&&(t.strokeStyle=l.call(u,t));var g=i.lineDash,v=i.lineDashOffset,m=!!t.setLineDash,y=this.getGlobalScale();if(r.setScale(y[0],y[1]),this.__dirtyPath||g&&!m&&a?(r.beginPath(t),g&&!m&&(r.setLineDash(g),r.setLineDashOffset(v)),this.buildPath(r,this.shape,!1),this.path&&(this.__dirtyPath=!1)):(t.beginPath(),this.path.rebuildPath(t)),o)if(null!=i.fillOpacity){var x=t.globalAlpha;t.globalAlpha=i.fillOpacity*i.opacity,r.fill(t),t.globalAlpha=x}else r.fill(t);if(g&&m&&(t.setLineDash(g),t.lineDashOffset=v),a)if(null!=i.strokeOpacity){x=t.globalAlpha;t.globalAlpha=i.strokeOpacity*i.opacity,r.stroke(t),t.globalAlpha=x}else r.stroke(t);g&&m&&t.setLineDash([]),null!=i.text&&(this.restoreTransform(t),this.drawRectText(t,this.getBoundingRect()))},buildPath:function(t,e,n){},createPathProxy:function(){this.path=new a},getBoundingRect:function(){var t=this._rect,e=this.style,n=!t;if(n){var i=this.path;i||(i=this.path=new a),this.__dirtyPath&&(i.beginPath(),this.buildPath(i,this.shape,!1)),t=i.getBoundingRect()}if(this._rect=t,e.hasStroke()){var r=this._rectWithStroke||(this._rectWithStroke=t.clone());if(this.__dirty||n){r.copy(t);var o=e.lineWidth,s=e.strokeNoScale?this.getLineScale():1;e.hasFill()||(o=Math.max(o,this.strokeContainThreshold||4)),s>1e-10&&(r.width+=o/s,r.height+=o/s,r.x-=o/s/2,r.y-=o/s/2)}return r}return t},contain:function(t,e){var n=this.transformCoordToLocal(t,e),i=this.getBoundingRect(),r=this.style;if(t=n[0],e=n[1],i.contain(t,e)){var a=this.path.data;if(r.hasStroke()){var s=r.lineWidth,l=r.strokeNoScale?this.getLineScale():1;if(l>1e-10&&(r.hasFill()||(s=Math.max(s,this.strokeContainThreshold)),o.containStroke(a,s/l,t,e)))return!0}if(r.hasFill())return o.contain(a,t,e)}return!1},dirty:function(t){null==t&&(t=!0),t&&(this.__dirtyPath=t,this._rect=null),this.__dirty=this.__dirtyText=!0,this.__zr&&this.__zr.refresh(),this.__clipTarget&&this.__clipTarget.dirty()},animateShape:function(t){return this.animate("shape",t)},attrKV:function(t,e){"shape"===t?(this.setShape(e),this.__dirtyPath=!0,this._rect=null):i.prototype.attrKV.call(this,t,e)},setShape:function(t,e){var n=this.shape;if(n){if(r.isObject(t))for(var i in t)t.hasOwnProperty(i)&&(n[i]=t[i]);else n[t]=e;this.dirty(!0)}return this},getLineScale:function(){var t=this.transform;return t&&u(t[0]-1)>1e-10&&u(t[3]-1)>1e-10?Math.sqrt(u(t[0]*t[3]-t[2]*t[1])):1}},c.extend=function(t){var e=function(e){c.call(this,e),t.style&&this.style.extendFrom(t.style,!1);var n=t.shape;if(n){this.shape=this.shape||{};var i=this.shape;for(var r in n)!i.hasOwnProperty(r)&&n.hasOwnProperty(r)&&(i[r]=n[r])}t.init&&t.init.call(this,e)};for(var n in r.inherits(e,c),t)"style"!==n&&"shape"!==n&&(e.prototype[n]=t[n]);return e},r.inherits(c,i);var d=c;t.exports=d},cbe9:function(t,e,n){var i=n("6d8b"),r=n("cf7e");function a(t){r.call(this,t)}a.prototype={constructor:a,type:"cartesian2d",dimensions:["x","y"],getBaseAxis:function(){return this.getAxesByScale("ordinal")[0]||this.getAxesByScale("time")[0]||this.getAxis("x")},containPoint:function(t){var e=this.getAxis("x"),n=this.getAxis("y");return e.contain(e.toLocalCoord(t[0]))&&n.contain(n.toLocalCoord(t[1]))},containData:function(t){return this.getAxis("x").containData(t[0])&&this.getAxis("y").containData(t[1])},dataToPoint:function(t,e,n){var i=this.getAxis("x"),r=this.getAxis("y");return n=n||[],n[0]=i.toGlobalCoord(i.dataToCoord(t[0])),n[1]=r.toGlobalCoord(r.dataToCoord(t[1])),n},clampData:function(t,e){var n=this.getAxis("x").scale,i=this.getAxis("y").scale,r=n.getExtent(),a=i.getExtent(),o=n.parse(t[0]),s=i.parse(t[1]);return e=e||[],e[0]=Math.min(Math.max(Math.min(r[0],r[1]),o),Math.max(r[0],r[1])),e[1]=Math.min(Math.max(Math.min(a[0],a[1]),s),Math.max(a[0],a[1])),e},pointToData:function(t,e){var n=this.getAxis("x"),i=this.getAxis("y");return e=e||[],e[0]=n.coordToData(n.toLocalCoord(t[0])),e[1]=i.coordToData(i.toLocalCoord(t[1])),e},getOtherAxis:function(t){return this.getAxis("x"===t.dim?"y":"x")}},i.inherits(a,r);var o=a;t.exports=o},cccd:function(t,e,n){var i=n("e0d3"),r=i.makeInner;function a(){var t=r();return function(e){var n=t(e),i=e.pipelineContext,r=n.large,a=n.progressiveRender,o=n.large=i.large,s=n.progressiveRender=i.progressiveRender;return!!(r^o||a^s)&&"reset"}}t.exports=a},cd33:function(t,e,n){var i=n("6d8b"),r=n("4319"),a=i.each,o=i.curry;function s(t,e){var n={axesInfo:{},seriesInvolved:!1,coordSysAxesInfo:{},coordSysMap:{}};return l(n,t,e),n.seriesInvolved&&h(n,t),n}function l(t,e,n){var i=e.getComponent("tooltip"),r=e.getComponent("axisPointer"),s=r.get("link",!0)||[],l=[];a(n.getCoordinateSystems(),(function(n){if(n.axisPointerEnabled){var h=m(n.model),d=t.coordSysAxesInfo[h]={};t.coordSysMap[h]=n;var f=n.model,p=f.getModel("tooltip",i);if(a(n.getAxes(),o(_,!1,null)),n.getTooltipAxes&&i&&p.get("show")){var g="axis"===p.get("trigger"),y="cross"===p.get("axisPointer.type"),x=n.getTooltipAxes(p.get("axisPointer.axis"));(g||y)&&a(x.baseAxes,o(_,!y||"cross",g)),y&&a(x.otherAxes,o(_,"cross",!1))}}function _(i,a,o){var h=o.model.getModel("axisPointer",r),f=h.get("show");if(f&&("auto"!==f||i||v(h))){null==a&&(a=h.get("triggerTooltip")),h=i?u(o,p,r,e,i,a):h;var g=h.get("snap"),y=m(o.model),x=a||g||"category"===o.type,_=t.axesInfo[y]={key:y,axis:o,coordSys:n,axisPointerModel:h,triggerTooltip:a,involveSeries:x,snap:g,useHandle:v(h),seriesModels:[]};d[y]=_,t.seriesInvolved|=x;var b=c(s,o);if(null!=b){var w=l[b]||(l[b]={axesInfo:{}});w.axesInfo[y]=_,w.mapper=s[b].mapper,_.linkGroup=w}}}}))}function u(t,e,n,o,s,l){var u=e.getModel("axisPointer"),h={};a(["type","snap","lineStyle","shadowStyle","label","animation","animationDurationUpdate","animationEasingUpdate","z"],(function(t){h[t]=i.clone(u.get(t))})),h.snap="category"!==t.type&&!!l,"cross"===u.get("type")&&(h.type="line");var c=h.label||(h.label={});if(null==c.show&&(c.show=!1),"cross"===s){var d=u.get("label.show");if(c.show=null==d||d,!l){var f=h.lineStyle=u.get("crossStyle");f&&i.defaults(c,f.textStyle)}}return t.model.getModel("axisPointer",new r(h,n,o))}function h(t,e){e.eachSeries((function(e){var n=e.coordinateSystem,i=e.get("tooltip.trigger",!0),r=e.get("tooltip.show",!0);n&&"none"!==i&&!1!==i&&"item"!==i&&!1!==r&&!1!==e.get("axisPointer.show",!0)&&a(t.coordSysAxesInfo[m(n.model)],(function(t){var i=t.axis;n.getAxis(i.dim)===i&&(t.seriesModels.push(e),null==t.seriesDataCount&&(t.seriesDataCount=0),t.seriesDataCount+=e.getData().count())}))}),this)}function c(t,e){for(var n=e.model,i=e.dim,r=0;r<t.length;r++){var a=t[r]||{};if(d(a[i+"AxisId"],n.id)||d(a[i+"AxisIndex"],n.componentIndex)||d(a[i+"AxisName"],n.name))return r}}function d(t,e){return"all"===t||i.isArray(t)&&i.indexOf(t,e)>=0||t===e}function f(t){var e=p(t);if(e){var n=e.axisPointerModel,i=e.axis.scale,r=n.option,a=n.get("status"),o=n.get("value");null!=o&&(o=i.parse(o));var s=v(n);null==a&&(r.status=s?"show":"hide");var l=i.getExtent().slice();l[0]>l[1]&&l.reverse(),(null==o||o>l[1])&&(o=l[1]),o<l[0]&&(o=l[0]),r.value=o,s&&(r.status=e.axis.scale.isBlank()?"hide":"show")}}function p(t){var e=(t.ecModel.getComponent("axisPointer")||{}).coordSysAxesInfo;return e&&e.axesInfo[m(t)]}function g(t){var e=p(t);return e&&e.axisPointerModel}function v(t){return!!t.get("handle.show")}function m(t){return t.type+"||"+t.id}e.collect=s,e.fixValue=f,e.getAxisInfo=p,e.getAxisPointerModel=g,e.makeKey=m},cdaa:function(t,e,n){var i=n("607d"),r=i.addEventListener,a=i.removeEventListener,o=i.normalizeEvent,s=n("6d8b"),l=n("1fab"),u=n("22d1"),h=300,c=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],d=["touchstart","touchend","touchmove"],f={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},p=s.map(c,(function(t){var e=t.replace("mouse","pointer");return f[e]?e:t}));function g(t){return"mousewheel"===t&&u.browser.firefox?"DOMMouseScroll":t}function v(t){t._touching=!0,clearTimeout(t._touchTimer),t._touchTimer=setTimeout((function(){t._touching=!1}),700)}var m={mousemove:function(t){t=o(this.dom,t),this.trigger("mousemove",t)},mouseout:function(t){t=o(this.dom,t);var e=t.toElement||t.relatedTarget;if(e!==this.dom)while(e&&9!==e.nodeType){if(e===this.dom)return;e=e.parentNode}this.trigger("mouseout",t)},touchstart:function(t){t=o(this.dom,t),t.zrByTouch=!0,this._lastTouchMoment=new Date,this.handler.processGesture(this,t,"start"),m.mousemove.call(this,t),m.mousedown.call(this,t),v(this)},touchmove:function(t){t=o(this.dom,t),t.zrByTouch=!0,this.handler.processGesture(this,t,"change"),m.mousemove.call(this,t),v(this)},touchend:function(t){t=o(this.dom,t),t.zrByTouch=!0,this.handler.processGesture(this,t,"end"),m.mouseup.call(this,t),+new Date-this._lastTouchMoment<h&&m.click.call(this,t),v(this)},pointerdown:function(t){m.mousedown.call(this,t)},pointermove:function(t){y(t)||m.mousemove.call(this,t)},pointerup:function(t){m.mouseup.call(this,t)},pointerout:function(t){y(t)||m.mouseout.call(this,t)}};function y(t){var e=t.pointerType;return"pen"===e||"touch"===e}function x(t){function e(t,e){return function(){if(!e._touching)return t.apply(e,arguments)}}s.each(d,(function(e){t._handlers[e]=s.bind(m[e],t)})),s.each(p,(function(e){t._handlers[e]=s.bind(m[e],t)})),s.each(c,(function(n){t._handlers[n]=e(m[n],t)}))}function _(t){function e(e,n){s.each(e,(function(e){r(t,g(e),n._handlers[e])}),n)}l.call(this),this.dom=t,this._touching=!1,this._touchTimer,this._handlers={},x(this),u.pointerEventsSupported?e(p,this):(u.touchEventsSupported&&e(d,this),e(c,this))}s.each(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],(function(t){m[t]=function(e){e=o(this.dom,e),this.trigger(t,e)}}));var b=_.prototype;b.dispose=function(){for(var t=c.concat(d),e=0;e<t.length;e++){var n=t[e];a(this.dom,g(n),this._handlers[n])}},b.setCursor=function(t){this.dom.style&&(this.dom.style.cursor=t||"default")},s.mixin(_,l);var w=_;t.exports=w},cf7e:function(t,e,n){var i=n("6d8b");function r(t){return this._axes[t]}var a=function(t){this._axes={},this._dimList=[],this.name=t||""};a.prototype={constructor:a,type:"cartesian",getAxis:function(t){return this._axes[t]},getAxes:function(){return i.map(this._dimList,r,this)},getAxesByScale:function(t){return t=t.toLowerCase(),i.filter(this.getAxes(),(function(e){return e.scale.type===t}))},addAxis:function(t){var e=t.dim;this._axes[e]=t,this._dimList.push(e)},dataToCoord:function(t){return this._dataCoordConvert(t,"dataToCoord")},coordToData:function(t){return this._dataCoordConvert(t,"coordToData")},_dataCoordConvert:function(t,e){for(var n=this._dimList,i=t instanceof Array?[]:{},r=0;r<n.length;r++){var a=n[r],o=this._axes[a];i[a]=o[e](t[a])}return i}};var o=a;t.exports=o},d15d:function(t,e,n){var i=n("6d8b"),r=i.createHashMap,a=i.each;function o(t){var e=r();t.eachSeries((function(t){var n=t.get("stack");if(n){var i=e.get(n)||e.set(n,[]),r=t.getData(),a={stackResultDimension:r.getCalculationInfo("stackResultDimension"),stackedOverDimension:r.getCalculationInfo("stackedOverDimension"),stackedDimension:r.getCalculationInfo("stackedDimension"),stackedByDimension:r.getCalculationInfo("stackedByDimension"),isStackedByIndex:r.getCalculationInfo("isStackedByIndex"),data:r,seriesModel:t};if(!a.stackedDimension||!a.isStackedByIndex&&!a.stackedByDimension)return;i.length&&r.setCalculationInfo("stackedOnSeries",i[i.length-1].seriesModel),i.push(a)}})),e.each(s)}function s(t){a(t,(function(e,n){var i=[],r=[NaN,NaN],a=[e.stackResultDimension,e.stackedOverDimension],o=e.data,s=e.isStackedByIndex,l=o.map(a,(function(a,l,u){var h,c,d=o.get(e.stackedDimension,u);if(isNaN(d))return r;s?c=o.getRawIndex(u):h=o.get(e.stackedByDimension,u);for(var f=NaN,p=n-1;p>=0;p--){var g=t[p];if(s||(c=g.data.rawIndexOf(g.stackedByDimension,h)),c>=0){var v=g.data.getByRawIndex(g.stackResultDimension,c);if(d>=0&&v>0||d<=0&&v<0){d+=v,f=v;break}}}return i[0]=d,i[1]=f,i}));o.hostModel.setData(l),e.data=l}))}t.exports=o},d2cf:function(t,e,n){var i=n("6d8b"),r=n("401b"),a=n("cb6d"),o=n("1fab"),s=n("607d"),l=n("0b44"),u="silent";function h(t,e,n){return{type:t,event:n,target:e.target,topTarget:e.topTarget,cancelBubble:!1,offsetX:n.zrX,offsetY:n.zrY,gestureEvent:n.gestureEvent,pinchX:n.pinchX,pinchY:n.pinchY,pinchScale:n.pinchScale,wheelDelta:n.zrDelta,zrByTouch:n.zrByTouch,which:n.which,stop:c}}function c(t){s.stop(this.event)}function d(){}d.prototype.dispose=function(){};var f=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],p=function(t,e,n,i){o.call(this),this.storage=t,this.painter=e,this.painterRoot=i,n=n||new d,this.proxy=null,this._hovered={},this._lastTouchMoment,this._lastX,this._lastY,this._gestureMgr,a.call(this),this.setHandlerProxy(n)};function g(t,e,n){if(t[t.rectHover?"rectContain":"contain"](e,n)){var i,r=t;while(r){if(r.clipPath&&!r.clipPath.contain(e,n))return!1;r.silent&&(i=!0),r=r.parent}return!i||u}return!1}p.prototype={constructor:p,setHandlerProxy:function(t){this.proxy&&this.proxy.dispose(),t&&(i.each(f,(function(e){t.on&&t.on(e,this[e],this)}),this),t.handler=this),this.proxy=t},mousemove:function(t){var e=t.zrX,n=t.zrY,i=this._hovered,r=i.target;r&&!r.__zr&&(i=this.findHover(i.x,i.y),r=i.target);var a=this._hovered=this.findHover(e,n),o=a.target,s=this.proxy;s.setCursor&&s.setCursor(o?o.cursor:"default"),r&&o!==r&&this.dispatchToElement(i,"mouseout",t),this.dispatchToElement(a,"mousemove",t),o&&o!==r&&this.dispatchToElement(a,"mouseover",t)},mouseout:function(t){this.dispatchToElement(this._hovered,"mouseout",t);var e,n=t.toElement||t.relatedTarget;do{n=n&&n.parentNode}while(n&&9!==n.nodeType&&!(e=n===this.painterRoot));!e&&this.trigger("globalout",{event:t})},resize:function(t){this._hovered={}},dispatch:function(t,e){var n=this[t];n&&n.call(this,e)},dispose:function(){this.proxy.dispose(),this.storage=this.proxy=this.painter=null},setCursorStyle:function(t){var e=this.proxy;e.setCursor&&e.setCursor(t)},dispatchToElement:function(t,e,n){t=t||{};var i=t.target;if(!i||!i.silent){var r="on"+e,a=h(e,t,n);while(i)if(i[r]&&(a.cancelBubble=i[r].call(i,a)),i.trigger(e,a),i=i.parent,a.cancelBubble)break;a.cancelBubble||(this.trigger(e,a),this.painter&&this.painter.eachOtherLayer((function(t){"function"===typeof t[r]&&t[r].call(t,a),t.trigger&&t.trigger(e,a)})))}},findHover:function(t,e,n){for(var i=this.storage.getDisplayList(),r={x:t,y:e},a=i.length-1;a>=0;a--){var o;if(i[a]!==n&&!i[a].ignore&&(o=g(i[a],t,e))&&(!r.topTarget&&(r.topTarget=i[a]),o!==u)){r.target=i[a];break}}return r},processGesture:function(t,e){this._gestureMgr||(this._gestureMgr=new l);var n=this._gestureMgr;"start"===e&&n.clear();var i=n.recognize(t,this.findHover(t.zrX,t.zrY,null).target,this.proxy.dom);if("end"===e&&n.clear(),i){var r=i.type;t.gestureEvent=r,this.dispatchToElement({target:i.target},r,i.event)}}},i.each(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],(function(t){p.prototype[t]=function(e){var n=this.findHover(e.zrX,e.zrY),i=n.target;if("mousedown"===t)this._downEl=i,this._downPoint=[e.zrX,e.zrY],this._upEl=i;else if("mouseup"===t)this._upEl=i;else if("click"===t){if(this._downEl!==this._upEl||!this._downPoint||r.dist(this._downPoint,[e.zrX,e.zrY])>4)return;this._downPoint=null}this.dispatchToElement(n,t,e)}})),i.mixin(p,o),i.mixin(p,a);var v=p;t.exports=v},d498:function(t,e,n){var i=n("cbe5"),r=n("4fac"),a=i.extend({type:"polyline",shape:{points:null,smooth:!1,smoothConstraint:null},style:{stroke:"#000",fill:null},buildPath:function(t,e){r.buildPath(t,e,!1)}});t.exports=a},d4b1:function(t,e,n){var i=n("3eba"),r=n("17d6"),a=i.extendComponentView({type:"axisPointer",render:function(t,e,n){var i=e.getComponent("tooltip"),a=t.get("triggerOn")||i&&i.get("triggerOn")||"mousemove|click";r.register("axisPointer",n,(function(t,e,n){"none"!==a&&("leave"===t||a.indexOf(t)>=0)&&n({type:"updateAxisPointer",currTrigger:t,x:e&&e.offsetX,y:e&&e.offsetY})}))},remove:function(t,e){r.unregister(e.getZr(),"axisPointer"),a.superApply(this._model,"remove",arguments)},dispose:function(t,e){r.unregister("axisPointer",e),a.superApply(this._model,"dispose",arguments)}}),o=a;t.exports=o},d4c6:function(t,e,n){var i=n("cbe5"),r=i.extend({type:"compound",shape:{paths:null},_updatePathDirty:function(){for(var t=this.__dirtyPath,e=this.shape.paths,n=0;n<e.length;n++)t=t||e[n].__dirtyPath;this.__dirtyPath=t,this.__dirty=this.__dirty||t},beforeBrush:function(){this._updatePathDirty();for(var t=this.shape.paths||[],e=this.getGlobalScale(),n=0;n<t.length;n++)t[n].path||t[n].createPathProxy(),t[n].path.setScale(e[0],e[1])},buildPath:function(t,e){for(var n=e.paths||[],i=0;i<n.length;i++)n[i].buildPath(t,n[i].shape,!0)},afterBrush:function(){for(var t=this.shape.paths||[],e=0;e<t.length;e++)t[e].__dirtyPath=!1},getBoundingRect:function(){return this._updatePathDirty(),i.prototype.getBoundingRect.call(this)}});t.exports=r},d4d1:function(t,e,n){var i=n("cbe5"),r=n("401b"),a=n("897a"),o=r.min,s=r.max,l=r.scaleAndAdd,u=r.copy,h=[],c=[],d=[];function f(t){return isNaN(t[0])||isNaN(t[1])}function p(t,e,n,i,r,a,o,s,l,u,h){return"none"!==u&&u?g.apply(this,arguments):v.apply(this,arguments)}function g(t,e,n,i,r,a,o,s,l,h,p){for(var g=0,v=n,m=0;m<i;m++){var y=e[v];if(v>=r||v<0)break;if(f(y)){if(p){v+=a;continue}break}if(v===n)t[a>0?"moveTo":"lineTo"](y[0],y[1]);else if(l>0){var x=e[g],_="y"===h?1:0,b=(y[_]-x[_])*l;u(c,x),c[_]=x[_]+b,u(d,y),d[_]=y[_]-b,t.bezierCurveTo(c[0],c[1],d[0],d[1],y[0],y[1])}else t.lineTo(y[0],y[1]);g=v,v+=a}return m}function v(t,e,n,i,a,p,g,v,m,y,x){for(var _=0,b=n,w=0;w<i;w++){var S=e[b];if(b>=a||b<0)break;if(f(S)){if(x){b+=p;continue}break}if(b===n)t[p>0?"moveTo":"lineTo"](S[0],S[1]),u(c,S);else if(m>0){var M=b+p,T=e[M];if(x)while(T&&f(e[M]))M+=p,T=e[M];var A=.5,C=e[_];T=e[M];if(!T||f(T))u(d,S);else{var I,k;if(f(T)&&!x&&(T=S),r.sub(h,T,C),"x"===y||"y"===y){var D="x"===y?0:1;I=Math.abs(S[D]-C[D]),k=Math.abs(S[D]-T[D])}else I=r.dist(S,C),k=r.dist(S,T);A=k/(k+I),l(d,S,h,-m*(1-A))}o(c,c,v),s(c,c,g),o(d,d,v),s(d,d,g),t.bezierCurveTo(c[0],c[1],d[0],d[1],S[0],S[1]),l(c,S,h,m*A)}else t.lineTo(S[0],S[1]);_=b,b+=p}return w}function m(t,e){var n=[1/0,1/0],i=[-1/0,-1/0];if(e)for(var r=0;r<t.length;r++){var a=t[r];a[0]<n[0]&&(n[0]=a[0]),a[1]<n[1]&&(n[1]=a[1]),a[0]>i[0]&&(i[0]=a[0]),a[1]>i[1]&&(i[1]=a[1])}return{min:e?n:i,max:e?i:n}}var y=i.extend({type:"ec-polyline",shape:{points:[],smooth:0,smoothConstraint:!0,smoothMonotone:null,connectNulls:!1},style:{fill:null,stroke:"#000"},brush:a(i.prototype.brush),buildPath:function(t,e){var n=e.points,i=0,r=n.length,a=m(n,e.smoothConstraint);if(e.connectNulls){for(;r>0;r--)if(!f(n[r-1]))break;for(;i<r;i++)if(!f(n[i]))break}while(i<r)i+=p(t,n,i,r,r,1,a.min,a.max,e.smooth,e.smoothMonotone,e.connectNulls)+1}}),x=i.extend({type:"ec-polygon",shape:{points:[],stackedOnPoints:[],smooth:0,stackedOnSmooth:0,smoothConstraint:!0,smoothMonotone:null,connectNulls:!1},brush:a(i.prototype.brush),buildPath:function(t,e){var n=e.points,i=e.stackedOnPoints,r=0,a=n.length,o=e.smoothMonotone,s=m(n,e.smoothConstraint),l=m(i,e.smoothConstraint);if(e.connectNulls){for(;a>0;a--)if(!f(n[a-1]))break;for(;r<a;r++)if(!f(n[r]))break}while(r<a){var u=p(t,n,r,a,a,1,s.min,s.max,e.smooth,o,e.connectNulls);p(t,i,r+u-1,u,a,-1,l.min,l.max,e.stackedOnSmooth,o,e.connectNulls),r+=u+1,t.closePath()}}});e.Polyline=y,e.Polygon=x},d51b:function(t,e){var n=function(){this.head=null,this.tail=null,this._len=0},i=n.prototype;i.insert=function(t){var e=new r(t);return this.insertEntry(e),e},i.insertEntry=function(t){this.head?(this.tail.next=t,t.prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},i.remove=function(t){var e=t.prev,n=t.next;e?e.next=n:this.head=n,n?n.prev=e:this.tail=e,t.next=t.prev=null,this._len--},i.len=function(){return this._len},i.clear=function(){this.head=this.tail=null,this._len=0};var r=function(t){this.value=t,this.next,this.prev},a=function(t){this._list=new n,this._map={},this._maxSize=t||10,this._lastRemovedEntry=null},o=a.prototype;o.put=function(t,e){var n=this._list,i=this._map,a=null;if(null==i[t]){var o=n.len(),s=this._lastRemovedEntry;if(o>=this._maxSize&&o>0){var l=n.head;n.remove(l),delete i[l.key],a=l.value,this._lastRemovedEntry=l}s?s.value=e:s=new r(e),s.key=t,n.insertEntry(s),i[t]=s}return a},o.get=function(t){var e=this._map[t],n=this._list;if(null!=e)return e!==n.tail&&(n.remove(e),n.insertEntry(e)),e.value},o.clear=function(){this._list.clear(),this._map={}};var s=a;t.exports=s},d5b7:function(t,e,n){var i=n("de00"),r=n("1fab"),a=n("0cde"),o=n("bd6b"),s=n("6d8b"),l=function(t){a.call(this,t),r.call(this,t),o.call(this,t),this.id=t.id||i()};l.prototype={type:"element",name:"",__zr:null,ignore:!1,clipPath:null,isGroup:!1,drift:function(t,e){switch(this.draggable){case"horizontal":e=0;break;case"vertical":t=0;break}var n=this.transform;n||(n=this.transform=[1,0,0,1,0,0]),n[4]+=t,n[5]+=e,this.decomposeTransform(),this.dirty(!1)},beforeUpdate:function(){},afterUpdate:function(){},update:function(){this.updateTransform()},traverse:function(t,e){},attrKV:function(t,e){if("position"===t||"scale"===t||"origin"===t){if(e){var n=this[t];n||(n=this[t]=[]),n[0]=e[0],n[1]=e[1]}}else this[t]=e},hide:function(){this.ignore=!0,this.__zr&&this.__zr.refresh()},show:function(){this.ignore=!1,this.__zr&&this.__zr.refresh()},attr:function(t,e){if("string"===typeof t)this.attrKV(t,e);else if(s.isObject(t))for(var n in t)t.hasOwnProperty(n)&&this.attrKV(n,t[n]);return this.dirty(!1),this},setClipPath:function(t){var e=this.__zr;e&&t.addSelfToZr(e),this.clipPath&&this.clipPath!==t&&this.removeClipPath(),this.clipPath=t,t.__zr=e,t.__clipTarget=this,this.dirty(!1)},removeClipPath:function(){var t=this.clipPath;t&&(t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__clipTarget=null,this.clipPath=null,this.dirty(!1))},addSelfToZr:function(t){this.__zr=t;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.addAnimator(e[n]);this.clipPath&&this.clipPath.addSelfToZr(t)},removeSelfFromZr:function(t){this.__zr=null;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.removeAnimator(e[n]);this.clipPath&&this.clipPath.removeSelfFromZr(t)}},s.mixin(l,o),s.mixin(l,a),s.mixin(l,r);var u=l;t.exports=u},d833:function(t,e,n){var i=n("20c8"),r=n("9680"),a=n("e7d2"),o=n("68ab"),s=n("9f51"),l=n("857d"),u=l.normalizeRadian,h=n("4a3f"),c=n("8728"),d=i.CMD,f=2*Math.PI,p=1e-4;function g(t,e){return Math.abs(t-e)<p}var v=[-1,-1,-1],m=[-1,-1];function y(){var t=m[0];m[0]=m[1],m[1]=t}function x(t,e,n,i,r,a,o,s,l,u){if(u>e&&u>i&&u>a&&u>s||u<e&&u<i&&u<a&&u<s)return 0;var c=h.cubicRootAt(e,i,a,s,u,v);if(0===c)return 0;for(var d,f,p=0,g=-1,x=0;x<c;x++){var _=v[x],b=0===_||1===_?.5:1,w=h.cubicAt(t,n,r,o,_);w<l||(g<0&&(g=h.cubicExtrema(e,i,a,s,m),m[1]<m[0]&&g>1&&y(),d=h.cubicAt(e,i,a,s,m[0]),g>1&&(f=h.cubicAt(e,i,a,s,m[1]))),2===g?_<m[0]?p+=d<e?b:-b:_<m[1]?p+=f<d?b:-b:p+=s<f?b:-b:_<m[0]?p+=d<e?b:-b:p+=s<d?b:-b)}return p}function _(t,e,n,i,r,a,o,s){if(s>e&&s>i&&s>a||s<e&&s<i&&s<a)return 0;var l=h.quadraticRootAt(e,i,a,s,v);if(0===l)return 0;var u=h.quadraticExtremum(e,i,a);if(u>=0&&u<=1){for(var c=0,d=h.quadraticAt(e,i,a,u),f=0;f<l;f++){var p=0===v[f]||1===v[f]?.5:1,g=h.quadraticAt(t,n,r,v[f]);g<o||(v[f]<u?c+=d<e?p:-p:c+=a<d?p:-p)}return c}p=0===v[0]||1===v[0]?.5:1,g=h.quadraticAt(t,n,r,v[0]);return g<o?0:a<e?p:-p}function b(t,e,n,i,r,a,o,s){if(s-=e,s>n||s<-n)return 0;var l=Math.sqrt(n*n-s*s);v[0]=-l,v[1]=l;var h=Math.abs(i-r);if(h<1e-4)return 0;if(h%f<1e-4){i=0,r=f;var c=a?1:-1;return o>=v[0]+t&&o<=v[1]+t?c:0}if(a){l=i;i=u(r),r=u(l)}else i=u(i),r=u(r);i>r&&(r+=f);for(var d=0,p=0;p<2;p++){var g=v[p];if(g+t>o){var m=Math.atan2(s,g);c=a?1:-1;m<0&&(m=f+m),(m>=i&&m<=r||m+f>=i&&m+f<=r)&&(m>Math.PI/2&&m<1.5*Math.PI&&(c=-c),d+=c)}}return d}function w(t,e,n,i,l){for(var u=0,h=0,f=0,p=0,v=0,m=0;m<t.length;){var y=t[m++];switch(y===d.M&&m>1&&(n||(u+=c(h,f,p,v,i,l))),1===m&&(h=t[m],f=t[m+1],p=h,v=f),y){case d.M:p=t[m++],v=t[m++],h=p,f=v;break;case d.L:if(n){if(r.containStroke(h,f,t[m],t[m+1],e,i,l))return!0}else u+=c(h,f,t[m],t[m+1],i,l)||0;h=t[m++],f=t[m++];break;case d.C:if(n){if(a.containStroke(h,f,t[m++],t[m++],t[m++],t[m++],t[m],t[m+1],e,i,l))return!0}else u+=x(h,f,t[m++],t[m++],t[m++],t[m++],t[m],t[m+1],i,l)||0;h=t[m++],f=t[m++];break;case d.Q:if(n){if(o.containStroke(h,f,t[m++],t[m++],t[m],t[m+1],e,i,l))return!0}else u+=_(h,f,t[m++],t[m++],t[m],t[m+1],i,l)||0;h=t[m++],f=t[m++];break;case d.A:var w=t[m++],S=t[m++],M=t[m++],T=t[m++],A=t[m++],C=t[m++];m+=1;var I=1-t[m++],k=Math.cos(A)*M+w,D=Math.sin(A)*T+S;m>1?u+=c(h,f,k,D,i,l):(p=k,v=D);var P=(i-w)*T/M+w;if(n){if(s.containStroke(w,S,T,A,A+C,I,e,P,l))return!0}else u+=b(w,S,T,A,A+C,I,P,l);h=Math.cos(A+C)*M+w,f=Math.sin(A+C)*T+S;break;case d.R:p=h=t[m++],v=f=t[m++];var O=t[m++],L=t[m++];k=p+O,D=v+L;if(n){if(r.containStroke(p,v,k,v,e,i,l)||r.containStroke(k,v,k,D,e,i,l)||r.containStroke(k,D,p,D,e,i,l)||r.containStroke(p,D,p,v,e,i,l))return!0}else u+=c(k,v,k,D,i,l),u+=c(p,D,p,v,i,l);break;case d.Z:if(n){if(r.containStroke(h,f,p,v,e,i,l))return!0}else u+=c(h,f,p,v,i,l);h=p,f=v;break}}return n||g(f,v)||(u+=c(h,f,p,v,i,l)||0),0!==u}function S(t,e,n){return w(t,0,!1,e,n)}function M(t,e,n,i){return w(t,e,!0,n,i)}e.contain=S,e.containStroke=M},d9f1:function(t,e,n){var i=n("6d8b"),r=n("6cb7"),a=n("9e47"),o=n("2023"),s=r.extend({type:"polarAxis",axis:null,getCoordSysModel:function(){return this.ecModel.queryComponents({mainType:"polar",index:this.option.polarIndex,id:this.option.polarId})[0]}});i.merge(s.prototype,o);var l={angle:{startAngle:90,clockwise:!0,splitNumber:12,axisLabel:{rotate:!1}},radius:{splitNumber:5}};function u(t,e){return e.type||(e.data?"category":"value")}a("angle",s,u,l.angle),a("radius",s,u,l.radius)},d9fc:function(t,e,n){var i=n("cbe5"),r=i.extend({type:"circle",shape:{cx:0,cy:0,r:0},buildPath:function(t,e,n){n&&t.moveTo(e.cx+e.r,e.cy),t.arc(e.cx,e.cy,e.r,0,2*Math.PI,!0)}});t.exports=r},dc2f:function(t,e){var n=function(t,e){this.image=t,this.repeat=e,this.type="pattern"};n.prototype.getCanvasPattern=function(t){return t.createPattern(this.image,this.repeat||"repeat")};var i=n;t.exports=i},dcb3:function(t,e,n){var i=n("6d8b"),r=n("625e"),a=n("2306"),o=n("cd33"),s=n("607d"),l=n("88b3"),u=n("e0d3"),h=u.makeInner,c=h(),d=i.clone,f=i.bind;function p(){}function g(t,e,n,i){v(c(n).lastProp,i)||(c(n).lastProp=i,e?a.updateProps(n,i,t):(n.stopAnimation(),n.attr(i)))}function v(t,e){if(i.isObject(t)&&i.isObject(e)){var n=!0;return i.each(e,(function(e,i){n=n&&v(t[i],e)})),!!n}return t===e}function m(t,e){t[e.get("label.show")?"show":"hide"]()}function y(t){return{position:t.position.slice(),rotation:t.rotation||0}}function x(t,e,n){var i=e.get("z"),r=e.get("zlevel");t&&t.traverse((function(t){"group"!==t.type&&(null!=i&&(t.z=i),null!=r&&(t.zlevel=r),t.silent=n)}))}p.prototype={_group:null,_lastGraphicKey:null,_handle:null,_dragging:!1,_lastValue:null,_lastStatus:null,_payloadInfo:null,animationThreshold:15,render:function(t,e,n,r){var o=e.get("value"),s=e.get("status");if(this._axisModel=t,this._axisPointerModel=e,this._api=n,r||this._lastValue!==o||this._lastStatus!==s){this._lastValue=o,this._lastStatus=s;var l=this._group,u=this._handle;if(!s||"hide"===s)return l&&l.hide(),void(u&&u.hide());l&&l.show(),u&&u.show();var h={};this.makeElOption(h,o,t,e,n);var c=h.graphicKey;c!==this._lastGraphicKey&&this.clear(n),this._lastGraphicKey=c;var d=this._moveAnimation=this.determineAnimation(t,e);if(l){var f=i.curry(g,e,d);this.updatePointerEl(l,h,f,e),this.updateLabelEl(l,h,f,e)}else l=this._group=new a.Group,this.createPointerEl(l,h,t,e),this.createLabelEl(l,h,t,e),n.getZr().add(l);x(l,e,!0),this._renderHandle(o)}},remove:function(t){this.clear(t)},dispose:function(t){this.clear(t)},determineAnimation:function(t,e){var n=e.get("animation"),i=t.axis,r="category"===i.type,a=e.get("snap");if(!a&&!r)return!1;if("auto"===n||null==n){var s=this.animationThreshold;if(r&&i.getBandWidth()>s)return!0;if(a){var l=o.getAxisInfo(t).seriesDataCount,u=i.getExtent();return Math.abs(u[0]-u[1])/l>s}return!1}return!0===n},makeElOption:function(t,e,n,i,r){},createPointerEl:function(t,e,n,i){var r=e.pointer;if(r){var o=c(t).pointerEl=new a[r.type](d(e.pointer));t.add(o)}},createLabelEl:function(t,e,n,i){if(e.label){var r=c(t).labelEl=new a.Rect(d(e.label));t.add(r),m(r,i)}},updatePointerEl:function(t,e,n){var i=c(t).pointerEl;i&&(i.setStyle(e.pointer.style),n(i,{shape:e.pointer.shape}))},updateLabelEl:function(t,e,n,i){var r=c(t).labelEl;r&&(r.setStyle(e.label.style),n(r,{shape:e.label.shape,position:e.label.position}),m(r,i))},_renderHandle:function(t){if(!this._dragging&&this.updateHandleTransform){var e,n=this._axisPointerModel,r=this._api.getZr(),o=this._handle,u=n.getModel("handle"),h=n.get("status");if(!u.get("show")||!h||"hide"===h)return o&&r.remove(o),void(this._handle=null);this._handle||(e=!0,o=this._handle=a.createIcon(u.get("icon"),{cursor:"move",draggable:!0,onmousemove:function(t){s.stop(t.event)},onmousedown:f(this._onHandleDragMove,this,0,0),drift:f(this._onHandleDragMove,this),ondragend:f(this._onHandleDragEnd,this)}),r.add(o)),x(o,n,!1);var c=["color","borderColor","borderWidth","opacity","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"];o.setStyle(u.getItemStyle(null,c));var d=u.get("size");i.isArray(d)||(d=[d,d]),o.attr("scale",[d[0]/2,d[1]/2]),l.createOrUpdate(this,"_doDispatchAxisPointer",u.get("throttle")||0,"fixRate"),this._moveHandleToValue(t,e)}},_moveHandleToValue:function(t,e){g(this._axisPointerModel,!e&&this._moveAnimation,this._handle,y(this.getHandleTransform(t,this._axisModel,this._axisPointerModel)))},_onHandleDragMove:function(t,e){var n=this._handle;if(n){this._dragging=!0;var i=this.updateHandleTransform(y(n),[t,e],this._axisModel,this._axisPointerModel);this._payloadInfo=i,n.stopAnimation(),n.attr(y(i)),c(n).lastProp=null,this._doDispatchAxisPointer()}},_doDispatchAxisPointer:function(){var t=this._handle;if(t){var e=this._payloadInfo,n=this._axisModel;this._api.dispatchAction({type:"updateAxisPointer",x:e.cursorPoint[0],y:e.cursorPoint[1],tooltipOption:e.tooltipOption,axesInfo:[{axisDim:n.axis.dim,axisIndex:n.componentIndex}]})}},_onHandleDragEnd:function(t){this._dragging=!1;var e=this._handle;if(e){var n=this._axisPointerModel.get("value");this._moveHandleToValue(n),this._api.dispatchAction({type:"hideTip"})}},getHandleTransform:null,updateHandleTransform:null,clear:function(t){this._lastValue=null,this._lastStatus=null;var e=t.getZr(),n=this._group,i=this._handle;e&&n&&(this._lastGraphicKey=null,n&&e.remove(n),i&&e.remove(i),this._group=null,this._handle=null,this._payloadInfo=null)},doClear:function(){},buildLabel:function(t,e,n){return n=n||0,{x:t[n],y:t[1-n],width:e[n],height:e[1-n]}}},p.prototype.constructor=p,r.enableClassExtend(p);var _=p;t.exports=_},dded:function(t,e,n){var i=n("6d8b"),r=n("42e5"),a=function(t,e,n,i,a){this.x=null==t?.5:t,this.y=null==e?.5:e,this.r=null==n?.5:n,this.type="radial",this.global=a||!1,r.call(this,i)};a.prototype={constructor:a},i.inherits(a,r);var o=a;t.exports=o},de00:function(t,e){var n=2311;function i(){return n++}t.exports=i},de1c:function(t,e){var n={getBoxLayoutParams:function(){return{left:this.get("left"),top:this.get("top"),right:this.get("right"),bottom:this.get("bottom"),width:this.get("width"),height:this.get("height")}}};t.exports=n},e073:function(t,e,n){var i=n("6d8b"),r=n("e86a"),a=n("e0d3"),o=a.makeInner,s=n("697e"),l=s.makeLabelFormatter,u=s.getOptionCategoryInterval,h=s.shouldShowAllLabels,c=o();function d(t){return"category"===t.type?p(t):m(t)}function f(t,e){return"category"===t.type?v(t,e):{ticks:t.scale.getTicks()}}function p(t){var e=t.getLabelModel(),n=g(t,e);return!e.get("show")||t.scale.isBlank()?{labels:[],labelCategoryInterval:n.labelCategoryInterval}:n}function g(t,e){var n,r,a=y(t,"labels"),o=u(e),s=x(a,o);return s||(i.isFunction(o)?n=T(t,o):(r="auto"===o?b(t):o,n=M(t,r)),_(a,o,{labels:n,labelCategoryInterval:r}))}function v(t,e){var n,r,a=y(t,"ticks"),o=u(e),s=x(a,o);if(s)return s;if(e.get("show")&&!t.scale.isBlank()||(n=[]),i.isFunction(o))n=T(t,o,!0);else if("auto"===o){var l=g(t,t.getLabelModel());r=l.labelCategoryInterval,n=i.map(l.labels,(function(t){return t.tickValue}))}else r=o,n=M(t,r,!0);return _(a,o,{ticks:n,tickCategoryInterval:r})}function m(t){var e=t.scale.getTicks(),n=l(t);return{labels:i.map(e,(function(e,i){return{formattedLabel:n(e,i),rawLabel:t.scale.getLabel(e),tickValue:e}}))}}function y(t,e){return c(t)[e]||(c(t)[e]=[])}function x(t,e){for(var n=0;n<t.length;n++)if(t[n].key===e)return t[n].value}function _(t,e,n){return t.push({key:e,value:n}),n}function b(t){var e=c(t).autoInterval;return null!=e?e:c(t).autoInterval=t.calculateCategoryInterval()}function w(t){var e=S(t),n=l(t),i=(e.axisRotate-e.labelRotate)/180*Math.PI,a=t.scale,o=a.getExtent(),s=a.count();if(o[1]-o[0]<1)return 0;var u=1;s>40&&(u=Math.max(1,Math.floor(s/40)));for(var h=o[0],d=t.dataToCoord(h+1)-t.dataToCoord(h),f=Math.abs(d*Math.cos(i)),p=Math.abs(d*Math.sin(i)),g=0,v=0;h<=o[1];h+=u){var m=0,y=0,x=r.getBoundingRect(n(h),e.font,"center","top");m=1.3*x.width,y=1.3*x.height,g=Math.max(g,m,7),v=Math.max(v,y,7)}var _=g/f,b=v/p;isNaN(_)&&(_=1/0),isNaN(b)&&(b=1/0);var w=Math.max(0,Math.floor(Math.min(_,b))),M=c(t.model),T=M.lastAutoInterval,A=M.lastTickCount;return null!=T&&null!=A&&Math.abs(T-w)<=1&&Math.abs(A-s)<=1&&T>w?w=T:(M.lastTickCount=s,M.lastAutoInterval=w),w}function S(t){var e=t.getLabelModel();return{axisRotate:t.getRotate?t.getRotate():t.isHorizontal&&!t.isHorizontal()?90:0,labelRotate:e.get("rotate")||0,font:e.getFont()}}function M(t,e,n){var i=l(t),r=t.scale,a=r.getExtent(),o=t.getLabelModel(),s=[],u=Math.max((e||0)+1,1),c=a[0],d=r.count();0!==c&&u>1&&d/u>2&&(c=Math.round(Math.ceil(c/u)*u));var f=h(t),p=o.get("showMinLabel")||f,g=o.get("showMaxLabel")||f;p&&c!==a[0]&&m(a[0]);for(var v=c;v<=a[1];v+=u)m(v);function m(t){s.push(n?t:{formattedLabel:i(t),rawLabel:r.getLabel(t),tickValue:t})}return g&&v!==a[1]&&m(a[1]),s}function T(t,e,n){var r=t.scale,a=l(t),o=[];return i.each(r.getTicks(),(function(t){var i=r.getLabel(t);e(t,i)&&o.push(n?t:{formattedLabel:a(t),rawLabel:i,tickValue:t})})),o}e.createAxisLabels=d,e.createAxisTicks=f,e.calculateCategoryInterval=w},e0d3:function(t,e,n){var i=n("6d8b"),r=n("22d1"),a=i.each,o=i.isObject,s=i.isArray,l="series\0";function u(t){return t instanceof Array?t:null==t?[]:[t]}function h(t,e,n){if(t){t[e]=t[e]||{},t.emphasis=t.emphasis||{},t.emphasis[e]=t.emphasis[e]||{};for(var i=0,r=n.length;i<r;i++){var a=n[i];!t.emphasis[e].hasOwnProperty(a)&&t[e].hasOwnProperty(a)&&(t.emphasis[e][a]=t[e][a])}}}var c=["fontStyle","fontWeight","fontSize","fontFamily","rich","tag","color","textBorderColor","textBorderWidth","width","height","lineHeight","align","verticalAlign","baseline","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY","backgroundColor","borderColor","borderWidth","borderRadius","padding"];function d(t){return!o(t)||s(t)||t instanceof Date?t:t.value}function f(t){return o(t)&&!(t instanceof Array)}function p(t,e){e=(e||[]).slice();var n=i.map(t||[],(function(t,e){return{exist:t}}));return a(e,(function(t,i){if(o(t)){for(var r=0;r<n.length;r++)if(!n[r].option&&null!=t.id&&n[r].exist.id===t.id+"")return n[r].option=t,void(e[i]=null);for(r=0;r<n.length;r++){var a=n[r].exist;if(!n[r].option&&(null==a.id||null==t.id)&&null!=t.name&&!m(t)&&!m(a)&&a.name===t.name+"")return n[r].option=t,void(e[i]=null)}}})),a(e,(function(t,e){if(o(t)){for(var i=0;i<n.length;i++){var r=n[i].exist;if(!n[i].option&&!m(r)&&null==t.id){n[i].option=t;break}}i>=n.length&&n.push({option:t})}})),n}function g(t){var e=i.createHashMap();a(t,(function(t,n){var i=t.exist;i&&e.set(i.id,t)})),a(t,(function(t,n){var r=t.option;i.assert(!r||null==r.id||!e.get(r.id)||e.get(r.id)===t,"id duplicates: "+(r&&r.id)),r&&null!=r.id&&e.set(r.id,t),!t.keyInfo&&(t.keyInfo={})})),a(t,(function(t,n){var i=t.exist,r=t.option,a=t.keyInfo;if(o(r)){if(a.name=null!=r.name?r.name+"":i?i.name:l+n,i)a.id=i.id;else if(null!=r.id)a.id=r.id+"";else{var s=0;do{a.id="\0"+a.name+"\0"+s++}while(e.get(a.id))}e.set(a.id,t)}}))}function v(t){var e=t.name;return!(!e||!e.indexOf(l))}function m(t){return o(t)&&t.id&&0===(t.id+"").indexOf("\0_ec_\0")}function y(t,e){var n={},i={};return r(t||[],n),r(e||[],i,n),[a(n),a(i)];function r(t,e,n){for(var i=0,r=t.length;i<r;i++)for(var a=t[i].seriesId,o=u(t[i].dataIndex),s=n&&n[a],l=0,h=o.length;l<h;l++){var c=o[l];s&&s[c]?s[c]=null:(e[a]||(e[a]={}))[c]=1}}function a(t,e){var n=[];for(var i in t)if(t.hasOwnProperty(i)&&null!=t[i])if(e)n.push(+i);else{var r=a(t[i],!0);r.length&&n.push({seriesId:i,dataIndex:r})}return n}}function x(t,e){return null!=e.dataIndexInside?e.dataIndexInside:null!=e.dataIndex?i.isArray(e.dataIndex)?i.map(e.dataIndex,(function(e){return t.indexOfRawIndex(e)})):t.indexOfRawIndex(e.dataIndex):null!=e.name?i.isArray(e.name)?i.map(e.name,(function(e){return t.indexOfName(e)})):t.indexOfName(e.name):void 0}function _(){var t="__\0ec_inner_"+b+++"_"+Math.random().toFixed(5);return function(e){return e[t]||(e[t]={})}}var b=0;function w(t,e,n){if(i.isString(e)){var r={};r[e+"Index"]=0,e=r}var o=n&&n.defaultMainType;!o||S(e,o+"Index")||S(e,o+"Id")||S(e,o+"Name")||(e[o+"Index"]=0);var s={};return a(e,(function(r,a){r=e[a];if("dataIndex"!==a&&"dataIndexInside"!==a){var o=a.match(/^(\w+)(Index|Id|Name)$/)||[],l=o[1],u=(o[2]||"").toLowerCase();if(!(!l||!u||null==r||"index"===u&&"none"===r||n&&n.includeMainTypes&&i.indexOf(n.includeMainTypes,l)<0)){var h={mainType:l};"index"===u&&"all"===r||(h[u]=r);var c=t.queryComponents(h);s[l+"Models"]=c,s[l+"Model"]=c[0]}}else s[a]=r})),s}function S(t,e){return t&&t.hasOwnProperty(e)}function M(t,e,n){t.setAttribute?t.setAttribute(e,n):t[e]=n}function T(t,e){return t.getAttribute?t.getAttribute(e):t[e]}function A(t){return"auto"===t?r.domSupported?"html":"richText":t||"html"}function C(t,e){var n=i.createHashMap(),r=[];return i.each(t,(function(t){var i=e(t);(n.get(i)||(r.push(i),n.set(i,[]))).push(t)})),{keys:r,buckets:n}}e.normalizeToArray=u,e.defaultEmphasis=h,e.TEXT_STYLE_OPTIONS=c,e.getDataItemValue=d,e.isDataItemOption=f,e.mappingToExists=p,e.makeIdAndName=g,e.isNameSpecified=v,e.isIdInner=m,e.compressBatches=y,e.queryDataIndex=x,e.makeInner=_,e.parseFinder=w,e.setAttribute=M,e.getAttribute=T,e.getTooltipRenderMode=A,e.groupData=C},e0d8:function(t,e,n){var i=n("625e");function r(t){this._setting=t||{},this._extent=[1/0,-1/0],this._interval=0,this.init&&this.init.apply(this,arguments)}r.prototype.parse=function(t){return t},r.prototype.getSetting=function(t){return this._setting[t]},r.prototype.contain=function(t){var e=this._extent;return t>=e[0]&&t<=e[1]},r.prototype.normalize=function(t){var e=this._extent;return e[1]===e[0]?.5:(t-e[0])/(e[1]-e[0])},r.prototype.scale=function(t){var e=this._extent;return t*(e[1]-e[0])+e[0]},r.prototype.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1])},r.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},r.prototype.getExtent=function(){return this._extent.slice()},r.prototype.setExtent=function(t,e){var n=this._extent;isNaN(t)||(n[0]=t),isNaN(e)||(n[1]=e)},r.prototype.isBlank=function(){return this._isBlank},r.prototype.setBlank=function(t){this._isBlank=t},r.prototype.getLabel=null,i.enableClassExtend(r),i.enableClassManagement(r,{registerWhenExtend:!0});var a=r;t.exports=a},e1fc:function(t,e,n){var i=n("6d8b"),r=n("d5b7"),a=n("9850"),o=function(t){for(var e in t=t||{},r.call(this,t),t)t.hasOwnProperty(e)&&(this[e]=t[e]);this._children=[],this.__storage=null,this.__dirty=!0};o.prototype={constructor:o,isGroup:!0,type:"group",silent:!1,children:function(){return this._children.slice()},childAt:function(t){return this._children[t]},childOfName:function(t){for(var e=this._children,n=0;n<e.length;n++)if(e[n].name===t)return e[n]},childCount:function(){return this._children.length},add:function(t){return t&&t!==this&&t.parent!==this&&(this._children.push(t),this._doAdd(t)),this},addBefore:function(t,e){if(t&&t!==this&&t.parent!==this&&e&&e.parent===this){var n=this._children,i=n.indexOf(e);i>=0&&(n.splice(i,0,t),this._doAdd(t))}return this},_doAdd:function(t){t.parent&&t.parent.remove(t),t.parent=this;var e=this.__storage,n=this.__zr;e&&e!==t.__storage&&(e.addToStorage(t),t instanceof o&&t.addChildrenToStorage(e)),n&&n.refresh()},remove:function(t){var e=this.__zr,n=this.__storage,r=this._children,a=i.indexOf(r,t);return a<0||(r.splice(a,1),t.parent=null,n&&(n.delFromStorage(t),t instanceof o&&t.delChildrenFromStorage(n)),e&&e.refresh()),this},removeAll:function(){var t,e,n=this._children,i=this.__storage;for(e=0;e<n.length;e++)t=n[e],i&&(i.delFromStorage(t),t instanceof o&&t.delChildrenFromStorage(i)),t.parent=null;return n.length=0,this},eachChild:function(t,e){for(var n=this._children,i=0;i<n.length;i++){var r=n[i];t.call(e,r,i)}return this},traverse:function(t,e){for(var n=0;n<this._children.length;n++){var i=this._children[n];t.call(e,i),"group"===i.type&&i.traverse(t,e)}return this},addChildrenToStorage:function(t){for(var e=0;e<this._children.length;e++){var n=this._children[e];t.addToStorage(n),n instanceof o&&n.addChildrenToStorage(t)}},delChildrenFromStorage:function(t){for(var e=0;e<this._children.length;e++){var n=this._children[e];t.delFromStorage(n),n instanceof o&&n.delChildrenFromStorage(t)}},dirty:function(){return this.__dirty=!0,this.__zr&&this.__zr.refresh(),this},getBoundingRect:function(t){for(var e=null,n=new a(0,0,0,0),i=t||this._children,r=[],o=0;o<i.length;o++){var s=i[o];if(!s.ignore&&!s.invisible){var l=s.getBoundingRect(),u=s.getLocalTransform(r);u?(n.copy(l),n.applyTransform(u),e=e||n.clone(),e.union(n)):(e=e||l.clone(),e.union(l))}}return e||n}},i.inherits(o,r);var s=o;t.exports=s},e263:function(t,e,n){var i=n("401b"),r=n("4a3f"),a=Math.min,o=Math.max,s=Math.sin,l=Math.cos,u=2*Math.PI,h=i.create(),c=i.create(),d=i.create();function f(t,e,n){if(0!==t.length){var i,r=t[0],s=r[0],l=r[0],u=r[1],h=r[1];for(i=1;i<t.length;i++)r=t[i],s=a(s,r[0]),l=o(l,r[0]),u=a(u,r[1]),h=o(h,r[1]);e[0]=s,e[1]=u,n[0]=l,n[1]=h}}function p(t,e,n,i,r,s){r[0]=a(t,n),r[1]=a(e,i),s[0]=o(t,n),s[1]=o(e,i)}var g=[],v=[];function m(t,e,n,i,s,l,u,h,c,d){var f,p=r.cubicExtrema,m=r.cubicAt,y=p(t,n,s,u,g);for(c[0]=1/0,c[1]=1/0,d[0]=-1/0,d[1]=-1/0,f=0;f<y;f++){var x=m(t,n,s,u,g[f]);c[0]=a(x,c[0]),d[0]=o(x,d[0])}for(y=p(e,i,l,h,v),f=0;f<y;f++){var _=m(e,i,l,h,v[f]);c[1]=a(_,c[1]),d[1]=o(_,d[1])}c[0]=a(t,c[0]),d[0]=o(t,d[0]),c[0]=a(u,c[0]),d[0]=o(u,d[0]),c[1]=a(e,c[1]),d[1]=o(e,d[1]),c[1]=a(h,c[1]),d[1]=o(h,d[1])}function y(t,e,n,i,s,l,u,h){var c=r.quadraticExtremum,d=r.quadraticAt,f=o(a(c(t,n,s),1),0),p=o(a(c(e,i,l),1),0),g=d(t,n,s,f),v=d(e,i,l,p);u[0]=a(t,s,g),u[1]=a(e,l,v),h[0]=o(t,s,g),h[1]=o(e,l,v)}function x(t,e,n,r,a,o,f,p,g){var v=i.min,m=i.max,y=Math.abs(a-o);if(y%u<1e-4&&y>1e-4)return p[0]=t-n,p[1]=e-r,g[0]=t+n,void(g[1]=e+r);if(h[0]=l(a)*n+t,h[1]=s(a)*r+e,c[0]=l(o)*n+t,c[1]=s(o)*r+e,v(p,h,c),m(g,h,c),a%=u,a<0&&(a+=u),o%=u,o<0&&(o+=u),a>o&&!f?o+=u:a<o&&f&&(a+=u),f){var x=o;o=a,a=x}for(var _=0;_<o;_+=Math.PI/2)_>a&&(d[0]=l(_)*n+t,d[1]=s(_)*r+e,v(p,d,p),m(g,d,g))}e.fromPoints=f,e.fromLine=p,e.fromCubic=m,e.fromQuadratic=y,e.fromArc=x},e47b:function(t,e,n){var i=n("e0d3"),r=i.makeInner,a=i.normalizeToArray,o=r();function s(t,e){for(var n=t.length,i=0;i<n;i++)if(t[i].length>e)return t[i];return t[n-1]}var l={clearColorPalette:function(){o(this).colorIdx=0,o(this).colorNameMap={}},getColorFromPalette:function(t,e,n){e=e||this;var i=o(e),r=i.colorIdx||0,l=i.colorNameMap=i.colorNameMap||{};if(l.hasOwnProperty(t))return l[t];var u=a(this.get("color",!0)),h=this.get("colorLayer",!0),c=null!=n&&h?s(h,n):u;if(c=c||u,c&&c.length){var d=c[r];return t&&(l[t]=d),i.colorIdx=(r+1)%c.length,d}}};t.exports=l},e7d2:function(t,e,n){var i=n("4a3f");function r(t,e,n,r,a,o,s,l,u,h,c){if(0===u)return!1;var d=u;if(c>e+d&&c>r+d&&c>o+d&&c>l+d||c<e-d&&c<r-d&&c<o-d&&c<l-d||h>t+d&&h>n+d&&h>a+d&&h>s+d||h<t-d&&h<n-d&&h<a-d&&h<s-d)return!1;var f=i.cubicProjectPoint(t,e,n,r,a,o,s,l,h,c,null);return f<=d/2}e.containStroke=r},e86a:function(t,e,n){var i=n("9850"),r=n("5e76"),a=n("6d8b"),o=a.getContext,s=a.extend,l=a.retrieve2,u=a.retrieve3,h=a.trim,c={},d=0,f=5e3,p=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g,g="12px sans-serif",v={};function m(t,e){v[t]=e}function y(t,e){e=e||g;var n=t+":"+e;if(c[n])return c[n];for(var i=(t+"").split("\n"),r=0,a=0,o=i.length;a<o;a++)r=Math.max(D(i[a],e).width,r);return d>f&&(d=0,c={}),d++,c[n]=r,r}function x(t,e,n,i,r,a,o,s){return o?b(t,e,n,i,r,a,o,s):_(t,e,n,i,r,a,s)}function _(t,e,n,r,a,o,s){var l=P(t,e,a,o,s),u=y(t,e);a&&(u+=a[1]+a[3]);var h=l.outerHeight,c=w(0,u,n),d=S(0,h,r),f=new i(c,d,u,h);return f.lineHeight=l.lineHeight,f}function b(t,e,n,r,a,o,s,l){var u=O(t,{rich:s,truncate:l,font:e,textAlign:n,textPadding:a,textLineHeight:o}),h=u.outerWidth,c=u.outerHeight,d=w(0,h,n),f=S(0,c,r);return new i(d,f,h,c)}function w(t,e,n){return"right"===n?t-=e:"center"===n&&(t-=e/2),t}function S(t,e,n){return"middle"===n?t-=e/2:"bottom"===n&&(t-=e),t}function M(t,e,n){var i=e.x,r=e.y,a=e.height,o=e.width,s=a/2,l="left",u="top";switch(t){case"left":i-=n,r+=s,l="right",u="middle";break;case"right":i+=n+o,r+=s,u="middle";break;case"top":i+=o/2,r-=n,l="center",u="bottom";break;case"bottom":i+=o/2,r+=a+n,l="center";break;case"inside":i+=o/2,r+=s,l="center",u="middle";break;case"insideLeft":i+=n,r+=s,u="middle";break;case"insideRight":i+=o-n,r+=s,l="right",u="middle";break;case"insideTop":i+=o/2,r+=n,l="center";break;case"insideBottom":i+=o/2,r+=a-n,l="center",u="bottom";break;case"insideTopLeft":i+=n,r+=n;break;case"insideTopRight":i+=o-n,r+=n,l="right";break;case"insideBottomLeft":i+=n,r+=a-n,u="bottom";break;case"insideBottomRight":i+=o-n,r+=a-n,l="right",u="bottom";break}return{x:i,y:r,textAlign:l,textVerticalAlign:u}}function T(t,e,n,i,r){if(!e)return"";var a=(t+"").split("\n");r=A(e,n,i,r);for(var o=0,s=a.length;o<s;o++)a[o]=C(a[o],r);return a.join("\n")}function A(t,e,n,i){i=s({},i),i.font=e;n=l(n,"...");i.maxIterations=l(i.maxIterations,2);var r=i.minChar=l(i.minChar,0);i.cnCharWidth=y("国",e);var a=i.ascCharWidth=y("a",e);i.placeholder=l(i.placeholder,"");for(var o=t=Math.max(0,t-1),u=0;u<r&&o>=a;u++)o-=a;var h=y(n,e);return h>o&&(n="",h=0),o=t-h,i.ellipsis=n,i.ellipsisWidth=h,i.contentWidth=o,i.containerWidth=t,i}function C(t,e){var n=e.containerWidth,i=e.font,r=e.contentWidth;if(!n)return"";var a=y(t,i);if(a<=n)return t;for(var o=0;;o++){if(a<=r||o>=e.maxIterations){t+=e.ellipsis;break}var s=0===o?I(t,r,e.ascCharWidth,e.cnCharWidth):a>0?Math.floor(t.length*r/a):0;t=t.substr(0,s),a=y(t,i)}return""===t&&(t=e.placeholder),t}function I(t,e,n,i){for(var r=0,a=0,o=t.length;a<o&&r<e;a++){var s=t.charCodeAt(a);r+=0<=s&&s<=127?n:i}return a}function k(t){return y("国",t)}function D(t,e){return v.measureText(t,e)}function P(t,e,n,i,r){null!=t&&(t+="");var a=l(i,k(e)),o=t?t.split("\n"):[],s=o.length*a,u=s;if(n&&(u+=n[0]+n[2]),t&&r){var h=r.outerHeight,c=r.outerWidth;if(null!=h&&u>h)t="",o=[];else if(null!=c)for(var d=A(c-(n?n[1]+n[3]:0),e,r.ellipsis,{minChar:r.minChar,placeholder:r.placeholder}),f=0,p=o.length;f<p;f++)o[f]=C(o[f],d)}return{lines:o,height:s,outerHeight:u,lineHeight:a}}function O(t,e){var n={lines:[],width:0,height:0};if(null!=t&&(t+=""),!t)return n;var i,a=p.lastIndex=0;while(null!=(i=p.exec(t))){var o=i.index;o>a&&L(n,t.substring(a,o)),L(n,i[2],i[1]),a=p.lastIndex}a<t.length&&L(n,t.substring(a,t.length));var s=n.lines,h=0,c=0,d=[],f=e.textPadding,g=e.truncate,v=g&&g.outerWidth,m=g&&g.outerHeight;f&&(null!=v&&(v-=f[1]+f[3]),null!=m&&(m-=f[0]+f[2]));for(var x=0;x<s.length;x++){for(var _=s[x],b=0,w=0,S=0;S<_.tokens.length;S++){var M=_.tokens[S],A=M.styleName&&e.rich[M.styleName]||{},C=M.textPadding=A.textPadding,I=M.font=A.font||e.font,D=M.textHeight=l(A.textHeight,k(I));if(C&&(D+=C[0]+C[2]),M.height=D,M.lineHeight=u(A.textLineHeight,e.textLineHeight,D),M.textAlign=A&&A.textAlign||e.textAlign,M.textVerticalAlign=A&&A.textVerticalAlign||"middle",null!=m&&h+M.lineHeight>m)return{lines:[],width:0,height:0};M.textWidth=y(M.text,I);var P=A.textWidth,O=null==P||"auto"===P;if("string"===typeof P&&"%"===P.charAt(P.length-1))M.percentWidth=P,d.push(M),P=0;else{if(O){P=M.textWidth;var E=A.textBackgroundColor,R=E&&E.image;R&&(R=r.findExistImage(R),r.isImageReady(R)&&(P=Math.max(P,R.width*D/R.height)))}var B=C?C[1]+C[3]:0;P+=B;var N=null!=v?v-w:null;null!=N&&N<P&&(!O||N<B?(M.text="",M.textWidth=P=0):(M.text=T(M.text,N-B,I,g.ellipsis,{minChar:g.minChar}),M.textWidth=y(M.text,I),P=M.textWidth+B))}w+=M.width=P,A&&(b=Math.max(b,M.lineHeight))}_.width=w,_.lineHeight=b,h+=b,c=Math.max(c,w)}n.outerWidth=n.width=l(e.textWidth,c),n.outerHeight=n.height=l(e.textHeight,h),f&&(n.outerWidth+=f[1]+f[3],n.outerHeight+=f[0]+f[2]);for(x=0;x<d.length;x++){M=d[x];var z=M.percentWidth;M.width=parseInt(z,10)/100*c}return n}function L(t,e,n){for(var i=""===e,r=e.split("\n"),a=t.lines,o=0;o<r.length;o++){var s=r[o],l={styleName:n,text:s,isLineHolder:!s&&!i};if(o)a.push({tokens:[l]});else{var u=(a[a.length-1]||(a[0]={tokens:[]})).tokens,h=u.length;1===h&&u[0].isLineHolder?u[0]=l:(s||!h||i)&&u.push(l)}}}function E(t){var e=(t.fontSize||t.fontFamily)&&[t.fontStyle,t.fontWeight,(t.fontSize||12)+"px",t.fontFamily||"sans-serif"].join(" ");return e&&h(e)||t.textFont||t.font}v.measureText=function(t,e){var n=o();return n.font=e||g,n.measureText(t)},e.DEFAULT_FONT=g,e.$override=m,e.getWidth=y,e.getBoundingRect=x,e.adjustTextX=w,e.adjustTextY=S,e.adjustTextPositionOnRect=M,e.truncateText=T,e.getLineHeight=k,e.measureText=D,e.parsePlainText=P,e.parseRichText=O,e.makeFont=E},e887:function(t,e,n){var i=n("6d8b"),r=i.each,a=n("e1fc"),o=n("8918"),s=n("625e"),l=n("e0d3"),u=n("f47d"),h=u.createTask,c=n("cccd"),d=l.makeInner(),f=c();function p(){this.group=new a,this.uid=o.getUID("viewChart"),this.renderTask=h({plan:y,reset:x}),this.renderTask.context={view:this}}p.prototype={type:"chart",init:function(t,e){},render:function(t,e,n,i){},highlight:function(t,e,n,i){m(t.getData(),i,"emphasis")},downplay:function(t,e,n,i){m(t.getData(),i,"normal")},remove:function(t,e){this.group.removeAll()},dispose:function(){},incrementalPrepareRender:null,incrementalRender:null,updateTransform:null,filterForExposedEvent:null};var g=p.prototype;function v(t,e){if(t&&(t.trigger(e),"group"===t.type))for(var n=0;n<t.childCount();n++)v(t.childAt(n),e)}function m(t,e,n){var i=l.queryDataIndex(t,e);null!=i?r(l.normalizeToArray(i),(function(e){v(t.getItemGraphicEl(e),n)})):t.eachItemGraphicEl((function(t){v(t,n)}))}function y(t){return f(t.model)}function x(t){var e=t.model,n=t.ecModel,i=t.api,r=t.payload,a=e.pipelineContext.progressiveRender,o=t.view,s=r&&d(r).updateMethod,l=a?"incrementalPrepareRender":s&&o[s]?s:"render";return"render"!==l&&o[l](e,n,i,r),_[l]}g.updateView=g.updateLayout=g.updateVisual=function(t,e,n,i){this.render(t,e,n,i)},s.enableClassExtend(p,["dispose"]),s.enableClassManagement(p,{registerWhenExtend:!0}),p.markUpdateMethod=function(t,e){d(t).updateMethod=e};var _={incrementalPrepareRender:{progress:function(t,e){e.view.incrementalRender(t,e.model,e.ecModel,e.api,e.payload)}},render:{forceFirstProgress:!0,progress:function(t,e){e.view.render(e.model,e.ecModel,e.api,e.payload)}}},b=p;t.exports=b},eb6b:function(t,e,n){var i=n("6d8b"),r=n("e0d3"),a=r.makeInner,o=n("cd33"),s=n("133d"),l=i.each,u=i.curry,h=a();function c(t,e,n){var r=t.currTrigger,a=[t.x,t.y],o=t,h=t.dispatchAction||i.bind(n.dispatchAction,n),c=e.getComponent("axisPointer").coordSysAxesInfo;if(c){b(a)&&(a=s({seriesIndex:o.seriesIndex,dataIndex:o.dataIndex},e).point);var f=b(a),w=o.axesInfo,S=c.axesInfo,M="leave"===r||b(a),T={},A={},C={list:[],map:{}},I={showPointer:u(p,A),showTooltip:u(g,C)};l(c.coordSysMap,(function(t,e){var n=f||t.containPoint(a);l(c.coordSysAxesInfo[e],(function(t,e){var i=t.axis,r=x(w,t);if(!M&&n&&(!w||r)){var o=r&&r.value;null!=o||f||(o=i.pointToData(a)),null!=o&&d(t,o,I,!1,T)}}))}));var k={};return l(S,(function(t,e){var n=t.linkGroup;n&&!A[e]&&l(n.axesInfo,(function(e,i){var r=A[i];if(e!==t&&r){var a=r.value;n.mapper&&(a=t.axis.scale.parse(n.mapper(a,_(e),_(t)))),k[t.key]=a}}))})),l(k,(function(t,e){d(S[e],t,I,!0,T)})),v(A,S,T),m(C,a,t,h),y(S,h,n),T}}function d(t,e,n,r,a){var o=t.axis;if(!o.scale.isBlank()&&o.containData(e))if(t.involveSeries){var s=f(e,t),l=s.payloadBatch,u=s.snapToValue;l[0]&&null==a.seriesIndex&&i.extend(a,l[0]),!r&&t.snap&&o.containData(u)&&null!=u&&(e=u),n.showPointer(t,e,l,a),n.showTooltip(t,s,u)}else n.showPointer(t,e)}function f(t,e){var n=e.axis,i=n.dim,r=t,a=[],o=Number.MAX_VALUE,s=-1;return l(e.seriesModels,(function(e,u){var h,c,d=e.getData().mapDimension(i,!0);if(e.getAxisTooltipData){var f=e.getAxisTooltipData(d,t,n);c=f.dataIndices,h=f.nestestValue}else{if(c=e.getData().indicesOfNearest(d[0],t,"category"===n.type?.5:null),!c.length)return;h=e.getData().get(d[0],c[0])}if(null!=h&&isFinite(h)){var p=t-h,g=Math.abs(p);g<=o&&((g<o||p>=0&&s<0)&&(o=g,s=p,r=h,a.length=0),l(c,(function(t){a.push({seriesIndex:e.seriesIndex,dataIndexInside:t,dataIndex:e.getData().getRawIndex(t)})})))}})),{payloadBatch:a,snapToValue:r}}function p(t,e,n,i){t[e.key]={value:n,payloadBatch:i}}function g(t,e,n,i){var r=n.payloadBatch,a=e.axis,s=a.model,l=e.axisPointerModel;if(e.triggerTooltip&&r.length){var u=e.coordSys.model,h=o.makeKey(u),c=t.map[h];c||(c=t.map[h]={coordSysId:u.id,coordSysIndex:u.componentIndex,coordSysType:u.type,coordSysMainType:u.mainType,dataByAxis:[]},t.list.push(c)),c.dataByAxis.push({axisDim:a.dim,axisIndex:s.componentIndex,axisType:s.type,axisId:s.id,value:i,valueLabelOpt:{precision:l.get("label.precision"),formatter:l.get("label.formatter")},seriesDataIndices:r.slice()})}}function v(t,e,n){var i=n.axesInfo=[];l(e,(function(e,n){var r=e.axisPointerModel.option,a=t[n];a?(!e.useHandle&&(r.status="show"),r.value=a.value,r.seriesDataIndices=(a.payloadBatch||[]).slice()):!e.useHandle&&(r.status="hide"),"show"===r.status&&i.push({axisDim:e.axis.dim,axisIndex:e.axis.model.componentIndex,value:r.value})}))}function m(t,e,n,i){if(!b(e)&&t.list.length){var r=((t.list[0].dataByAxis[0]||{}).seriesDataIndices||[])[0]||{};i({type:"showTip",escapeConnect:!0,x:e[0],y:e[1],tooltipOption:n.tooltipOption,position:n.position,dataIndexInside:r.dataIndexInside,dataIndex:r.dataIndex,seriesIndex:r.seriesIndex,dataByCoordSys:t.list})}else i({type:"hideTip"})}function y(t,e,n){var r=n.getZr(),a="axisPointerLastHighlights",o=h(r)[a]||{},s=h(r)[a]={};l(t,(function(t,e){var n=t.axisPointerModel.option;"show"===n.status&&l(n.seriesDataIndices,(function(t){var e=t.seriesIndex+" | "+t.dataIndex;s[e]=t}))}));var u=[],c=[];i.each(o,(function(t,e){!s[e]&&c.push(t)})),i.each(s,(function(t,e){!o[e]&&u.push(t)})),c.length&&n.dispatchAction({type:"downplay",escapeConnect:!0,batch:c}),u.length&&n.dispatchAction({type:"highlight",escapeConnect:!0,batch:u})}function x(t,e){for(var n=0;n<(t||[]).length;n++){var i=t[n];if(e.axis.dim===i.axisDim&&e.axis.model.componentIndex===i.axisIndex)return i}}function _(t){var e=t.axis.model,n={},i=n.axisDim=t.axis.dim;return n.axisIndex=n[i+"AxisIndex"]=e.componentIndex,n.axisName=n[i+"AxisName"]=e.name,n.axisId=n[i+"AxisId"]=e.id,n}function b(t){return!t||null==t[0]||isNaN(t[0])||null==t[1]||isNaN(t[1])}t.exports=c},ec02:function(t,e,n){var i=n("6d8b"),r=n("84ce"),a=function(t,e,n,i,a){r.call(this,t,e,n),this.type=i||"value",this.position=a||"bottom"};a.prototype={constructor:a,index:0,getAxesOnZeroOf:null,model:null,isHorizontal:function(){var t=this.position;return"top"===t||"bottom"===t},getGlobalExtent:function(t){var e=this.getExtent();return e[0]=this.toGlobalCoord(e[0]),e[1]=this.toGlobalCoord(e[1]),t&&e[0]>e[1]&&e.reverse(),e},getOtherAxis:function(){this.grid.getOtherAxis()},pointToData:function(t,e){return this.coordToData(this.toLocalCoord(t["x"===this.dim?0:1]),e)},toLocalCoord:null,toGlobalCoord:null},i.inherits(a,r);var o=a;t.exports=o},ec34:function(t,e,n){var i=n("4e08"),r=(i.__DEV__,n("6d8b")),a=r.createHashMap,o=r.isString,s=r.isArray,l=r.each,u=(r.assert,n("3041")),h=u.parseXML,c=a(),d={registerMap:function(t,e,n){var i;return s(e)?i=e:e.svg?i=[{type:"svg",source:e.svg,specialAreas:e.specialAreas}]:(e.geoJson&&!e.features&&(n=e.specialAreas,e=e.geoJson),i=[{type:"geoJSON",source:e,specialAreas:n}]),l(i,(function(t){var e=t.type;"geoJson"===e&&(e=t.type="geoJSON");var n=f[e];n(t)})),c.set(t,i)},retrieveMap:function(t){return c.get(t)}},f={geoJSON:function(t){var e=t.source;t.geoJSON=o(e)?"undefined"!==typeof JSON&&JSON.parse?JSON.parse(e):new Function("return ("+e+");")():e},svg:function(t){t.svgXML=h(t.source)}};t.exports=d},ec6f:function(t,e,n){var i=n("6d8b"),r=i.createHashMap,a=i.isTypedArray,o=n("625e"),s=o.enableClassCheck,l=n("93d0"),u=l.SOURCE_FORMAT_ORIGINAL,h=l.SERIES_LAYOUT_BY_COLUMN,c=l.SOURCE_FORMAT_UNKNOWN,d=l.SOURCE_FORMAT_TYPED_ARRAY,f=l.SOURCE_FORMAT_KEYED_COLUMNS;function p(t){this.fromDataset=t.fromDataset,this.data=t.data||(t.sourceFormat===f?{}:[]),this.sourceFormat=t.sourceFormat||c,this.seriesLayoutBy=t.seriesLayoutBy||h,this.dimensionsDefine=t.dimensionsDefine,this.encodeDefine=t.encodeDefine&&r(t.encodeDefine),this.startIndex=t.startIndex||0,this.dimensionsDetectCount=t.dimensionsDetectCount}p.seriesDataToSource=function(t){return new p({data:t,sourceFormat:a(t)?d:u,fromDataset:!1})},s(p);var g=p;t.exports=g},ed21:function(t,e,n){var i=n("2cf4"),r=i.devicePixelRatio,a=n("6d8b"),o=n("4942"),s=n("9850"),l=n("04f6"),u=n("5e68"),h=n("98b7"),c=n("0da8"),d=n("22d1"),f=1e5,p=314159,g=.01,v=.001;function m(t){return parseInt(t,10)}function y(t){return!!t&&(!!t.__builtin__||"function"===typeof t.resize&&"function"===typeof t.refresh)}var x=new s(0,0,0,0),_=new s(0,0,0,0);function b(t,e,n){return x.copy(t.getBoundingRect()),t.transform&&x.applyTransform(t.transform),_.width=e,_.height=n,!x.intersect(_)}function w(t,e){if(t===e)return!1;if(!t||!e||t.length!==e.length)return!0;for(var n=0;n<t.length;n++)if(t[n]!==e[n])return!0}function S(t,e){for(var n=0;n<t.length;n++){var i=t[n];i.setTransform(e),e.beginPath(),i.buildPath(e,i.shape),e.clip(),i.restoreTransform(e)}}function M(t,e){var n=document.createElement("div");return n.style.cssText=["position:relative","overflow:hidden","width:"+t+"px","height:"+e+"px","padding:0","margin:0","border-width:0"].join(";")+";",n}var T=function(t,e,n){this.type="canvas";var i=!t.nodeName||"CANVAS"===t.nodeName.toUpperCase();this._opts=n=a.extend({},n||{}),this.dpr=n.devicePixelRatio||r,this._singleCanvas=i,this.root=t;var o=t.style;o&&(o["-webkit-tap-highlight-color"]="transparent",o["-webkit-user-select"]=o["user-select"]=o["-webkit-touch-callout"]="none",t.innerHTML=""),this.storage=e;var s=this._zlevelList=[],l=this._layers={};if(this._layerConfig={},this._needsManuallyCompositing=!1,i){var h=t.width,c=t.height;null!=n.width&&(h=n.width),null!=n.height&&(c=n.height),this.dpr=n.devicePixelRatio||1,t.width=h*this.dpr,t.height=c*this.dpr,this._width=h,this._height=c;var d=new u(t,this,this.dpr);d.__builtin__=!0,d.initContext(),l[p]=d,d.zlevel=p,s.push(p),this._domRoot=t}else{this._width=this._getSize(0),this._height=this._getSize(1);var f=this._domRoot=M(this._width,this._height);t.appendChild(f)}this._hoverlayer=null,this._hoverElements=[]};T.prototype={constructor:T,getType:function(){return"canvas"},isSingleCanvas:function(){return this._singleCanvas},getViewportRoot:function(){return this._domRoot},getViewportRootOffset:function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},refresh:function(t){var e=this.storage.getDisplayList(!0),n=this._zlevelList;this._redrawId=Math.random(),this._paintList(e,t,this._redrawId);for(var i=0;i<n.length;i++){var r=n[i],a=this._layers[r];if(!a.__builtin__&&a.refresh){var o=0===i?this._backgroundColor:null;a.refresh(o)}}return this.refreshHover(),this},addHover:function(t,e){if(!t.__hoverMir){var n=new t.constructor({style:t.style,shape:t.shape,z:t.z,z2:t.z2,silent:t.silent});return n.__from=t,t.__hoverMir=n,e&&n.setStyle(e),this._hoverElements.push(n),n}},removeHover:function(t){var e=t.__hoverMir,n=this._hoverElements,i=a.indexOf(n,e);i>=0&&n.splice(i,1),t.__hoverMir=null},clearHover:function(t){for(var e=this._hoverElements,n=0;n<e.length;n++){var i=e[n].__from;i&&(i.__hoverMir=null)}e.length=0},refreshHover:function(){var t=this._hoverElements,e=t.length,n=this._hoverlayer;if(n&&n.clear(),e){l(t,this.storage.displayableSortFunc),n||(n=this._hoverlayer=this.getLayer(f));var i={};n.ctx.save();for(var r=0;r<e;){var a=t[r],o=a.__from;o&&o.__zr?(r++,o.invisible||(a.transform=o.transform,a.invTransform=o.invTransform,a.__clipPaths=o.__clipPaths,this._doPaintEl(a,n,!0,i))):(t.splice(r,1),o.__hoverMir=null,e--)}n.ctx.restore()}},getHoverLayer:function(){return this.getLayer(f)},_paintList:function(t,e,n){if(this._redrawId===n){e=e||!1,this._updateLayerStatus(t);var i=this._doPaintList(t,e);if(this._needsManuallyCompositing&&this._compositeManually(),!i){var r=this;h((function(){r._paintList(t,e,n)}))}}},_compositeManually:function(){var t=this.getLayer(p).ctx,e=this._domRoot.width,n=this._domRoot.height;t.clearRect(0,0,e,n),this.eachBuiltinLayer((function(i){i.virtual&&t.drawImage(i.dom,0,0,e,n)}))},_doPaintList:function(t,e){for(var n=[],i=0;i<this._zlevelList.length;i++){var r=this._zlevelList[i],o=this._layers[r];o.__builtin__&&o!==this._hoverlayer&&(o.__dirty||e)&&n.push(o)}for(var s=!0,l=0;l<n.length;l++){o=n[l];var u=o.ctx,h={};u.save();var c=e?o.__startIndex:o.__drawIndex,f=!e&&o.incremental&&Date.now,p=f&&Date.now(),g=o.zlevel===this._zlevelList[0]?this._backgroundColor:null;if(o.__startIndex===o.__endIndex)o.clear(!1,g);else if(c===o.__startIndex){var v=t[c];v.incremental&&v.notClear&&!e||o.clear(!1,g)}-1===c&&(console.error("For some unknown reason. drawIndex is -1"),c=o.__startIndex);for(var m=c;m<o.__endIndex;m++){var y=t[m];if(this._doPaintEl(y,o,e,h),y.__dirty=y.__dirtyText=!1,f){var x=Date.now()-p;if(x>15)break}}o.__drawIndex=m,o.__drawIndex<o.__endIndex&&(s=!1),h.prevElClipPaths&&u.restore(),u.restore()}return d.wxa&&a.each(this._layers,(function(t){t&&t.ctx&&t.ctx.draw&&t.ctx.draw()})),s},_doPaintEl:function(t,e,n,i){var r=e.ctx,a=t.transform;if((e.__dirty||n)&&!t.invisible&&0!==t.style.opacity&&(!a||a[0]||a[3])&&(!t.culling||!b(t,this._width,this._height))){var o=t.__clipPaths;i.prevElClipPaths&&!w(o,i.prevElClipPaths)||(i.prevElClipPaths&&(e.ctx.restore(),i.prevElClipPaths=null,i.prevEl=null),o&&(r.save(),S(o,r),i.prevElClipPaths=o)),t.beforeBrush&&t.beforeBrush(r),t.brush(r,i.prevEl||null),i.prevEl=t,t.afterBrush&&t.afterBrush(r)}},getLayer:function(t,e){this._singleCanvas&&!this._needsManuallyCompositing&&(t=p);var n=this._layers[t];return n||(n=new u("zr_"+t,this,this.dpr),n.zlevel=t,n.__builtin__=!0,this._layerConfig[t]&&a.merge(n,this._layerConfig[t],!0),e&&(n.virtual=e),this.insertLayer(t,n),n.initContext()),n},insertLayer:function(t,e){var n=this._layers,i=this._zlevelList,r=i.length,a=null,s=-1,l=this._domRoot;if(n[t])o("ZLevel "+t+" has been used already");else if(y(e)){if(r>0&&t>i[0]){for(s=0;s<r-1;s++)if(i[s]<t&&i[s+1]>t)break;a=n[i[s]]}if(i.splice(s+1,0,t),n[t]=e,!e.virtual)if(a){var u=a.dom;u.nextSibling?l.insertBefore(e.dom,u.nextSibling):l.appendChild(e.dom)}else l.firstChild?l.insertBefore(e.dom,l.firstChild):l.appendChild(e.dom)}else o("Layer of zlevel "+t+" is not valid")},eachLayer:function(t,e){var n,i,r=this._zlevelList;for(i=0;i<r.length;i++)n=r[i],t.call(e,this._layers[n],n)},eachBuiltinLayer:function(t,e){var n,i,r,a=this._zlevelList;for(r=0;r<a.length;r++)i=a[r],n=this._layers[i],n.__builtin__&&t.call(e,n,i)},eachOtherLayer:function(t,e){var n,i,r,a=this._zlevelList;for(r=0;r<a.length;r++)i=a[r],n=this._layers[i],n.__builtin__||t.call(e,n,i)},getLayers:function(){return this._layers},_updateLayerStatus:function(t){function e(t){r&&(r.__endIndex!==t&&(r.__dirty=!0),r.__endIndex=t)}if(this.eachBuiltinLayer((function(t,e){t.__dirty=t.__used=!1})),this._singleCanvas)for(var n=1;n<t.length;n++){var i=t[n];if(i.zlevel!==t[n-1].zlevel||i.incremental){this._needsManuallyCompositing=!0;break}}var r=null,a=0;for(n=0;n<t.length;n++){i=t[n];var s,l=i.zlevel;i.incremental?(s=this.getLayer(l+v,this._needsManuallyCompositing),s.incremental=!0,a=1):s=this.getLayer(l+(a>0?g:0),this._needsManuallyCompositing),s.__builtin__||o("ZLevel "+l+" has been used by unkown layer "+s.id),s!==r&&(s.__used=!0,s.__startIndex!==n&&(s.__dirty=!0),s.__startIndex=n,s.incremental?s.__drawIndex=-1:s.__drawIndex=n,e(n),r=s),i.__dirty&&(s.__dirty=!0,s.incremental&&s.__drawIndex<0&&(s.__drawIndex=n))}e(n),this.eachBuiltinLayer((function(t,e){!t.__used&&t.getElementCount()>0&&(t.__dirty=!0,t.__startIndex=t.__endIndex=t.__drawIndex=0),t.__dirty&&t.__drawIndex<0&&(t.__drawIndex=t.__startIndex)}))},clear:function(){return this.eachBuiltinLayer(this._clearLayer),this},_clearLayer:function(t){t.clear()},setBackgroundColor:function(t){this._backgroundColor=t},configLayer:function(t,e){if(e){var n=this._layerConfig;n[t]?a.merge(n[t],e,!0):n[t]=e;for(var i=0;i<this._zlevelList.length;i++){var r=this._zlevelList[i];if(r===t||r===t+g){var o=this._layers[r];a.merge(o,n[t],!0)}}}},delLayer:function(t){var e=this._layers,n=this._zlevelList,i=e[t];i&&(i.dom.parentNode.removeChild(i.dom),delete e[t],n.splice(a.indexOf(n,t),1))},resize:function(t,e){if(this._domRoot.style){var n=this._domRoot;n.style.display="none";var i=this._opts;if(null!=t&&(i.width=t),null!=e&&(i.height=e),t=this._getSize(0),e=this._getSize(1),n.style.display="",this._width!==t||e!==this._height){for(var r in n.style.width=t+"px",n.style.height=e+"px",this._layers)this._layers.hasOwnProperty(r)&&this._layers[r].resize(t,e);a.each(this._progressiveLayers,(function(n){n.resize(t,e)})),this.refresh(!0)}this._width=t,this._height=e}else{if(null==t||null==e)return;this._width=t,this._height=e,this.getLayer(p).resize(t,e)}return this},clearLayer:function(t){var e=this._layers[t];e&&e.clear()},dispose:function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},getRenderedCanvas:function(t){if(t=t||{},this._singleCanvas&&!this._compositeManually)return this._layers[p].dom;var e=new u("image",this,t.pixelRatio||this.dpr);if(e.initContext(),e.clear(!1,t.backgroundColor||this._backgroundColor),t.pixelRatio<=this.dpr){this.refresh();var n=e.dom.width,i=e.dom.height,r=e.ctx;this.eachLayer((function(t){t.__builtin__?r.drawImage(t.dom,0,0,n,i):t.renderToCanvas&&(e.ctx.save(),t.renderToCanvas(e.ctx),e.ctx.restore())}))}else for(var a={},o=this.storage.getDisplayList(!0),s=0;s<o.length;s++){var l=o[s];this._doPaintEl(l,e,!0,a)}return e.dom},getWidth:function(){return this._width},getHeight:function(){return this._height},_getSize:function(t){var e=this._opts,n=["width","height"][t],i=["clientWidth","clientHeight"][t],r=["paddingLeft","paddingTop"][t],a=["paddingRight","paddingBottom"][t];if(null!=e[n]&&"auto"!==e[n])return parseFloat(e[n]);var o=this.root,s=document.defaultView.getComputedStyle(o);return(o[i]||m(s[n])||m(o.style[n]))-(m(s[r])||0)-(m(s[a])||0)|0},pathToImage:function(t,e){e=e||this.dpr;var n=document.createElement("canvas"),i=n.getContext("2d"),r=t.getBoundingRect(),a=t.style,o=a.shadowBlur*e,s=a.shadowOffsetX*e,l=a.shadowOffsetY*e,u=a.hasStroke()?a.lineWidth:0,h=Math.max(u/2,-s+o),d=Math.max(u/2,s+o),f=Math.max(u/2,-l+o),p=Math.max(u/2,l+o),g=r.width+h+d,v=r.height+f+p;n.width=g*e,n.height=v*e,i.scale(e,e),i.clearRect(0,0,g,v),i.dpr=e;var m={position:t.position,rotation:t.rotation,scale:t.scale};t.position=[h-r.x,f-r.y],t.rotation=0,t.scale=[1,1],t.updateTransform(),t&&t.brush(i);var y=c,x=new y({style:{x:0,y:0,image:n}});return null!=m.position&&(x.position=t.position=m.position),null!=m.rotation&&(x.rotation=t.rotation=m.rotation),null!=m.scale&&(x.scale=t.scale=m.scale),x}};var A=T;t.exports=A},eda2:function(t,e,n){var i=n("6d8b"),r=n("e86a"),a=n("3842");function o(t){return isNaN(t)?"-":(t=(t+"").split("."),t[0].replace(/(\d{1,3})(?=(?:\d{3})+(?!\d))/g,"$1,")+(t.length>1?"."+t[1]:""))}function s(t,e){return t=(t||"").toLowerCase().replace(/-(.)/g,(function(t,e){return e.toUpperCase()})),e&&t&&(t=t.charAt(0).toUpperCase()+t.slice(1)),t}var l=i.normalizeCssArray,u=/([&<>"'])/g,h={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};function c(t){return null==t?"":(t+"").replace(u,(function(t,e){return h[e]}))}var d=["a","b","c","d","e","f","g"],f=function(t,e){return"{"+t+(null==e?"":e)+"}"};function p(t,e,n){i.isArray(e)||(e=[e]);var r=e.length;if(!r)return"";for(var a=e[0].$vars||[],o=0;o<a.length;o++){var s=d[o];t=t.replace(f(s),f(s,0))}for(var l=0;l<r;l++)for(var u=0;u<a.length;u++){var h=e[l][a[u]];t=t.replace(f(d[u],l),n?c(h):h)}return t}function g(t,e,n){return i.each(e,(function(e,i){t=t.replace("{"+i+"}",n?c(e):e)})),t}function v(t,e){t=i.isString(t)?{color:t,extraCssText:e}:t||{};var n=t.color,r=t.type,a=(e=t.extraCssText,t.renderMode||"html"),o=t.markerId||"X";return n?"html"===a?"subItem"===r?'<span style="display:inline-block;vertical-align:middle;margin-right:8px;margin-left:3px;border-radius:4px;width:4px;height:4px;background-color:'+c(n)+";"+(e||"")+'"></span>':'<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:'+c(n)+";"+(e||"")+'"></span>':{renderMode:a,content:"{marker"+o+"|}  ",style:{color:n}}:""}function m(t,e){return t+="","0000".substr(0,e-t.length)+t}function y(t,e,n){"week"!==t&&"month"!==t&&"quarter"!==t&&"half-year"!==t&&"year"!==t||(t="MM-dd\nyyyy");var i=a.parseDate(e),r=n?"UTC":"",o=i["get"+r+"FullYear"](),s=i["get"+r+"Month"]()+1,l=i["get"+r+"Date"](),u=i["get"+r+"Hours"](),h=i["get"+r+"Minutes"](),c=i["get"+r+"Seconds"](),d=i["get"+r+"Milliseconds"]();return t=t.replace("MM",m(s,2)).replace("M",s).replace("yyyy",o).replace("yy",o%100).replace("dd",m(l,2)).replace("d",l).replace("hh",m(u,2)).replace("h",u).replace("mm",m(h,2)).replace("m",h).replace("ss",m(c,2)).replace("s",c).replace("SSS",m(d,3)),t}function x(t){return t?t.charAt(0).toUpperCase()+t.substr(1):t}var _=r.truncateText;function b(t){return r.getBoundingRect(t.text,t.font,t.textAlign,t.textVerticalAlign,t.textPadding,t.textLineHeight,t.rich,t.truncate)}function w(t,e,n,i,a,o,s,l){return r.getBoundingRect(t,e,n,i,a,l,o,s)}e.addCommas=o,e.toCamelCase=s,e.normalizeCssArray=l,e.encodeHTML=c,e.formatTpl=p,e.formatTplSimple=g,e.getTooltipMarker=v,e.formatTime=y,e.capitalFirst=x,e.truncateText=_,e.getTextBoundingRect=b,e.getTextRect=w},ee1a:function(t,e,n){var i=n("6d8b"),r=i.each,a=i.isString;function o(t,e,n){n=n||{};var i,o,s,l,u=n.byIndex,h=n.stackedCoordDimension,c=!(!t||!t.get("stack"));if(r(e,(function(t,n){a(t)&&(e[n]=t={name:t}),c&&!t.isExtraCoord&&(u||i||!t.ordinalMeta||(i=t),o||"ordinal"===t.type||"time"===t.type||h&&h!==t.coordDim||(o=t))})),!o||u||i||(u=!0),o){s="__\0ecstackresult",l="__\0ecstackedover",i&&(i.createInvertedIndices=!0);var d=o.coordDim,f=o.type,p=0;r(e,(function(t){t.coordDim===d&&p++})),e.push({name:s,coordDim:d,coordDimIndex:p,type:f,isExtraCoord:!0,isCalculationCoord:!0}),p++,e.push({name:l,coordDim:l,coordDimIndex:p,type:f,isExtraCoord:!0,isCalculationCoord:!0})}return{stackedDimension:o&&o.name,stackedByDimension:i&&i.name,isStackedByIndex:u,stackedOverDimension:l,stackResultDimension:s}}function s(t,e){return!!e&&e===t.getCalculationInfo("stackedDimension")}function l(t,e){return s(t,e)?t.getCalculationInfo("stackResultDimension"):e}e.enableDataStack=o,e.isDimensionStacked=s,e.getStackedDimension=l},ee84:function(t,e,n){var i=n("20c8"),r=n("401b"),a=r.applyTransform,o=i.CMD,s=[[],[],[]],l=Math.sqrt,u=Math.atan2;function h(t,e){var n,i,r,h,c,d=t.data,f=o.M,p=o.C,g=o.L,v=o.R,m=o.A,y=o.Q;for(r=0,h=0;r<d.length;){switch(n=d[r++],h=r,i=0,n){case f:i=1;break;case g:i=1;break;case p:i=3;break;case y:i=2;break;case m:var x=e[4],_=e[5],b=l(e[0]*e[0]+e[1]*e[1]),w=l(e[2]*e[2]+e[3]*e[3]),S=u(-e[1]/w,e[0]/b);d[r]*=b,d[r++]+=x,d[r]*=w,d[r++]+=_,d[r++]*=b,d[r++]*=w,d[r++]+=S,d[r++]+=S,r+=2,h=r;break;case v:M[0]=d[r++],M[1]=d[r++],a(M,M,e),d[h++]=M[0],d[h++]=M[1],M[0]+=d[r++],M[1]+=d[r++],a(M,M,e),d[h++]=M[0],d[h++]=M[1]}for(c=0;c<i;c++){var M=s[c];M[0]=d[r++],M[1]=d[r++],a(M,M,e),d[h++]=M[0],d[h++]=M[1]}}}t.exports=h},ef97:function(t,e,n){var i=n("3eba");n("217b8"),n("f17f");var r=n("7f96"),a=n("87c3"),o=n("fdde");n("01ed"),i.registerVisual(r("line","circle","line")),i.registerLayout(a("line")),i.registerProcessor(i.PRIORITY.PROCESSOR.STATISTIC,o("line"))},f123:function(t,e,n){var i=n("9f82"),r=i.prepareDataCoordInfo,a=i.getStackedOnPoint;function o(t,e){var n=[];return e.diff(t).add((function(t){n.push({cmd:"+",idx:t})})).update((function(t,e){n.push({cmd:"=",idx:e,idx1:t})})).remove((function(t){n.push({cmd:"-",idx:t})})).execute(),n}function s(t,e,n,i,s,l,u,h){for(var c=o(t,e),d=[],f=[],p=[],g=[],v=[],m=[],y=[],x=r(s,e,u),_=r(l,t,h),b=0;b<c.length;b++){var w=c[b],S=!0;switch(w.cmd){case"=":var M=t.getItemLayout(w.idx),T=e.getItemLayout(w.idx1);(isNaN(M[0])||isNaN(M[1]))&&(M=T.slice()),d.push(M),f.push(T),p.push(n[w.idx]),g.push(i[w.idx1]),y.push(e.getRawIndex(w.idx1));break;case"+":var A=w.idx;d.push(s.dataToPoint([e.get(x.dataDimsForPoint[0],A),e.get(x.dataDimsForPoint[1],A)])),f.push(e.getItemLayout(A).slice()),p.push(a(x,s,e,A)),g.push(i[A]),y.push(e.getRawIndex(A));break;case"-":A=w.idx;var C=t.getRawIndex(A);C!==A?(d.push(t.getItemLayout(A)),f.push(l.dataToPoint([t.get(_.dataDimsForPoint[0],A),t.get(_.dataDimsForPoint[1],A)])),p.push(n[A]),g.push(a(_,l,t,A)),y.push(C)):S=!1}S&&(v.push(w),m.push(m.length))}m.sort((function(t,e){return y[t]-y[e]}));var I=[],k=[],D=[],P=[],O=[];for(b=0;b<m.length;b++){A=m[b];I[b]=d[A],k[b]=f[A],D[b]=p[A],P[b]=g[A],O[b]=v[A]}return{current:I,next:k,stackedOnCurrent:D,stackedOnNext:P,status:O}}t.exports=s},f17f:function(t,e,n){var i=n("4e08"),r=(i.__DEV__,n("6d8b")),a=n("f706"),o=n("1418"),s=n("f123"),l=n("2306"),u=n("e0d3"),h=n("d4d1"),c=h.Polyline,d=h.Polygon,f=n("e887"),p=n("3842"),g=p.round,v=n("9f82"),m=v.prepareDataCoordInfo,y=v.getStackedOnPoint;function x(t,e){if(t.length===e.length){for(var n=0;n<t.length;n++){var i=t[n],r=e[n];if(i[0]!==r[0]||i[1]!==r[1])return}return!0}}function _(t){return"number"===typeof t?t:t?.5:0}function b(t){var e=t.getGlobalExtent();if(t.onBand){var n=t.getBandWidth()/2-1,i=e[1]>e[0]?1:-1;e[0]+=i*n,e[1]-=i*n}return e}function w(t,e,n){if(!n.valueDim)return[];for(var i=[],r=0,a=e.count();r<a;r++)i.push(y(n,t,e,r));return i}function S(t,e,n,i){var r=b(t.getAxis("x")),a=b(t.getAxis("y")),o=t.getBaseAxis().isHorizontal(),s=Math.min(r[0],r[1]),u=Math.min(a[0],a[1]),h=Math.max(r[0],r[1])-s,c=Math.max(a[0],a[1])-u;if(n)s-=.5,h+=.5,u-=.5,c+=.5;else{var d=i.get("lineStyle.width")||2,f=i.get("clipOverflow")?d/2:Math.max(h,c);o?(u-=f,c+=2*f):(s-=f,h+=2*f)}var p=new l.Rect({shape:{x:s,y:u,width:h,height:c}});return e&&(p.shape[o?"width":"height"]=0,l.initProps(p,{shape:{width:h,height:c}},i)),p}function M(t,e,n,i){var r=t.getAngleAxis(),a=t.getRadiusAxis(),o=a.getExtent().slice();o[0]>o[1]&&o.reverse();var s=r.getExtent(),u=Math.PI/180;n&&(o[0]-=.5,o[1]+=.5);var h=new l.Sector({shape:{cx:g(t.cx,1),cy:g(t.cy,1),r0:g(o[0],1),r:g(o[1],1),startAngle:-s[0]*u,endAngle:-s[1]*u,clockwise:r.inverse}});return e&&(h.shape.endAngle=-s[0]*u,l.initProps(h,{shape:{endAngle:-s[1]*u}},i)),h}function T(t,e,n,i){return"polar"===t.type?M(t,e,n,i):S(t,e,n,i)}function A(t,e,n){for(var i=e.getBaseAxis(),r="x"===i.dim||"radius"===i.dim?0:1,a=[],o=0;o<t.length-1;o++){var s=t[o+1],l=t[o];a.push(l);var u=[];switch(n){case"end":u[r]=s[r],u[1-r]=l[1-r],a.push(u);break;case"middle":var h=(l[r]+s[r])/2,c=[];u[r]=c[r]=h,u[1-r]=l[1-r],c[1-r]=s[1-r],a.push(u),a.push(c);break;default:u[r]=l[r],u[1-r]=s[1-r],a.push(u)}}return t[o]&&a.push(t[o]),a}function C(t,e){var n=t.getVisual("visualMeta");if(n&&n.length&&t.count()&&"cartesian2d"===e.type){for(var i,a,o=n.length-1;o>=0;o--){var s=n[o].dimension,u=t.dimensions[s],h=t.getDimensionInfo(u);if(i=h&&h.coordDim,"x"===i||"y"===i){a=n[o];break}}if(a){var c=e.getAxis(i),d=r.map(a.stops,(function(t){return{coord:c.toGlobalCoord(c.dataToCoord(t.value)),color:t.color}})),f=d.length,p=a.outerColors.slice();f&&d[0].coord>d[f-1].coord&&(d.reverse(),p.reverse());var g=10,v=d[0].coord-g,m=d[f-1].coord+g,y=m-v;if(y<.001)return"transparent";r.each(d,(function(t){t.offset=(t.coord-v)/y})),d.push({offset:f?d[f-1].offset:.5,color:p[1]||"transparent"}),d.unshift({offset:f?d[0].offset:.5,color:p[0]||"transparent"});var x=new l.LinearGradient(0,0,0,0,d,!0);return x[i]=v,x[i+"2"]=m,x}}}function I(t,e,n){var i=t.get("showAllSymbol"),a="auto"===i;if(!i||a){var o=n.getAxesByScale("ordinal")[0];if(o&&(!a||!k(o,e))){var s=e.mapDimension(o.dim),l={};return r.each(o.getViewLabels(),(function(t){l[t.tickValue]=1})),function(t){return!l.hasOwnProperty(e.get(s,t))}}}}function k(t,e){var n=t.getExtent(),i=Math.abs(n[1]-n[0])/t.scale.count();isNaN(i)&&(i=0);for(var r=e.count(),a=Math.max(1,Math.round(r/5)),s=0;s<r;s+=a)if(1.5*o.getSymbolSize(e,s)[t.isHorizontal()?1:0]>i)return!1;return!0}var D=f.extend({type:"line",init:function(){var t=new l.Group,e=new a;this.group.add(e.group),this._symbolDraw=e,this._lineGroup=t},render:function(t,e,n){var i=t.coordinateSystem,a=this.group,o=t.getData(),s=t.getModel("lineStyle"),l=t.getModel("areaStyle"),u=o.mapArray(o.getItemLayout),h="polar"===i.type,c=this._coordSys,d=this._symbolDraw,f=this._polyline,p=this._polygon,g=this._lineGroup,v=t.get("animation"),y=!l.isEmpty(),b=l.get("origin"),S=m(i,o,b),M=w(i,o,S),k=t.get("showSymbol"),D=k&&!h&&I(t,o,i),P=this._data;P&&P.eachItemGraphicEl((function(t,e){t.__temp&&(a.remove(t),P.setItemGraphicEl(e,null))})),k||d.remove(),a.add(g);var O=!h&&t.get("step");f&&c.type===i.type&&O===this._step?(y&&!p?p=this._newPolygon(u,M,i,v):p&&!y&&(g.remove(p),p=this._polygon=null),g.setClipPath(T(i,!1,!1,t)),k&&d.updateData(o,{isIgnore:D,clipShape:T(i,!1,!0,t)}),o.eachItemGraphicEl((function(t){t.stopAnimation(!0)})),x(this._stackedOnPoints,M)&&x(this._points,u)||(v?this._updateAnimation(o,M,i,n,O,b):(O&&(u=A(u,i,O),M=A(M,i,O)),f.setShape({points:u}),p&&p.setShape({points:u,stackedOnPoints:M})))):(k&&d.updateData(o,{isIgnore:D,clipShape:T(i,!1,!0,t)}),O&&(u=A(u,i,O),M=A(M,i,O)),f=this._newPolyline(u,i,v),y&&(p=this._newPolygon(u,M,i,v)),g.setClipPath(T(i,!0,!1,t)));var L=C(o,i)||o.getVisual("color");f.useStyle(r.defaults(s.getLineStyle(),{fill:"none",stroke:L,lineJoin:"bevel"}));var E=t.get("smooth");if(E=_(t.get("smooth")),f.setShape({smooth:E,smoothMonotone:t.get("smoothMonotone"),connectNulls:t.get("connectNulls")}),p){var R=o.getCalculationInfo("stackedOnSeries"),B=0;p.useStyle(r.defaults(l.getAreaStyle(),{fill:L,opacity:.7,lineJoin:"bevel"})),R&&(B=_(R.get("smooth"))),p.setShape({smooth:E,stackedOnSmooth:B,smoothMonotone:t.get("smoothMonotone"),connectNulls:t.get("connectNulls")})}this._data=o,this._coordSys=i,this._stackedOnPoints=M,this._points=u,this._step=O,this._valueOrigin=b},dispose:function(){},highlight:function(t,e,n,i){var r=t.getData(),a=u.queryDataIndex(r,i);if(!(a instanceof Array)&&null!=a&&a>=0){var s=r.getItemGraphicEl(a);if(!s){var l=r.getItemLayout(a);if(!l)return;s=new o(r,a),s.position=l,s.setZ(t.get("zlevel"),t.get("z")),s.ignore=isNaN(l[0])||isNaN(l[1]),s.__temp=!0,r.setItemGraphicEl(a,s),s.stopSymbolAnimation(!0),this.group.add(s)}s.highlight()}else f.prototype.highlight.call(this,t,e,n,i)},downplay:function(t,e,n,i){var r=t.getData(),a=u.queryDataIndex(r,i);if(null!=a&&a>=0){var o=r.getItemGraphicEl(a);o&&(o.__temp?(r.setItemGraphicEl(a,null),this.group.remove(o)):o.downplay())}else f.prototype.downplay.call(this,t,e,n,i)},_newPolyline:function(t){var e=this._polyline;return e&&this._lineGroup.remove(e),e=new c({shape:{points:t},silent:!0,z2:10}),this._lineGroup.add(e),this._polyline=e,e},_newPolygon:function(t,e){var n=this._polygon;return n&&this._lineGroup.remove(n),n=new d({shape:{points:t,stackedOnPoints:e},silent:!0}),this._lineGroup.add(n),this._polygon=n,n},_updateAnimation:function(t,e,n,i,r,a){var o=this._polyline,u=this._polygon,h=t.hostModel,c=s(this._data,t,this._stackedOnPoints,e,this._coordSys,n,this._valueOrigin,a),d=c.current,f=c.stackedOnCurrent,p=c.next,g=c.stackedOnNext;r&&(d=A(c.current,n,r),f=A(c.stackedOnCurrent,n,r),p=A(c.next,n,r),g=A(c.stackedOnNext,n,r)),o.shape.__points=c.current,o.shape.points=d,l.updateProps(o,{shape:{points:p}},h),u&&(u.setShape({points:d,stackedOnPoints:f}),l.updateProps(u,{shape:{points:p,stackedOnPoints:g}},h));for(var v=[],m=c.status,y=0;y<m.length;y++){var x=m[y].cmd;if("="===x){var _=t.getItemGraphicEl(m[y].idx1);_&&v.push({el:_,ptIdx:y})}}o.animators&&o.animators.length&&o.animators[0].during((function(){for(var t=0;t<v.length;t++){var e=v[t].el;e.attr("position",o.shape.__points[v[t].ptIdx])}}))},remove:function(t){var e=this.group,n=this._data;this._lineGroup.removeAll(),this._symbolDraw.remove(!0),n&&n.eachItemGraphicEl((function(t,i){t.__temp&&(e.remove(t),n.setItemGraphicEl(i,null))})),this._polyline=this._polygon=this._coordSys=this._points=this._stackedOnPoints=this._data=null}});t.exports=D},f219:function(t,e){var n="#eee",i=function(){return{axisLine:{lineStyle:{color:n}},axisTick:{lineStyle:{color:n}},axisLabel:{textStyle:{color:n}},splitLine:{lineStyle:{type:"dashed",color:"#aaa"}},splitArea:{areaStyle:{color:n}}}},r=["#dd6b66","#759aa0","#e69d87","#8dc1a9","#ea7e53","#eedd78","#73a373","#73b9bc","#7289ab","#91ca8c","#f49f42"],a={color:r,backgroundColor:"#333",tooltip:{axisPointer:{lineStyle:{color:n},crossStyle:{color:n}}},legend:{textStyle:{color:n}},textStyle:{color:n},title:{textStyle:{color:n}},toolbox:{iconStyle:{normal:{borderColor:n}}},dataZoom:{textStyle:{color:n}},visualMap:{textStyle:{color:n}},timeline:{lineStyle:{color:n},itemStyle:{normal:{color:r[1]}},label:{normal:{textStyle:{color:n}}},controlStyle:{normal:{color:n,borderColor:n}}},timeAxis:i(),logAxis:i(),valueAxis:i(),categoryAxis:i(),line:{symbol:"circle"},graph:{color:r},gauge:{title:{textStyle:{color:n}}},candlestick:{itemStyle:{normal:{color:"#FD1050",color0:"#0CF49B",borderColor:"#FD1050",borderColor0:"#0CF49B"}}}};a.categoryAxis.splitLine.show=!1;var o=a;t.exports=o},f273:function(t,e,n){var i=n("6d8b"),r=n("2306"),a=n("fab22"),o=n("6679"),s=n("0156"),l=["axisLine","axisTickLabel","axisName"],u=["splitArea","splitLine"],h=o.extend({type:"cartesianAxis",axisPointerClass:"CartesianAxisPointer",render:function(t,e,n,o){this.group.removeAll();var c=this._axisGroup;if(this._axisGroup=new r.Group,this.group.add(this._axisGroup),t.get("show")){var d=t.getCoordSysModel(),f=s.layout(d,t),p=new a(t,f);i.each(l,p.add,p),this._axisGroup.add(p.getGroup()),i.each(u,(function(e){t.get(e+".show")&&this["_"+e](t,d)}),this),r.groupTransition(c,this._axisGroup,t),h.superCall(this,"render",t,e,n,o)}},remove:function(){this._splitAreaColors=null},_splitLine:function(t,e){var n=t.axis;if(!n.scale.isBlank()){var a=t.getModel("splitLine"),o=a.getModel("lineStyle"),s=o.get("color");s=i.isArray(s)?s:[s];for(var l=e.coordinateSystem.getRect(),u=n.isHorizontal(),h=0,c=n.getTicksCoords({tickModel:a}),d=[],f=[],p=o.getLineStyle(),g=0;g<c.length;g++){var v=n.toGlobalCoord(c[g].coord);u?(d[0]=v,d[1]=l.y,f[0]=v,f[1]=l.y+l.height):(d[0]=l.x,d[1]=v,f[0]=l.x+l.width,f[1]=v);var m=h++%s.length,y=c[g].tickValue;this._axisGroup.add(new r.Line(r.subPixelOptimizeLine({anid:null!=y?"line_"+c[g].tickValue:null,shape:{x1:d[0],y1:d[1],x2:f[0],y2:f[1]},style:i.defaults({stroke:s[m]},p),silent:!0})))}}},_splitArea:function(t,e){var n=t.axis;if(!n.scale.isBlank()){var a=t.getModel("splitArea"),o=a.getModel("areaStyle"),s=o.get("color"),l=e.coordinateSystem.getRect(),u=n.getTicksCoords({tickModel:a,clamp:!0});if(u.length){var h=s.length,c=this._splitAreaColors,d=i.createHashMap(),f=0;if(c)for(var p=0;p<u.length;p++){var g=c.get(u[p].tickValue);if(null!=g){f=(g+(h-1)*p)%h;break}}var v=n.toGlobalCoord(u[0].coord),m=o.getAreaStyle();s=i.isArray(s)?s:[s];for(p=1;p<u.length;p++){var y,x,_,b,w=n.toGlobalCoord(u[p].coord);n.isHorizontal()?(y=v,x=l.y,_=w-y,b=l.height,v=y+_):(y=l.x,x=v,_=l.width,b=w-x,v=x+b);var S=u[p-1].tickValue;null!=S&&d.set(S,f),this._axisGroup.add(new r.Rect({anid:null!=S?"area_"+S:null,shape:{x:y,y:x,width:_,height:b},style:i.defaults({fill:s[f]},m),silent:!0})),f=(f+1)%h}this._splitAreaColors=d}}}});h.extend({type:"xAxis"}),h.extend({type:"yAxis"})},f279:function(t,e,n){var i=n("9850"),r=n("e263"),a=n("401b"),o=n("0655");function s(t,e,n){if(this.name=t,this.geometries=e,n)n=[n[0],n[1]];else{var i=this.getBoundingRect();n=[i.x+i.width/2,i.y+i.height/2]}this.center=n}s.prototype={constructor:s,properties:null,getBoundingRect:function(){var t=this._rect;if(t)return t;for(var e=Number.MAX_VALUE,n=[e,e],o=[-e,-e],s=[],l=[],u=this.geometries,h=0;h<u.length;h++)if("polygon"===u[h].type){var c=u[h].exterior;r.fromPoints(c,s,l),a.min(n,n,s),a.max(o,o,l)}return 0===h&&(n[0]=n[1]=o[0]=o[1]=0),this._rect=new i(n[0],n[1],o[0]-n[0],o[1]-n[1])},contain:function(t){var e=this.getBoundingRect(),n=this.geometries;if(!e.contain(t[0],t[1]))return!1;t:for(var i=0,r=n.length;i<r;i++)if("polygon"===n[i].type){var a=n[i].exterior,s=n[i].interiors;if(o.contain(a,t[0],t[1])){for(var l=0;l<(s?s.length:0);l++)if(o.contain(s[l]))continue t;return!0}}return!1},transformTo:function(t,e,n,r){var o=this.getBoundingRect(),s=o.width/o.height;n?r||(r=n/s):n=s*r;for(var l=new i(t,e,n,r),u=o.calculateTransform(l),h=this.geometries,c=0;c<h.length;c++)if("polygon"===h[c].type){for(var d=h[c].exterior,f=h[c].interiors,p=0;p<d.length;p++)a.applyTransform(d[p],d[p],u);for(var g=0;g<(f?f.length:0);g++)for(p=0;p<f[g].length;p++)a.applyTransform(f[g][p],f[g][p],u)}o=this._rect,o.copy(l),this.center=[o.x+o.width/2,o.y+o.height/2]},cloneShallow:function(t){null==t&&(t=this.name);var e=new s(t,this.geometries,this.center);return e._rect=this._rect,e.transformTo=null,e}};var l=s;t.exports=l},f47d:function(t,e,n){var i=n("6d8b"),r=(i.assert,i.isArray),a=n("4e08");a.__DEV__;function o(t){return new s(t)}function s(t){t=t||{},this._reset=t.reset,this._plan=t.plan,this._count=t.count,this._onDirty=t.onDirty,this._dirty=!0,this.context}var l=s.prototype;l.perform=function(t){var e,n=this._upstream,i=t&&t.skip;if(this._dirty&&n){var a=this.context;a.data=a.outputData=n.context.outputData}this.__pipeline&&(this.__pipeline.currentTask=this),this._plan&&!i&&(e=this._plan(this.context));var o,s=f(this._modBy),l=this._modDataCount||0,u=f(t&&t.modBy),d=t&&t.modDataCount||0;function f(t){return!(t>=1)&&(t=1),t}s===u&&l===d||(e="reset"),(this._dirty||"reset"===e)&&(this._dirty=!1,o=c(this,i)),this._modBy=u,this._modDataCount=d;var p=t&&t.step;if(this._dueEnd=n?n._outputDueEnd:this._count?this._count(this.context):1/0,this._progress){var g=this._dueIndex,v=Math.min(null!=p?this._dueIndex+p:1/0,this._dueEnd);if(!i&&(o||g<v)){var m=this._progress;if(r(m))for(var y=0;y<m.length;y++)h(this,m[y],g,v,u,d);else h(this,m,g,v,u,d)}this._dueIndex=v;var x=null!=this._settedOutputEnd?this._settedOutputEnd:v;this._outputDueEnd=x}else this._dueIndex=this._outputDueEnd=null!=this._settedOutputEnd?this._settedOutputEnd:this._dueEnd;return this.unfinished()};var u=function(){var t,e,n,i,r,a={reset:function(l,u,h,c){e=l,t=u,n=h,i=c,r=Math.ceil(i/n),a.next=n>1&&i>0?s:o}};return a;function o(){return e<t?e++:null}function s(){var a=e%r*n+Math.ceil(e/r),o=e>=t?null:a<i?a:e;return e++,o}}();function h(t,e,n,i,r,a){u.reset(n,i,r,a),t._callingProgress=e,t._callingProgress({start:n,end:i,count:i-n,next:u.next},t.context)}function c(t,e){var n,i;t._dueIndex=t._outputDueEnd=t._dueEnd=0,t._settedOutputEnd=null,!e&&t._reset&&(n=t._reset(t.context),n&&n.progress&&(i=n.forceFirstProgress,n=n.progress),r(n)&&!n.length&&(n=null)),t._progress=n,t._modBy=t._modDataCount=null;var a=t._downstream;return a&&a.dirty(),i}l.dirty=function(){this._dirty=!0,this._onDirty&&this._onDirty(this.context)},l.unfinished=function(){return this._progress&&this._dueIndex<this._dueEnd},l.pipe=function(t){(this._downstream!==t||this._dirty)&&(this._downstream=t,t._upstream=this,t.dirty())},l.dispose=function(){this._disposed||(this._upstream&&(this._upstream._downstream=null),this._downstream&&(this._downstream._upstream=null),this._dirty=!1,this._disposed=!0)},l.getUpstream=function(){return this._upstream},l.getDownstream=function(){return this._downstream},l.setOutputEnd=function(t){this._outputDueEnd=this._settedOutputEnd=t},e.createTask=o},f5e6:function(t,e,n){n("1ccf"),n("b419")},f706:function(t,e,n){var i=n("2306"),r=n("1418"),a=n("6d8b"),o=a.isObject;function s(t){this.group=new i.Group,this._symbolCtor=t||r}var l=s.prototype;function u(t,e,n,i){return e&&!isNaN(e[0])&&!isNaN(e[1])&&!(i.isIgnore&&i.isIgnore(n))&&!(i.clipShape&&!i.clipShape.contain(e[0],e[1]))&&"none"!==t.getItemVisual(n,"symbol")}function h(t){return null==t||o(t)||(t={isIgnore:t}),t||{}}function c(t){var e=t.hostModel;return{itemStyle:e.getModel("itemStyle").getItemStyle(["color"]),hoverItemStyle:e.getModel("emphasis.itemStyle").getItemStyle(),symbolRotate:e.get("symbolRotate"),symbolOffset:e.get("symbolOffset"),hoverAnimation:e.get("hoverAnimation"),labelModel:e.getModel("label"),hoverLabelModel:e.getModel("emphasis.label"),cursorStyle:e.get("cursor")}}l.updateData=function(t,e){e=h(e);var n=this.group,r=t.hostModel,a=this._data,o=this._symbolCtor,s=c(t);a||n.removeAll(),t.diff(a).add((function(i){var r=t.getItemLayout(i);if(u(t,r,i,e)){var a=new o(t,i,s);a.attr("position",r),t.setItemGraphicEl(i,a),n.add(a)}})).update((function(l,h){var c=a.getItemGraphicEl(h),d=t.getItemLayout(l);u(t,d,l,e)?(c?(c.updateData(t,l,s),i.updateProps(c,{position:d},r)):(c=new o(t,l),c.attr("position",d)),n.add(c),t.setItemGraphicEl(l,c)):n.remove(c)})).remove((function(t){var e=a.getItemGraphicEl(t);e&&e.fadeOut((function(){n.remove(e)}))})).execute(),this._data=t},l.isPersistent=function(){return!0},l.updateLayout=function(){var t=this._data;t&&t.eachItemGraphicEl((function(e,n){var i=t.getItemLayout(n);e.attr("position",i)}))},l.incrementalPrepareUpdate=function(t){this._seriesScope=c(t),this._data=null,this.group.removeAll()},l.incrementalUpdate=function(t,e,n){function i(t){t.isGroup||(t.incremental=t.useHoverLayer=!0)}n=h(n);for(var r=t.start;r<t.end;r++){var a=e.getItemLayout(r);if(u(e,a,r,n)){var o=new this._symbolCtor(e,r,this._seriesScope);o.traverse(i),o.attr("position",a),this.group.add(o),e.setItemGraphicEl(r,o)}}},l.remove=function(t){var e=this.group,n=this._data;n&&t?n.eachItemGraphicEl((function(t){t.fadeOut((function(){e.remove(t)}))})):e.removeAll()};var d=s;t.exports=d},f934:function(t,e,n){var i=n("6d8b"),r=n("9850"),a=n("3842"),o=a.parsePercent,s=n("eda2"),l=i.each,u=["left","right","top","bottom","width","height"],h=[["width","left","right"],["height","top","bottom"]];function c(t,e,n,i,r){var a=0,o=0;null==i&&(i=1/0),null==r&&(r=1/0);var s=0;e.eachChild((function(l,u){var h,c,d=l.position,f=l.getBoundingRect(),p=e.childAt(u+1),g=p&&p.getBoundingRect();if("horizontal"===t){var v=f.width+(g?-g.x+f.x:0);h=a+v,h>i||l.newline?(a=0,h=v,o+=s+n,s=f.height):s=Math.max(s,f.height)}else{var m=f.height+(g?-g.y+f.y:0);c=o+m,c>r||l.newline?(a+=s+n,o=0,c=m,s=f.width):s=Math.max(s,f.width)}l.newline||(d[0]=a,d[1]=o,"horizontal"===t?a=h+n:o=c+n)}))}var d=c,f=i.curry(c,"vertical"),p=i.curry(c,"horizontal");function g(t,e,n){var i=e.width,r=e.height,a=o(t.x,i),l=o(t.y,r),u=o(t.x2,i),h=o(t.y2,r);return(isNaN(a)||isNaN(parseFloat(t.x)))&&(a=0),(isNaN(u)||isNaN(parseFloat(t.x2)))&&(u=i),(isNaN(l)||isNaN(parseFloat(t.y)))&&(l=0),(isNaN(h)||isNaN(parseFloat(t.y2)))&&(h=r),n=s.normalizeCssArray(n||0),{width:Math.max(u-a-n[1]-n[3],0),height:Math.max(h-l-n[0]-n[2],0)}}function v(t,e,n){n=s.normalizeCssArray(n||0);var i=e.width,a=e.height,l=o(t.left,i),u=o(t.top,a),h=o(t.right,i),c=o(t.bottom,a),d=o(t.width,i),f=o(t.height,a),p=n[2]+n[0],g=n[1]+n[3],v=t.aspect;switch(isNaN(d)&&(d=i-h-g-l),isNaN(f)&&(f=a-c-p-u),null!=v&&(isNaN(d)&&isNaN(f)&&(v>i/a?d=.8*i:f=.8*a),isNaN(d)&&(d=v*f),isNaN(f)&&(f=d/v)),isNaN(l)&&(l=i-h-d-g),isNaN(u)&&(u=a-c-f-p),t.left||t.right){case"center":l=i/2-d/2-n[3];break;case"right":l=i-d-g;break}switch(t.top||t.bottom){case"middle":case"center":u=a/2-f/2-n[0];break;case"bottom":u=a-f-p;break}l=l||0,u=u||0,isNaN(d)&&(d=i-g-l-(h||0)),isNaN(f)&&(f=a-p-u-(c||0));var m=new r(l+n[3],u+n[0],d,f);return m.margin=n,m}function m(t,e,n,a,o){var s=!o||!o.hv||o.hv[0],l=!o||!o.hv||o.hv[1],u=o&&o.boundingMode||"all";if(s||l){var h;if("raw"===u)h="group"===t.type?new r(0,0,+e.width||0,+e.height||0):t.getBoundingRect();else if(h=t.getBoundingRect(),t.needLocalTransform()){var c=t.getLocalTransform();h=h.clone(),h.applyTransform(c)}e=v(i.defaults({width:h.width,height:h.height},e),n,a);var d=t.position,f=s?e.x-h.x:0,p=l?e.y-h.y:0;t.attr("position","raw"===u?[f,p]:[d[0]+f,d[1]+p])}}function y(t,e){return null!=t[h[e][0]]||null!=t[h[e][1]]&&null!=t[h[e][2]]}function x(t,e,n){!i.isObject(n)&&(n={});var r=n.ignoreSize;!i.isArray(r)&&(r=[r,r]);var a=s(h[0],0),o=s(h[1],1);function s(n,i){var a={},o=0,s={},h=0,d=2;if(l(n,(function(e){s[e]=t[e]})),l(n,(function(t){u(e,t)&&(a[t]=s[t]=e[t]),c(a,t)&&o++,c(s,t)&&h++})),r[i])return c(e,n[1])?s[n[2]]=null:c(e,n[2])&&(s[n[1]]=null),s;if(h!==d&&o){if(o>=d)return a;for(var f=0;f<n.length;f++){var p=n[f];if(!u(a,p)&&u(t,p)){a[p]=t[p];break}}return a}return s}function u(t,e){return t.hasOwnProperty(e)}function c(t,e){return null!=t[e]&&"auto"!==t[e]}function d(t,e,n){l(t,(function(t){e[t]=n[t]}))}d(h[0],t,a),d(h[1],t,o)}function _(t){return b({},t)}function b(t,e){return e&&t&&l(u,(function(n){e.hasOwnProperty(n)&&(t[n]=e[n])})),t}e.LOCATION_PARAMS=u,e.HV_NAMES=h,e.box=d,e.vbox=f,e.hbox=p,e.getAvailableSize=g,e.getLayoutRect=v,e.positionElement=m,e.sizeCalculable=y,e.mergeLayoutParam=x,e.getLayoutParams=_,e.copyLayoutParams=b},fab22:function(t,e,n){var i=n("6d8b"),r=i.retrieve,a=i.defaults,o=i.extend,s=i.each,l=n("eda2"),u=n("2306"),h=n("4319"),c=n("3842"),d=c.isRadianAroundZero,f=c.remRadian,p=n("a15a"),g=p.createSymbol,v=n("1687"),m=n("401b"),y=m.applyTransform,x=n("697e"),_=x.shouldShowAllLabels,b=Math.PI;function w(t){var e={componentType:t.mainType,componentIndex:t.componentIndex};return e[t.mainType+"Index"]=t.componentIndex,e}var S=function(t,e){this.opt=e,this.axisModel=t,a(e,{labelOffset:0,nameDirection:1,tickDirection:1,labelDirection:1,silent:!0}),this.group=new u.Group;var n=new u.Group({position:e.position.slice(),rotation:e.rotation});n.updateTransform(),this._transform=n.transform,this._dumbGroup=n};S.prototype={constructor:S,hasBuilder:function(t){return!!M[t]},add:function(t){M[t].call(this)},getGroup:function(){return this.group}};var M={axisLine:function(){var t=this.opt,e=this.axisModel;if(e.get("axisLine.show")){var n=this.axisModel.axis.getExtent(),i=this._transform,r=[n[0],0],a=[n[1],0];i&&(y(r,r,i),y(a,a,i));var l=o({lineCap:"round"},e.getModel("axisLine.lineStyle").getLineStyle());this.group.add(new u.Line(u.subPixelOptimizeLine({anid:"line",shape:{x1:r[0],y1:r[1],x2:a[0],y2:a[1]},style:l,strokeContainThreshold:t.strokeContainThreshold||5,silent:!0,z2:1})));var h=e.get("axisLine.symbol"),c=e.get("axisLine.symbolSize"),d=e.get("axisLine.symbolOffset")||0;if("number"===typeof d&&(d=[d,d]),null!=h){"string"===typeof h&&(h=[h,h]),"string"!==typeof c&&"number"!==typeof c||(c=[c,c]);var f=c[0],p=c[1];s([{rotate:t.rotation+Math.PI/2,offset:d[0],r:0},{rotate:t.rotation-Math.PI/2,offset:d[1],r:Math.sqrt((r[0]-a[0])*(r[0]-a[0])+(r[1]-a[1])*(r[1]-a[1]))}],(function(e,n){if("none"!==h[n]&&null!=h[n]){var i=g(h[n],-f/2,-p/2,f,p,l.stroke,!0),a=e.r+e.offset,o=[r[0]+a*Math.cos(t.rotation),r[1]-a*Math.sin(t.rotation)];i.attr({rotation:e.rotate,position:o,silent:!0,z2:11}),this.group.add(i)}}),this)}}},axisTickLabel:function(){var t=this.axisModel,e=this.opt,n=O(this,t,e),i=L(this,t,e);I(t,i,n)},axisName:function(){var t=this.opt,e=this.axisModel,n=r(t.axisName,e.get("name"));if(n){var i,a,s=e.get("nameLocation"),h=t.nameDirection,c=e.getModel("nameTextStyle"),d=e.get("nameGap")||0,f=this.axisModel.axis.getExtent(),p=f[0]>f[1]?-1:1,g=["start"===s?f[0]-p*d:"end"===s?f[1]+p*d:(f[0]+f[1])/2,P(s)?t.labelOffset+h*d:0],v=e.get("nameRotate");null!=v&&(v=v*b/180),P(s)?i=T(t.rotation,null!=v?v:t.rotation,h):(i=A(t,s,v||0,f),a=t.axisNameAvailableWidth,null!=a&&(a=Math.abs(a/Math.sin(i.rotation)),!isFinite(a)&&(a=null)));var m=c.getFont(),y=e.get("nameTruncate",!0)||{},x=y.ellipsis,_=r(t.nameTruncateMaxWidth,y.maxWidth,a),S=null!=x&&null!=_?l.truncateText(n,_,m,x,{minChar:2,placeholder:y.placeholder}):n,M=e.get("tooltip",!0),I=e.mainType,k={componentType:I,name:n,$vars:["name"]};k[I+"Index"]=e.componentIndex;var D=new u.Text({anid:"name",__fullText:n,__truncatedText:S,position:g,rotation:i.rotation,silent:C(e),z2:1,tooltip:M&&M.show?o({content:n,formatter:function(){return n},formatterParams:k},M):null});u.setTextStyle(D.style,c,{text:S,textFont:m,textFill:c.getTextColor()||e.get("axisLine.lineStyle.color"),textAlign:i.textAlign,textVerticalAlign:i.textVerticalAlign}),e.get("triggerEvent")&&(D.eventData=w(e),D.eventData.targetType="axisName",D.eventData.name=n),this._dumbGroup.add(D),D.updateTransform(),this.group.add(D),D.decomposeTransform()}}},T=S.innerTextLayout=function(t,e,n){var i,r,a=f(e-t);return d(a)?(r=n>0?"top":"bottom",i="center"):d(a-b)?(r=n>0?"bottom":"top",i="center"):(r="middle",i=a>0&&a<b?n>0?"right":"left":n>0?"left":"right"),{rotation:a,textAlign:i,textVerticalAlign:r}};function A(t,e,n,i){var r,a,o=f(n-t.rotation),s=i[0]>i[1],l="start"===e&&!s||"start"!==e&&s;return d(o-b/2)?(a=l?"bottom":"top",r="center"):d(o-1.5*b)?(a=l?"top":"bottom",r="center"):(a="middle",r=o<1.5*b&&o>b/2?l?"left":"right":l?"right":"left"),{rotation:o,textAlign:r,textVerticalAlign:a}}function C(t){var e=t.get("tooltip");return t.get("silent")||!(t.get("triggerEvent")||e&&e.show)}function I(t,e,n){if(!_(t.axis)){var i=t.get("axisLabel.showMinLabel"),r=t.get("axisLabel.showMaxLabel");e=e||[],n=n||[];var a=e[0],o=e[1],s=e[e.length-1],l=e[e.length-2],u=n[0],h=n[1],c=n[n.length-1],d=n[n.length-2];!1===i?(k(a),k(u)):D(a,o)&&(i?(k(o),k(h)):(k(a),k(u))),!1===r?(k(s),k(c)):D(l,s)&&(r?(k(l),k(d)):(k(s),k(c)))}}function k(t){t&&(t.ignore=!0)}function D(t,e,n){var i=t&&t.getBoundingRect().clone(),r=e&&e.getBoundingRect().clone();if(i&&r){var a=v.identity([]);return v.rotate(a,a,-t.rotation),i.applyTransform(v.mul([],a,t.getLocalTransform())),r.applyTransform(v.mul([],a,e.getLocalTransform())),i.intersect(r)}}function P(t){return"middle"===t||"center"===t}function O(t,e,n){var i=e.axis;if(e.get("axisTick.show")&&!i.scale.isBlank()){for(var r=e.getModel("axisTick"),o=r.getModel("lineStyle"),s=r.get("length"),l=i.getTicksCoords(),h=[],c=[],d=t._transform,f=[],p=0;p<l.length;p++){var g=l[p].coord;h[0]=g,h[1]=0,c[0]=g,c[1]=n.tickDirection*s,d&&(y(h,h,d),y(c,c,d));var v=new u.Line(u.subPixelOptimizeLine({anid:"tick_"+l[p].tickValue,shape:{x1:h[0],y1:h[1],x2:c[0],y2:c[1]},style:a(o.getLineStyle(),{stroke:e.get("axisLine.lineStyle.color")}),z2:2,silent:!0}));t.group.add(v),f.push(v)}return f}}function L(t,e,n){var i=e.axis,a=r(n.axisLabelShow,e.get("axisLabel.show"));if(a&&!i.scale.isBlank()){var o=e.getModel("axisLabel"),l=o.get("margin"),c=i.getViewLabels(),d=(r(n.labelRotate,o.get("rotate"))||0)*b/180,f=T(n.rotation,d,n.labelDirection),p=e.getCategories(!0),g=[],v=C(e),m=e.get("triggerEvent");return s(c,(function(r,a){var s=r.tickValue,c=r.formattedLabel,d=r.rawLabel,y=o;p&&p[s]&&p[s].textStyle&&(y=new h(p[s].textStyle,o,e.ecModel));var x=y.getTextColor()||e.get("axisLine.lineStyle.color"),_=i.dataToCoord(s),b=[_,n.labelOffset+n.labelDirection*l],S=new u.Text({anid:"label_"+s,position:b,rotation:f.rotation,silent:v,z2:10});u.setTextStyle(S.style,y,{text:c,textAlign:y.getShallow("align",!0)||f.textAlign,textVerticalAlign:y.getShallow("verticalAlign",!0)||y.getShallow("baseline",!0)||f.textVerticalAlign,textFill:"function"===typeof x?x("category"===i.type?d:"value"===i.type?s+"":s,a):x}),m&&(S.eventData=w(e),S.eventData.targetType="axisLabel",S.eventData.value=d),t._dumbGroup.add(S),S.updateTransform(),g.push(S),t.group.add(S),S.decomposeTransform()})),g}}var E=S;t.exports=E},fb05:function(t,e,n){var i=n("6d8b"),r=i.each,a=i.isArray,o=i.isObject,s=n("26e1"),l=n("e0d3"),u=l.normalizeToArray;function h(t,e){e=e.split(",");for(var n=t,i=0;i<e.length;i++)if(n=n&&n[e[i]],null==n)break;return n}function c(t,e,n,i){e=e.split(",");for(var r,a=t,o=0;o<e.length-1;o++)r=e[o],null==a[r]&&(a[r]={}),a=a[r];(i||null==a[e[o]])&&(a[e[o]]=n)}function d(t){r(f,(function(e){e[0]in t&&!(e[1]in t)&&(t[e[1]]=t[e[0]])}))}var f=[["x","left"],["y","top"],["x2","right"],["y2","bottom"]],p=["grid","geo","parallel","legend","toolbox","title","visualMap","dataZoom","timeline"];function g(t,e){s(t,e),t.series=u(t.series),r(t.series,(function(t){if(o(t)){var e=t.type;if("pie"!==e&&"gauge"!==e||null!=t.clockWise&&(t.clockwise=t.clockWise),"gauge"===e){var n=h(t,"pointer.color");null!=n&&c(t,"itemStyle.normal.color",n)}d(t)}})),t.dataRange&&(t.visualMap=t.dataRange),r(p,(function(e){var n=t[e];n&&(a(n)||(n=[n]),r(n,(function(t){d(t)})))}))}t.exports=g},fd27:function(t,e,n){var i=n("9273"),r=n("a991"),a=function(t){this.name=t||"",this.cx=0,this.cy=0,this._radiusAxis=new i,this._angleAxis=new r,this._radiusAxis.polar=this._angleAxis.polar=this};a.prototype={type:"polar",axisPointerEnabled:!0,constructor:a,dimensions:["radius","angle"],model:null,containPoint:function(t){var e=this.pointToCoord(t);return this._radiusAxis.contain(e[0])&&this._angleAxis.contain(e[1])},containData:function(t){return this._radiusAxis.containData(t[0])&&this._angleAxis.containData(t[1])},getAxis:function(t){return this["_"+t+"Axis"]},getAxes:function(){return[this._radiusAxis,this._angleAxis]},getAxesByScale:function(t){var e=[],n=this._angleAxis,i=this._radiusAxis;return n.scale.type===t&&e.push(n),i.scale.type===t&&e.push(i),e},getAngleAxis:function(){return this._angleAxis},getRadiusAxis:function(){return this._radiusAxis},getOtherAxis:function(t){var e=this._angleAxis;return t===e?this._radiusAxis:e},getBaseAxis:function(){return this.getAxesByScale("ordinal")[0]||this.getAxesByScale("time")[0]||this.getAngleAxis()},getTooltipAxes:function(t){var e=null!=t&&"auto"!==t?this.getAxis(t):this.getBaseAxis();return{baseAxes:[e],otherAxes:[this.getOtherAxis(e)]}},dataToPoint:function(t,e){return this.coordToPoint([this._radiusAxis.dataToRadius(t[0],e),this._angleAxis.dataToAngle(t[1],e)])},pointToData:function(t,e){var n=this.pointToCoord(t);return[this._radiusAxis.radiusToData(n[0],e),this._angleAxis.angleToData(n[1],e)]},pointToCoord:function(t){var e=t[0]-this.cx,n=t[1]-this.cy,i=this.getAngleAxis(),r=i.getExtent(),a=Math.min(r[0],r[1]),o=Math.max(r[0],r[1]);i.inverse?a=o-360:o=a+360;var s=Math.sqrt(e*e+n*n);e/=s,n/=s;var l=Math.atan2(-n,e)/Math.PI*180,u=l<a?1:-1;while(l<a||l>o)l+=360*u;return[s,l]},coordToPoint:function(t){var e=t[0],n=t[1]/180*Math.PI,i=Math.cos(n)*e+this.cx,r=-Math.sin(n)*e+this.cy;return[i,r]}};var o=a;t.exports=o},fd63:function(t,e,n){var i=n("42e5"),r={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){var n=t.getData(),r=(t.visualColorAccessPath||"itemStyle.color").split("."),a=t.get(r)||t.getColorFromPalette(t.name,null,e.getSeriesCount());if(n.setVisual("color",a),!e.isSeriesFiltered(t)){"function"!==typeof a||a instanceof i||n.each((function(e){n.setItemVisual(e,"color",a(t.getDataParams(e)))}));var o=function(t,e){var n=t.getItemModel(e),i=n.get(r,!0);null!=i&&t.setItemVisual(e,"color",i)};return{dataEach:n.hasItemOption?o:null}}}};t.exports=r},fdde:function(t,e){var n={average:function(t){for(var e=0,n=0,i=0;i<t.length;i++)isNaN(t[i])||(e+=t[i],n++);return 0===n?NaN:e/n},sum:function(t){for(var e=0,n=0;n<t.length;n++)e+=t[n]||0;return e},max:function(t){for(var e=-1/0,n=0;n<t.length;n++)t[n]>e&&(e=t[n]);return isFinite(e)?e:NaN},min:function(t){for(var e=1/0,n=0;n<t.length;n++)t[n]<e&&(e=t[n]);return isFinite(e)?e:NaN},nearest:function(t){return t[0]}},i=function(t,e){return Math.round(t.length/2)};function r(t){return{seriesType:t,modifyOutputEnd:!0,reset:function(t,e,r){var a=t.getData(),o=t.get("sampling"),s=t.coordinateSystem;if("cartesian2d"===s.type&&o){var l,u=s.getBaseAxis(),h=s.getOtherAxis(u),c=u.getExtent(),d=c[1]-c[0],f=Math.round(a.count()/d);if(f>1)"string"===typeof o?l=n[o]:"function"===typeof o&&(l=o),l&&t.setData(a.downSample(a.mapDimension(h.dim),1/f,l,i))}}}}t.exports=r},fe21:function(t,e,n){var i=n("e86a"),r=n("2306"),a=["textStyle","color"],o={getTextColor:function(t){var e=this.ecModel;return this.getShallow("color")||(!t&&e?e.get(a):null)},getFont:function(){return r.getFont({fontStyle:this.getShallow("fontStyle"),fontWeight:this.getShallow("fontWeight"),fontSize:this.getShallow("fontSize"),fontFamily:this.getShallow("fontFamily")},this.ecModel)},getTextRect:function(t){return i.getBoundingRect(t,this.getFont(),this.getShallow("align"),this.getShallow("verticalAlign")||this.getShallow("baseline"),this.getShallow("padding"),this.getShallow("lineHeight"),this.getShallow("rich"),this.getShallow("truncateText"))}};t.exports=o},ff2e:function(t,e,n){var i=n("6d8b"),r=n("2306"),a=n("e86a"),o=n("eda2"),s=n("1687"),l=n("697e"),u=n("fab22");function h(t){var e,n=t.get("type"),i=t.getModel(n+"Style");return"line"===n?(e=i.getLineStyle(),e.fill=null):"shadow"===n&&(e=i.getAreaStyle(),e.stroke=null),e}function c(t,e,n,i,r){var s=n.get("value"),l=f(s,e.axis,e.ecModel,n.get("seriesDataIndices"),{precision:n.get("label.precision"),formatter:n.get("label.formatter")}),u=n.getModel("label"),h=o.normalizeCssArray(u.get("padding")||0),c=u.getFont(),p=a.getBoundingRect(l,c),g=r.position,v=p.width+h[1]+h[3],m=p.height+h[0]+h[2],y=r.align;"right"===y&&(g[0]-=v),"center"===y&&(g[0]-=v/2);var x=r.verticalAlign;"bottom"===x&&(g[1]-=m),"middle"===x&&(g[1]-=m/2),d(g,v,m,i);var _=u.get("backgroundColor");_&&"auto"!==_||(_=e.get("axisLine.lineStyle.color")),t.label={shape:{x:0,y:0,width:v,height:m,r:u.get("borderRadius")},position:g.slice(),style:{text:l,textFont:c,textFill:u.getTextColor(),textPosition:"inside",fill:_,stroke:u.get("borderColor")||"transparent",lineWidth:u.get("borderWidth")||0,shadowBlur:u.get("shadowBlur"),shadowColor:u.get("shadowColor"),shadowOffsetX:u.get("shadowOffsetX"),shadowOffsetY:u.get("shadowOffsetY")},z2:10}}function d(t,e,n,i){var r=i.getWidth(),a=i.getHeight();t[0]=Math.min(t[0]+e,r)-e,t[1]=Math.min(t[1]+n,a)-n,t[0]=Math.max(t[0],0),t[1]=Math.max(t[1],0)}function f(t,e,n,r,a){t=e.scale.parse(t);var o=e.scale.getLabel(t,{precision:a.precision}),s=a.formatter;if(s){var u={value:l.getAxisRawValue(e,t),seriesData:[]};i.each(r,(function(t){var e=n.getSeriesByIndex(t.seriesIndex),i=t.dataIndexInside,r=e&&e.getDataParams(i);r&&u.seriesData.push(r)})),i.isString(s)?o=s.replace("{value}",o):i.isFunction(s)&&(o=s(u))}return o}function p(t,e,n){var i=s.create();return s.rotate(i,i,n.rotation),s.translate(i,i,n.position),r.applyTransform([t.dataToCoord(e),(n.labelOffset||0)+(n.labelDirection||1)*(n.labelMargin||0)],i)}function g(t,e,n,i,r,a){var o=u.innerTextLayout(n.rotation,0,n.labelDirection);n.labelMargin=r.get("label.margin"),c(e,i,r,a,{position:p(i.axis,t,n),align:o.textAlign,verticalAlign:o.textVerticalAlign})}function v(t,e,n){return n=n||0,{x1:t[n],y1:t[1-n],x2:e[n],y2:e[1-n]}}function m(t,e,n){return n=n||0,{x:t[n],y:t[1-n],width:e[n],height:e[1-n]}}function y(t,e,n,i,r,a){return{cx:t,cy:e,r0:n,r:i,startAngle:r,endAngle:a,clockwise:!0}}e.buildElStyle=h,e.buildLabelElOption=c,e.getValueLabel=f,e.getTransformedPosition=p,e.buildCartesianSingleLabelElOption=g,e.makeLineShape=v,e.makeRectShape=m,e.makeSectorShape=y}}]);