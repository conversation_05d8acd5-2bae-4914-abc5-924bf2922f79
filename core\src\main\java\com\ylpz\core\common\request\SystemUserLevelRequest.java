package com.ylpz.core.common.request;

import java.io.Serializable;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Length;

import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 设置用户等级表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("system_user_level")
@ApiModel(value = "SystemUserLevelRequest对象", description = "设置用户等级表")
public class SystemUserLevelRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "等级id")
    private Integer id;

    @ApiModelProperty(value = "等级名称")
    @NotBlank(message = "等级名称不能为空")
    @Length(max = 50, message = "等级名称不能超过50个字符")
    private String name;

    @ApiModelProperty(value = "达到多少升级经验")
    @NotNull(message = "等级经验不能为空")
    private Integer experience;

    @ApiModelProperty(value = "会员等级")
    @NotNull(message = "会员等级不能为空")
    @Min(value = 1, message = "会员等级最小为1")
    private Integer grade;

    @ApiModelProperty(value = "是否启用会员折扣 1=是,0=否")
    private Boolean discountEnabled;

    @ApiModelProperty(value = "享受折扣")
    private Integer discount;

    @ApiModelProperty(value = "会员图标")
    @NotBlank(message = "会员图标不能为空")
    private String icon;

    @ApiModelProperty(value = "成长值来源（多选：自购消费、推广金额、会员充值，用逗号分隔）")
    private String experienceSource;

    @ApiModelProperty(value = "是否达到成长值自动升级 1=是,0=否")
    private Boolean autoUpgrade;

    @ApiModelProperty(value = "是否需要手机号 1=是,0=否")
    private Boolean phoneRequired;

    @ApiModelProperty(value = "是否需要头像和昵称 1=是,0=否")
    private Boolean avatarNicknameRequired;

    @ApiModelProperty(value = "是否需要生日 1=是,0=否")
    private Boolean birthdayRequired;

    @ApiModelProperty(value = "是否开启优惠券赠送 1=是,0=否")
    private Boolean couponEnabled;

    @ApiModelProperty(value = "补充资料赠送的优惠券ID")
    private Integer couponId;

    @ApiModelProperty(value = "是否启用生日券 1=是,0=否")
    private Boolean birthCouponEnabled;

    @ApiModelProperty(value = "生日券ID")
    private Integer birthCouponId;

    @ApiModelProperty(value = "是否启用佣金返现 1=是,0=否")
    private Boolean commissionEnabled;

    @ApiModelProperty(value = "分销提成比例")
    private Integer commissionRate;

    @ApiModelProperty(value = "是否启用专属客服 1=是,0=否")
    private Boolean customerServiceEnabled;

    @ApiModelProperty(value = "客服微信号")
    private String customerServiceWechat;

    @ApiModelProperty(value = "是否显示 1=显示,0=隐藏")
    private Boolean isShow;

}
