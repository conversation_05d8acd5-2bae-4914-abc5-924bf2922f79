(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-22b83f50"],{7256:function(t,e,n){"use strict";n("dee7")},"754f":function(t,e,n){t.exports=n.p+"static/img/line.05bf1c84.jpg"},"7dca":function(t,e,n){"use strict";n.r(e);var r=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"order-details pos-order-details"},[n("div",{staticClass:"header acea-row row-middle"},[n("div",{staticClass:"state"},[t._v(t._s(t.title))]),t._v(" "),n("div",{staticClass:"data"},[n("div",{staticClass:"order-num"},[t._v("订单："+t._s(t.orderInfo.orderId))]),t._v(" "),n("div",[n("span",{staticClass:"time"},[t._v(t._s(t.orderInfo.createTime))])])])]),t._v(" "),"looks"!=t.$route.params.goname?n("div",{staticClass:"remarks acea-row row-between-wrapper"},[n("span",{staticClass:"iconfont iconios-flag"}),t._v(" "),n("input",{staticClass:"line1",staticStyle:{"text-align":"left"},attrs:{type:"button",value:t.orderInfo.remark?t.orderInfo.remark:"订单未备注，点击添加备注信息"},on:{click:function(e){return t.modify(1)}}})]):t._e(),t._v(" "),n("div",{staticClass:"orderingUser acea-row row-middle"},[n("span",{staticClass:"iconfont iconmd-contact"}),t._v(t._s(t.orderInfo.realName)+"\n  ")]),t._v(" "),n("div",{staticClass:"address"},[n("div",{staticClass:"name"},[t._v("\n      "+t._s(t.orderInfo.realName)),n("span",{staticClass:"phone"},[t._v(t._s(t.orderInfo.userPhone))])]),t._v(" "),n("div",[t._v(t._s(t.orderInfo.userAddress))])]),t._v(" "),t._m(0),t._v(" "),n("div",{staticClass:"pos-order-goods"},t._l(t.orderInfo.orderInfo,(function(e,r){return n("div",{key:r,staticClass:"goods acea-row row-between-wrapper"},[n("div",{staticClass:"picTxt acea-row row-between-wrapper"},[n("div",{staticClass:"pictrue"},[n("img",{attrs:{src:e.info.image}})]),t._v(" "),n("div",{staticClass:"text"},[n("div",{staticClass:"info line2"},[t._v("\n            "+t._s(e.info.productName)+"\n          ")]),t._v(" "),n("div",{staticClass:"attr overflow"},[t._v(t._s(e.info.sku))])])]),t._v(" "),n("div",{staticClass:"money"},[n("div",{staticClass:"x-money"},[t._v("￥"+t._s(e.info.price))]),t._v(" "),n("div",{staticClass:"num"},[t._v("x"+t._s(e.info.payNum))])])])})),0),t._v(" "),n("div",{staticClass:"public-total"},[t._v("\n    共"+t._s(t.orderInfo.totalNum)+"件商品，应支付\n    "),n("span",{staticClass:"money"},[t._v("￥"+t._s(t.orderInfo.payPrice))]),t._v(" ( 邮费 ¥"+t._s(t.orderInfo.payPostage)+")\n  ")]),t._v(" "),n("div",{staticClass:"wrapper"},[n("div",{staticClass:"item acea-row row-between"},[n("div",[t._v("订单编号：")]),t._v(" "),n("div",{staticClass:"conter acea-row row-middle row-right"},[t._v("\n        "+t._s(t.orderInfo.orderId)+"\n        "),n("span",{staticClass:"copy copy-data",attrs:{"data-clipboard-text":t.orderInfo.orderId}},[t._v("复制")])])]),t._v(" "),n("div",{staticClass:"item acea-row row-between"},[n("div",[t._v("下单时间：")]),t._v(" "),n("div",{staticClass:"conter"},[t._v(t._s(t.orderInfo.createTime))])]),t._v(" "),n("div",{staticClass:"item acea-row row-between"},[n("div",[t._v("支付状态：")]),t._v(" "),n("div",{staticClass:"conter"},[t._v("\n        "+t._s(t.orderInfo.statusStr.value)+"\n      ")])]),t._v(" "),n("div",{staticClass:"item acea-row row-between"},[n("div",[t._v("支付方式：")]),t._v(" "),n("div",{staticClass:"conter"},[t._v(t._s(t.orderInfo.payTypeStr))])]),t._v(" "),n("div",{staticClass:"item acea-row row-between"},[n("div",[t._v("买家留言：")]),t._v(" "),n("div",{staticClass:"conter"},[t._v(t._s(t.orderInfo.mark))])])]),t._v(" "),n("div",{staticClass:"wrapper"},[n("div",{staticClass:"item acea-row row-between"},[n("div",[t._v("支付金额：")]),t._v(" "),n("div",{staticClass:"conter"},[t._v("￥"+t._s(t.orderInfo.totalPrice))])]),t._v(" "),n("div",{staticClass:"item acea-row row-between"},[n("div",[t._v("优惠券抵扣：")]),t._v(" "),n("div",{staticClass:"conter"},[t._v("-￥"+t._s(t.orderInfo.couponPrice))])]),t._v(" "),n("div",{staticClass:"item acea-row row-between"},[n("div",[t._v("运费：")]),t._v(" "),n("div",{staticClass:"conter"},[t._v("￥"+t._s(t.orderInfo.payPostage))])]),t._v(" "),n("div",{staticClass:"actualPay acea-row row-right"},[t._v("\n      实付款："),n("span",{staticClass:"money font-color-red"},[t._v("￥"+t._s(t.orderInfo.payPrice))])])]),t._v(" "),"express"===t.orderInfo.deliveryType?n("div",{staticClass:"wrapper"},[n("div",{staticClass:"item acea-row row-between"},[n("div",[t._v("配送方式：")]),t._v(" "),"express"===t.orderInfo.deliveryType?n("div",{staticClass:"conter"},[t._v("\n        快递\n      ")]):t._e(),t._v(" "),"send"===t.orderInfo.deliveryType?n("div",{staticClass:"conter"},[t._v("送货")]):t._e()]),t._v(" "),n("div",{staticClass:"item acea-row row-between"},["express"===t.orderInfo.deliveryType?n("div",[t._v("快递公司：")]):t._e(),t._v(" "),"send"===t.orderInfo.deliveryType?n("div",[t._v("送货人：")]):t._e(),t._v(" "),n("div",{staticClass:"conter"},[t._v(t._s(t.orderInfo.deliveryName))])]),t._v(" "),n("div",{staticClass:"item acea-row row-between"},["express"===t.orderInfo.deliveryType?n("div",[t._v("快递单号：")]):t._e(),t._v(" "),"send"===t.orderInfo.deliveryType?n("div",[t._v("送货人电话：")]):t._e(),t._v(" "),n("div",{staticClass:"conter"},[t._v("\n        "+t._s(t.orderInfo.deliveryId)),n("span",{staticClass:"copy copy-data",attrs:{"data-clipboard-text":t.orderInfo.deliveryId}},[t._v("复制")])])])]):t._e(),t._v(" "),n("div",{staticStyle:{height:"1.2rem"}}),t._v(" "),"looks"!=t.$route.params.goname?n("div",{staticClass:"footer acea-row row-right row-middle"},[n("div",{staticClass:"more"}),t._v(" "),"unPaid"===t.types?n("div",{staticClass:"bnt cancel",on:{click:function(e){return t.modify(0)}}},[t._v("\n      一键改价\n    ")]):t._e(),t._v(" "),"refunding"===t.types?n("div",{staticClass:"bnt cancel",on:{click:function(e){return t.modify(0)}}},[t._v("\n      立即退款\n    ")]):t._e(),t._v(" "),n("div",{staticClass:"bnt cancel",on:{click:function(e){return t.modify(1)}}},[t._v("订单备注")]),t._v(" "),"notShipped"==t.types&&2!==t.orderInfo.shippingType&&2!==t.orderInfo.refundStatus?n("router-link",{staticClass:"bnt delivery",attrs:{to:"/javaMobile/orderDelivery/"+t.orderInfo.orderId+"/"+t.orderInfo.id}},[t._v("去发货")]):t._e(),t._v(" "),"toBeWrittenOff"===t.types&&2===t.orderInfo.shippingType&&t.isWriteOff&&0===t.orderInfo.refundStatus&&1==t.orderInfo.paid?n("router-link",{staticClass:"bnt delivery",attrs:{to:"/operation/systemStore/orderCancellation"}},[t._v("去核销\n    ")]):t._e()],1):t._e(),t._v(" "),n("PriceChange",{attrs:{change:t.change,orderInfo:t.orderInfo,status:t.status},on:{closechange:function(e){return t.changeclose(e)}}})],1)},o=[function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"line"},[r("img",{attrs:{src:n("754f")}})])}],i=n("4c60"),a=n("b311"),s=n.n(a),c=n("f8b7"),u=(n("61f7"),n("69ae"),n("ed08")),d={name:"AdminOrder",components:{PriceChange:i["a"]},props:{},data:function(){return{isWriteOff:Object(u["e"])(),order:!1,change:!1,orderId:"",orderInfo:{},status:0,title:"",payType:"",types:""}},watch:{"$route.params.id":function(t){var e=this;void 0!=t&&(e.orderId=t,e.getIndex())}},mounted:function(){this.getIndex(),this.$nextTick((function(){var t=this,e=document.getElementsByClassName("copy-data"),n=new s.a(e);n.on("success",(function(){t.$dialog.success("复制成功")}))}))},methods:{more:function(){this.order=!this.order},modify:function(t){this.change=!0,this.status=t},changeclose:function(t){this.change=t,this.getIndex()},getIndex:function(){var t=this,e=this;Object(c["f"])({orderNo:this.$route.params.id}).then((function(n){e.orderInfo=n,e.types=n.statusStr.key,e.title=n.statusStr.value,e.payType=n.payTypeStr,t.$nextTick((function(){var t=this,e=document.getElementsByClassName("copy-data"),n=new s.a(e);n.on("success",(function(){t.$dialog.success("复制成功")}))}))}),(function(t){e.$dialog.error(t.msg)}))},offlinePay:function(){var t=this;setOfflinePay({orderId:this.orderInfo.orderId}).then((function(e){t.$dialog.success(e.msg),t.getIndex()}),(function(e){t.$dialog.error(e.msg)}))}}},l=d,f=(n("7256"),n("2877")),v=Object(f["a"])(l,r,o,!1,null,"3c275def",null);e["default"]=v.exports},b311:function(t,e,n){
/*!
 * clipboard.js v2.0.11
 * https://clipboardjs.com/
 *
 * Licensed MIT © Zeno Rocha
 */
(function(e,n){t.exports=n()})(0,(function(){return function(){var t={686:function(t,e,n){"use strict";n.d(e,{default:function(){return j}});var r=n(279),o=n.n(r),i=n(370),a=n.n(i),s=n(817),c=n.n(s);function u(t){try{return document.execCommand(t)}catch(e){return!1}}var d=function(t){var e=c()(t);return u("cut"),e},l=d;function f(t){var e="rtl"===document.documentElement.getAttribute("dir"),n=document.createElement("textarea");n.style.fontSize="12pt",n.style.border="0",n.style.padding="0",n.style.margin="0",n.style.position="absolute",n.style[e?"right":"left"]="-9999px";var r=window.pageYOffset||document.documentElement.scrollTop;return n.style.top="".concat(r,"px"),n.setAttribute("readonly",""),n.value=t,n}var v=function(t,e){var n=f(t);e.container.appendChild(n);var r=c()(n);return u("copy"),n.remove(),r},p=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{container:document.body},n="";return"string"===typeof t?n=v(t,e):t instanceof HTMLInputElement&&!["text","search","url","tel","password"].includes(null===t||void 0===t?void 0:t.type)?n=v(t.value,e):(n=c()(t),u("copy")),n},y=p;function m(t){return m="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"===typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},m(t)}var _=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.action,n=void 0===e?"copy":e,r=t.container,o=t.target,i=t.text;if("copy"!==n&&"cut"!==n)throw new Error('Invalid "action" value, use either "copy" or "cut"');if(void 0!==o){if(!o||"object"!==m(o)||1!==o.nodeType)throw new Error('Invalid "target" value, use a valid Element');if("copy"===n&&o.hasAttribute("disabled"))throw new Error('Invalid "target" attribute. Please use "readonly" instead of "disabled" attribute');if("cut"===n&&(o.hasAttribute("readonly")||o.hasAttribute("disabled")))throw new Error('Invalid "target" attribute. You can\'t cut text from elements with "readonly" or "disabled" attributes')}return i?y(i,{container:r}):o?"cut"===n?l(o):y(o,{container:r}):void 0},h=_;function g(t){return g="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"===typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},g(t)}function b(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function w(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function C(t,e,n){return e&&w(t.prototype,e),n&&w(t,n),t}function I(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&x(t,e)}function x(t,e){return x=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t},x(t,e)}function S(t){var e=k();return function(){var n,r=O(t);if(e){var o=O(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return T(this,n)}}function T(t,e){return!e||"object"!==g(e)&&"function"!==typeof e?E(t):e}function E(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function k(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}function O(t){return O=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)},O(t)}function A(t,e){var n="data-clipboard-".concat(t);if(e.hasAttribute(n))return e.getAttribute(n)}var P=function(t){I(n,t);var e=S(n);function n(t,r){var o;return b(this,n),o=e.call(this),o.resolveOptions(r),o.listenClick(t),o}return C(n,[{key:"resolveOptions",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.action="function"===typeof t.action?t.action:this.defaultAction,this.target="function"===typeof t.target?t.target:this.defaultTarget,this.text="function"===typeof t.text?t.text:this.defaultText,this.container="object"===g(t.container)?t.container:document.body}},{key:"listenClick",value:function(t){var e=this;this.listener=a()(t,"click",(function(t){return e.onClick(t)}))}},{key:"onClick",value:function(t){var e=t.delegateTarget||t.currentTarget,n=this.action(e)||"copy",r=h({action:n,container:this.container,target:this.target(e),text:this.text(e)});this.emit(r?"success":"error",{action:n,text:r,trigger:e,clearSelection:function(){e&&e.focus(),window.getSelection().removeAllRanges()}})}},{key:"defaultAction",value:function(t){return A("action",t)}},{key:"defaultTarget",value:function(t){var e=A("target",t);if(e)return document.querySelector(e)}},{key:"defaultText",value:function(t){return A("text",t)}},{key:"destroy",value:function(){this.listener.destroy()}}],[{key:"copy",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{container:document.body};return y(t,e)}},{key:"cut",value:function(t){return l(t)}},{key:"isSupported",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:["copy","cut"],e="string"===typeof t?[t]:t,n=!!document.queryCommandSupported;return e.forEach((function(t){n=n&&!!document.queryCommandSupported(t)})),n}}]),n}(o()),j=P},828:function(t){var e=9;if("undefined"!==typeof Element&&!Element.prototype.matches){var n=Element.prototype;n.matches=n.matchesSelector||n.mozMatchesSelector||n.msMatchesSelector||n.oMatchesSelector||n.webkitMatchesSelector}function r(t,n){while(t&&t.nodeType!==e){if("function"===typeof t.matches&&t.matches(n))return t;t=t.parentNode}}t.exports=r},438:function(t,e,n){var r=n(828);function o(t,e,n,r,o){var i=a.apply(this,arguments);return t.addEventListener(n,i,o),{destroy:function(){t.removeEventListener(n,i,o)}}}function i(t,e,n,r,i){return"function"===typeof t.addEventListener?o.apply(null,arguments):"function"===typeof n?o.bind(null,document).apply(null,arguments):("string"===typeof t&&(t=document.querySelectorAll(t)),Array.prototype.map.call(t,(function(t){return o(t,e,n,r,i)})))}function a(t,e,n,o){return function(n){n.delegateTarget=r(n.target,e),n.delegateTarget&&o.call(t,n)}}t.exports=i},879:function(t,e){e.node=function(t){return void 0!==t&&t instanceof HTMLElement&&1===t.nodeType},e.nodeList=function(t){var n=Object.prototype.toString.call(t);return void 0!==t&&("[object NodeList]"===n||"[object HTMLCollection]"===n)&&"length"in t&&(0===t.length||e.node(t[0]))},e.string=function(t){return"string"===typeof t||t instanceof String},e.fn=function(t){var e=Object.prototype.toString.call(t);return"[object Function]"===e}},370:function(t,e,n){var r=n(879),o=n(438);function i(t,e,n){if(!t&&!e&&!n)throw new Error("Missing required arguments");if(!r.string(e))throw new TypeError("Second argument must be a String");if(!r.fn(n))throw new TypeError("Third argument must be a Function");if(r.node(t))return a(t,e,n);if(r.nodeList(t))return s(t,e,n);if(r.string(t))return c(t,e,n);throw new TypeError("First argument must be a String, HTMLElement, HTMLCollection, or NodeList")}function a(t,e,n){return t.addEventListener(e,n),{destroy:function(){t.removeEventListener(e,n)}}}function s(t,e,n){return Array.prototype.forEach.call(t,(function(t){t.addEventListener(e,n)})),{destroy:function(){Array.prototype.forEach.call(t,(function(t){t.removeEventListener(e,n)}))}}}function c(t,e,n){return o(document.body,t,e,n)}t.exports=i},817:function(t){function e(t){var e;if("SELECT"===t.nodeName)t.focus(),e=t.value;else if("INPUT"===t.nodeName||"TEXTAREA"===t.nodeName){var n=t.hasAttribute("readonly");n||t.setAttribute("readonly",""),t.select(),t.setSelectionRange(0,t.value.length),n||t.removeAttribute("readonly"),e=t.value}else{t.hasAttribute("contenteditable")&&t.focus();var r=window.getSelection(),o=document.createRange();o.selectNodeContents(t),r.removeAllRanges(),r.addRange(o),e=r.toString()}return e}t.exports=e},279:function(t){function e(){}e.prototype={on:function(t,e,n){var r=this.e||(this.e={});return(r[t]||(r[t]=[])).push({fn:e,ctx:n}),this},once:function(t,e,n){var r=this;function o(){r.off(t,o),e.apply(n,arguments)}return o._=e,this.on(t,o,n)},emit:function(t){var e=[].slice.call(arguments,1),n=((this.e||(this.e={}))[t]||[]).slice(),r=0,o=n.length;for(r;r<o;r++)n[r].fn.apply(n[r].ctx,e);return this},off:function(t,e){var n=this.e||(this.e={}),r=n[t],o=[];if(r&&e)for(var i=0,a=r.length;i<a;i++)r[i].fn!==e&&r[i].fn._!==e&&o.push(r[i]);return o.length?n[t]=o:delete n[t],this}},t.exports=e,t.exports.TinyEmitter=e}},e={};function n(r){if(e[r])return e[r].exports;var o=e[r]={exports:{}};return t[r](o,o.exports,n),o.exports}return function(){n.n=function(t){var e=t&&t.__esModule?function(){return t["default"]}:function(){return t};return n.d(e,{a:e}),e}}(),function(){n.d=function(t,e){for(var r in e)n.o(e,r)&&!n.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})}}(),function(){n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)}}(),n(686)}().default}))},dee7:function(t,e,n){}}]);