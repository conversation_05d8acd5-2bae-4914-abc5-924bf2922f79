(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d210bad"],{b8ab:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"divBox"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("div",{staticClass:"container"},[a("el-form",{attrs:{inline:"",size:"small"},nativeOn:{submit:function(t){t.preventDefault()}}},[a("el-form-item",{attrs:{label:"关键字"}},[a("el-input",{attrs:{placeholder:"表名称",clearable:"",size:"small"},model:{value:t.codeListData.pram.tableName,callback:function(e){t.$set(t.codeListData.pram,"tableName",e)},expression:"codeListData.pram.tableName"}},[a("el-button",{attrs:{slot:"append",icon:"el-icon-search",size:"small"},on:{click:t.handlerSearch},slot:"append"})],1)],1),t._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary",disabled:0===t.codeListData.selectedTables.length},nativeOn:{click:function(e){return t.handleGenCode(e)}}},[t._v("生成代码")])],1)],1)],1)]),t._v(" "),a("el-table",{ref:"codeList",staticClass:"table",attrs:{data:t.codeListData.data.list,"highlight-current-row":!0,size:"mini","header-cell-style":{fontWeight:"bold"}},on:{"selection-change":t.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55"}}),t._v(" "),a("el-table-column",{attrs:{label:"表名称",prop:"tableName","min-width":"180"}}),t._v(" "),a("el-table-column",{attrs:{label:"表描述",prop:"tableComment","min-width":"180"}}),t._v(" "),a("el-table-column",{attrs:{label:"创建时间",prop:"createTime","min-width":"200"}})],1),t._v(" "),a("el-pagination",{attrs:{"current-page":t.codeListData.pram.page,"page-sizes":t.constants.page.limit,layout:t.constants.page.layout,total:t.codeListData.data.totalCount},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)],1)},n=[],s=a("b775");function l(t){var e={page:t.page,limit:t.limit,tableName:t.tableName};return Object(s["a"])({url:"/codegen/list",method:"GET",params:e})}var o=a("27c7"),c={name:"codegenList",data:function(){return{constants:this.$constants,codeListData:{pram:{page:1,limit:10,tableName:""},data:{list:[],totalCount:0},selectedTables:[]}}},created:function(){},mounted:function(){},methods:{handlerSearch:function(){this.codeListData.pram.limit=10,this.codeListData.pram.page=1,this.getList(this.codeListData.pram)},getList:function(t){var e=this;l(t).then((function(t){e.codeListData.data=t}))},handleSizeChange:function(t){this.codeListData.pram.limit=t,this.getList(this.codeListData.pram)},handleCurrentChange:function(t){this.codeListData.pram.page=t,this.getList(this.codeListData.pram)},handleSelectionChange:function(t){var e=this;this.codeListData.selectedTables=[],t.forEach((function(t){e.codeListData.selectedTables.push(t.tableName)}))},handleGenCode:function(){window.open("".concat(o["a"].apiBaseURL,"codegen/code?tables=").concat(this.codeListData.selectedTables.join(",")))}}},r=c,d=a("2877"),h=Object(d["a"])(r,i,n,!1,null,"4bbaaf8e",null);e["default"]=h.exports}}]);