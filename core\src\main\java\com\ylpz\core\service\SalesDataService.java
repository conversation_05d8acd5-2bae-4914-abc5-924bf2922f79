package com.ylpz.core.service;

import com.github.pagehelper.PageInfo;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.core.common.request.SalesDataRequest;
import com.ylpz.core.common.response.SalesDataDetailResponse;
import com.ylpz.core.common.response.SalesDataResponse;
import com.ylpz.core.common.response.SalesDataStatisticsResponse;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 销售数据服务接口
 */
public interface SalesDataService {

    /**
     * 获取销售数据列表
     *
     * @param request          查询条件
     * @param pageParamRequest 分页参数
     * @return 销售数据列表
     */
    PageInfo<SalesDataResponse> getSalesDataList(SalesDataRequest request, PageParamRequest pageParamRequest);

    /**
     * 获取销售数据统计
     *
     * @param request 查询条件
     * @return 销售数据统计
     */
    SalesDataStatisticsResponse getSalesDataStatistics(SalesDataRequest request);

    /**
     * 获取会员销售明细
     *
     * @param uid              用户ID
     * @param request          查询条件
     * @param pageParamRequest 分页参数
     * @return 销售明细列表
     */
    PageInfo<SalesDataDetailResponse> getSalesDataDetail(Integer uid, SalesDataRequest request,
            PageParamRequest pageParamRequest);

    /**
     * 导出销售数据
     *
     * @param request  查询条件
     * @param response HTTP响应对象
     * @throws IOException IO异常
     */
    void exportSalesData(SalesDataRequest request, HttpServletResponse response) throws IOException;
}
