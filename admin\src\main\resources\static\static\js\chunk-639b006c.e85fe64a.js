(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-639b006c"],{"2f2c":function(t,e,r){"use strict";r.d(e,"b",(function(){return u})),r.d(e,"c",(function(){return d})),r.d(e,"r",(function(){return m})),r.d(e,"d",(function(){return f})),r.d(e,"a",(function(){return p})),r.d(e,"g",(function(){return h})),r.d(e,"h",(function(){return v})),r.d(e,"j",(function(){return g})),r.d(e,"i",(function(){return y})),r.d(e,"e",(function(){return b})),r.d(e,"o",(function(){return w})),r.d(e,"q",(function(){return x})),r.d(e,"l",(function(){return F})),r.d(e,"m",(function(){return O})),r.d(e,"n",(function(){return _})),r.d(e,"p",(function(){return j})),r.d(e,"k",(function(){return k})),r.d(e,"f",(function(){return L}));var n=r("b775");function a(t){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},a(t)}function i(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function o(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?i(Object(r),!0).forEach((function(e){s(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function s(t,e,r){return(e=l(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function l(t){var e=c(t,"string");return"symbol"==a(e)?e:e+""}function c(t,e){if("object"!=a(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=a(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function u(t){return Object(n["a"])({url:"/admin/system/city/list",method:"get",params:o({},t)})}function d(){return Object(n["a"])({url:"/admin/system/city/list/tree",method:"get"})}function m(t){return Object(n["a"])({url:"/admin/system/city/update/status",method:"post",params:o({},t)})}function f(t){return Object(n["a"])({url:"/admin/system/city/update",method:"post",params:o({},t)})}function p(t){return Object(n["a"])({url:"/admin/system/city/info",method:"get",params:o({},t)})}function h(t){return Object(n["a"])({url:"/admin/express/list",method:"get",params:o({},t)})}function v(){return Object(n["a"])({url:"/admin/express/sync/express",method:"post"})}function g(t){return Object(n["a"])({url:"/admin/express/update/show",method:"post",data:t})}function y(t){return Object(n["a"])({url:"/admin/express/update",method:"post",data:t})}function b(t){return Object(n["a"])({url:"/admin/express/delete",method:"GET",params:o({},t)})}function w(t){return Object(n["a"])({url:"/admin/express/shipping/templates/list",method:"get",params:o({},t)})}function x(t){return Object(n["a"])({url:"/admin/express/shipping/templates/info",method:"get",params:o({},t)})}function F(t){return Object(n["a"])({url:"/admin/express/shipping/free/list",method:"get",params:o({},t)})}function O(t){return Object(n["a"])({url:"admin/express/shipping/region/list",method:"get",params:o({},t)})}function _(t){return Object(n["a"])({url:"admin/express/shipping/templates/save",method:"post",data:t})}function j(t,e){return Object(n["a"])({url:"admin/express/shipping/templates/update",method:"post",data:t,params:o({},e)})}function k(t){return Object(n["a"])({url:"admin/express/shipping/templates/delete",method:"get",params:t})}function L(t){return Object(n["a"])({url:"admin/express/info",method:"get",params:o({},t)})}},"36d0":function(t,e,r){"use strict";r("f8ef")},5317:function(t,e,r){"use strict";var n=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div")},a=[],i=r("2877"),o={},s=Object(i["a"])(o,n,a,!1,null,null,null);e["a"]=s.exports},"9e95":function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"divBox"},[r("el-card",{staticClass:"box-card"},[r("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t.checkPermi(["admin:system:store:count","admin:system:store:list"])?r("el-tabs",{on:{"tab-click":t.onClickTab},model:{value:t.artFrom.status,callback:function(e){t.$set(t.artFrom,"status",e)},expression:"artFrom.status"}},[r("el-tab-pane",{attrs:{label:"显示中的提货点("+t.headerCount.show+")",name:"1"}}),t._v(" "),r("el-tab-pane",{attrs:{label:"隐藏中的提货点("+t.headerCount.hide+")",name:"0"}}),t._v(" "),r("el-tab-pane",{attrs:{label:"回收站的提货点("+t.headerCount.recycle+")",name:"2"}})],1):t._e(),t._v(" "),r("el-form",{ref:"form",attrs:{inline:"",model:t.artFrom},nativeOn:{submit:function(t){t.preventDefault()}}},[r("el-form-item",{attrs:{label:"关键字："}},[r("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入提货点名称/电话",size:"small",clearable:""},model:{value:t.artFrom.keywords,callback:function(e){t.$set(t.artFrom,"keywords",e)},expression:"artFrom.keywords"}},[r("el-button",{attrs:{slot:"append",icon:"el-icon-search"},on:{click:t.search},slot:"append"})],1)],1)],1),t._v(" "),r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:system:store:save"],expression:"['admin:system:store:save']"}],attrs:{type:"primary",size:"small"},on:{click:t.add}},[t._v("添加提货点")])],1),t._v(" "),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{size:"small",data:t.tableData,"header-cell-style":{fontWeight:"bold"}}},[r("el-table-column",{attrs:{prop:"id",label:"ID","min-width":"80"}}),t._v(" "),r("el-table-column",{attrs:{prop:"image",label:"提货点图片","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(t){var e=t.row;t.index;return[r("div",{staticClass:"demo-image__preview"},[r("el-image",{staticStyle:{width:"36px",height:"36px"},attrs:{src:e.image,"preview-src-list":[e.image]}})],1)]}}])}),t._v(" "),r("el-table-column",{attrs:{prop:"name",label:"提货点名称","min-width":"150"}}),t._v(" "),r("el-table-column",{attrs:{prop:"phone",label:"提货点电话","min-width":"100"}}),t._v(" "),r("el-table-column",{attrs:{prop:"detailedAddress",label:"地址","min-width":"100"}}),t._v(" "),r("el-table-column",{attrs:{prop:"dayTime",label:"营业时间","min-width":"180"}}),t._v(" "),r("el-table-column",{attrs:{prop:"isShow",label:"是否显示","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;e.index;return t.checkPermi(["admin:system:store:update:status"])?[r("el-switch",{attrs:{"active-value":!0,"inactive-value":!1,"active-text":"显示","inactive-text":"隐藏"},on:{change:function(e){return t.onchangeIsShow(n.id,n.isShow)}},model:{value:n.isShow,callback:function(e){t.$set(n,"isShow",e)},expression:"row.isShow"}})]:void 0}}],null,!0)}),t._v(" "),r("el-table-column",{attrs:{fixed:"right",label:"操作","min-width":"120"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;e.index;return[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:system:store:info"],expression:"['admin:system:store:info']"}],attrs:{type:"text",size:"small"},on:{click:function(e){return t.edit(n.id)}}},[t._v("编辑")]),t._v(" "),r("el-divider",{attrs:{direction:"vertical"}}),t._v(" "),"2"===t.artFrom.status?r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:system:store:recovery"],expression:"['admin:system:store:recovery']"}],attrs:{type:"text",size:"small"},on:{click:function(e){return t.storeRecovery(n.id)}}},[t._v("恢复")]):t._e(),t._v(" "),"2"===t.artFrom.status?r("el-divider",{attrs:{direction:"vertical"}}):t._e(),t._v(" "),r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:system:store:delete","admin:system:store:completely:delete"],expression:"['admin:system:store:delete','admin:system:store:completely:delete']"}],attrs:{type:"text",size:"small"},on:{click:function(e){"2"===t.artFrom.status?t.allDelete(n.id):t.storeDelete(n.id)}}},[t._v("删除")])]}}])})],1),t._v(" "),r("el-pagination",{staticClass:"mt20",attrs:{"current-page":t.artFrom.page,"page-sizes":[20,40,60,100],"page-size":t.artFrom.limit,layout:"total, sizes, prev, pager, next, jumper",total:t.total},on:{"size-change":t.sizeChange,"current-change":t.pageChange}})],1),t._v(" "),r("system-store",{ref:"template"})],1)},a=[],i=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("el-dialog",{attrs:{title:t.id?"修改提货点":"添加提货点",visible:t.dialogFormVisible,width:"750px"},on:{"update:visible":function(e){t.dialogFormVisible=e},close:t.cancel},model:{value:t.dialogFormVisible,callback:function(e){t.dialogFormVisible=e},expression:"dialogFormVisible"}},[t.dialogFormVisible?[r("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],ref:"ruleForm",staticClass:"demo-ruleForm",attrs:{model:t.ruleForm,rules:t.rules,"label-width":"150px"},nativeOn:{submit:function(t){t.preventDefault()}}},[r("el-form-item",{attrs:{label:"提货点名称：",prop:"name"}},[r("el-input",{staticClass:"dialogWidth",attrs:{maxlength:"40",placeholder:"请输入提货点名称"},model:{value:t.ruleForm.name,callback:function(e){t.$set(t.ruleForm,"name",e)},expression:"ruleForm.name"}})],1),t._v(" "),r("el-form-item",{attrs:{label:"提货点简介："}},[r("el-input",{staticClass:"dialogWidth",attrs:{maxlength:"100",placeholder:"请输入提货点简介"},model:{value:t.ruleForm.introduction,callback:function(e){t.$set(t.ruleForm,"introduction",e)},expression:"ruleForm.introduction"}})],1),t._v(" "),r("el-form-item",{attrs:{label:"提货点手机号：",prop:"phone"}},[r("el-input",{staticClass:"dialogWidth",attrs:{placeholder:"请输入提货点手机号"},model:{value:t.ruleForm.phone,callback:function(e){t.$set(t.ruleForm,"phone",e)},expression:"ruleForm.phone"}})],1),t._v(" "),r("el-form-item",{attrs:{label:"提货点地址：",prop:"address"}},[r("el-cascader",{staticClass:"dialogWidth",attrs:{clearable:"",options:t.addresData,props:{value:"name",label:"name",children:"child",expandTrigger:"hover"}},on:{change:t.handleChange},model:{value:t.ruleForm.address,callback:function(e){t.$set(t.ruleForm,"address",e)},expression:"ruleForm.address"}})],1),t._v(" "),r("el-form-item",{attrs:{label:"详细地址：",prop:"detailedAddress"}},[r("el-input",{staticClass:"dialogWidth",attrs:{placeholder:"请输入详细地址"},model:{value:t.ruleForm.detailedAddress,callback:function(e){t.$set(t.ruleForm,"detailedAddress",e)},expression:"ruleForm.detailedAddress"}})],1),t._v(" "),r("el-form-item",{attrs:{label:"提货点营业："}},[r("el-time-picker",{attrs:{"is-range":"","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",placeholder:"请选择时间营业时间","value-format":"HH:mm:ss"},on:{change:t.onchangeTime},model:{value:t.dayTime,callback:function(e){t.dayTime=e},expression:"dayTime"}})],1),t._v(" "),r("el-form-item",{attrs:{label:"提货点logo："}},[r("div",{staticClass:"upLoadPicBox",on:{click:function(e){return t.modalPicTap("1")}}},[t.ruleForm.image?r("div",{staticClass:"pictrue"},[r("img",{attrs:{src:t.ruleForm.image}})]):r("div",{staticClass:"upLoad"},[r("i",{staticClass:"el-icon-camera cameraIconfont"})])])]),t._v(" "),r("el-form-item",{attrs:{label:"经纬度：",prop:"latitude"}},[r("el-tooltip",{attrs:{content:"请点击查找位置选择位置"}},[r("el-input",{staticClass:"dialogWidth",attrs:{placeholder:"请查找位置",readOnly:""},model:{value:t.ruleForm.latitude,callback:function(e){t.$set(t.ruleForm,"latitude",e)},expression:"ruleForm.latitude"}},[r("el-button",{attrs:{slot:"append"},on:{click:t.onSearch},slot:"append"},[t._v("查找位置")])],1)],1)],1)],1),t._v(" "),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{on:{click:t.cancel}},[t._v("取 消")]),t._v(" "),t.id?r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:system:store:update"],expression:"['admin:system:store:update']"}],attrs:{type:"primary"},on:{click:function(e){return t.editForm("ruleForm")}}},[t._v("修改")]):r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:system:store:save"],expression:"['admin:system:store:save']"}],attrs:{type:"primary"},on:{click:function(e){return t.submitForm("ruleForm")}}},[t._v("提交")])],1),t._v(" "),r("el-dialog",{staticClass:"mapBox",attrs:{title:"上传经纬度",visible:t.modalMap,"append-to-body":"",width:"500px"},on:{"update:visible":function(e){t.modalMap=e}},model:{value:t.modalMap,callback:function(e){t.modalMap=e},expression:"modalMap"}},[r("iframe",{attrs:{id:"mapPage",width:"100%",height:"100%",frameborder:"0",src:t.keyUrl}})])]:t._e()],2)},o=[],s=r("6537"),l=r("2f2c"),c=r("2b9b"),u=r("5317"),d=r("61f7");function m(t){return m="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},m(t)}function f(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */f=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",l=i.toStringTag||"@@toStringTag";function c(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(t){c=function(t,e,r){return t[e]=r}}function u(t,e,r,n){var i=e&&e.prototype instanceof b?e:b,o=Object.create(i.prototype),s=new $(n||[]);return a(o,"_invoke",{value:S(t,r,s)}),o}function d(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=u;var p="suspendedStart",h="suspendedYield",v="executing",g="completed",y={};function b(){}function w(){}function x(){}var F={};c(F,o,(function(){return this}));var O=Object.getPrototypeOf,_=O&&O(O(T([])));_&&_!==r&&n.call(_,o)&&(F=_);var j=x.prototype=b.prototype=Object.create(F);function k(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function L(t,e){function r(a,i,o,s){var l=d(t[a],t,i);if("throw"!==l.type){var c=l.arg,u=c.value;return u&&"object"==m(u)&&n.call(u,"__await")?e.resolve(u.__await).then((function(t){r("next",t,o,s)}),(function(t){r("throw",t,o,s)})):e.resolve(u).then((function(t){c.value=t,o(c)}),(function(t){return r("throw",t,o,s)}))}s(l.arg)}var i;a(this,"_invoke",{value:function(t,n){function a(){return new e((function(e,a){r(t,n,e,a)}))}return i=i?i.then(a,a):a()}})}function S(e,r,n){var a=p;return function(i,o){if(a===v)throw Error("Generator is already running");if(a===g){if("throw"===i)throw o;return{value:t,done:!0}}for(n.method=i,n.arg=o;;){var s=n.delegate;if(s){var l=C(s,n);if(l){if(l===y)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===p)throw a=g,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=v;var c=d(e,r,n);if("normal"===c.type){if(a=n.done?g:h,c.arg===y)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(a=g,n.method="throw",n.arg=c.arg)}}}function C(e,r){var n=r.method,a=e.iterator[n];if(a===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,C(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),y;var i=d(a,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,y;var o=i.arg;return o?o.done?(r[e.resultName]=o.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,y):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function P(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function E(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function $(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(P,this),this.reset(!0)}function T(e){if(e||""===e){var r=e[o];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,i=function r(){for(;++a<e.length;)if(n.call(e,a))return r.value=e[a],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(m(e)+" is not iterable")}return w.prototype=x,a(j,"constructor",{value:x,configurable:!0}),a(x,"constructor",{value:w,configurable:!0}),w.displayName=c(x,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,x):(t.__proto__=x,c(t,l,"GeneratorFunction")),t.prototype=Object.create(j),t},e.awrap=function(t){return{__await:t}},k(L.prototype),c(L.prototype,s,(function(){return this})),e.AsyncIterator=L,e.async=function(t,r,n,a,i){void 0===i&&(i=Promise);var o=new L(u(t,r,n,a),i);return e.isGeneratorFunction(r)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},k(j),c(j,l,"Generator"),c(j,o,(function(){return this})),c(j,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=T,$.prototype={constructor:$,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(E),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function a(n,a){return s.type="throw",s.arg=e,r.next=n,a&&(r.method="next",r.arg=t),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],s=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var l=n.call(o,"catchLoc"),c=n.call(o,"finallyLoc");if(l&&c){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(l){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=t,o.arg=e,i?(this.method="next",this.next=i.finallyLoc,y):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),y},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),E(r),y}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var a=n.arg;E(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:T(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),y}},e}function p(t,e,r,n,a,i,o){try{var s=t[i](o),l=s.value}catch(t){return void r(t)}s.done?e(l):Promise.resolve(l).then(n,a)}function h(t){return function(){var e=this,r=arguments;return new Promise((function(n,a){var i=t.apply(e,r);function o(t){p(i,n,a,o,s,"next",t)}function s(t){p(i,n,a,o,s,"throw",t)}o(void 0)}))}}var v={name:"index",components:{Templates:u["a"]},data:function(){var t=this,e=function(t,e,r){if(!e)return r(new Error("请填写手机号"));/^1[3456789]\d{9}$/.test(e)?r():r(new Error("手机号格式不正确!"))},r=function(e,r,n){t.ruleForm.image?n():n(new Error("请上传提货点logo"))};return{loading:!1,dialogFormVisible:!1,modalMap:!1,keyUrl:"",addresData:[],ruleForm:{name:"",introduction:"",phone:"",address:"",detailedAddress:"",dayTime:"",image:"",latitude:""},id:0,dayTime:["",""],rules:{name:[{required:!0,message:"请输入提货点名称",trigger:"blur"}],address:[{required:!0,message:"请选择提货点地址",trigger:"change"}],dayTime:[{required:!0,type:"array",message:"请选择提货点营业时间",trigger:"change"}],phone:[{required:!0,validator:e,trigger:"blur"}],detailedAddress:[{required:!0,message:"请输入详细地址",trigger:"blur"}],image:[{required:!0,validator:r,trigger:"change"}],latitude:[{required:!0,message:"请选择经纬度",trigger:"blur"}]}}},created:function(){this.ruleForm.image="";var t=JSON.parse(sessionStorage.getItem("cityList"));this.addresData=t,this.getCityList(),this.getKey()},mounted:function(){window.addEventListener("message",(function(t){var e=t.data;e&&"locationPicker"===e.module&&window.parent.selectAdderss(e)}),!1),window.selectAdderss=this.selectAdderss},methods:{getInfo:function(t){var e=this,r=this;r.id=t,this.loading=!0,Object(s["e"])({id:t}).then((function(t){r.ruleForm=t,r.ruleForm.address=t.address.split(","),r.dayTime=t.dayTime.split(","),e.loading=!1}))},cancel:function(){this.dialogFormVisible=!1,this.clearFrom(),this.ruleForm.image="",this.resetForm("ruleForm"),this.id=0},resetForm:function(t){this.$refs[t].resetFields()},submitForm:Object(d["a"])((function(t){var e=this;this.$refs[t].validate((function(r){if(!r)return!1;Object(s["h"])(e.ruleForm).then(h(f().mark((function r(){return f().wrap((function(r){while(1)switch(r.prev=r.next){case 0:e.$message.success("提交成功"),e.dialogFormVisible=!1,e.$parent.tableList(),e.$parent.storeGetCount(),e.clearFrom(),e.resetForm(t),e.id=0;case 7:case"end":return r.stop()}}),r)}))))}))})),editForm:Object(d["a"])((function(t){var e=this;this.$refs[t].validate((function(r){if(!r)return!1;e.handleChange(e.ruleForm.address),Object(s["o"])(e.ruleForm,e.id).then(h(f().mark((function r(){return f().wrap((function(r){while(1)switch(r.prev=r.next){case 0:e.$message.success("编辑成功"),e.dialogFormVisible=!1,e.$parent.tableList(),e.clearFrom(),e.resetForm(t),e.id=0;case 6:case"end":return r.stop()}}),r)}))))}))})),clearFrom:function(){this.ruleForm.introduction="",this.dayTime=["",""]},handleChange:function(t){var e=t[0],r=t[1],n=t[2];2===t.length?this.ruleForm.address=e+","+r:3===t.length&&(this.ruleForm.address=e+","+r+","+n)},onchangeTime:function(t){this.ruleForm.dayTime=t?t.join(","):""},modalPicTap:function(t){var e=this;this.$modalUpload((function(t){e.ruleForm.image=t[0].sattDir}),t,"system")},onSearch:function(){this.modalMap=!0},selectAdderss:function(t){this.ruleForm.latitude=t.latlng.lat+","+t.latlng.lng,this.modalMap=!1},getKey:function(){var t=this,e={id:74};Object(c["b"])(e).then(function(){var e=h(f().mark((function e(r){var n;return f().wrap((function(e){while(1)switch(e.prev=e.next){case 0:n=r.tengxun_map_key,t.keyUrl="https://apis.map.qq.com/tools/locpicker?type=1&key=".concat(n,"&referer=myapp");case 2:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}())},getCityList:function(){var t=this;l["c"]().then((function(e){sessionStorage.setItem("cityList",JSON.stringify(e));var r=JSON.parse(sessionStorage.getItem("cityList"));t.addresData=r})).catch((function(e){t.$message.error(e.message)}))}}},g=v,y=(r("36d0"),r("2877")),b=Object(y["a"])(g,i,o,!1,null,null,null),w=b.exports,x=r("e350"),F={name:"Point",components:{systemStore:w},data:function(){return{artFrom:{page:1,limit:20,status:"1",keywords:""},loading:!1,tableData:[],total:0,headerCount:{}}},created:function(){this.storeGetCount(),this.tableList()},methods:{checkPermi:x["a"],storeGetCount:function(){var t=this;Object(s["d"])().then((function(e){t.headerCount=e}))},tableList:function(){var t=this;t.loading=!0,Object(s["f"])(t.artFrom).then((function(e){t.loading=!1,t.tableData=e.list,t.total=e.total}))},pageChange:function(t){this.artFrom.page=t,this.tableList()},sizeChange:function(t){this.artFrom.limit=t,this.tableList()},onClickTab:function(){this.artFrom.keywords="",this.tableList()},search:function(){this.artFrom.page=1,this.tableList()},onchangeIsShow:function(t,e){var r=this;Object(s["p"])({id:t,status:e}).then((function(){r.$message.success("操作成功"),r.tableList(),r.storeGetCount()})).catch((function(){row.isShow=!row.isShow}))},storeRecovery:function(t){var e=this;this.$modalSure("恢复提货吗").then((function(){Object(s["g"])({id:t}).then((function(){e.$message.success("恢复成功"),e.storeGetCount(),e.tableList()}))}))},storeDelete:function(t){var e=this;e.$modalSure("删除提货点吗？").then((function(){Object(s["c"])({id:t}).then((function(){e.$message.success("删除成功"),e.storeGetCount(),e.tableList()}))}))},allDelete:function(t){var e=this;this.$modalSure().then((function(){Object(s["a"])({id:t}).then((function(){e.$message.success("删除成功"),e.storeGetCount(),e.tableList()}))}))},add:function(){this.$refs.template.dialogFormVisible=!0},edit:function(t){this.$refs.template.dialogFormVisible=!0,this.$refs.template.getInfo(t)}}},O=F,_=Object(y["a"])(O,n,a,!1,null,"6029dce8",null);e["default"]=_.exports},f8ef:function(t,e,r){}}]);