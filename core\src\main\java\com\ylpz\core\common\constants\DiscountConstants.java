package com.ylpz.core.common.constants;

/**
 * 折扣常量类
 */
public class DiscountConstants {

    /**
     * ---------------------------------------
     * --------折扣常量----------------------
     * ---------------------------------------
     */

    /** 折扣类型—手动领取 */
    public static final Integer DISCOUNT_TYPE_RECEIVE = 1;

    /** 折扣类型—新人折扣 */
    public static final Integer DISCOUNT_TYPE_NEW_PEOPLE = 2;

    /** 折扣类型—赠送折扣 */
    public static final Integer DISCOUNT_TYPE_GIVE_AWAY = 3;

    /** 折扣使用类型-通用 */
    public static final Integer DISCOUNT_USE_TYPE_COMMON = 1;

    /** 折扣使用类型-商品 */
    public static final Integer DISCOUNT_USE_TYPE_PRODUCT = 2;

    /** 折扣使用类型-品类 */
    public static final Integer DISCOUNT_USE_TYPE_CATEGORY = 3;


    /**
     * ---------------------------------------
     * --------用户折扣常量-------------------
     * ---------------------------------------
     */

    /** 用户折扣领取类型—用户注册 */
    public static final String STORE_DISCOUNT_USER_TYPE_REGISTER = "new";

    /** 用户折扣领取类型—用户领取 */
    public static final String STORE_DISCOUNT_USER_TYPE_GET = "receive";

    /** 用户折扣领取类型—后台发放 */
    public static final String STORE_DISCOUNT_USER_TYPE_SEND = "send";

    /** 用户折扣领取类型—买赠送 */
    public static final String STORE_DISCOUNT_USER_TYPE_BUY = "buy";

    /** 用户折扣状态—未使用 */
    public static final Integer STORE_DISCOUNT_USER_STATUS_USABLE = 0;

    /** 用户折扣状态—已使用 */
    public static final Integer STORE_DISCOUNT_USER_STATUS_USED = 1;

    /** 用户折扣状态—已失效 */
    public static final Integer STORE_DISCOUNT_USER_STATUS_LAPSED = 2;
} 