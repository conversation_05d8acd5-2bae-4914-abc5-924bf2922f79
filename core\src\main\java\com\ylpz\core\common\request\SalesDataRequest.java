package com.ylpz.core.common.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 销售数据查询请求对象
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "SalesDataRequest对象", description = "销售数据查询请求对象")
public class SalesDataRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "手机号，支持模糊查询")
    private String mobile;

    @ApiModelProperty(value = "开始时间，格式：yyyy-MM-dd HH:mm:ss")
    private String startTime;

    @ApiModelProperty(value = "结束时间，格式：yyyy-MM-dd HH:mm:ss")
    private String endTime;

    @ApiModelProperty(value = "会员等级：1-VIP会员，2-SVIP会员")
    private Integer memberLevel;

    @ApiModelProperty(value = "用户昵称，支持模糊查询")
    private String nickname;

    @ApiModelProperty(value = "用户ID")
    private Integer uid;
}
