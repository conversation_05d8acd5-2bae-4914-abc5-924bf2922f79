(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-828d253a"],{"125f":function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"divBox"},[i("el-card",{staticClass:"box-card"},[i("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[i("div",{staticClass:"container"},[i("el-form",{attrs:{inline:"",size:"small"}},[i("el-form-item",{attrs:{label:"商品组合名："}},[i("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入组合名",clearable:""},model:{value:t.tableFrom.name,callback:function(e){t.$set(t.tableFrom,"name",e)},expression:"tableFrom.name"}},[i("el-button",{attrs:{slot:"append",icon:"el-icon-search"},on:{click:t.seachList},slot:"append"})],1)],1)],1)],1),t._v(" "),i("div",{staticClass:"acea-row"},[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:product:rule:save"],expression:"['admin:product:rule:save']"}],attrs:{size:"small",type:"primary"},on:{click:t.add}},[t._v("组合商品")])],1)]),t._v(" "),i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"table",staticStyle:{width:"100%"},attrs:{data:t.tableData.data,size:"mini","highlight-current-row":""},on:{"selection-change":t.handleSelectionChange}},[i("el-table-column",{attrs:{prop:"id",label:"ID","min-width":"60"}}),t._v(" "),i("el-table-column",{attrs:{prop:"name",label:"搭配组合名称","min-width":"150"}}),t._v(" "),i("el-table-column",{attrs:{prop:"combinationPrice",label:"组合优惠价","min-width":"150"}}),t._v(" "),i("el-table-column",{attrs:{prop:"startTime",label:"售卖开始时间","min-width":"150"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(t.parseTime(e.row.startTime,"{y}-{m}-{d} {h}:{i}:{s}")))])]}}])}),t._v(" "),i("el-table-column",{attrs:{prop:"endTime",label:"售卖结束时间","min-width":"150"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(t.parseTime(e.row.endTime,"{y}-{m}-{d} {h}:{i}:{s}")))])]}}])}),t._v(" "),i("el-table-column",{attrs:{prop:"initialSales",label:"初始已售数量","min-width":"150"}}),t._v(" "),i("el-table-column",{attrs:{label:"是否启用","min-width":"120",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-switch",{attrs:{width:60,"active-value":1,"inactive-value":0,"active-text":"启用","inactive-text":"禁用"},on:{change:function(i){return t.onchangeIsShow(e.row)}},model:{value:e.row.status,callback:function(i){t.$set(e.row,"status",i)},expression:"scope.row.status"}})]}}])}),t._v(" "),i("el-table-column",{attrs:{label:"操作","min-width":"120",align:"center",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:product:rule:update","admin:product:rule:info"],expression:"[\n              'admin:product:rule:update',\n              'admin:product:rule:info',\n            ]"}],staticClass:"mr10",attrs:{type:"text",size:"small"},on:{click:function(i){return t.onEdit(e.row)}}},[t._v("编辑")]),t._v(" "),i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:product:rule:delete"],expression:"['admin:product:rule:delete']"}],attrs:{type:"text",size:"small"},on:{click:function(i){return t.handleDelete(e.row.id,e.$index)}}},[t._v("删除")])]}}])})],1),t._v(" "),i("div",{staticClass:"block"},[i("el-pagination",{attrs:{"page-sizes":[20,40,60,80],"page-size":t.tableFrom.limit,"current-page":t.tableFrom.page,layout:"total, sizes, prev, pager, next, jumper",total:t.tableData.total},on:{"size-change":t.handleSizeChange,"current-change":t.pageChange}})],1),t._v(" "),i("el-dialog",{attrs:{title:0===t.isCreate?"组合商品":"编辑组合商品",visible:t.dialogVisible,width:"700px","z-index":"4"},on:{"update:visible":function(e){t.dialogVisible=e}}},[t.dialogVisible?i("creatCombinationProduct",{key:t.timer,attrs:{"is-create":t.isCreate,"edit-data":t.editData},on:{getList:t.seachList}}):t._e()],1)],1)],1)},n=[],r=(i("73f5"),function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],ref:"formValidate",staticClass:"demo-formValidate",attrs:{model:t.formValidate,rules:t.rules,"label-width":"120px"}},[i("el-form-item",{attrs:{label:"搭配组合名称：",prop:"name"}},[i("el-input",{attrs:{type:"text"},model:{value:t.formValidate.name,callback:function(e){t.$set(t.formValidate,"name",e)},expression:"formValidate.name"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"搭配组合描述：",prop:"description"}},[i("el-input",{attrs:{type:"text"},model:{value:t.formValidate.description,callback:function(e){t.$set(t.formValidate,"description",e)},expression:"formValidate.description"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"搭配组合商品：",prop:"productIds"}},[i("div",{staticClass:"acea-row"},[t.formValidate.productIds.length?t._l(t.formValidate.productIds,(function(e,a){return i("div",{key:a,staticClass:"pictrue"},[i("img",{attrs:{src:e.image}}),t._v(" "),i("i",{staticClass:"el-icon-error btndel",on:{click:function(e){return t.handleRemove(a)}}})])})):t._e(),t._v(" "),i("div",{staticClass:"upLoadPicBox",on:{click:t.changeGood}},[i("div",{staticClass:"upLoad"},[i("i",{staticClass:"el-icon-camera cameraIconfont"})])])],2)]),t._v(" "),i("el-form-item",{attrs:{label:"组合优惠价：",prop:"combinationPrice"}},[i("el-input",{attrs:{type:"text"},model:{value:t.formValidate.combinationPrice,callback:function(e){t.$set(t.formValidate,"combinationPrice",e)},expression:"formValidate.combinationPrice"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"初始已售数量："}},[i("el-input",{attrs:{type:"text"},model:{value:t.formValidate.initialSales,callback:function(e){t.$set(t.formValidate,"initialSales",e)},expression:"formValidate.initialSales"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"售卖开始时间："}},[i("el-date-picker",{attrs:{type:"datetime",placeholder:"请选择"},model:{value:t.formValidate.startTime,callback:function(e){t.$set(t.formValidate,"startTime",e)},expression:"formValidate.startTime"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"售卖结束时间："}},[i("el-date-picker",{attrs:{type:"datetime",placeholder:"请选择"},model:{value:t.formValidate.endTime,callback:function(e){t.$set(t.formValidate,"endTime",e)},expression:"formValidate.endTime"}})],1),t._v(" "),i("el-form-item",[i("el-button",{attrs:{size:"mini",type:"primary",loading:t.loadingbtn},on:{click:function(e){return t.submitForm("formValidate")}}},[t._v("提交")]),t._v(" "),i("el-button",{attrs:{size:"mini"},on:{click:function(e){return t.resetForm("formValidate")}}},[t._v("重置")])],1)],1)}),o=[],l=i("85e3"),s=i("61f7");function c(t){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},c(t)}function u(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,a)}return i}function d(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?u(Object(i),!0).forEach((function(e){m(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):u(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}function m(t,e,i){return(e=f(e))in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function f(t){var e=p(t,"string");return"symbol"==c(e)?e:e+""}function p(t,e){if("object"!=c(t)||!t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var a=i.call(t,e||"default");if("object"!=c(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var b={name:"",description:"",startTime:null,endTime:null,initialSales:null,combinationPrice:null,productIds:[]},h={name:"creatComment",props:{isCreate:{type:Number,default:0},editData:{type:Object,dfault:function(){return{}}}},data:function(){return{loadingbtn:!1,loading:!1,pics:[],image:"",formValidate:Object.assign({},b),rules:{productIds:[{required:!0,message:"请至少选择一个商品",trigger:"change",type:"array"}],name:[{required:!0,message:"请填写搭配组合名称",trigger:"blur"}],description:[{required:!0,message:"请填写搭配组合描述",trigger:"blur"}],combinationPrice:[{required:!0,message:"请填写组合优惠价格",trigger:"blur"}]}}},watch:{isCreate:{handler:function(t){var e=this;0===t?this.$nextTick((function(){e.resetForm("formValidate")})):(this.formValidate=Object.assign({},this.editData),this.formValidate.productIds=this.editData.productDetailList.map((function(t){return{id:t.id,name:t.storeName,description:t.description,image:t.image}})))},immediate:!0}},methods:{changeGood:function(){var t=this;this.$modalGoodList((function(e){t.formValidate.productIds=e}),"many",t.formValidate.productIds)},handleRemove:function(t){this.formValidate.productIds.splice(t,1)},submitForm:Object(s["a"])((function(t){var e=this,i=this.formValidate.productIds.map((function(t){return t.id}));this.$refs[t].validate((function(t){if(!t)return!1;e.loadingbtn=!0,0===e.isCreate?Object(l["a"])(d(d({},e.formValidate),{},{productIds:i})).then((function(){e.$message.success("新增成功"),setTimeout((function(){e.$emit("getList")}),600),e.loadingbtn=!1})).catch((function(){e.loadingbtn=!1})):Object(l["d"])(d(d({},e.formValidate),{},{productIds:i})).then((function(){e.$message.success("修改成功"),setTimeout((function(){e.$emit("getList")}),600),e.loadingbtn=!1})).catch((function(){e.loadingbtn=!1}))}))})),resetForm:function(t){this.$refs[t].resetFields()}}},g=h,v=(i("8c86"),i("2877")),y=Object(v["a"])(g,r,o,!1,null,"11b50ebf",null),w=y.exports;function O(t){return O="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},O(t)}function S(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,a)}return i}function V(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?S(Object(i),!0).forEach((function(e){P(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):S(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}function P(t,e,i){return(e=_(e))in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function _(t){var e=j(t,"string");return"symbol"==O(e)?e:e+""}function j(t,e){if("object"!=O(t)||!t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var a=i.call(t,e||"default");if("object"!=O(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var x={name:"StoreAttr",components:{creatCombinationProduct:w},data:function(){return{formDynamic:{ruleName:"",ruleValue:[]},tableFrom:{page:1,limit:20,name:""},tableData:{data:[],loading:!1,total:0},listLoading:!0,selectionList:[],multipleSelectionAll:[],idKey:"id",nextPageFlag:!1,keyNum:0,dialogVisible:!1,isCreate:0,editData:{},timer:""}},mounted:function(){this.getList()},methods:{seachList:function(){this.tableFrom.page=1,this.dialogVisible=!1,this.getList()},handleSelectionChange:function(t){var e=this;this.selectionList=t,setTimeout((function(){e.changePageCoreRecordData()}),50)},onchangeIsShow:function(t){var e=this;console.log(t.status,8686868),t.status?Object(l["c"])(t.id,1).then((function(){e.$message.success("启用成功"),e.getList()})).catch((function(){t.status=!t.status})):Object(l["c"])(t.id,0).then((function(){e.$message.success("禁用成功"),e.getList()})).catch((function(){t.status=!t.status}))},setSelectRow:function(){if(this.multipleSelectionAll&&!(this.multipleSelectionAll.length<=0)){var t=this.idKey,e=[];this.multipleSelectionAll.forEach((function(i){e.push(i[t])})),this.$refs.table.clearSelection();for(var i=0;i<this.tableData.data.length;i++)e.indexOf(this.tableData.data[i][t])>=0&&this.$refs.table.toggleRowSelection(this.tableData.data[i],!0)}},changePageCoreRecordData:function(){var t=this.idKey,e=this;if(this.multipleSelectionAll.length<=0)this.multipleSelectionAll=this.selectionList;else{var i=[];this.multipleSelectionAll.forEach((function(e){i.push(e[t])}));var a=[];this.selectionList.forEach((function(n){a.push(n[t]),i.indexOf(n[t])<0&&e.multipleSelectionAll.push(n)}));var n=[];this.tableData.data.forEach((function(e){a.indexOf(e[t])<0&&n.push(e[t])})),n.forEach((function(a){if(i.indexOf(a)>=0)for(var n=0;n<e.multipleSelectionAll.length;n++)if(e.multipleSelectionAll[n][t]==a){e.multipleSelectionAll.splice(n,1);break}}))}},add:function(){this.dialogVisible=!0,this.isCreate=0,this.timer=(new Date).getTime()},getList:function(){var t=this;this.listLoading=!0,Object(l["e"])(this.tableFrom).then((function(e){var i=e.list;t.tableData.data=i,t.tableData.total=e.total;for(var a=0;a<i.length;a++)i[a].ruleValue=JSON.parse(i[a].ruleValue);t.$nextTick((function(){this.setSelectRow()})),t.listLoading=!1})).catch((function(){t.listLoading=!1}))},pageChange:function(t){this.changePageCoreRecordData(),this.tableFrom.page=t,this.getList()},handleSizeChange:function(t){this.changePageCoreRecordData(),this.tableFrom.limit=t,this.getList()},handleDelete:function(t,e){var i=this;this.$modalSure().then((function(){Object(l["b"])(t).then((function(){i.$message.success("删除成功"),i.tableData.data.splice(e,1)}))})).catch((function(){}))},onEdit:function(t){this.dialogVisible=!0,this.isCreate=1,this.editData=V({},t)}}},C=x,k=(i("a7d1"),Object(v["a"])(C,a,n,!1,null,"43fbb179",null));e["default"]=k.exports},"8c86":function(t,e,i){"use strict";i("bd5e")},a7d1:function(t,e,i){"use strict";i("ecd9")},bd5e:function(t,e,i){},ecd9:function(t,e,i){}}]);