package com.ylpz.admin.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.ylpz.core.common.request.SystemUserLevelRequest;
import com.ylpz.core.common.request.SystemUserLevelUpdateShowRequest;
import com.ylpz.core.common.response.CommonResult;
import com.ylpz.core.service.StoreCouponService;
import com.ylpz.core.service.SystemUserLevelService;
import com.ylpz.model.coupon.StoreCoupon;
import com.ylpz.model.system.SystemUserLevel;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * 设置用户等级表 前端控制器
 */
@Slf4j
@RestController
@RequestMapping("api/admin/system/user/level")
@Api(tags = "设置 -- 会员等级")
public class SystemUserLevelController {

    @Autowired
    private SystemUserLevelService systemUserLevelService;

    @Autowired
    private StoreCouponService storeCouponService;

    /**
     * 分页显示设置用户等级表
     */
    @PreAuthorize("hasAuthority('admin:system:user:level:list')")
    @ApiOperation(value = "分页列表")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public CommonResult<List<SystemUserLevel>> getList() {
        return CommonResult.success(systemUserLevelService.getList());
    }

    /**
     * 新增等级
     */
    @PreAuthorize("hasAuthority('admin:system:user:level:save')")
    @ApiOperation(value = "新增等级")
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public CommonResult<Object> save(@RequestBody @Validated SystemUserLevelRequest request) {
        if (systemUserLevelService.create(request)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 删除等级
     * 
     * @param id 等级id
     */
    @PreAuthorize("hasAuthority('admin:system:user:level:delete')")
    @ApiOperation(value = "删除等级")
    @RequestMapping(value = "/delete/{id}", method = RequestMethod.POST)
    public CommonResult<Object> delete(@PathVariable(value = "id") Integer id) {
        if (systemUserLevelService.delete(id)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 更新等级
     */
    @PreAuthorize("hasAuthority('admin:system:user:level:update')")
    @ApiOperation(value = "更新等级")
    @RequestMapping(value = "/update/{id}", method = RequestMethod.POST)
    public CommonResult<Object> update(@PathVariable(value = "id") Integer id,
        @RequestBody @Validated SystemUserLevelRequest request) {
        if (systemUserLevelService.update(id, request)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 使用/禁用
     */
    @PreAuthorize("hasAuthority('admin:system:user:level:use')")
    @ApiOperation(value = "使用/禁用")
    @RequestMapping(value = "/use", method = RequestMethod.POST)
    public CommonResult<Object> use(@RequestBody @Validated SystemUserLevelUpdateShowRequest request) {
        if (systemUserLevelService.updateShow(request)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 获取会员等级详情
     * 
     * @param id 等级id
     */
    // @PreAuthorize("hasAuthority('admin:system:user:level:info')")
    @ApiOperation(value = "获取会员等级详情")
    @RequestMapping(value = "/info/{id}", method = RequestMethod.GET)
    public CommonResult<Map<String, Object>> info(@PathVariable(value = "id") Integer id) {
        SystemUserLevel level = systemUserLevelService.getByLevelId(id);
        if (level == null) {
            return CommonResult.failed("会员等级不存在");
        }

        // 创建返回结果Map
        Map<String, Object> resultMap = new HashMap<>();

        // 添加用户等级信息
        resultMap.put("userLevel", level);

        // 获取会员券信息
        if (level.getCouponId() != null && level.getCouponEnabled() != null && level.getCouponEnabled()) {
            StoreCoupon memberCoupon = storeCouponService.getById(level.getCouponId());
            if (memberCoupon != null) {
                resultMap.put("memberCoupon", memberCoupon);
            }
        }

        // 获取生日券信息
        if (level.getBirthCouponId() != null && level.getBirthCouponEnabled() != null
            && level.getBirthCouponEnabled()) {
            StoreCoupon birthCoupon = storeCouponService.getById(level.getBirthCouponId());
            if (birthCoupon != null) {
                resultMap.put("birthCoupon", birthCoupon);
            }
        }

        return CommonResult.success(resultMap);
    }
}
