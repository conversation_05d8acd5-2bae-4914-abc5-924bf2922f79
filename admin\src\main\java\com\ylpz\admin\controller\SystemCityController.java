package com.ylpz.admin.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ylpz.core.common.request.SystemCityRequest;
import com.ylpz.core.common.request.SystemCitySearchRequest;
import com.ylpz.core.common.response.CommonResult;
import com.ylpz.core.common.vo.SystemCityTreeVo;
import com.ylpz.core.service.SystemCityService;
import com.ylpz.model.system.SystemCity;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;


/**
 * 城市表 前端控制器
 */
@Slf4j
@RestController
@RequestMapping("api/admin/system/city")
@Api(tags = "城市管理")
public class SystemCityController {

    @Autowired
    private SystemCityService systemCityService;

    /**
     * 分页城市列表
     * @param request 搜索条件
     */
    @PreAuthorize("hasAuthority('admin:system:city:list')")
    @ApiOperation(value = "分页城市列表")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public CommonResult<Object> getList(@Validated SystemCitySearchRequest request) {
        return CommonResult.success(systemCityService.getList(request));
    }
    
    /**
     * 按区域分组获取城市列表
     * @param parentId 父级ID，默认为0
     */
    @ApiOperation(value = "按区域分组获取城市列表")
    @RequestMapping(value = "/list/grouped", method = RequestMethod.GET)
    public CommonResult<Object> getListGroupedByRegion(
            @RequestParam(required = false, defaultValue = "0") Integer parentId) {
        SystemCitySearchRequest request = new SystemCitySearchRequest();
        request.setParentId(parentId);
        // 不设置regionType，让service返回分组数据
        return CommonResult.success(systemCityService.getList(request));
    }
    
    /**
     * 根据区域分类获取城市列表
     * @param regionType 区域分类：1-一二区，2-三区，3-港澳台
     */
    //@PreAuthorize("hasAuthority('admin:system:city:list:byRegion')")
    @ApiOperation(value = "根据区域分类获取城市列表")
    @RequestMapping(value = "/list/region", method = RequestMethod.GET)
    public CommonResult<Object> getListByRegion(@RequestParam(required = false, defaultValue = "0") Integer regionType,
                                                @RequestParam(required = false, defaultValue = "0") Integer parentId) {
        SystemCitySearchRequest request = new SystemCitySearchRequest();
        request.setParentId(parentId);
        request.setRegionType(regionType);
        return CommonResult.success(systemCityService.getList(request));
    }

    /**
     * 修改城市
     * @param id 城市id
     * @param request 修改参数
     */
    @PreAuthorize("hasAuthority('admin:system:city:update')")
    @ApiOperation(value = "修改")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public CommonResult<String> update(@RequestParam Integer id, @Validated SystemCityRequest request) {
        if (systemCityService.update(id, request)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 修改状态
     * @param id 城市id
     * @param status 状态
     */
    @PreAuthorize("hasAuthority('admin:system:city:update:status')")
    @ApiOperation(value = "修改状态")
    @RequestMapping(value = "/update/status", method = RequestMethod.POST)
    public CommonResult<String> updateStatus(@RequestParam Integer id, @RequestParam Boolean status) {
        if (systemCityService.updateStatus(id, status)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 城市详情
     * @param id 城市id
     */
    @PreAuthorize("hasAuthority('admin:system:city:info')")
    @ApiOperation(value = "城市详情")
    @RequestMapping(value = "/info", method = RequestMethod.GET)
    public CommonResult<SystemCity> info(@RequestParam(value = "id") Integer id) {
        return CommonResult.success(systemCityService.getById(id));
    }

    /**
     * 获取tree结构的列表
     */
    @PreAuthorize("hasAuthority('admin:system:city:list:tree')")
    @ApiOperation(value = "获取tree结构的列表")
    @RequestMapping(value = "/list/tree", method = RequestMethod.GET)
    public CommonResult<List<SystemCityTreeVo>> getListTree() {
        return CommonResult.success(systemCityService.getListTree());
    }
}



