(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-96cb8cb0"],{"1db4":function(t,s,a){"use strict";a.r(s);var i=function(){var t=this,s=t.$createElement,a=t._self._c||s;return a("div",{staticClass:"wscn-http404-container"},[a("div",{staticClass:"wscn-http404"},[t._m(0),t._v(" "),a("div",{staticClass:"bullshit"},[a("div",{staticClass:"bullshit__oops"},[t._v("OOPS!")]),t._v(" "),a("div",{staticClass:"bullshit__headline"},[t._v(t._s(t.message))]),t._v(" "),a("div",{staticClass:"bullshit__info"},[t._v("请检查您输入的URL是否正确，或单击下面的按钮返回主页.")]),t._v(" "),a("router-link",{attrs:{to:{path:"/dashboard"}}},[a("span",{staticClass:"bullshit__return-home"},[t._v("返回控制台")])])],1)])])},c=[function(){var t=this,s=t.$createElement,i=t._self._c||s;return i("div",{staticClass:"pic-404"},[i("img",{staticClass:"pic-404__parent",attrs:{src:a("a36b"),alt:"404"}}),t._v(" "),i("img",{staticClass:"pic-404__child left",attrs:{src:a("26fc"),alt:"404"}}),t._v(" "),i("img",{staticClass:"pic-404__child mid",attrs:{src:a("26fc"),alt:"404"}}),t._v(" "),i("img",{staticClass:"pic-404__child right",attrs:{src:a("26fc"),alt:"404"}})])}],n={name:"Page404",data:function(){return{}},computed:{message:function(){return"你不能进入这个页面..."}},methods:{}},l=n,e=(a("d3c3"),a("2877")),r=Object(e["a"])(l,i,c,!1,null,"563e839a",null);s["default"]=r.exports},"26fc":function(t,s,a){t.exports=a.p+"static/img/404_cloud.0f4bc32b.png"},3553:function(t,s,a){},a36b:function(t,s,a){t.exports=a.p+"static/img/404.a57b6f31.png"},d3c3:function(t,s,a){"use strict";a("3553")}}]);