(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5bafe1c0"],{"0b54":function(t,e,r){},2638:function(t,e,r){"use strict";function n(){return n=Object.assign?Object.assign.bind():function(t){for(var e,r=1;r<arguments.length;r++)for(var n in e=arguments[r],e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t},n.apply(this,arguments)}var o=["attrs","props","domProps"],i=["class","style","directives"],a=["on","nativeOn"],s=function(t){return t.reduce((function(t,e){for(var r in e)if(t[r])if(-1!==o.indexOf(r))t[r]=n({},t[r],e[r]);else if(-1!==i.indexOf(r)){var s=t[r]instanceof Array?t[r]:[t[r]],c=e[r]instanceof Array?e[r]:[e[r]];t[r]=[].concat(s,c)}else if(-1!==a.indexOf(r))for(var u in e[r])if(t[r][u]){var d=t[r][u]instanceof Array?t[r][u]:[t[r][u]],f=e[r][u]instanceof Array?e[r][u]:[e[r][u]];t[r][u]=[].concat(d,f)}else t[r][u]=e[r][u];else if("hook"===r)for(var m in e[r])t[r][m]=t[r][m]?l(t[r][m],e[r][m]):e[r][m];else t[r]=e[r];else t[r]=e[r];return t}),{})},l=function(t,e){return function(){t&&t.apply(this,arguments),e&&e.apply(this,arguments)}};t.exports=s},4324:function(t,e,r){},"634a":function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"divBox relative"},[r("el-card",{staticClass:"box-card"},[r("div",{staticClass:"clearfix"},[r("div",{staticClass:"container"},[r("el-form",{attrs:{size:"small","label-width":"100px"}},[t.checkPermi(["admin:order:status:num"])?r("el-form-item",{attrs:{label:"订单状态："}},[r("el-radio-group",{attrs:{type:"button"},on:{change:t.seachList},model:{value:t.tableFrom.status,callback:function(e){t.$set(t.tableFrom,"status",e)},expression:"tableFrom.status"}},[r("el-radio-button",{attrs:{label:"all"}},[t._v("全部\n                "+t._s(t.orderChartType.all))]),t._v(" "),r("el-radio-button",{attrs:{label:"unPaid"}},[t._v("未支付\n                "+t._s(t.orderChartType.unPaid))]),t._v(" "),r("el-radio-button",{attrs:{label:"notShipped"}},[t._v("未发货\n                "+t._s(t.orderChartType.notShipped))]),t._v(" "),r("el-radio-button",{attrs:{label:"spike"}},[t._v("待收货\n                "+t._s(t.orderChartType.spike))]),t._v(" "),r("el-radio-button",{attrs:{label:"bargain"}},[t._v("待评价\n                "+t._s(t.orderChartType.bargain))]),t._v(" "),r("el-radio-button",{attrs:{label:"complete"}},[t._v("交易完成\n                "+t._s(t.orderChartType.complete))]),t._v(" "),r("el-radio-button",{attrs:{label:"toBeWrittenOff"}},[t._v("待核销\n                "+t._s(t.orderChartType.toBeWrittenOff))]),t._v(" "),r("el-radio-button",{attrs:{label:"refunding"}},[t._v("退款中\n                "+t._s(t.orderChartType.refunding))]),t._v(" "),r("el-radio-button",{attrs:{label:"refunded"}},[t._v("已退款\n                "+t._s(t.orderChartType.refunded))]),t._v(" "),r("el-radio-button",{attrs:{label:"deleted"}},[t._v("已删除\n                "+t._s(t.orderChartType.deleted))])],1)],1):t._e(),t._v(" "),r("el-form-item",{staticClass:"width100",attrs:{label:"时间选择："}},[r("el-radio-group",{staticClass:"mr20",attrs:{type:"button",size:"small"},on:{change:function(e){return t.selectChange(t.tableFrom.dateLimit)}},model:{value:t.tableFrom.dateLimit,callback:function(e){t.$set(t.tableFrom,"dateLimit",e)},expression:"tableFrom.dateLimit"}},t._l(t.fromList.fromTxt,(function(e,n){return r("el-radio-button",{key:n,attrs:{label:e.val}},[t._v(t._s(e.text))])})),1),t._v(" "),r("el-date-picker",{staticStyle:{width:"220px"},attrs:{"value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",size:"small",type:"daterange",placement:"bottom-end",placeholder:"自定义时间"},on:{change:t.onchangeTime},model:{value:t.timeVal,callback:function(e){t.timeVal=e},expression:"timeVal"}})],1),t._v(" "),r("el-row",[r("el-col",{attrs:{span:11}},[r("el-form-item",{staticClass:"width100",attrs:{label:"商品名称："}},[r("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入商品名称",size:"small",clearable:""},model:{value:t.tableFrom.storeName,callback:function(e){t.$set(t.tableFrom,"storeName",e)},expression:"tableFrom.storeName"}},[r("el-button",{attrs:{slot:"append",icon:"el-icon-search",size:"small"},on:{click:t.seachList},slot:"append"})],1)],1)],1),t._v(" "),r("el-col",{attrs:{span:11}},[r("el-form-item",{staticClass:"width100",attrs:{label:"订单号："}},[r("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入订单号",size:"small",clearable:""},model:{value:t.tableFrom.orderNo,callback:function(e){t.$set(t.tableFrom,"orderNo",e)},expression:"tableFrom.orderNo"}},[r("el-button",{attrs:{slot:"append",icon:"el-icon-search",size:"small"},on:{click:t.seachList},slot:"append"})],1)],1)],1),t._v(" "),r("el-col",{attrs:{span:11}},[r("el-form-item",{staticClass:"width100",attrs:{label:"快递单号："}},[r("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入快递单号",size:"small",clearable:""},model:{value:t.tableFrom.expressNo,callback:function(e){t.$set(t.tableFrom,"expressNo",e)},expression:"tableFrom.expressNo"}},[r("el-button",{attrs:{slot:"append",icon:"el-icon-search",size:"small"},on:{click:t.seachList},slot:"append"})],1)],1)],1),t._v(" "),r("el-col",{attrs:{span:11}},[r("el-form-item",{staticClass:"width100",attrs:{label:"订单备注："}},[r("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入订单备注",size:"small",clearable:""},model:{value:t.tableFrom.remark,callback:function(e){t.$set(t.tableFrom,"remark",e)},expression:"tableFrom.remark"}},[r("el-button",{attrs:{slot:"append",icon:"el-icon-search",size:"small"},on:{click:t.seachList},slot:"append"})],1)],1)],1)],1),t._v(" "),r("el-form-item",{staticClass:"width100"},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:export:excel:order"],expression:"['admin:export:excel:order']"}],attrs:{size:"small"},on:{click:t.exports}},[t._v("导出")])],1)],1)],1)])]),t._v(" "),r("div",{staticClass:"mt20"}),t._v(" "),r("el-card",{staticClass:"box-card"},[r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticClass:"table",attrs:{data:t.tableData.data,size:"mini","highlight-current-row":"","header-cell-style":{fontWeight:"bold"},"row-key":function(t){return t.orderId}}},[r("el-table-column",{attrs:{label:"订单号","min-width":"210"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("span",{staticStyle:{display:"block"},domProps:{textContent:t._s(e.row.orderId)}}),t._v(" "),r("span",{directives:[{name:"show",rawName:"v-show",value:e.row.isDel,expression:"scope.row.isDel"}],staticStyle:{color:"#ed4014",display:"block"}},[t._v("用户已删除")])]}}])}),t._v(" "),r("el-table-column",{attrs:{prop:"orderType",label:"订单类型","min-width":"110"}}),t._v(" "),r("el-table-column",{attrs:{prop:"realName",label:"收货人","min-width":"100"}}),t._v(" "),r("el-table-column",{attrs:{prop:"expressNo",label:"快递单号","min-width":"100"}}),t._v(" "),r("el-table-column",{attrs:{label:"商品信息","min-width":"400"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("el-popover",{attrs:{trigger:"hover",placement:"right","open-delay":800}},[e.row.productList&&e.row.productList.length?r("div",{attrs:{slot:"reference"},slot:"reference"},t._l(e.row.productList,(function(e,n){return r("div",{key:n,staticClass:"tabBox acea-row row-middle",staticStyle:{"flex-wrap":"inherit"}},[r("div",{staticClass:"demo-image__preview mr10"},[r("el-image",{attrs:{src:e.info.image,"preview-src-list":[e.info.image]}})],1),t._v(" "),r("div",{staticClass:"text_overflow"},[r("span",{staticClass:"tabBox_tit mr10"},[t._v(t._s(e.info.productName+" | ")+t._s(e.info.sku?e.info.sku:"-"))]),t._v(" "),r("span",{staticClass:"tabBox_pice"},[t._v(t._s(e.info.price+" x "+e.info.payNum))])])])})),0):t._e(),t._v(" "),e.row.productList&&e.row.productList.length?r("div",{staticClass:"pup_card"},t._l(e.row.productList,(function(e,n){return r("div",{key:n,staticClass:"tabBox acea-row row-middle",staticStyle:{"flex-wrap":"inherit"}},[r("div",{},[r("span",{staticClass:"tabBox_tit mr10"},[t._v(t._s(e.info.productName+" | ")+t._s(e.info.sku?e.info.sku:"-"))]),t._v(" "),r("span",{staticClass:"tabBox_pice"},[t._v(t._s(e.info.price+" x "+e.info.payNum))])])])})),0):t._e()])]}}])}),t._v(" "),r("el-table-column",{attrs:{prop:"payPrice",label:"实际支付","min-width":"80"}}),t._v(" "),r("el-table-column",{attrs:{label:"支付方式","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("span",[t._v(t._s(e.row.payTypeStr))])]}}])}),t._v(" "),r("el-table-column",{attrs:{label:"订单状态","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("div",[1===e.row.refundStatus||2===e.row.refundStatus?r("div",{staticClass:"refunding"},[[r("el-popover",{attrs:{trigger:"hover",placement:"left","open-delay":800}},[r("b",{staticStyle:{color:"#f124c7"},attrs:{slot:"reference"},slot:"reference"},[t._v(t._s(e.row.statusStr.value))]),t._v(" "),r("div",{staticClass:"pup_card flex-column"},[r("span",[t._v("退款原因："+t._s(e.row.refundReasonWap))]),t._v(" "),r("span",[t._v("备注说明："+t._s(e.row.refundReasonWapExplain))]),t._v(" "),r("span",[t._v("退款时间："+t._s(e.row.refundReasonTime))]),t._v(" "),r("span",{staticClass:"acea-row"},[t._v("\n                      退款凭证：\n                      "),e.row.refundReasonWapImg?t._l(e.row.refundReasonWapImg.split(","),(function(t,e){return r("div",{key:e,staticClass:"demo-image__preview",staticStyle:{width:"35px",height:"auto",display:"inline-block"}},[r("el-image",{attrs:{src:t,"preview-src-list":[t]}})],1)})):r("span",{staticStyle:{display:"inline-block"}},[t._v("无")])],2)])])]],2):r("span",[t._v(t._s(e.row.statusStr.value))])])]}}])}),t._v(" "),r("el-table-column",{attrs:{prop:"createTime",label:"下单时间","min-width":"150"}}),t._v(" "),r("el-table-column",{attrs:{prop:"remark",label:"订单备注","min-width":"150"}}),t._v(" "),r("el-table-column",{attrs:{label:"操作","min-width":"150",fixed:"right",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[!1===e.row.paid?r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:order:update:price"],expression:"['admin:order:update:price']"}],staticClass:"mr10",attrs:{type:"text",size:"small"},on:{click:function(r){return t.edit(e.row)}}},[t._v("编辑")]):t._e(),t._v(" "),"notShipped"===e.row.statusStr.key&&0===e.row.refundStatus?r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:order:send"],expression:"['admin:order:send']"}],staticClass:"mr10",attrs:{type:"text",size:"small"},on:{click:function(r){return t.sendOrder(e.row)}}},[t._v("发送货")]):t._e(),t._v(" "),"toBeWrittenOff"===e.row.statusStr.key&&1==e.row.paid&&0===e.row.refundStatus?r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:order:write:update"],expression:"['admin:order:write:update']"}],staticClass:"mr10",attrs:{type:"text",size:"small"},on:{click:function(r){return t.onWriteOff(e.row)}}},[t._v("立即核销")]):t._e(),t._v(" "),r("el-dropdown",{attrs:{trigger:"click"}},[r("span",{staticClass:"el-dropdown-link"},[t._v("\n              更多"),r("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),t._v(" "),r("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[t.checkPermi(["admin:order:info"])?r("el-dropdown-item",{nativeOn:{click:function(r){return t.onOrderDetails(e.row.orderId)}}},[t._v("订单详情")]):t._e(),t._v(" "),t.checkPermi(["admin:order:status:list"])?r("el-dropdown-item",{nativeOn:{click:function(r){return t.onOrderLog(e.row.orderId)}}},[t._v("订单记录")]):t._e(),t._v(" "),t.checkPermi(["admin:order:mark"])?r("el-dropdown-item",{nativeOn:{click:function(r){return t.onOrderMark(e.row)}}},[t._v("订单备注")]):t._e(),t._v(" "),1===e.row.refundStatus&&t.checkPermi(["admin:order:refund:refuse"])?r("el-dropdown-item",{nativeOn:{click:function(r){return t.onOrderRefuse(e.row)}}},[t._v("拒绝退款")]):t._e(),t._v(" "),1===e.row.refundStatus&&t.checkPermi(["admin:order:refund"])?r("el-dropdown-item",{nativeOn:{click:function(r){return t.onOrderRefund(e.row)}}},[t._v("立即退款")]):t._e(),t._v(" "),"deleted"===e.row.statusStr.key&&t.checkPermi(["admin:order:delete"])?r("el-dropdown-item",{nativeOn:{click:function(r){return t.handleDelete(e.row,e.$index)}}},[t._v("删除订单")]):t._e(),t._v(" "),"unPaid"!==e.row.statusStr.key?r("el-dropdown-item",{nativeOn:{click:function(r){return t.onOrderPrint(e.row)}}},[t._v("打印小票")]):t._e()],1)],1)]}}])})],1),t._v(" "),r("div",{staticClass:"block"},[r("el-pagination",{attrs:{"page-sizes":[20,40,60,80],"page-size":t.tableFrom.limit,"current-page":t.tableFrom.page,layout:"total, sizes, prev, pager, next, jumper",total:t.tableData.total},on:{"size-change":t.handleSizeChange,"current-change":t.pageChange}})],1)],1),t._v(" "),r("el-dialog",{attrs:{title:"编辑订单",visible:t.dialogVisible,width:"500px","before-close":t.handleClose},on:{"update:visible":function(e){t.dialogVisible=e}}},[t.dialogVisible?r("zb-parser",{attrs:{"form-id":104,"is-create":t.isCreate,"edit-data":t.editData},on:{submit:t.handlerSubmit,resetForm:t.resetForm}}):t._e()],1),t._v(" "),r("el-dialog",{attrs:{title:"操作记录",visible:t.dialogVisibleJI,width:"700px"},on:{"update:visible":function(e){t.dialogVisibleJI=e}}},[r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.LogLoading,expression:"LogLoading"}],staticStyle:{width:"100%"},attrs:{border:"",data:t.tableDataLog.data}},[r("el-table-column",{attrs:{prop:"oid",align:"center",label:"ID","min-width":"80"}}),t._v(" "),r("el-table-column",{attrs:{prop:"changeMessage",label:"操作记录",align:"center","min-width":"280"}}),t._v(" "),r("el-table-column",{attrs:{prop:"createTime",label:"操作时间",align:"center","min-width":"280"}})],1),t._v(" "),r("div",{staticClass:"block"},[r("el-pagination",{attrs:{"page-sizes":[10,20,30,40],"page-size":t.tableFromLog.limit,"current-page":t.tableFromLog.page,layout:"total, sizes, prev, pager, next, jumper",total:t.tableDataLog.total},on:{"size-change":t.handleSizeChangeLog,"current-change":t.pageChangeLog}})],1)],1),t._v(" "),r("details-from",{ref:"orderDetail",attrs:{orderId:t.orderId}}),t._v(" "),r("order-send",{ref:"send",attrs:{orderId:t.orderId},on:{submitFail:t.getList}}),t._v(" "),r("order-video-send",{ref:"videoSend",attrs:{orderId:t.orderId},on:{submitFail:t.getList}}),t._v(" "),t.RefuseVisible?r("el-dialog",{attrs:{title:"拒绝退款原因",visible:t.RefuseVisible,width:"500px","before-close":t.RefusehandleClose},on:{"update:visible":function(e){t.RefuseVisible=e}}},[r("zb-parser",{attrs:{"form-id":106,"is-create":1,"edit-data":t.RefuseData},on:{submit:t.RefusehandlerSubmit,resetForm:t.resetFormRefusehand}})],1):t._e(),t._v(" "),r("el-dialog",{attrs:{title:"退款处理",visible:t.refundVisible,width:"500px","before-close":t.refundhandleClose},on:{"update:visible":function(e){t.refundVisible=e}}},[t.refundVisible?r("zb-parser",{attrs:{"form-id":107,"is-create":1,"edit-data":t.refundData},on:{submit:t.refundhandlerSubmit,resetForm:t.resetFormRefundhandler}}):t._e()],1)],1)},o=[],i=r("f8b7"),a=r("0f56"),s=r("a356"),l=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[t.orderDatalist?n("el-dialog",{attrs:{title:"订单信息",visible:t.dialogVisible,width:"700px"},on:{"update:visible":function(e){t.dialogVisible=e}}},[n("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"description"},[n("div",{staticClass:"title"},[t._v("用户信息")]),t._v(" "),n("div",{staticClass:"acea-row"},[n("div",{staticClass:"description-term"},[t._v("用户昵称："+t._s(t.orderDatalist.nikeName))]),t._v(" "),n("div",{staticClass:"description-term"},[t._v("绑定电话："+t._s(t.orderDatalist.phone?t.orderDatalist.phone:"无"))])]),t._v(" "),n("el-divider"),t._v(" "),n("div",{staticClass:"title"},[t._v(t._s("toBeWrittenOff"===t.orderDatalist.statusStr.key?"提货信息":"收货信息"))]),t._v(" "),n("div",{staticClass:"acea-row"},[n("div",{staticClass:"description-term"},[t._v(t._s("toBeWrittenOff"===t.orderDatalist.statusStr.key?"提货人":"收货人")+"："+t._s(t.orderDatalist.realName))]),t._v(" "),n("div",{staticClass:"description-term"},[t._v(t._s("toBeWrittenOff"===t.orderDatalist.statusStr.key?"提货电话":"收货电话")+"："+t._s(t.orderDatalist.userPhone))]),t._v(" "),"toBeWrittenOff"!==t.orderDatalist.statusStr.key?n("div",{staticClass:"description-term"},[t._v(t._s("toBeWrittenOff"===t.orderDatalist.statusStr.key?"提货地址":"收货地址")+"："+t._s(t.orderDatalist.userAddress))]):t._e()]),t._v(" "),n("el-divider"),t._v(" "),n("div",{staticClass:"title"},[t._v("订单信息")]),t._v(" "),n("div",{staticClass:"acea-row"},[n("div",{staticClass:"description-term"},[t._v("订单编号："+t._s(t.orderDatalist.orderId))]),t._v(" "),n("div",{staticClass:"description-term",staticStyle:{color:"red"}},[t._v("订单状态："+t._s(t.orderDatalist.statusStr.value))]),t._v(" "),n("div",{staticClass:"description-term"},[t._v("商品总数："+t._s(t.orderDatalist.totalNum))]),t._v(" "),n("div",{staticClass:"description-term"},[t._v("商品总价："+t._s(t.orderDatalist.proTotalPrice))]),t._v(" "),n("div",{staticClass:"description-term"},[t._v("支付邮费："+t._s(t.orderDatalist.payPostage))]),t._v(" "),n("div",{staticClass:"description-term"},[t._v("优惠券金额："+t._s(t.orderDatalist.couponPrice))]),t._v(" "),n("div",{staticClass:"description-term"},[t._v("实际支付："+t._s(t.orderDatalist.payPrice))]),t._v(" "),n("div",{staticClass:"description-term"},[t._v("抵扣金额："+t._s(t.orderDatalist.deductionPrice))]),t._v(" "),t.orderDatalist.refundPrice?n("div",{staticClass:"description-term fontColor3"},[t._v("退款金额："+t._s(t.orderDatalist.refundPrice))]):t._e(),t._v(" "),t.orderDatalist.useIntegral?n("div",{staticClass:"description-term"},[t._v("使用积分："+t._s(t.orderDatalist.useIntegral))]):t._e(),t._v(" "),t.orderDatalist.backIntegral?n("div",{staticClass:"description-term"},[t._v("退回积分："+t._s(t.orderDatalist.backIntegral))]):t._e(),t._v(" "),n("div",{staticClass:"description-term"},[t._v("创建时间："+t._s(t.orderDatalist.createTime))]),t._v(" "),t.orderDatalist.refundReasonTime?n("div",{staticClass:"description-term"},[t._v("退款时间："+t._s(t.orderDatalist.refundReasonTime))]):t._e(),t._v(" "),n("div",{staticClass:"description-term"},[t._v("支付方式："+t._s(t.orderDatalist.payTypeStr))]),t._v(" "),n("div",{staticClass:"description-term"},[t._v("推广人："+t._s(t._f("filterEmpty")(t.orderDatalist.spreadName)))]),t._v(" "),2===t.orderDatalist.shippingType&&"notShipped"===t.orderDatalist.statusStr.key?n("div",{staticClass:"description-term"},[t._v("门店名称："+t._s(t.orderDatalist.storeName))]):t._e(),t._v(" "),2===t.orderDatalist.shippingType&&"notShipped"===t.orderDatalist.statusStr.key?n("div",{staticClass:"description-term"},[t._v("核销码："+t._s(t.orderDatalist.user_phone))]):t._e(),t._v(" "),n("div",{staticClass:"description-term"},[t._v("商家备注："+t._s(t.orderDatalist.remark))]),t._v(" "),"toBeWrittenOff"===t.orderDatalist.statusStr.key&&t.orderDatalist.systemStore?[n("div",{staticClass:"description-term"},[t._v("提货码："+t._s(t.orderDatalist.verifyCode))]),t._v(" "),n("div",{staticClass:"description-term"},[t._v("门店名称："+t._s(t.orderDatalist.systemStore.name))]),t._v(" "),n("div",{staticClass:"description-term"},[t._v("门店电话："+t._s(t.orderDatalist.systemStore.phone))]),t._v(" "),n("div",{staticClass:"description-term"},[t._v("门店地址："+t._s(t.orderDatalist.systemStore.address+t.orderDatalist.systemStore.detailedAddress))])]:t._e()],2),t._v(" "),"express"===t.orderDatalist.deliveryType?[n("el-divider"),t._v(" "),n("div",{staticClass:"title"},[t._v("物流信息")]),t._v(" "),n("div",{staticClass:"acea-row"},[n("div",{staticClass:"description-term"},[t._v("快递公司："+t._s(t.orderDatalist.deliveryName))]),t._v(" "),n("div",{staticClass:"description-term"},[t._v("快递单号："+t._s(t.orderDatalist.deliveryId)+"\n            "),n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:order:logistics:info"],expression:"['admin:order:logistics:info']"}],staticStyle:{"margin-left":"5px"},attrs:{type:"primary",size:"mini"},on:{click:t.openLogistics}},[t._v("物流查询")])],1)])]:t._e(),t._v(" "),"send"===t.orderDatalist.deliveryType?[n("el-divider"),t._v(" "),n("div",{staticClass:"title"},[t._v("配送信息")]),t._v(" "),n("div",{staticClass:"acea-row"},[n("div",{staticClass:"description-term"},[t._v("送货人姓名："+t._s(t.orderDatalist.deliveryName))]),t._v(" "),n("div",{staticClass:"description-term"},[t._v("送货人电话："+t._s(t.orderDatalist.deliveryId))])])]:t._e(),t._v(" "),t.orderDatalist.mark?[n("el-divider"),t._v(" "),n("div",{staticClass:"title"},[t._v("用户备注")]),t._v(" "),n("div",{staticClass:"acea-row"},[n("div",{staticClass:"description-term"},[t._v(t._s(t.orderDatalist.mark))])])]:t._e()],2)]):t._e(),t._v(" "),t.orderDatalist?n("el-dialog",{attrs:{title:"提示",visible:t.modal2,width:"30%"},on:{"update:visible":function(e){t.modal2=e}}},[n("div",{staticClass:"logistics acea-row row-top"},[n("div",{staticClass:"logistics_img"},[n("img",{attrs:{src:r("df87")}})]),t._v(" "),n("div",{staticClass:"logistics_cent"},[n("span",{staticClass:"mb10"},[t._v("物流公司："+t._s(t.orderDatalist.deliveryName))]),t._v(" "),n("span",[t._v("物流单号："+t._s(t.orderDatalist.deliveryId))])])]),t._v(" "),n("div",{staticClass:"acea-row row-column-around trees-coadd"},[n("div",{staticClass:"scollhide"},[n("el-timeline",{attrs:{reverse:t.reverse}},t._l(t.result,(function(e,r){return n("el-timeline-item",{key:r},[n("p",{staticClass:"time",domProps:{textContent:t._s(e.time)}}),t._v(" "),n("p",{staticClass:"content",domProps:{textContent:t._s(e.status)}})])})),1)],1)]),t._v(" "),n("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{attrs:{type:"primary"},on:{click:function(e){t.modal2=!1}}},[t._v("关闭")])],1)]):t._e()],1)},c=[];function u(t){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},u(t)}function d(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */d=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",l=i.toStringTag||"@@toStringTag";function c(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(t){c=function(t,e,r){return t[e]=r}}function f(t,e,r,n){var i=e&&e.prototype instanceof b?e:b,a=Object.create(i.prototype),s=new T(n||[]);return o(a,"_invoke",{value:S(t,r,s)}),a}function m(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var p="suspendedStart",h="suspendedYield",v="executing",g="completed",y={};function b(){}function _(){}function w(){}var x={};c(x,a,(function(){return this}));var L=Object.getPrototypeOf,O=L&&L(L(E([])));O&&O!==r&&n.call(O,a)&&(x=O);var C=w.prototype=b.prototype=Object.create(x);function I(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function k(t,e){function r(o,i,a,s){var l=m(t[o],t,i);if("throw"!==l.type){var c=l.arg,d=c.value;return d&&"object"==u(d)&&n.call(d,"__await")?e.resolve(d.__await).then((function(t){r("next",t,a,s)}),(function(t){r("throw",t,a,s)})):e.resolve(d).then((function(t){c.value=t,a(c)}),(function(t){return r("throw",t,a,s)}))}s(l.arg)}var i;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return i=i?i.then(o,o):o()}})}function S(e,r,n){var o=p;return function(i,a){if(o===v)throw Error("Generator is already running");if(o===g){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var s=n.delegate;if(s){var l=D(s,n);if(l){if(l===y)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===p)throw o=g,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=v;var c=m(e,r,n);if("normal"===c.type){if(o=n.done?g:h,c.arg===y)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o=g,n.method="throw",n.arg=c.arg)}}}function D(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,D(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),y;var i=m(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,y;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,y):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function N(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function j(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(N,this),this.reset(!0)}function E(e){if(e||""===e){var r=e[a];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(u(e)+" is not iterable")}return _.prototype=w,o(C,"constructor",{value:w,configurable:!0}),o(w,"constructor",{value:_,configurable:!0}),_.displayName=c(w,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===_||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,c(t,l,"GeneratorFunction")),t.prototype=Object.create(C),t},e.awrap=function(t){return{__await:t}},I(k.prototype),c(k.prototype,s,(function(){return this})),e.AsyncIterator=k,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new k(f(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},I(C),c(C,l,"Generator"),c(C,a,(function(){return this})),c(C,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=E,T.prototype={constructor:T,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(j),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return s.type="throw",s.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],s=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var l=n.call(a,"catchLoc"),c=n.call(a,"finallyLoc");if(l&&c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,y):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),y},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),j(r),y}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;j(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:E(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),y}},e}function f(t,e,r,n,o,i,a){try{var s=t[i](a),l=s.value}catch(t){return void r(t)}s.done?e(l):Promise.resolve(l).then(n,o)}function m(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){f(i,n,o,a,s,"next",t)}function s(t){f(i,n,o,a,s,"throw",t)}a(void 0)}))}}var p={name:"OrderDetail",props:{orderId:{type:String,default:0}},data:function(){return{reverse:!0,dialogVisible:!1,orderDatalist:null,loading:!1,modal2:!1,result:[]}},mounted:function(){},methods:{openLogistics:function(){this.getOrderData(),this.modal2=!0},getOrderData:function(){var t=this;Object(i["d"])({orderNo:this.orderId}).then(function(){var e=m(d().mark((function e(r){return d().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.result=r.list;case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}())},getDetail:function(t){var e=this;this.loading=!0,Object(i["f"])({orderNo:t}).then((function(t){e.orderDatalist=t,e.loading=!1})).catch((function(){e.orderDatalist=null,e.loading=!1}))}}},h=p,v=(r("c2c0"),r("2877")),g=Object(v["a"])(h,l,c,!1,null,"312b6dab",null),y=g.exports,b=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("el-dialog",{staticClass:"order_box",attrs:{visible:t.modals,title:"发送货","before-close":t.handleClose,width:"600px"},on:{"update:visible":function(e){t.modals=e}}},[r("el-form",{ref:"formItem",attrs:{model:t.formItem,"label-width":"110px",rules:t.rules},nativeOn:{submit:function(t){t.preventDefault()}}},[r("el-form-item",{attrs:{label:"选择类型："}},[r("el-radio-group",{on:{change:function(e){return t.changeRadioType(t.formItem.type)}},model:{value:t.formItem.type,callback:function(e){t.$set(t.formItem,"type",e)},expression:"formItem.type"}},[r("el-radio",{attrs:{label:"1"}},[t._v("发货")]),t._v(" "),r("el-radio",{attrs:{label:"2"}},[t._v("送货")]),t._v(" "),r("el-radio",{attrs:{label:"3"}},[t._v("虚拟")])],1)],1),t._v(" "),"1"===t.formItem.type?r("div",[r("el-form-item",{attrs:{label:"发货类型：",prop:"expressId"}},[r("el-radio-group",{on:{change:function(e){return t.changeRadio(t.formItem.expressRecordType)}},model:{value:t.formItem.expressRecordType,callback:function(e){t.$set(t.formItem,"expressRecordType",e)},expression:"formItem.expressRecordType"}},[r("el-radio",{attrs:{label:"1"}},[t._v("手动填写")]),t._v(" "),t.checkPermi(["admin:order:sheet:info"])?r("el-radio",{attrs:{label:"2"}},[t._v("电子面单打印")]):t._e()],1)],1),t._v(" "),r("el-form-item",{attrs:{label:"快递公司：",prop:"expressCode"}},[r("el-select",{staticStyle:{width:"80%"},attrs:{filterable:""},on:{change:function(e){return t.onChangeExport(t.formItem.expressCode)}},model:{value:t.formItem.expressCode,callback:function(e){t.$set(t.formItem,"expressCode",e)},expression:"formItem.expressCode"}},t._l(t.express,(function(t,e){return r("el-option",{key:e,attrs:{value:t.code,label:t.name}})})),1)],1),t._v(" "),"1"===t.formItem.expressRecordType?r("el-form-item",{attrs:{label:"快递单号：",prop:"expressNumber"}},[r("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"请输入快递单号"},model:{value:t.formItem.expressNumber,callback:function(e){t.$set(t.formItem,"expressNumber",e)},expression:"formItem.expressNumber"}})],1):t._e(),t._v(" "),"2"===t.formItem.expressRecordType?[r("el-form-item",{staticClass:"express_temp_id",attrs:{label:"电子面单：",prop:"expressTempId"}},[r("div",{staticClass:"acea-row"},[r("el-select",{class:[t.formItem.expressTempId?"width9":"width8"],attrs:{placeholder:"请选择电子面单"},on:{change:t.onChangeImg},model:{value:t.formItem.expressTempId,callback:function(e){t.$set(t.formItem,"expressTempId",e)},expression:"formItem.expressTempId"}},t._l(t.exportTempList,(function(t,e){return r("el-option",{key:e,attrs:{value:t.temp_id,label:t.title}})})),1),t._v(" "),t.formItem.expressTempId?r("div",{staticStyle:{position:"relative"}},[r("div",{staticClass:"tempImgList ml10"},[r("div",{staticClass:"demo-image__preview"},[r("el-image",{staticStyle:{width:"36px",height:"36px"},attrs:{src:t.tempImg,"preview-src-list":[t.tempImg]}})],1)])]):t._e()],1)]),t._v(" "),r("el-form-item",{attrs:{label:"寄件人姓名：",prop:"toName"}},[r("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"请输入寄件人姓名"},model:{value:t.formItem.toName,callback:function(e){t.$set(t.formItem,"toName",e)},expression:"formItem.toName"}})],1),t._v(" "),r("el-form-item",{attrs:{label:"寄件人电话：",prop:"toTel"}},[r("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"请输入寄件人电话"},model:{value:t.formItem.toTel,callback:function(e){t.$set(t.formItem,"toTel",e)},expression:"formItem.toTel"}})],1),t._v(" "),r("el-form-item",{attrs:{label:"寄件人地址：",prop:"toAddr"}},[r("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"请输入寄件人地址"},model:{value:t.formItem.toAddr,callback:function(e){t.$set(t.formItem,"toAddr",e)},expression:"formItem.toAddr"}})],1)]:t._e()],2):t._e(),t._v(" "),"2"===t.formItem.type?r("div",[r("el-form-item",{attrs:{label:"送货人姓名：",prop:"deliveryName"}},[r("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"请输入送货人姓名"},model:{value:t.formItem.deliveryName,callback:function(e){t.$set(t.formItem,"deliveryName",e)},expression:"formItem.deliveryName"}})],1),t._v(" "),r("el-form-item",{attrs:{label:"送货人电话：",prop:"deliveryTel"}},[r("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"请输入送货人电话"},model:{value:t.formItem.deliveryTel,callback:function(e){t.$set(t.formItem,"deliveryTel",e)},expression:"formItem.deliveryTel"}})],1)],1):t._e(),t._v(" "),r("div",[r("el-form-item",{attrs:{label:""}},[r("div",{staticStyle:{color:"#CECECE"}},[t._v("顺丰请输入单号：收件人或寄件人手机号后四位")]),t._v(" "),r("div",{staticStyle:{color:"#CECECE"}},[t._v("例如：SF000000000000:3941")])])],1)],1),t._v(" "),r("div",{attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.putSend("formItem")}}},[t._v("提交")]),t._v(" "),r("el-button",{on:{click:function(e){return t.cancel("formItem")}}},[t._v("取消")])],1)],1)},_=[],w=r("b61d"),x=r("e350"),L=r("61f7");function O(t){return O="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},O(t)}function C(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */C=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",l=i.toStringTag||"@@toStringTag";function c(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(t){c=function(t,e,r){return t[e]=r}}function u(t,e,r,n){var i=e&&e.prototype instanceof g?e:g,a=Object.create(i.prototype),s=new T(n||[]);return o(a,"_invoke",{value:S(t,r,s)}),a}function d(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=u;var f="suspendedStart",m="suspendedYield",p="executing",h="completed",v={};function g(){}function y(){}function b(){}var _={};c(_,a,(function(){return this}));var w=Object.getPrototypeOf,x=w&&w(w(E([])));x&&x!==r&&n.call(x,a)&&(_=x);var L=b.prototype=g.prototype=Object.create(_);function I(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function k(t,e){function r(o,i,a,s){var l=d(t[o],t,i);if("throw"!==l.type){var c=l.arg,u=c.value;return u&&"object"==O(u)&&n.call(u,"__await")?e.resolve(u.__await).then((function(t){r("next",t,a,s)}),(function(t){r("throw",t,a,s)})):e.resolve(u).then((function(t){c.value=t,a(c)}),(function(t){return r("throw",t,a,s)}))}s(l.arg)}var i;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return i=i?i.then(o,o):o()}})}function S(e,r,n){var o=f;return function(i,a){if(o===p)throw Error("Generator is already running");if(o===h){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var s=n.delegate;if(s){var l=D(s,n);if(l){if(l===v)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===f)throw o=h,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=p;var c=d(e,r,n);if("normal"===c.type){if(o=n.done?h:m,c.arg===v)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o=h,n.method="throw",n.arg=c.arg)}}}function D(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,D(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=d(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,v;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function N(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function j(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(N,this),this.reset(!0)}function E(e){if(e||""===e){var r=e[a];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(O(e)+" is not iterable")}return y.prototype=b,o(L,"constructor",{value:b,configurable:!0}),o(b,"constructor",{value:y,configurable:!0}),y.displayName=c(b,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===y||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,c(t,l,"GeneratorFunction")),t.prototype=Object.create(L),t},e.awrap=function(t){return{__await:t}},I(k.prototype),c(k.prototype,s,(function(){return this})),e.AsyncIterator=k,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new k(u(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},I(L),c(L,l,"Generator"),c(L,a,(function(){return this})),c(L,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=E,T.prototype={constructor:T,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(j),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return s.type="throw",s.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],s=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var l=n.call(a,"catchLoc"),c=n.call(a,"finallyLoc");if(l&&c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),j(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;j(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:E(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),v}},e}function I(t,e,r,n,o,i,a){try{var s=t[i](a),l=s.value}catch(t){return void r(t)}s.done?e(l):Promise.resolve(l).then(n,o)}function k(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){I(i,n,o,a,s,"next",t)}function s(t){I(i,n,o,a,s,"throw",t)}a(void 0)}))}}var S=function(t,e,r){if(!e)return r(new Error("请填写手机号"));/^1[3456789]\d{9}$/.test(e)?r():r(new Error("手机号格式不正确!"))},D={name:"orderSend",props:{orderId:String},data:function(){return{formItem:{type:"1",expressRecordType:"1",expressId:"",expressCode:"",deliveryName:"",deliveryTel:"",expressNumber:"",expressTempId:"",toAddr:"",toName:"",toTel:"",orderNo:""},modals:!1,express:[],exportTempList:[],tempImg:"",rules:{toName:[{required:!0,message:"请输寄件人姓名",trigger:"blur"}],toTel:[{required:!0,validator:S,trigger:"blur"}],toAddr:[{required:!0,message:"请输入寄件人地址",trigger:"blur"}],expressCode:[{required:!0,message:"请选择快递公司",trigger:"change"}],expressNumber:[{required:!0,message:"请输入快递单号",trigger:"blur"}],expressTempId:[{required:!0,message:"请选择电子面单",trigger:"change"}],deliveryName:[{required:!0,message:"请输入送货人姓名",trigger:"blur"}],deliveryTel:[{required:!0,validator:S,trigger:"blur"}]},expressType:"normal"}},mounted:function(){},methods:{checkPermi:x["a"],sheetInfo:function(){var t=this;Object(i["r"])().then(function(){var e=k(C().mark((function e(r){return C().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.formItem.toAddr=r.exportToAddress||"",t.formItem.toName=r.exportToName||"",t.formItem.toTel=r.exportToTel||"";case 3:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}())},onChangeExport:function(t){this.formItem.expressTempId="","2"===this.formItem.expressRecordType&&this.exportTemp(t)},exportTemp:function(t){var e=this;Object(w["c"])({com:t}).then(function(){var t=k(C().mark((function t(r){return C().wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.exportTempList=r.data.data||[];case 1:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())},onChangeImg:function(t){var e=this;this.exportTempList.map((function(r){r.temp_id===t&&(e.tempImg=r.pic)}))},changeRadioType:function(){this.formItem.expressId="",this.formItem.expressCode=""},changeRadio:function(t){this.expressType=2==t?"elec":"normal",this.formItem.expressId="",this.formItem.expressCode="",this.getList()},getList:function(){var t=this;Object(w["d"])({type:this.expressType}).then(function(){var e=k(C().mark((function e(r){return C().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.express=r;case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}())},putSend:Object(L["a"])((function(t){var e=this;this.formItem.orderNo=this.orderId,this.$refs[t].validate((function(r){r?Object(i["n"])(e.formItem).then((function(r){e.$message.success("发送货成功"),e.modals=!1,e.$refs[t].resetFields(),e.$emit("submitFail")})):e.$message.error("请填写信息")}))})),handleClose:function(){this.cancel("formItem")},cancel:function(t){this.modals=!1,this.$refs[t].resetFields(),this.formItem.type="1",this.formItem.expressRecordType="1"}}},N=D,j=(r("fa4e"),Object(v["a"])(N,b,_,!1,null,"b1502590",null)),T=j.exports,E=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("el-dialog",{staticClass:"order_box",attrs:{visible:t.modals,title:"发送货","before-close":t.handleClose,width:"600px"},on:{"update:visible":function(e){t.modals=e}}},[r("el-form",{ref:"formItem",attrs:{model:t.formItem,"label-width":"110px",rules:t.rules},nativeOn:{submit:function(t){t.preventDefault()}}},[r("el-form-item",{attrs:{label:"快递公司：",prop:"expressCode"}},[r("el-select",{staticStyle:{width:"80%"},attrs:{filterable:""},model:{value:t.formItem.deliveryId,callback:function(e){t.$set(t.formItem,"deliveryId",e)},expression:"formItem.deliveryId"}},t._l(t.express,(function(t,e){return r("el-option",{key:e,attrs:{value:t.deliveryId,label:t.deliveryName}})})),1)],1),t._v(" "),r("el-form-item",{attrs:{label:"快递单号：",prop:"waybillId"}},[r("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"请输入快递单号"},model:{value:t.formItem.waybillId,callback:function(e){t.$set(t.formItem,"waybillId",e)},expression:"formItem.waybillId"}})],1)],1),t._v(" "),r("div",{attrs:{slot:"footer"},slot:"footer"},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:order:video:send"],expression:"['admin:order:video:send']"}],attrs:{size:"mini",type:"primary"},on:{click:function(e){return t.putSend("formItem")}}},[t._v("提交")]),t._v(" "),r("el-button",{attrs:{size:"mini"},on:{click:function(e){return t.cancel("formItem")}}},[t._v("取消")])],1)],1)},F=[];function A(t){return A="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},A(t)}function P(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */P=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",l=i.toStringTag||"@@toStringTag";function c(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(t){c=function(t,e,r){return t[e]=r}}function u(t,e,r,n){var i=e&&e.prototype instanceof g?e:g,a=Object.create(i.prototype),s=new N(n||[]);return o(a,"_invoke",{value:I(t,r,s)}),a}function d(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=u;var f="suspendedStart",m="suspendedYield",p="executing",h="completed",v={};function g(){}function y(){}function b(){}var _={};c(_,a,(function(){return this}));var w=Object.getPrototypeOf,x=w&&w(w(j([])));x&&x!==r&&n.call(x,a)&&(_=x);var L=b.prototype=g.prototype=Object.create(_);function O(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function C(t,e){function r(o,i,a,s){var l=d(t[o],t,i);if("throw"!==l.type){var c=l.arg,u=c.value;return u&&"object"==A(u)&&n.call(u,"__await")?e.resolve(u.__await).then((function(t){r("next",t,a,s)}),(function(t){r("throw",t,a,s)})):e.resolve(u).then((function(t){c.value=t,a(c)}),(function(t){return r("throw",t,a,s)}))}s(l.arg)}var i;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return i=i?i.then(o,o):o()}})}function I(e,r,n){var o=f;return function(i,a){if(o===p)throw Error("Generator is already running");if(o===h){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var s=n.delegate;if(s){var l=k(s,n);if(l){if(l===v)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===f)throw o=h,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=p;var c=d(e,r,n);if("normal"===c.type){if(o=n.done?h:m,c.arg===v)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o=h,n.method="throw",n.arg=c.arg)}}}function k(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,k(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=d(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,v;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function S(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function D(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function N(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(S,this),this.reset(!0)}function j(e){if(e||""===e){var r=e[a];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(A(e)+" is not iterable")}return y.prototype=b,o(L,"constructor",{value:b,configurable:!0}),o(b,"constructor",{value:y,configurable:!0}),y.displayName=c(b,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===y||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,c(t,l,"GeneratorFunction")),t.prototype=Object.create(L),t},e.awrap=function(t){return{__await:t}},O(C.prototype),c(C.prototype,s,(function(){return this})),e.AsyncIterator=C,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new C(u(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},O(L),c(L,l,"Generator"),c(L,a,(function(){return this})),c(L,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=j,N.prototype={constructor:N,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(D),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return s.type="throw",s.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],s=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var l=n.call(a,"catchLoc"),c=n.call(a,"finallyLoc");if(l&&c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),D(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;D(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:j(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),v}},e}function V(t,e,r,n,o,i,a){try{var s=t[i](a),l=s.value}catch(t){return void r(t)}s.done?e(l):Promise.resolve(l).then(n,o)}function R(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){V(i,n,o,a,s,"next",t)}function s(t){V(i,n,o,a,s,"throw",t)}a(void 0)}))}}var G={name:"orderSend",props:{orderId:String},data:function(){return{formItem:{deliveryId:"",orderNo:"",waybillId:""},modals:!1,express:[],exportTempList:[],tempImg:"",rules:{deliveryId:[{required:!0,message:"请选择快递公司",trigger:"change"}],waybillId:[{required:!0,message:"请输入快递单号",trigger:"blur"}]},expressType:"normal"}},mounted:function(){this.express=JSON.parse(sessionStorage.getItem("videoExpress"))},methods:{companyGetList:function(){var t=this;Object(i["b"])().then(function(){var e=R(P().mark((function e(r){return P().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.express=r,sessionStorage.setItem("videoExpress",JSON.stringify(r));case 2:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}())},putSend:Object(L["a"])((function(t){var e=this;this.formItem.orderNo=this.orderId,this.$refs[t].validate((function(r){r?Object(i["u"])(e.formItem).then((function(r){e.$message.success("发送货成功"),e.modals=!1,e.$refs[t].resetFields(),e.$emit("submitFail")})):e.$message.error("请填写信息")}))})),handleClose:function(){this.cancel("formItem")},cancel:function(t){this.modals=!1,this.$refs[t].resetFields(),this.formItem.type="1",this.formItem.expressRecordType="1"}}},z=G,B=(r("88ce"),Object(v["a"])(z,E,F,!1,null,"d2116198",null)),W=B.exports,M=(r("6537"),r("a78e"),r("ed08")),q=r("73f5"),J={name:"orderlistDetails",components:{cardsData:a["a"],zbParser:s["a"],detailsFrom:y,orderSend:T,orderVideoSend:W},data:function(){return{RefuseVisible:!1,RefuseData:{},orderId:"",refundVisible:!1,refundData:{},dialogVisibleJI:!1,tableDataLog:{data:[],total:0},tableFromLog:{page:1,limit:10,orderNo:0,storeName:null,remark:null,expressNo:0},LogLoading:!1,isCreate:1,editData:null,dialogVisible:!1,tableData:{data:[],total:0},listLoading:!0,tableFrom:{status:"all",dateLimit:"",orderNo:"",page:1,limit:10,type:0},orderChartType:{},timeVal:[],fromList:this.$constants.fromList,fromType:[{value:"all",text:"全部"},{value:"info",text:"普通"},{value:"pintuan",text:"拼团"},{value:"bragin",text:"砍价"},{value:"miaosha",text:"秒杀"}],selectionList:[],ids:"",orderids:"",cardLists:[],isWriteOff:Object(M["e"])(),proType:0,active:!1}},mounted:function(){this.getList(),this.getOrderStatusNum()},methods:{checkPermi:x["a"],resetFormRefundhandler:function(){this.refundVisible=!1},resetFormRefusehand:function(){this.RefuseVisible=!1},resetForm:function(t){this.dialogVisible=!1},onWriteOff:function(t){var e=this;this.$modalSure("核销订单吗").then((function(){Object(i["w"])(t.verifyCode).then((function(){e.$message.success("核销成功"),e.tableFrom.page=1,e.getList()}))}))},seachList:function(){this.tableFrom.page=1,this.getList(),this.getOrderStatusNum()},RefusehandleClose:function(){this.RefuseVisible=!1},onOrderRefuse:function(t){this.orderids=t.orderId,this.RefuseData={orderId:t.orderId,reason:""},this.RefuseVisible=!0},RefusehandlerSubmit:function(t){var e=this;Object(i["m"])({orderNo:this.orderids,reason:t.reason}).then((function(t){e.$message.success("操作成功"),e.RefuseVisible=!1,e.getList()}))},refundhandleClose:function(){this.refundVisible=!1},onOrderRefund:function(t){this.refundData={orderId:t.orderId,amount:t.payPrice,type:""},this.orderids=t.orderId,this.refundVisible=!0},refundhandlerSubmit:function(t){var e=this;Object(i["l"])({amount:t.amount,orderNo:this.orderids}).then((function(t){e.$message.success("操作成功"),e.refundVisible=!1,e.getList()}))},sendOrder:function(t){0===t.type?(this.$refs.send.modals=!0,this.$refs.send.getList(),this.$refs.send.sheetInfo()):(this.$refs.videoSend.modals=!0,JSON.parse(sessionStorage.getItem("videoExpress"))||this.$refs.videoSend.companyGetList()),this.orderId=t.orderId},handleDelete:function(t,e){var r=this;t.isDel?this.$modalSure().then((function(){Object(i["e"])({orderNo:t.orderId}).then((function(){r.$message.success("删除成功"),r.tableData.data.splice(e,1)}))})):this.$confirm("您选择的的订单存在用户未删除的订单，无法删除用户未删除的订单！","提示",{confirmButtonText:"确定",type:"error"})},onOrderDetails:function(t){this.orderId=t,this.$refs.orderDetail.getDetail(t),this.$refs.orderDetail.dialogVisible=!0},onOrderLog:function(t){var e=this;this.dialogVisibleJI=!0,this.LogLoading=!0,this.tableFromLog.orderNo=t,Object(i["i"])(this.tableFromLog).then((function(t){e.tableDataLog.data=t.list,e.tableDataLog.total=t.total,e.LogLoading=!1})).catch((function(){e.LogLoading=!1}))},pageChangeLog:function(t){this.tableFromLog.page=t,this.onOrderLog()},handleSizeChangeLog:function(t){this.tableFromLog.limit=t,this.onOrderLog()},handleClose:function(){this.dialogVisible=!1},onOrderMark:function(t){var e=this;this.$prompt("订单备注",{confirmButtonText:"确定",cancelButtonText:"取消",inputErrorMessage:"请输入订单备注",inputType:"textarea",inputValue:t.remark,inputPlaceholder:"请输入订单备注",inputValidator:function(t){if(!t)return"输入不能为空"}}).then((function(r){var n=r.value;Object(i["j"])({mark:n,orderNo:t.orderId}).then((function(){e.$message.success("操作成功"),e.getList()}))})).catch((function(){e.$message.info("取消输入")}))},handleSelectionChange:function(t){this.selectionList=t;var e=[];this.selectionList.map((function(t){e.push(t.orderId)})),this.ids=e.join(",")},selectChange:function(t){this.timeVal=[],this.tableFrom.page=1,this.getList(),this.getOrderStatusNum()},onchangeTime:function(t){this.timeVal=t,this.tableFrom.dateLimit=t?this.timeVal.join(","):"",this.tableFrom.page=1,this.getList(),this.getOrderStatusNum()},edit:function(t){this.orderId=t.orderId,this.editData={orderId:t.orderId,totalPrice:t.totalPrice,totalPostage:t.totalPostage,payPrice:t.payPrice,payPostage:t.payPostage,gainIntegral:t.gainIntegral},this.dialogVisible=!0},handlerSubmit:function(t){var e=this,r={orderNo:t.orderId,payPrice:t.payPrice};Object(i["t"])(r).then((function(t){e.$message.success("编辑数据成功"),e.dialogVisible=!1,e.getList()}))},getList:function(){var t=this;this.listLoading=!0,Object(i["g"])(this.tableFrom).then((function(e){t.tableData.data=e.list||[],t.tableData.total=e.total,t.listLoading=!1,t.checkedCities=t.$cache.local.has("order_stroge")?t.$cache.local.getJSON("order_stroge"):t.checkedCities})).catch((function(){t.listLoading=!1}))},getOrderListData:function(){var t=this;Object(i["h"])({dateLimit:this.tableFrom.dateLimit}).then((function(e){t.cardLists=[{name:"订单数量",count:e.count,color:"#1890FF",class:"one",icon:"icondingdan"},{name:"订单金额",count:e.amount,color:"#A277FF",class:"two",icon:"icondingdanjine"},{name:"微信支付金额",count:e.weChatAmount,color:"#EF9C20",class:"three",icon:"iconweixinzhifujine"},{name:"余额支付金额",count:e.yueAmount,color:"#1BBE6B",class:"four",icon:"iconyuezhifujine2"}]}))},getOrderStatusNum:function(){var t=this;Object(i["p"])({dateLimit:this.tableFrom.dateLimit,type:this.tableFrom.type}).then((function(e){t.orderChartType=e}))},pageChange:function(t){this.tableFrom.page=t,this.getList()},handleSizeChange:function(t){this.tableFrom.limit=t,this.getList()},exports:function(){var t={dateLimit:this.tableFrom.dateLimit,orderNo:this.tableFrom.orderNo,status:this.tableFrom.status,type:this.tableFrom.type};Object(q["i"])(t).then((function(t){window.open(t.fileName)}))},onOrderPrint:function(t){var e=this;Object(i["k"])(t.orderId).then((function(t){e.$modal.msgSuccess("打印成功")})).catch((function(t){e.$modal.msgError(t.message)}))}}},Y=J,Q=(r("aa43"),Object(v["a"])(Y,n,o,!1,null,"29d0d101",null));e["default"]=Q.exports},"685d":function(t,e,r){},"88ce":function(t,e,r){"use strict";r("0b54")},"92c6":function(t,e,r){"use strict";r.d(e,"b",(function(){return o})),r.d(e,"c",(function(){return i})),r.d(e,"d",(function(){return a})),r.d(e,"a",(function(){return s})),r.d(e,"f",(function(){return l})),r.d(e,"g",(function(){return c})),r.d(e,"j",(function(){return u})),r.d(e,"h",(function(){return d})),r.d(e,"e",(function(){return f})),r.d(e,"i",(function(){return m}));var n=r("b775");function o(t){var e={id:t.id};return Object(n["a"])({url:"/admin/system/form/temp/info",method:"GET",params:e})}function i(t){var e={keywords:t.keywords,page:t.page,limit:t.limit};return Object(n["a"])({url:"/admin/system/form/temp/list",method:"GET",params:e})}function a(t){var e={content:t.content,info:t.info,name:t.name};return Object(n["a"])({url:"/admin/system/form/temp/save",method:"POST",data:e})}function s(t){var e={id:t.id},r={content:t.content,info:t.info,name:t.name};return Object(n["a"])({url:"/admin/system/form/temp/update",method:"POST",params:e,data:r})}function l(t){var e={sendType:t.sendType};return Object(n["a"])({url:"/admin/system/notification/list",method:"GET",params:e})}function c(t){return Object(n["a"])({url:"/admin/system/notification/routine/switch/".concat(t),method:"post"})}function u(t){return Object(n["a"])({url:"/admin/system/notification/wechat/switch/".concat(t),method:"post"})}function d(t){return Object(n["a"])({url:"/admin/system/notification/sms/switch/".concat(t),method:"post"})}function f(t){var e={detailType:t.type,id:t.id};return Object(n["a"])({url:"/admin/system/notification/detail",method:"get",params:e})}function m(t){var e={detailType:t.type,id:t.id,status:t.status,tempId:t.tempId};return Object(n["a"])({url:"/admin/system/notification/update",method:"post",data:e})}},aa43:function(t,e,r){"use strict";r("685d")},c2c0:function(t,e,r){"use strict";r("d760")},d760:function(t,e,r){},df87:function(t,e){t.exports="data:image/jpeg;base64,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"},f8b7:function(t,e,r){"use strict";r.d(e,"g",(function(){return o})),r.d(e,"p",(function(){return i})),r.d(e,"h",(function(){return a})),r.d(e,"e",(function(){return s})),r.d(e,"i",(function(){return l})),r.d(e,"f",(function(){return c})),r.d(e,"j",(function(){return u})),r.d(e,"n",(function(){return d})),r.d(e,"m",(function(){return f})),r.d(e,"l",(function(){return m})),r.d(e,"w",(function(){return p})),r.d(e,"v",(function(){return h})),r.d(e,"o",(function(){return v})),r.d(e,"s",(function(){return g})),r.d(e,"t",(function(){return y})),r.d(e,"q",(function(){return b})),r.d(e,"r",(function(){return _})),r.d(e,"d",(function(){return w})),r.d(e,"b",(function(){return x})),r.d(e,"u",(function(){return L})),r.d(e,"k",(function(){return O})),r.d(e,"a",(function(){return C}));var n=r("b775");function o(t){return Object(n["a"])({url:"/admin/store/order/list",method:"get",params:t})}function i(t){return Object(n["a"])({url:"/admin/store/order/status/num",method:"get",params:t})}function a(t){return Object(n["a"])({url:"/admin/store/order/list/data",method:"get",params:t})}function s(t){return Object(n["a"])({url:"/admin/store/order/delete",method:"get",params:t})}function l(t){return Object(n["a"])({url:"/admin/store/order/status/list",method:"get",params:t})}function c(t){return Object(n["a"])({url:"/admin/store/order/info",method:"get",params:t})}function u(t){return Object(n["a"])({url:"/admin/store/order/mark",method:"post",params:t})}function d(t){return Object(n["a"])({url:"/admin/store/order/send",method:"post",data:t})}function f(t){return Object(n["a"])({url:"/admin/store/order/refund/refuse",method:"get",params:t})}function m(t){return Object(n["a"])({url:"/admin/store/order/refund",method:"get",params:t})}function p(t){return Object(n["a"])({url:"/admin/store/order/writeUpdate/".concat(t),method:"get"})}function h(t){return Object(n["a"])({url:"/admin/store/order/writeConfirm/".concat(t),method:"get"})}function v(){return Object(n["a"])({url:"/admin/store/order/statistics",method:"get"})}function g(t){return Object(n["a"])({url:"/admin/store/order/statisticsData",method:"get",params:t})}function y(t){return Object(n["a"])({url:"admin/store/order/update/price",method:"post",data:t})}function b(t){return Object(n["a"])({url:"/admin/store/order/time",method:"get",params:t})}function _(){return Object(n["a"])({url:"/admin/store/order/sheet/info",method:"get"})}function w(t){return Object(n["a"])({url:"/admin/store/order/getLogisticsInfo",method:"get",params:t})}function x(){return Object(n["a"])({url:"/admin/pay/component/delivery/company/get/list",method:"get"})}function L(t){return Object(n["a"])({url:"/admin/store/order/video/send",method:"post",data:t})}function O(t){return Object(n["a"])({url:"/admin/yly/print/".concat(t),method:"get"})}function C(t){return Object(n["a"])({url:"/admin/store/order/auditAddressList",method:"get",params:t})}},fa4e:function(t,e,r){"use strict";r("4324")},fb9d:function(t,e,r){var n={"./el-button.js":"64b1","./el-checkbox-group.js":"cc1a","./el-input.js":"373c","./el-radio-group.js":"afca","./el-select.js":"a938","./el-upload.js":"73ee"};function o(t){var e=i(t);return r(e)}function i(t){var e=n[t];if(!(e+1)){var r=new Error("Cannot find module '"+t+"'");throw r.code="MODULE_NOT_FOUND",r}return e}o.keys=function(){return Object.keys(n)},o.resolve=i,t.exports=o,o.id="fb9d"}}]);