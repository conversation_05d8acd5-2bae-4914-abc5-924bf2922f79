(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1735edf1"],{"4c8c":function(t,e,r){},"6f30":function(t,e,r){"use strict";r.r(e);var i=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"divBox"},[r("el-card",{staticClass:"box-card"},[r("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[r("div",{staticClass:"container"},[r("el-form",{attrs:{inline:"",size:"small"}},[r("el-form-item",{attrs:{label:"商品名称："}},[r("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入商品名称",clearable:""},model:{value:t.tableFrom.storeName,callback:function(e){t.$set(t.tableFrom,"storeName",e)},expression:"tableFrom.storeName"}},[r("el-button",{attrs:{slot:"append",icon:"el-icon-search"},on:{click:t.seachList},slot:"append"})],1)],1)],1)],1),t._v(" "),r("div",{staticClass:"acea-row"})]),t._v(" "),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"table",staticStyle:{width:"100%"},attrs:{data:t.tableData.data,size:"mini","highlight-current-row":""},on:{"selection-change":t.handleSelectionChange}},[r("el-table-column",{attrs:{label:"订单号","min-width":"210"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("span",{staticStyle:{display:"block"},domProps:{textContent:t._s(e.row.orderId)}}),t._v(" "),r("span",{directives:[{name:"show",rawName:"v-show",value:e.row.isDel,expression:"scope.row.isDel"}],staticStyle:{color:"#ed4014",display:"block"}},[t._v("用户已删除")])]}}])}),t._v(" "),r("el-table-column",{attrs:{prop:"orderType",label:"订单类型","min-width":"110"}}),t._v(" "),r("el-table-column",{attrs:{prop:"realName",label:"收货人","min-width":"100"}}),t._v(" "),r("el-table-column",{attrs:{label:"商品信息","min-width":"400"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("el-popover",{attrs:{trigger:"hover",placement:"right","open-delay":800}},[e.row.productList&&e.row.productList.length?r("div",{attrs:{slot:"reference"},slot:"reference"},t._l(e.row.productList,(function(e,i){return r("div",{key:i,staticClass:"tabBox acea-row row-middle",staticStyle:{"flex-wrap":"inherit"}},[r("div",{staticClass:"demo-image__preview mr10"},[r("el-image",{attrs:{src:e.info.image,"preview-src-list":[e.info.image]}})],1),t._v(" "),r("div",{staticClass:"text_overflow"},[r("span",{staticClass:"tabBox_tit mr10"},[t._v(t._s(e.info.productName+" | ")+t._s(e.info.sku?e.info.sku:"-"))]),t._v(" "),r("span",{staticClass:"tabBox_pice"},[t._v(t._s(e.info.price+" x "+e.info.payNum))])])])})),0):t._e(),t._v(" "),e.row.productList&&e.row.productList.length?r("div",{staticClass:"pup_card"},t._l(e.row.productList,(function(e,i){return r("div",{key:i,staticClass:"tabBox acea-row row-middle",staticStyle:{"flex-wrap":"inherit"}},[r("div",{},[r("span",{staticClass:"tabBox_tit mr10"},[t._v(t._s(e.info.productName+" | ")+t._s(e.info.sku?e.info.sku:"-"))]),t._v(" "),r("span",{staticClass:"tabBox_pice"},[t._v(t._s(e.info.price+" x "+e.info.payNum))])])])})),0):t._e()])]}}])}),t._v(" "),r("el-table-column",{attrs:{prop:"payPrice",label:"实际支付","min-width":"80"}}),t._v(" "),r("el-table-column",{attrs:{label:"支付方式","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("span",[t._v(t._s(e.row.payTypeStr))])]}}])}),t._v(" "),r("el-table-column",{attrs:{label:"订单状态","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("div",[1===e.row.refundStatus||2===e.row.refundStatus?r("div",{staticClass:"refunding"},[[r("el-popover",{attrs:{trigger:"hover",placement:"left","open-delay":800}},[r("b",{staticStyle:{color:"#f124c7"},attrs:{slot:"reference"},slot:"reference"},[t._v(t._s(e.row.statusStr.value))]),t._v(" "),r("div",{staticClass:"pup_card flex-column"},[r("span",[t._v("退款原因："+t._s(e.row.refundReasonWap))]),t._v(" "),r("span",[t._v("备注说明："+t._s(e.row.refundReasonWapExplain))]),t._v(" "),r("span",[t._v("退款时间："+t._s(e.row.refundReasonTime))]),t._v(" "),r("span",{staticClass:"acea-row"},[t._v("\n                      退款凭证：\n                      "),e.row.refundReasonWapImg?t._l(e.row.refundReasonWapImg.split(","),(function(t,e){return r("div",{key:e,staticClass:"demo-image__preview",staticStyle:{width:"35px",height:"auto",display:"inline-block"}},[r("el-image",{attrs:{src:t,"preview-src-list":[t]}})],1)})):r("span",{staticStyle:{display:"inline-block"}},[t._v("无")])],2)])])]],2):r("span",[t._v(t._s(e.row.statusStr.value))])])]}}])}),t._v(" "),r("el-table-column",{attrs:{prop:"createTime",label:"下单时间","min-width":"150"}}),t._v(" "),r("el-table-column",{attrs:{prop:"remark",label:"订单备注","min-width":"150"}}),t._v(" "),r("el-table-column",{attrs:{label:"操作","min-width":"120",align:"center",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:product:rule:delete"],expression:"['admin:product:rule:delete']"}],attrs:{type:"text",size:"small"},on:{click:function(r){return t.handleDelete(e.row.id,e.$index)}}},[t._v("审核地址")])]}}])})],1),t._v(" "),r("div",{staticClass:"block"},[r("el-pagination",{attrs:{"page-sizes":[20,40,60,80],"page-size":t.tableFrom.limit,"current-page":t.tableFrom.page,layout:"total, sizes, prev, pager, next, jumper",total:t.tableData.total},on:{"size-change":t.handleSizeChange,"current-change":t.pageChange}})],1),t._v(" "),r("el-dialog",{attrs:{title:0===t.isCreate?"组合商品":"编辑组合商品",visible:t.dialogVisible,width:"700px","z-index":"4"},on:{"update:visible":function(e){t.dialogVisible=e}}},[r("creatCombinationProduct",{key:t.timer,attrs:{"is-create":t.isCreate,"edit-data":t.editData},on:{getList:t.seachList}})],1)],1)],1)},n=[],a=r("f8b7"),o=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],ref:"formValidate",staticClass:"demo-formValidate",attrs:{model:t.formValidate,rules:t.rules,"label-width":"120px"}},[r("el-form-item",{attrs:{label:"搭配组合名称：",prop:"name"}},[r("el-input",{attrs:{type:"text"},model:{value:t.formValidate.name,callback:function(e){t.$set(t.formValidate,"name",e)},expression:"formValidate.name"}})],1),t._v(" "),r("el-form-item",{attrs:{label:"搭配组合商品：",prop:"productIds"}},[r("div",{staticClass:"acea-row"},[t.formValidate.productIds.length?t._l(t.formValidate.productIds,(function(e,i){return r("div",{key:i,staticClass:"pictrue"},[r("img",{attrs:{src:e.image}}),t._v(" "),r("i",{staticClass:"el-icon-error btndel",on:{click:function(e){return t.handleRemove(i)}}})])})):t._e(),t._v(" "),r("div",{staticClass:"upLoadPicBox",on:{click:t.changeGood}},[r("div",{staticClass:"upLoad"},[r("i",{staticClass:"el-icon-camera cameraIconfont"})])])],2)]),t._v(" "),r("el-form-item",{attrs:{label:"组合优惠价：",prop:"combinationPrice"}},[r("el-input",{attrs:{type:"text"},model:{value:t.formValidate.combinationPrice,callback:function(e){t.$set(t.formValidate,"combinationPrice",e)},expression:"formValidate.combinationPrice"}})],1),t._v(" "),r("el-form-item",[r("el-button",{attrs:{size:"mini",type:"primary",loading:t.loadingbtn},on:{click:function(e){return t.submitForm("formValidate")}}},[t._v("提交")]),t._v(" "),r("el-button",{attrs:{size:"mini"},on:{click:function(e){return t.resetForm("formValidate")}}},[t._v("重置")])],1)],1)},s=[],l=r("85e3"),c=r("61f7");function u(t){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},u(t)}function d(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,i)}return r}function m(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?d(Object(r),!0).forEach((function(e){f(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function f(t,e,r){return(e=p(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function p(t){var e=b(t,"string");return"symbol"==u(e)?e:e+""}function b(t,e){if("object"!=u(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var i=r.call(t,e||"default");if("object"!=u(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var h={name:"",nacombinationPriceme:null,productIds:[]},g={name:"creatComment",props:{isCreate:{type:Number,default:0},editData:{type:Object,dfault:function(){return{}}}},data:function(){return{loadingbtn:!1,loading:!1,pics:[],image:"",formValidate:Object.assign({},h),rules:{productIds:[{required:!0,message:"请至少选择一个商品",trigger:"change",type:"array"}],name:[{required:!0,message:"请填写搭配组合名称",trigger:"blur"}],nacombinationPriceme:[{required:!0,message:"请填写组合优惠价格",trigger:"blur"}]}}},watch:{isCreate:{handler:function(t){var e=this;0===t?this.$nextTick((function(){e.resetForm("formValidate")})):(console.log(this.editData),this.formValidate=Object.assign({},this.editData),this.formValidate.productIds=this.editData.productDetailList.map((function(t){return{id:t.id,name:t.storeName,image:t.image}})))},immediate:!0}},methods:{changeGood:function(){var t=this;this.$modalGoodList((function(e){t.formValidate.productIds=e}),"many",t.formValidate.productIds)},handleRemove:function(t){this.formValidate.productIds.splice(t,1)},submitForm:Object(c["a"])((function(t){var e=this,r=this.formValidate.productIds.map((function(t){return t.id}));this.$refs[t].validate((function(t){if(!t)return!1;e.loadingbtn=!0,0===e.isCreate?Object(l["a"])(m(m({},e.formValidate),{},{productIds:r})).then((function(){e.$message.success("新增成功"),setTimeout((function(){e.$emit("getList")}),600),e.loadingbtn=!1})).catch((function(){e.loadingbtn=!1})):Object(l["d"])(m(m({},e.formValidate),{},{productIds:r})).then((function(){e.$message.success("修改成功"),setTimeout((function(){e.$emit("getList")}),600),e.loadingbtn=!1})).catch((function(){e.loadingbtn=!1}))}))})),resetForm:function(t){this.$refs[t].resetFields()}}},v=g,y=(r("f86f"),r("2877")),w=Object(y["a"])(v,o,s,!1,null,"093a96e8",null),_=w.exports;function O(t){return O="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},O(t)}function j(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,i)}return r}function S(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?j(Object(r),!0).forEach((function(e){C(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):j(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function C(t,e,r){return(e=x(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function x(t){var e=k(t,"string");return"symbol"==O(e)?e:e+""}function k(t,e){if("object"!=O(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var i=r.call(t,e||"default");if("object"!=O(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var P={name:"StoreAttr",components:{creatCombinationProduct:_},data:function(){return{formDynamic:{ruleName:"",ruleValue:[]},headeNum:[],tableFrom:{page:1,limit:20,type:2,storeName:null},tableData:{data:[],loading:!1,total:0},listLoading:!0,selectionList:[],multipleSelectionAll:[],idKey:"id",nextPageFlag:!1,keyNum:0,dialogVisible:!1,isCreate:0,editData:{},timer:""}},mounted:function(){this.getList()},methods:{seachList:function(){this.tableFrom.page=1,this.dialogVisible=!1,this.getList()},goodHeade:function(){var t=this;productHeadersApi().then((function(e){t.headeNum=e})).catch((function(e){t.$message.error(e.message)}))},handleSelectionChange:function(t){var e=this;this.selectionList=t,setTimeout((function(){e.changePageCoreRecordData()}),50)},onchangeIsShow:function(t){var e=this;console.log(t.status,8686868),t.status?Object(l["c"])(t.id,1).then((function(){e.$message.success("启用成功"),e.getList()})).catch((function(){t.status=!t.status})):Object(l["c"])(t.id,0).then((function(){e.$message.success("禁用成功"),e.getList()})).catch((function(){t.status=!t.status}))},setSelectRow:function(){if(this.multipleSelectionAll&&!(this.multipleSelectionAll.length<=0)){var t=this.idKey,e=[];this.multipleSelectionAll.forEach((function(r){e.push(r[t])})),this.$refs.table.clearSelection();for(var r=0;r<this.tableData.data.length;r++)e.indexOf(this.tableData.data[r][t])>=0&&this.$refs.table.toggleRowSelection(this.tableData.data[r],!0)}},changePageCoreRecordData:function(){var t=this.idKey,e=this;if(this.multipleSelectionAll.length<=0)this.multipleSelectionAll=this.selectionList;else{var r=[];this.multipleSelectionAll.forEach((function(e){r.push(e[t])}));var i=[];this.selectionList.forEach((function(n){i.push(n[t]),r.indexOf(n[t])<0&&e.multipleSelectionAll.push(n)}));var n=[];this.tableData.data.forEach((function(e){i.indexOf(e[t])<0&&n.push(e[t])})),n.forEach((function(i){if(r.indexOf(i)>=0)for(var n=0;n<e.multipleSelectionAll.length;n++)if(e.multipleSelectionAll[n][t]==i){e.multipleSelectionAll.splice(n,1);break}}))}},add:function(){this.dialogVisible=!0,this.isCreate=0,this.timer=(new Date).getTime()},getList:function(){var t=this;this.listLoading=!0,Object(a["a"])(this.tableFrom).then((function(e){var r=e.list;t.tableData.data=r,t.tableData.total=e.total;for(var i=0;i<r.length;i++)r[i].ruleValue=JSON.parse(r[i].ruleValue);t.$nextTick((function(){this.setSelectRow()})),t.listLoading=!1})).catch((function(){t.listLoading=!1}))},pageChange:function(t){this.changePageCoreRecordData(),this.tableFrom.page=t,this.getList()},handleSizeChange:function(t){this.changePageCoreRecordData(),this.tableFrom.limit=t,this.getList()},handleDelete:function(t,e){var r=this;this.$modalSure().then((function(){Object(l["b"])(t).then((function(){r.$message.success("删除成功"),r.tableData.data.splice(e,1)}))})).catch((function(){}))},onEdit:function(t){this.dialogVisible=!0,this.isCreate=1,this.editData=S({},t)}}},D=P,L=(r("95d1"),Object(y["a"])(D,i,n,!1,null,"5198deb3",null));e["default"]=L.exports},"95d1":function(t,e,r){"use strict";r("4c8c")},a664:function(t,e,r){},f86f:function(t,e,r){"use strict";r("a664")},f8b7:function(t,e,r){"use strict";r.d(e,"g",(function(){return n})),r.d(e,"p",(function(){return a})),r.d(e,"h",(function(){return o})),r.d(e,"e",(function(){return s})),r.d(e,"i",(function(){return l})),r.d(e,"f",(function(){return c})),r.d(e,"j",(function(){return u})),r.d(e,"n",(function(){return d})),r.d(e,"m",(function(){return m})),r.d(e,"l",(function(){return f})),r.d(e,"w",(function(){return p})),r.d(e,"v",(function(){return b})),r.d(e,"o",(function(){return h})),r.d(e,"s",(function(){return g})),r.d(e,"t",(function(){return v})),r.d(e,"q",(function(){return y})),r.d(e,"r",(function(){return w})),r.d(e,"d",(function(){return _})),r.d(e,"b",(function(){return O})),r.d(e,"u",(function(){return j})),r.d(e,"k",(function(){return S})),r.d(e,"a",(function(){return C}));var i=r("b775");function n(t){return Object(i["a"])({url:"/admin/store/order/list",method:"get",params:t})}function a(t){return Object(i["a"])({url:"/admin/store/order/status/num",method:"get",params:t})}function o(t){return Object(i["a"])({url:"/admin/store/order/list/data",method:"get",params:t})}function s(t){return Object(i["a"])({url:"/admin/store/order/delete",method:"get",params:t})}function l(t){return Object(i["a"])({url:"/admin/store/order/status/list",method:"get",params:t})}function c(t){return Object(i["a"])({url:"/admin/store/order/info",method:"get",params:t})}function u(t){return Object(i["a"])({url:"/admin/store/order/mark",method:"post",params:t})}function d(t){return Object(i["a"])({url:"/admin/store/order/send",method:"post",data:t})}function m(t){return Object(i["a"])({url:"/admin/store/order/refund/refuse",method:"get",params:t})}function f(t){return Object(i["a"])({url:"/admin/store/order/refund",method:"get",params:t})}function p(t){return Object(i["a"])({url:"/admin/store/order/writeUpdate/".concat(t),method:"get"})}function b(t){return Object(i["a"])({url:"/admin/store/order/writeConfirm/".concat(t),method:"get"})}function h(){return Object(i["a"])({url:"/admin/store/order/statistics",method:"get"})}function g(t){return Object(i["a"])({url:"/admin/store/order/statisticsData",method:"get",params:t})}function v(t){return Object(i["a"])({url:"admin/store/order/update/price",method:"post",data:t})}function y(t){return Object(i["a"])({url:"/admin/store/order/time",method:"get",params:t})}function w(){return Object(i["a"])({url:"/admin/store/order/sheet/info",method:"get"})}function _(t){return Object(i["a"])({url:"/admin/store/order/getLogisticsInfo",method:"get",params:t})}function O(){return Object(i["a"])({url:"/admin/pay/component/delivery/company/get/list",method:"get"})}function j(t){return Object(i["a"])({url:"/admin/store/order/video/send",method:"post",data:t})}function S(t){return Object(i["a"])({url:"/admin/yly/print/".concat(t),method:"get"})}function C(t){return Object(i["a"])({url:"/admin/store/order/auditAddressList",method:"get",params:t})}}}]);