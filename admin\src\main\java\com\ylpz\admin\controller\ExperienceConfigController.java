package com.ylpz.admin.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylpz.core.common.request.ExperienceConfigRequest;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.core.common.response.CommonResult;
import com.ylpz.core.service.ExperienceConfigService;
import com.ylpz.model.system.ExperienceConfig;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * 成长值配置 前端控制器
 */
@Slf4j
@RestController
@RequestMapping("api/admin/system/experience/config")
@Api(tags = "设置 -- 成长值配置")
public class ExperienceConfigController {

    @Autowired
    private ExperienceConfigService experienceConfigService;

    /**
     * 分页查询成长值配置
     */
    //@PreAuthorize("hasAuthority('admin:system:experience:config:list')")
    @ApiOperation(value = "分页查询成长值配置")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public CommonResult<Page<ExperienceConfig>> getList(PageParamRequest pageParamRequest) {
        return CommonResult.success(experienceConfigService.getPageList(pageParamRequest));
    }

    /**
     * 获取所有成长值配置列表
     */
    //@PreAuthorize("hasAuthority('admin:system:experience:config:list')")
    @ApiOperation(value = "获取所有成长值配置列表")
    @RequestMapping(value = "/all", method = RequestMethod.GET)
    public CommonResult<List<ExperienceConfig>> getAllList() {
        return CommonResult.success(experienceConfigService.getList());
    }

    /**
     * 新增成长值配置
     */
    //@PreAuthorize("hasAuthority('admin:system:experience:config:save')")
    @ApiOperation(value = "新增成长值配置")
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public CommonResult<String> save(@RequestBody @Validated ExperienceConfigRequest request) {
        if (experienceConfigService.save(request)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 更新成长值配置
     */
    //@PreAuthorize("hasAuthority('admin:system:experience:config:update')")
    @ApiOperation(value = "更新成长值配置")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public CommonResult<String> update(@RequestBody @Validated ExperienceConfigRequest request) {
        if (experienceConfigService.update(request)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 删除成长值配置
     */
    //@PreAuthorize("hasAuthority('admin:system:experience:config:delete')")
    @ApiOperation(value = "删除成长值配置")
    @RequestMapping(value = "/delete/{id}", method = RequestMethod.POST)
    public CommonResult<String> delete(@PathVariable(value = "id") Integer id) {
        if (experienceConfigService.delete(id)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 更新成长值配置状态
     */
    //@PreAuthorize("hasAuthority('admin:system:experience:config:status')")
    @ApiOperation(value = "更新成长值配置状态")
    @RequestMapping(value = "/status", method = RequestMethod.POST)
    public CommonResult<String> updateStatus(@RequestParam(value = "id") Integer id,
                                          @RequestParam(value = "status") Boolean status) {
        if (experienceConfigService.updateStatus(id, status)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }
} 