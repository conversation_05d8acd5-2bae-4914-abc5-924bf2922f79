package com.ylpz.core.common.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 奖励金记录查询请求类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BonusRecordRequest对象", description = "奖励金记录查询请求类")
public class BonusRecordRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户UID")
    private Integer uid;

    @ApiModelProperty(value = "人员手机号")
    private String mobile;

    @ApiModelProperty(value = "关联订单号")
    private String linkId;

    @ApiModelProperty(value = "奖励类型：upgrade-升级奖励，firstOrder-首单奖励，recharge-充值奖励，rank-排行榜奖励")
    private String bonusType;

    @ApiModelProperty(value = "查询开始时间")
    private Date startTime;

    @ApiModelProperty(value = "查询结束时间")
    private Date endTime;

    @ApiModelProperty(value = "奖励状态：1-创建，2-冻结期，3-完成，4-失效")
    private Integer status;

    @ApiModelProperty(value = "排行榜类型：week-周排行，month-月排行，quarter-季度排行")
    private String rankType;
}