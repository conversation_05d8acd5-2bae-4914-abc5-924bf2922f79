(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6e0a06b5"],{"19d9":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"divBox"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"page_title"},[e._v("授权证书申请")]),e._v(" "),a("div",{staticClass:"page_desc"},[e._v("您的支持是我们不断进步的动力，商业授权更多是一个保障和附加的增值服务，让您优先享受新版本的强大功能和安全保障")]),e._v(" "),a("el-form",{ref:"form",attrs:{model:e.form,"label-width":"80px",rules:e.rules}},[a("el-form-item",{attrs:{label:"企业名称",prop:"company_name"}},[a("el-input",{attrs:{placeholder:"请填写您的企业名称"},model:{value:e.form.company_name,callback:function(t){e.$set(e.form,"company_name",t)},expression:"form.company_name"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"企业域名",prop:"domain_name"}},[a("el-input",{attrs:{placeholder:"请输入域名，格式：baidu.com"},model:{value:e.form.domain_name,callback:function(t){e.$set(e.form,"domain_name",t)},expression:"form.domain_name"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"订单号",prop:"order_id"}},[a("el-input",{attrs:{placeholder:"请输入您在淘宝或小程序购买的源码订单号"},model:{value:e.form.order_id,callback:function(t){e.$set(e.form,"order_id",t)},expression:"form.order_id"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"手机号",prop:"phone"}},[a("el-input",{attrs:{placeholder:"负责人电话"},model:{value:e.form.phone,callback:function(t){e.$set(e.form,"phone",t)},expression:"form.phone"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"授权产品"}},[a("el-input",{attrs:{value:"java",disabled:""}})],1),e._v(" "),a("el-form-item",{attrs:{label:"验证码",prop:"captcha"}},[a("div",{staticClass:"captcha"},[a("el-input",{ref:"username",staticStyle:{width:"218px"},attrs:{"prefix-icon":"el-icon-message",placeholder:"验证码",name:"username",type:"text",tabindex:"3",autocomplete:"on"},model:{value:e.form.captcha,callback:function(t){e.$set(e.form,"captcha",t)},expression:"form.captcha"}}),e._v(" "),a("div",{staticClass:"imgs",on:{click:function(t){return e.getCaptcha()}}},[a("img",{attrs:{src:e.captchs}})])],1)]),e._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.submitForm("form")}}},[e._v("提交")]),e._v(" "),a("el-button",{on:{click:function(t){return e.resetForm("form")}}},[e._v("重置")])],1)],1)],1)],1)},o=[],n=a("bc3a"),s=a.n(n),i=s.a.create({timeout:4e4});i.interceptors.request.use((function(e){return e}),(function(e){Promise.reject(e)})),i.interceptors.response.use((function(e){var t=e;return 200!==t.status&&401!==t.status?(Message({message:t.data.msg||"Error",type:"error",duration:5e3}),Promise.reject()):t.data}),(function(e){}));var c=i;function m(e){return c({url:document.location.protocol+"//authorize.crmeb.net/api/auth_apply",method:"POST",data:e})}var l=a("2b9b"),u=a("61f7"),p={name:"index",data:function(){return{form:{company_name:"",domain_name:"",order_id:"",captcha:"",phone:"",label:22},captchs:"http://authorize.crmeb.net/api/captchs/",rules:{company_name:[{required:!0,message:"请填写您的企业名称",trigger:"blur"}],domain_name:[{required:!0,message:"请输入域名，格式：baidu.com",trigger:"blur"}],order_id:[{required:!0,message:"请输入您购买的源码订单号",trigger:"blur"}],phone:[{required:!0,message:"请输入负责人电话",trigger:"blur"}],captcha:[{required:!0,message:"请输入验证码",trigger:"blur"}]}}},mounted:function(){this.getCaptcha()},methods:{getCaptcha:function(){this.captchs=this.captchs+Date.parse(new Date)},submitForm:Object(u["a"])((function(e){var t=this;this.$refs[e].validate((function(e){if(!e)return!1;m(t.form).then((function(e){200===e.status?(t.$modal.msgSuccess(e.msg),Object(l["d"])({key:"authHost",value:t.form.domain_name})):t.$modal.msgError(e.msg)})).catch((function(e){return t.getCaptcha(),t.$modal.msgError(e.msg)}))}))})),resetForm:function(e){this.$refs[e].resetFields()}}},d=p,f=(a("ae64"),a("2877")),h=Object(f["a"])(d,r,o,!1,null,"39883f9d",null);t["default"]=h.exports},"8a02":function(e,t,a){},ae64:function(e,t,a){"use strict";a("8a02")}}]);