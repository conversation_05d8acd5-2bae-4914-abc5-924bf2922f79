package com.ylpz.core.common.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 销售数据统计响应对象
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "SalesDataStatisticsResponse对象", description = "销售数据统计响应对象")
public class SalesDataStatisticsResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "总销售金额")
    private BigDecimal totalSalesAmount;

    @ApiModelProperty(value = "总订单数量")
    private Integer totalOrderCount;

    @ApiModelProperty(value = "总待售数量")
    private Integer totalPendingCount;

    @ApiModelProperty(value = "总已售数量")
    private Integer totalCompletedCount;

    @ApiModelProperty(value = "总自购金额")
    private BigDecimal totalSelfPurchaseAmount;

    @ApiModelProperty(value = "会员总数")
    private Integer totalMemberCount;
}
