package com.ylpz.admin.controller;

import java.util.List;
import java.util.Map;
import java.util.HashMap;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.ylpz.core.common.page.CommonPage;
import com.ylpz.core.common.response.CommonResult;
import com.ylpz.core.common.response.ProductCombinationStatusCount;
import com.ylpz.core.common.vo.ProductCombinationVO;
import com.ylpz.core.service.ProductCombinationService;
import com.ylpz.model.product.ProductCombination;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

/**
 * 商品搭配管理
 */
@Slf4j
@RestController
@RequestMapping("api/admin/product/combination")
@Api(tags = "商品 -- 搭配管理")
public class ProductCombinationController {

    @Resource
    private ProductCombinationService productCombinationService;

    // @PreAuthorize("hasAuthority('admin:product:combination:create')")
    @ApiOperation("创建商品搭配")
    @PostMapping
    public CommonResult<Object> createCombination(@Validated @RequestBody ProductCombinationVO vo) {
        productCombinationService.createCombination(vo);
        return CommonResult.success();
    }

    // @PreAuthorize("hasAuthority('admin:product:combination:update')")
    @ApiOperation("更新商品搭配")
    @PutMapping
    public CommonResult<Object> updateCombination(@Validated @RequestBody ProductCombinationVO vo) {
        productCombinationService.updateCombination(vo);
        return CommonResult.success();
    }

    // @PreAuthorize("hasAuthority('admin:product:combination:delete')")
    @ApiOperation("删除商品搭配")
    @DeleteMapping("/{id}")
    public CommonResult<Object> deleteCombination(@PathVariable Long id) {
        productCombinationService.deleteCombination(id);
        return CommonResult.success();
    }

    // @PreAuthorize("hasAuthority('admin:product:combination:batchDelete')")
    @ApiOperation("批量删除商品搭配")
    @DeleteMapping("/batch")
    public CommonResult<Object> batchDeleteCombinations(@RequestBody List<Long> ids) {
        productCombinationService.batchDeleteCombinations(ids);
        return CommonResult.success();
    }

    // @PreAuthorize("hasAuthority('admin:product:combination:info')")
    @ApiOperation("获取商品搭配详情")
    @GetMapping("/{id}")
    public CommonResult<ProductCombinationVO> getCombinationDetail(@PathVariable Long id) {
        return CommonResult.success(productCombinationService.getCombinationDetail(id));
    }

    // @PreAuthorize("hasAuthority('admin:product:combination:status')")
    @ApiOperation("获取商品搭配状态统计")
    @GetMapping("/status/count")
    public CommonResult<ProductCombinationStatusCount> getStatusCount() {
        return CommonResult.success(productCombinationService.getStatusCount());
    }


    /**
     * 分页查询商品搭配列表
     * @param page
     * @param size
     * @param name
     * @param statusType 状态类型：0-全部，1-在售中，2-已售罄，3-仓库中
     * @return 
     */
    // @PreAuthorize("hasAuthority('admin:product:combination:list')")
    @ApiOperation("分页查询商品搭配列表")
    @GetMapping("/page")
    public CommonResult<CommonPage<ProductCombinationVO>> pageCombination(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String name,
            @RequestParam(required = false, defaultValue = "0") Integer statusType) {
        PageInfo<ProductCombinationVO> pageResult = productCombinationService.pageCombination(page, size, name, statusType);
        return CommonResult.success(CommonPage.restPage(pageResult));
    }

    // @PreAuthorize("hasAuthority('admin:product:combination:update:status')")
    @ApiOperation("更新商品搭配状态")
    @PutMapping("/{id}/status/{status}")
    public CommonResult<Object> updateStatus(@PathVariable Long id, @PathVariable Integer status) {
        productCombinationService.updateStatus(id, status);
        return CommonResult.success();
    }

    // @PreAuthorize("hasAuthority('admin:product:combination:update:storeStatus')")
    @ApiOperation("更新商品上架状态")
    @PutMapping("/{id}/storeStatus/{storeStatus}")
    public CommonResult<Object> updateStoreStatus(@PathVariable Long id, @PathVariable Integer storeStatus,
        @RequestParam(required = false) Integer saleTime) {
        productCombinationService.updateStoreStatus(id, storeStatus, saleTime);
        return CommonResult.success();
    }

    // @PreAuthorize("hasAuthority('admin:product:combination:update:batchStoreStatus')")
    @ApiOperation("批量更新商品上架状态")
    @PutMapping("/batch/storeStatus/{storeStatus}")
    public CommonResult<Object> batchUpdateStoreStatus(@RequestBody List<Long> ids, @PathVariable Integer storeStatus,
        @RequestParam(required = false) Integer saleTime) {
        productCombinationService.batchUpdateStoreStatus(ids, storeStatus, saleTime);
        return CommonResult.success();
    }

    // @PreAuthorize("hasAuthority('admin:product:combination:update:stock')")
    @ApiOperation("更新库存数量")
    @PutMapping("/{id}/stock/{stock}")
    public CommonResult<Object> updateStock(@PathVariable Long id, @PathVariable Integer stock) {
        productCombinationService.updateStock(id, stock);
        return CommonResult.success();
    }

    // @PreAuthorize("hasAuthority('admin:product:combination:update:batchStock')")
    @ApiOperation("批量更新库存数量")
    @PutMapping("/batch/stock/{stock}")
    public CommonResult<Object> batchUpdateStock(@RequestBody List<Long> ids, @PathVariable Integer stock) {
        productCombinationService.batchUpdateStock(ids, stock);
        return CommonResult.success();
    }

    // @PreAuthorize("hasAuthority('admin:product:combination:update:allowDiscount')")
    @ApiOperation("更新是否允许使用满减券")
    @PutMapping("/{id}/allowDiscount/{allowDiscount}")
    public CommonResult<Object> updateAllowDiscount(@PathVariable Long id, @PathVariable Integer allowDiscount) {
        productCombinationService.updateAllowDiscount(id, allowDiscount);
        return CommonResult.success();
    }

    /**
     * 根据商品ID查询相关的组合
     * @param productId 商品ID
     */
    @ApiOperation(value = "根据商品ID查询组合列表")
    @GetMapping("/queryByProductId")
    public CommonResult<Map<String, Object>> queryByProductId(
            @ApiParam(value = "商品ID", required = true) @RequestParam Integer productId) {
        
        List<ProductCombination> productCombinations = productCombinationService.getByProductId(productId);
        
        Map<String, Object> result = new HashMap<>();
        result.put("list", productCombinations);
        result.put("total", productCombinations.size());
        
        return CommonResult.success(result);
    }
}