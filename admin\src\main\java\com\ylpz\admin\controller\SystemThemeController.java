package com.ylpz.admin.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.ylpz.core.common.page.CommonPage;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.core.common.request.SystemThemeRequest;
import com.ylpz.core.common.response.CommonResult;
import com.ylpz.core.service.SystemThemeIconService;
import com.ylpz.core.service.SystemThemeService;
import com.ylpz.model.system.SystemTheme;
import com.ylpz.model.system.SystemThemeIcon;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("api/admin/system/theme")
@Api(tags = "系统主题管理")
public class SystemThemeController {

    @Autowired
    private SystemThemeService systemThemeService;

    @Autowired
    private SystemThemeIconService systemThemeIconService;

    /**
     * 分页获取主题列表
     * 
     * @param pageParamRequest 分页参数
     */
    // @PreAuthorize("hasAuthority('admin:system:theme:list')")
    @ApiOperation(value = "主题列表")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public CommonResult<CommonPage<SystemTheme>> getList(@Validated PageParamRequest pageParamRequest) {
        List<SystemTheme> themeList = systemThemeService.getList(pageParamRequest);
        return CommonResult.success(CommonPage.restPage(themeList));
    }

    /**
     * 获取主题详情
     * 
     * @param id 主题ID
     */
    // @PreAuthorize("hasAuthority('admin:system:theme:info')")
    @ApiOperation(value = "主题详情")
    @RequestMapping(value = "/info/{id}", method = RequestMethod.GET)
    public CommonResult<Map<String, Object>> getInfo(@PathVariable Integer id) {
        SystemTheme theme = systemThemeService.getById(id);
        if (theme == null) {
            return CommonResult.failed("主题不存在");
        }

        List<SystemThemeIcon> icons = systemThemeIconService.getListByThemeId(id);

        Map<String, Object> map = new HashMap<>();
        map.put("theme", theme);
        map.put("icons", icons);

        return CommonResult.success(map);
    }

    /**
     * 新增主题
     * 
     * @param request 主题请求对象
     */
    // @PreAuthorize("hasAuthority('admin:system:theme:save')")
    @ApiOperation(value = "新增主题")
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public CommonResult<String> save(@RequestBody @Validated SystemThemeRequest request) {
        if (systemThemeService.add(request)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 编辑主题
     * 
     * @param request 主题请求对象
     */
    // @PreAuthorize("hasAuthority('admin:system:theme:update')")
    @ApiOperation(value = "编辑主题")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public CommonResult<String> update(@RequestBody @Validated SystemThemeRequest request) {
        if (request.getId() == null) {
            return CommonResult.failed("主题ID不能为空");
        }

        if (systemThemeService.edit(request)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 删除主题
     * 
     * @param id 主题ID
     */
    // @PreAuthorize("hasAuthority('admin:system:theme:delete')")
    @ApiOperation(value = "删除主题")
    @RequestMapping(value = "/delete/{id}", method = RequestMethod.GET)
    public CommonResult<String> delete(@PathVariable Integer id) {
        if (systemThemeService.delete(id)) {
            return CommonResult.success();
        }
        return CommonResult.failed("删除失败，可能是默认主题不能删除");
    }

    /**
     * 设置默认主题
     * 
     * @param id 主题ID
     */
    // @PreAuthorize("hasAuthority('admin:system:theme:setDefault')")
    @ApiOperation(value = "设置默认主题")
    @RequestMapping(value = "/setDefault/{id}", method = RequestMethod.GET)
    public CommonResult<String> setDefault(@PathVariable Integer id) {
        if (systemThemeService.setDefault(id)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 获取当前主题
     */
    // @ApiOperation(value = "获取当前主题")
    @RequestMapping(value = "/current", method = RequestMethod.GET)
    public CommonResult<Map<String, Object>> getCurrentTheme() {
        SystemTheme theme = systemThemeService.getCurrentTheme();
        if (theme == null) {
            return CommonResult.failed("未找到可用主题");
        }

        List<SystemThemeIcon> icons = systemThemeIconService.getListByThemeId(theme.getId());

        Map<String, Object> map = new HashMap<>();
        map.put("theme", theme);
        map.put("icons", icons);

        return CommonResult.success(map);
    }
}