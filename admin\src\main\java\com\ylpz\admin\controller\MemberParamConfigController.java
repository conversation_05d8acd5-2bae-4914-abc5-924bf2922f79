package com.ylpz.admin.controller;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.ylpz.core.common.constants.MemberParamConstants;
import com.ylpz.core.common.response.CommonResult;
import com.ylpz.core.common.vo.MemberParamConfigGroupVO;
import com.ylpz.core.service.MemberParamConfigService;
import com.ylpz.model.system.MemberParamConfig;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * 会员参数设置 前端控制器
 */
@Slf4j
@RestController
@RequestMapping("api/admin/system/member/param")
@Api(tags = "设置 -- 会员参数设置")
public class MemberParamConfigController {

    @Autowired
    private MemberParamConfigService memberParamConfigService;

    /**
     * 获取所有会员参数设置列表（按配置类型分组为具名字段）
     */
    // @PreAuthorize("hasAuthority('admin:system:member:param:list')")
    @ApiOperation(value = "获取所有会员参数设置列表（按配置类型分组为具名字段）")
    @RequestMapping(value = "/group", method = RequestMethod.GET)
    public CommonResult<MemberParamConfigGroupVO> getGroupedConfig() {
        List<MemberParamConfig> list = memberParamConfigService.getList();
        // 按配置类型分组
        Map<Integer, List<MemberParamConfig>> groupedMap =
            list.stream().collect(Collectors.groupingBy(MemberParamConfig::getConfigType));
        // 转换为前端友好的VO对象
        MemberParamConfigGroupVO groupVO = MemberParamConfigGroupVO.buildFromMap(groupedMap);
        return CommonResult.success(groupVO);
    }

    /**
     * 根据配置类型获取会员参数设置列表
     */
    // @PreAuthorize("hasAuthority('admin:system:member:param:list')")
    @ApiOperation(value = "根据配置类型获取会员参数设置列表")
    @RequestMapping(value = "/list/{configType}", method = RequestMethod.GET)
    public CommonResult<List<MemberParamConfig>>
        getListByConfigType(@PathVariable(value = "configType") Integer configType) {
        return CommonResult.success(memberParamConfigService.getListByConfigType(configType));
    }

    /**
     * 获取所有会员参数设置列表
     */
    // @PreAuthorize("hasAuthority('admin:system:member:param:list')")
    @ApiOperation(value = "获取所有会员参数设置列表")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public CommonResult<List<MemberParamConfig>> getAllList() {
        return CommonResult.success(memberParamConfigService.getList());
    }

    /**
     * 按配置类型批量保存会员参数设置
     */
    // @PreAuthorize("hasAuthority('admin:system:member:param:save')")
    @ApiOperation(value = "按配置类型批量保存会员参数设置")
    @RequestMapping(value = "/batch/save/{configType}", method = RequestMethod.POST)
    public CommonResult<String> batchSaveByType(@PathVariable(value = "configType") Integer configType,
        @RequestBody @Validated List<MemberParamConfig> configList) {
        // 确保所有配置项都是同一类型
        for (MemberParamConfig config : configList) {
            config.setConfigType(configType);
        }

        if (memberParamConfigService.saveOrUpdateBatchByType(configType, configList)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 保存整个会员参数设置表单
     */
    // @PreAuthorize("hasAuthority('admin:system:member:param:save')")
    @ApiOperation(value = "保存整个会员参数设置表单")
    @RequestMapping(value = "/saveForm", method = RequestMethod.POST)
    public CommonResult<String> saveForm(@RequestBody @Validated MemberParamConfigGroupVO groupVO) {
        try {
            // 保存成长值设置
            if (groupVO.getExperienceConfigList() != null) {
                memberParamConfigService.saveOrUpdateBatchByType(MemberParamConstants.CONFIG_TYPE_EXPERIENCE,
                    groupVO.getExperienceConfigList());
            }

            // 保存提现设置
            if (groupVO.getWithdrawConfigList() != null) {
                memberParamConfigService.saveOrUpdateBatchByType(MemberParamConstants.CONFIG_TYPE_WITHDRAW,
                    groupVO.getWithdrawConfigList());
            }

            // 保存佣金返现设置
            if (groupVO.getCommissionConfigList() != null) {
                memberParamConfigService.saveOrUpdateBatchByType(MemberParamConstants.CONFIG_TYPE_COMMISSION,
                    groupVO.getCommissionConfigList());
            }

            // 保存奖励金设置
            if (groupVO.getBonusConfigList() != null) {
                memberParamConfigService.saveOrUpdateBatchByType(MemberParamConstants.CONFIG_TYPE_BONUS,
                    groupVO.getBonusConfigList());
            }

            return CommonResult.success("保存会员参数设置成功");
        } catch (Exception e) {
            log.error("保存会员参数设置失败", e);
            return CommonResult.failed("保存失败：" + e.getMessage());
        }
    }
}