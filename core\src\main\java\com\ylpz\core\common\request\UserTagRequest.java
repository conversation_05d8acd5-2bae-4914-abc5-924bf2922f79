package com.ylpz.core.common.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 用户分组表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="UserTagRequest对象", description="会员标签")
public class UserTagRequest implements Serializable {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "标签名称")
    @NotBlank(message = "请填写标签名称")
    @Length(max = 50, message = "标签名称不能超过50个字符")
    private String name;

    @ApiModelProperty(value = "标签类型，1-手动打标", example = "1")
    private Integer tagType = 1;
}
