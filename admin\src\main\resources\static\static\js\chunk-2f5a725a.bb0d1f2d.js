(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2f5a725a"],{"0974":function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"divBox"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("div",{staticClass:"filter-container"},[a("div",{staticClass:"demo-input-suffix acea-row"},[a("span",{staticClass:"seachTiele"},[t._v("状态：")]),t._v(" "),a("el-select",{staticClass:"filter-item selWidth mr20",attrs:{placeholder:"请选择",clearable:""},on:{change:t.seachList},model:{value:t.tableFrom.status,callback:function(e){t.$set(t.tableFrom,"status",e)},expression:"tableFrom.status"}},[a("el-option",{attrs:{label:"未开启",value:0}}),t._v(" "),a("el-option",{attrs:{label:"开启",value:1}})],1),t._v(" "),a("span",{staticClass:"seachTiele"},[t._v("折扣名称：")]),t._v(" "),a("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入折扣名称",clearable:""},model:{value:t.tableFrom.name,callback:function(e){t.$set(t.tableFrom,"name",e)},expression:"tableFrom.name"}},[a("el-button",{attrs:{slot:"append",icon:"el-icon-search",size:"small"},on:{click:t.seachList},slot:"append"})],1)],1)]),t._v(" "),a("router-link",{attrs:{to:{path:"/marketing/discount/list/save"}}},[a("el-button",{attrs:{size:"small",type:"primary"}},[t._v("添加折扣")])],1)],1),t._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:t.tableData.data,size:"mini","header-cell-style":{fontWeight:"bold"}}},[a("el-table-column",{attrs:{prop:"id",label:"ID","min-width":"50"}}),t._v(" "),a("el-table-column",{attrs:{prop:"name",label:"名称","min-width":"180"}}),t._v(" "),a("el-table-column",{attrs:{prop:"type",label:"折扣类型","min-width":"80"}}),t._v(" "),a("el-table-column",{attrs:{prop:"useType",label:"使用类型","min-width":"80"}}),t._v(" "),a("el-table-column",{attrs:{prop:"discount",label:"折扣率","min-width":"80"}}),t._v(" "),a("el-table-column",{attrs:{prop:"total",label:"发布数量","min-width":"80"}}),t._v(" "),a("el-table-column",{attrs:{"min-width":"260",label:"领取日期"},scopedSlots:t._u([{key:"default",fn:function(e){var s=e.row;return[s.receiveEndTime?a("div",[t._v("\n            "+t._s(s.receiveStartTime)+" - "+t._s(s.receiveEndTime)+"\n          ")]):a("span",[t._v("不限时")])]}}])}),t._v(" "),a("el-table-column",{attrs:{"min-width":"260",label:"使用时间"},scopedSlots:t._u([{key:"default",fn:function(e){var s=e.row;return[s.day?a("div",[t._v(t._s(s.day)+"天")]):a("span",[t._v(" "+t._s(s.useStartTime)+" - "+t._s(s.useEndTime)+" ")])]}}])}),t._v(" "),a("el-table-column",{attrs:{"min-width":"100",label:"发布数量"},scopedSlots:t._u([{key:"default",fn:function(e){var s=e.row;return[s.isLimited?a("div",[a("span",{staticClass:"fa"},[t._v("发布："+t._s(s.total))]),t._v(" "),a("span",{staticClass:"sheng"},[t._v("剩余："+t._s(s.lastTotal))])]):a("span",[t._v("不限量")])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"是否开启","min-width":"100",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return t.checkPermi(["admin:coupon:update:status"])?[a("el-switch",{attrs:{width:"60","active-value":!0,"inactive-value":!1,"active-text":"开启","inactive-text":"关闭"},nativeOn:{click:function(a){return t.onchangeIsShow(e.row)}},model:{value:e.row.status,callback:function(a){t.$set(e.row,"status",a)},expression:"scope.row.status"}})]:void 0}}],null,!0)}),t._v(" "),a("el-table-column",{attrs:{label:"操作","min-width":"180",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:coupon:user:list"],expression:"['admin:coupon:user:list']"}],staticClass:"mr10",attrs:{type:"text",size:"small"},on:{click:function(a){return t.receive(e.row)}}},[t._v("领取记录")]),t._v(" "),a("router-link",{attrs:{to:{path:"/marketing/coupon/list/save/"+e.row.id}}},[e.row.status?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:coupon:info"],expression:"['admin:coupon:info']"}],staticClass:"mr10",attrs:{type:"text",size:"small"}},[t._v("复制")]):t._e()],1),t._v(" "),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:coupon:delete"],expression:"['admin:coupon:delete']"}],staticClass:"mr10",attrs:{type:"text",size:"small"},on:{click:function(a){return t.handleDelMenu(e.row)}}},[t._v("删除")])]}}])})],1),t._v(" "),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{"page-sizes":[20,40,60,80],"page-size":t.tableFrom.limit,"current-page":t.tableFrom.page,layout:"total, sizes, prev, pager, next, jumper",total:t.tableData.total},on:{"size-change":t.handleSizeChange,"current-change":t.pageChange}})],1)],1),t._v(" "),a("el-dialog",{attrs:{title:"领取记录",visible:t.dialogVisible,width:"500px","before-close":t.handleClose},on:{"update:visible":function(e){t.dialogVisible=e}}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.Loading,expression:"Loading"}],staticStyle:{width:"100%"},attrs:{data:t.issueData.data}},[a("el-table-column",{attrs:{prop:"nickname",label:"用户名","min-width":"120"}}),t._v(" "),a("el-table-column",{attrs:{label:"用户头像","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(t){return[a("div",{staticClass:"demo-image__preview"},[a("el-image",{staticStyle:{width:"36px",height:"36px"},attrs:{src:t.row.avatar,"preview-src-list":[t.row.avatar]}})],1)]}}])}),t._v(" "),a("el-table-column",{attrs:{prop:"createTime",label:"领取时间","min-width":"180"}})],1),t._v(" "),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{"page-sizes":[10,20,30,40],"page-size":t.tableFromIssue.limit,"current-page":t.tableFromIssue.page,layout:"total, sizes, prev, pager, next, jumper",total:t.issueData.total},on:{"size-change":t.handleSizeChangeIssue,"current-change":t.pageChangeIssue}})],1)],1)],1)},i=[],l=a("b7be"),n=a("83d6"),o=a("e350"),r={name:"DiscountList",data:function(){return{Loading:!1,dialogVisible:!1,roterPre:n["roterPre"],listLoading:!0,tableData:{data:[],total:0},tableFrom:{page:1,limit:20,status:"",name:""},tableFromIssue:{page:1,limit:10,discountId:""},issueData:{data:[],total:0}}},mounted:function(){this.getList()},methods:{checkPermi:o["a"],seachList:function(){this.tableFrom.page=1,this.getList()},handleClose:function(){this.dialogVisible=!1},receive:function(t){this.dialogVisible=!0,this.tableFromIssue.discountId=t.id,this.getIssueList()},getIssueList:function(){var t=this;this.Loading=!0,Object(l["x"])(this.tableFromIssue).then((function(e){t.issueData.data=e.list,t.issueData.total=e.total,t.Loading=!1})).catch((function(e){t.Loading=!1,t.$message.error(e.message)}))},pageChangeIssue:function(t){this.tableFromIssue.page=t,this.getIssueList()},handleSizeChangeIssue:function(t){this.tableFromIssue.limit=t,this.getIssueList()},getList:function(){var t=this;this.listLoading=!0,Object(l["z"])(this.tableFrom).then((function(e){t.tableData.data=e.list,t.tableData.total=e.total,t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},pageChange:function(t){this.tableFrom.page=t,this.getList()},handleSizeChange:function(t){this.tableFrom.limit=t,this.getList()},onchangeIsShow:function(t){},handleDelMenu:function(t){var e=this;this.$confirm("确定删除当前数据?").then((function(){Object(l["y"])({id:t.id}).then((function(t){e.$message.success("删除成功"),e.getList()}))}))}}},c=r,u=(a("d54e"),a("2877")),d=Object(u["a"])(c,s,i,!1,null,"b882505a",null);e["default"]=d.exports},"65a4":function(t,e,a){},d54e:function(t,e,a){"use strict";a("65a4")}}]);