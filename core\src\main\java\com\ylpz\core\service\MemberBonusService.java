package com.ylpz.core.service;

import com.ylpz.core.common.request.BonusRecordRequest;
import com.ylpz.core.common.request.PageParamRequest;
import com.github.pagehelper.PageInfo;
import com.ylpz.model.user.UserBrokerageRecord;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 奖励金服务接口
 */
public interface MemberBonusService {

    /**
     * 获取奖励金记录列表
     * 
     * @param request          查询条件
     * @param pageParamRequest 分页参数
     * @return 奖励金记录分页数据
     */
    PageInfo<UserBrokerageRecord> getBonusList(BonusRecordRequest request, PageParamRequest pageParamRequest);

    /**
     * 发放排行榜奖励金
     * 
     * @param rankType 排行榜类型（周排行、月排行、季度排行）
     * @param rankDate 排行日期
     * @return 是否发放成功
     */
    boolean distributeRankBonus(String rankType, Date rankDate);

    /**
     * 发放推广升级奖励金
     * 
     * @param uid       被推广用户ID
     * @param spreadUid 推广人用户ID
     * @return 是否发放成功
     */
    boolean distributeUpgradeBonus(Integer uid, Integer spreadUid);

    /**
     * 发放推广充值奖励金
     * 
     * @param uid            充值用户ID
     * @param spreadUid      推广人用户ID
     * @param rechargeAmount 充值金额
     * @return 是否发放成功
     */
    boolean distributeRechargeBonus(Integer uid, Integer spreadUid, BigDecimal rechargeAmount);

    /**
     * 发放首单购买奖励金
     * 
     * @param uid         购买用户ID
     * @param spreadUid   推广人用户ID
     * @param orderNo     订单号
     * @param orderAmount 订单金额
     * @return 是否发放成功
     */
    boolean distributeFirstOrderBonus(Integer uid, Integer spreadUid, String orderNo, BigDecimal orderAmount);

    /**
     * 获取奖励金合计金额
     * 
     * @param request 查询条件
     * @return 奖励金合计金额
     */
    BigDecimal getBonusTotal(BonusRecordRequest request);
}