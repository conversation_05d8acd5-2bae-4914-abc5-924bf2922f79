(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0ed55421"],{"2f2c":function(t,e,r){"use strict";r.d(e,"b",(function(){return d})),r.d(e,"c",(function(){return l})),r.d(e,"r",(function(){return f})),r.d(e,"d",(function(){return p})),r.d(e,"a",(function(){return m})),r.d(e,"g",(function(){return h})),r.d(e,"h",(function(){return v})),r.d(e,"j",(function(){return y})),r.d(e,"i",(function(){return g})),r.d(e,"e",(function(){return b})),r.d(e,"o",(function(){return w})),r.d(e,"q",(function(){return x})),r.d(e,"l",(function(){return O})),r.d(e,"m",(function(){return j})),r.d(e,"n",(function(){return _})),r.d(e,"p",(function(){return N})),r.d(e,"k",(function(){return C})),r.d(e,"f",(function(){return L}));var n=r("b775");function o(t){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o(t)}function i(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function a(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?i(Object(r),!0).forEach((function(e){s(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function s(t,e,r){return(e=u(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function u(t){var e=c(t,"string");return"symbol"==o(e)?e:e+""}function c(t,e){if("object"!=o(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function d(t){return Object(n["a"])({url:"/admin/system/city/list",method:"get",params:a({},t)})}function l(){return Object(n["a"])({url:"/admin/system/city/list/tree",method:"get"})}function f(t){return Object(n["a"])({url:"/admin/system/city/update/status",method:"post",params:a({},t)})}function p(t){return Object(n["a"])({url:"/admin/system/city/update",method:"post",params:a({},t)})}function m(t){return Object(n["a"])({url:"/admin/system/city/info",method:"get",params:a({},t)})}function h(t){return Object(n["a"])({url:"/admin/express/list",method:"get",params:a({},t)})}function v(){return Object(n["a"])({url:"/admin/express/sync/express",method:"post"})}function y(t){return Object(n["a"])({url:"/admin/express/update/show",method:"post",data:t})}function g(t){return Object(n["a"])({url:"/admin/express/update",method:"post",data:t})}function b(t){return Object(n["a"])({url:"/admin/express/delete",method:"GET",params:a({},t)})}function w(t){return Object(n["a"])({url:"/admin/express/shipping/templates/list",method:"get",params:a({},t)})}function x(t){return Object(n["a"])({url:"/admin/express/shipping/templates/info",method:"get",params:a({},t)})}function O(t){return Object(n["a"])({url:"/admin/express/shipping/free/list",method:"get",params:a({},t)})}function j(t){return Object(n["a"])({url:"admin/express/shipping/region/list",method:"get",params:a({},t)})}function _(t){return Object(n["a"])({url:"admin/express/shipping/templates/save",method:"post",data:t})}function N(t,e){return Object(n["a"])({url:"admin/express/shipping/templates/update",method:"post",data:t,params:a({},e)})}function C(t){return Object(n["a"])({url:"admin/express/shipping/templates/delete",method:"get",params:t})}function L(t){return Object(n["a"])({url:"admin/express/info",method:"get",params:a({},t)})}},"719d":function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"deliver-goods"},[r("header",[r("div",{staticClass:"order-num acea-row row-between-wrapper"},[r("div",{staticClass:"num line1"},[t._v("订单号："+t._s(t.orderId))]),t._v(" "),r("div",{staticClass:"name line1"},[r("span",{staticClass:"iconfont iconios-contact"}),t._v(t._s(t.delivery.nikeName)+"\n      ")])]),t._v(" "),r("div",{staticClass:"address"},[r("div",{staticClass:"name"},[t._v("\n        "+t._s(t.delivery.realName)),r("span",{staticClass:"phone"},[t._v(t._s(t.delivery.userPhone))])]),t._v(" "),r("div",[t._v(t._s(t.delivery.userAddress))])]),t._v(" "),t._m(0)]),t._v(" "),r("div",{staticClass:"wrapper"},[r("div",{staticClass:"item acea-row row-between-wrapper"},[r("div",[t._v("发货方式")]),t._v(" "),r("div",{staticClass:"mode acea-row row-middle row-right"},t._l(t.types,(function(e,n){return r("div",{key:n,staticClass:"goods",class:t.active===n?"on":"",on:{click:function(r){return t.changeType(e,n)}}},[t._v("\n          "+t._s(e.title)),r("span",{staticClass:"iconfont icon-xuanzhong2"})])})),0)]),t._v(" "),r("div",{directives:[{name:"show",rawName:"v-show",value:0===t.active,expression:"active === 0"}],staticClass:"list"},[r("div",{staticClass:"item acea-row row-between-wrapper"},[r("div",[t._v("发货方式")]),t._v(" "),r("select",{directives:[{name:"model",rawName:"v-model",value:t.expressCode,expression:"expressCode"}],staticClass:"mode",on:{change:function(e){var r=Array.prototype.filter.call(e.target.options,(function(t){return t.selected})).map((function(t){var e="_value"in t?t._value:t.value;return e}));t.expressCode=e.target.multiple?r:r[0]}}},[r("option",{attrs:{value:""}},[t._v("选择快递公司")]),t._v(" "),t._l(t.express,(function(e,n){return r("option",{key:n,domProps:{value:e.code}},[t._v(t._s(e.name))])}))],2),t._v(" "),r("span",{staticClass:"iconfont icon-up"})]),t._v(" "),r("div",{staticClass:"item acea-row row-between-wrapper"},[r("div",[t._v("快递单号")]),t._v(" "),r("input",{directives:[{name:"model",rawName:"v-model",value:t.expressNumber,expression:"expressNumber"}],staticClass:"mode",attrs:{type:"text",placeholder:"填写快递单号"},domProps:{value:t.expressNumber},on:{input:function(e){e.target.composing||(t.expressNumber=e.target.value)}}})])]),t._v(" "),r("div",{directives:[{name:"show",rawName:"v-show",value:1===t.active,expression:"active === 1"}],staticClass:"list"},[r("div",{staticClass:"item acea-row row-between-wrapper"},[r("div",[t._v("送货人")]),t._v(" "),r("input",{directives:[{name:"model",rawName:"v-model",value:t.deliveryName,expression:"deliveryName"}],staticClass:"mode",attrs:{type:"text",placeholder:"填写送货人"},domProps:{value:t.deliveryName},on:{input:function(e){e.target.composing||(t.deliveryName=e.target.value)}}})]),t._v(" "),r("div",{staticClass:"item acea-row row-between-wrapper"},[r("div",[t._v("送货电话")]),t._v(" "),r("input",{directives:[{name:"model",rawName:"v-model",value:t.deliveryTel,expression:"deliveryTel"}],staticClass:"mode",attrs:{type:"text",placeholder:"填写送货电话"},domProps:{value:t.deliveryTel},on:{input:function(e){e.target.composing||(t.deliveryTel=e.target.value)}}})])])]),t._v(" "),r("div",{staticStyle:{height:"1.2rem"}}),t._v(" "),r("div",{staticClass:"confirm",on:{click:t.saveInfo}},[t._v("确认提交")])])},o=[function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"line"},[n("img",{attrs:{src:r("754f")}})])}],i=r("f8b7"),a=r("2f2c"),s=r("61f7"),u=r("69ae");function c(t){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},c(t)}function d(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */d=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function l(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function(t,e,r){return t[e]=r}}function f(t,e,r,n){var i=e&&e.prototype instanceof b?e:b,a=Object.create(i.prototype),s=new T(n||[]);return o(a,"_invoke",{value:k(t,r,s)}),a}function p(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var m="suspendedStart",h="suspendedYield",v="executing",y="completed",g={};function b(){}function w(){}function x(){}var O={};l(O,a,(function(){return this}));var j=Object.getPrototypeOf,_=j&&j(j(I([])));_&&_!==r&&n.call(_,a)&&(O=_);var N=x.prototype=b.prototype=Object.create(O);function C(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function L(t,e){function r(o,i,a,s){var u=p(t[o],t,i);if("throw"!==u.type){var d=u.arg,l=d.value;return l&&"object"==c(l)&&n.call(l,"__await")?e.resolve(l.__await).then((function(t){r("next",t,a,s)}),(function(t){r("throw",t,a,s)})):e.resolve(l).then((function(t){d.value=t,a(d)}),(function(t){return r("throw",t,a,s)}))}s(u.arg)}var i;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return i=i?i.then(o,o):o()}})}function k(e,r,n){var o=m;return function(i,a){if(o===v)throw Error("Generator is already running");if(o===y){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var s=n.delegate;if(s){var u=E(s,n);if(u){if(u===g)continue;return u}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===m)throw o=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=v;var c=p(e,r,n);if("normal"===c.type){if(o=n.done?y:h,c.arg===g)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o=y,n.method="throw",n.arg=c.arg)}}}function E(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,E(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),g;var i=p(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,g;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,g):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,g)}function P(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function S(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(P,this),this.reset(!0)}function I(e){if(e||""===e){var r=e[a];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(c(e)+" is not iterable")}return w.prototype=x,o(N,"constructor",{value:x,configurable:!0}),o(x,"constructor",{value:w,configurable:!0}),w.displayName=l(x,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,x):(t.__proto__=x,l(t,u,"GeneratorFunction")),t.prototype=Object.create(N),t},e.awrap=function(t){return{__await:t}},C(L.prototype),l(L.prototype,s,(function(){return this})),e.AsyncIterator=L,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new L(f(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},C(N),l(N,u,"Generator"),l(N,a,(function(){return this})),l(N,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=I,T.prototype={constructor:T,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(S),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return s.type="throw",s.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],s=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var u=n.call(a,"catchLoc"),c=n.call(a,"finallyLoc");if(u&&c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,g):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),g},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),S(r),g}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;S(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:I(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),g}},e}function l(t,e,r,n,o,i,a){try{var s=t[i](a),u=s.value}catch(t){return void r(t)}s.done?e(u):Promise.resolve(u).then(n,o)}function f(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){l(i,n,o,a,s,"next",t)}function s(t){l(i,n,o,a,s,"throw",t)}a(void 0)}))}}var p={name:"GoodsDeliver",components:{},props:{},data:function(){return{types:[{type:"1",title:"发货"},{type:"2",title:"送货"},{type:"3",title:"无需发货"}],active:0,orderId:"",delivery:{},express:[],type:"1",deliveryName:"",expressCode:"",expressNumber:"",deliveryTel:""}},watch:{"$route.params.oid":function(t){var e=this;void 0!=t&&(e.orderId=t,e.getIndex())}},created:function(){r.e("chunk-2d0d6f43").then(r.t.bind(null,"756e",7))},mounted:function(){this.orderId=this.$route.params.oid,this.getIndex(),this.getLogistics()},methods:{changeType:function(t,e){this.active=e,this.type=t.type,this.deliveryName="",this.deliveryTel="",this.expressCode="",this.expressNumber=""},getIndex:function(){var t=this;Object(i["f"])({orderNo:this.orderId}).then((function(e){t.delivery=e})).catch((function(e){t.$dialog.error(e.message)}))},getLogistics:function(){var t=this;Object(a["g"])({page:1,limit:999,isShow:1}).then(function(){var e=f(d().mark((function e(r){return d().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.express=r.list;case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}())},saveInfo:function(){var t=f(d().mark((function t(){var e,r,n,o;return d().wrap((function(t){while(1)switch(t.prev=t.next){case 0:e=this,r=e.deliveryName,n=e.deliveryTel,o={},o.type=e.type,o.orderNo=e.orderId,t.t0=e.type,t.next="1"===t.t0?6:"2"===t.t0?15:"3"===t.t0?27:29;break;case 6:if(e.expressCode){t.next=8;break}return t.abrupt("return",e.$dialog.error("请输入快递公司"));case 8:if(e.expressNumber){t.next=10;break}return t.abrupt("return",e.$dialog.error("请输入快递单号"));case 10:return o.expressNumber=e.expressNumber,o.expressRecordType=1,o.expressCode=e.expressCode,e.setInfo(o),t.abrupt("break",29);case 15:return t.prev=15,t.next=18,this.$validator({deliveryName:[Object(s["f"])(s["f"].message("发货人姓名"))],deliveryTel:[Object(s["f"])(s["f"].message("发货人电话"))]}).validate({deliveryName:r,deliveryTel:n});case 18:t.next=23;break;case 20:return t.prev=20,t.t1=t["catch"](15),t.abrupt("return",Object(u["b"])(t.t1));case 23:return o.deliveryName=r,o.deliveryTel=n,e.setInfo(o),t.abrupt("break",29);case 27:return e.setInfo(o),t.abrupt("break",29);case 29:case"end":return t.stop()}}),t,this,[[15,20]])})));function e(){return t.apply(this,arguments)}return e}(),setInfo:function(t){var e=this;Object(i["n"])(t).then((function(t){e.$dialog.success("发送货成功"),e.$router.go(-1)}),(function(t){e.$dialog.error(t.message)}))}}},m=p,h=(r("ed00"),r("2877")),v=Object(h["a"])(m,n,o,!1,null,"7a8e852e",null);e["default"]=v.exports},"754f":function(t,e,r){t.exports=r.p+"static/img/line.05bf1c84.jpg"},df8c:function(t,e,r){},ed00:function(t,e,r){"use strict";r("df8c")},f8b7:function(t,e,r){"use strict";r.d(e,"g",(function(){return o})),r.d(e,"p",(function(){return i})),r.d(e,"h",(function(){return a})),r.d(e,"e",(function(){return s})),r.d(e,"i",(function(){return u})),r.d(e,"f",(function(){return c})),r.d(e,"j",(function(){return d})),r.d(e,"n",(function(){return l})),r.d(e,"m",(function(){return f})),r.d(e,"l",(function(){return p})),r.d(e,"w",(function(){return m})),r.d(e,"v",(function(){return h})),r.d(e,"o",(function(){return v})),r.d(e,"s",(function(){return y})),r.d(e,"t",(function(){return g})),r.d(e,"q",(function(){return b})),r.d(e,"r",(function(){return w})),r.d(e,"d",(function(){return x})),r.d(e,"b",(function(){return O})),r.d(e,"u",(function(){return j})),r.d(e,"k",(function(){return _})),r.d(e,"a",(function(){return N}));var n=r("b775");function o(t){return Object(n["a"])({url:"/admin/store/order/list",method:"get",params:t})}function i(t){return Object(n["a"])({url:"/admin/store/order/status/num",method:"get",params:t})}function a(t){return Object(n["a"])({url:"/admin/store/order/list/data",method:"get",params:t})}function s(t){return Object(n["a"])({url:"/admin/store/order/delete",method:"get",params:t})}function u(t){return Object(n["a"])({url:"/admin/store/order/status/list",method:"get",params:t})}function c(t){return Object(n["a"])({url:"/admin/store/order/info",method:"get",params:t})}function d(t){return Object(n["a"])({url:"/admin/store/order/mark",method:"post",params:t})}function l(t){return Object(n["a"])({url:"/admin/store/order/send",method:"post",data:t})}function f(t){return Object(n["a"])({url:"/admin/store/order/refund/refuse",method:"get",params:t})}function p(t){return Object(n["a"])({url:"/admin/store/order/refund",method:"get",params:t})}function m(t){return Object(n["a"])({url:"/admin/store/order/writeUpdate/".concat(t),method:"get"})}function h(t){return Object(n["a"])({url:"/admin/store/order/writeConfirm/".concat(t),method:"get"})}function v(){return Object(n["a"])({url:"/admin/store/order/statistics",method:"get"})}function y(t){return Object(n["a"])({url:"/admin/store/order/statisticsData",method:"get",params:t})}function g(t){return Object(n["a"])({url:"admin/store/order/update/price",method:"post",data:t})}function b(t){return Object(n["a"])({url:"/admin/store/order/time",method:"get",params:t})}function w(){return Object(n["a"])({url:"/admin/store/order/sheet/info",method:"get"})}function x(t){return Object(n["a"])({url:"/admin/store/order/getLogisticsInfo",method:"get",params:t})}function O(){return Object(n["a"])({url:"/admin/pay/component/delivery/company/get/list",method:"get"})}function j(t){return Object(n["a"])({url:"/admin/store/order/video/send",method:"post",data:t})}function _(t){return Object(n["a"])({url:"/admin/yly/print/".concat(t),method:"get"})}function N(t){return Object(n["a"])({url:"/admin/store/order/auditAddressList",method:"get",params:t})}}}]);