package com.ylpz.admin.controller;

import com.github.pagehelper.PageInfo;
import com.ylpz.core.common.request.BonusRecordRequest;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.core.common.response.CommonResult;
import com.ylpz.core.service.MemberBonusService;
import com.ylpz.model.user.UserBrokerageRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 会员奖励金控制器
 */
@Slf4j
@RestController
@RequestMapping("api/admin/member/bonus")
@Api(tags = "会员奖励金管理")
public class MemberBonusController {

    @Autowired
    private MemberBonusService memberBonusService;

    /**
     * 获取奖励金记录列表
     */
    @ApiOperation(value = "获取奖励金记录列表")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public CommonResult<PageInfo<UserBrokerageRecord>> getBonusList(
            @ModelAttribute BonusRecordRequest request,
            @ModelAttribute PageParamRequest pageParamRequest) {
        PageInfo<UserBrokerageRecord> pageInfo = memberBonusService.getBonusList(request, pageParamRequest);
        return CommonResult.success(pageInfo);
    }

    /**
     * 获取奖励金合计金额
     */
    @ApiOperation(value = "获取奖励金合计金额")
    @RequestMapping(value = "/total", method = RequestMethod.GET)
    public CommonResult<BigDecimal> getBonusTotal(@ModelAttribute BonusRecordRequest request) {
        BigDecimal total = memberBonusService.getBonusTotal(request);
        return CommonResult.success(total);
    }

    /**
     * 发放排行榜奖励金
     */
    @ApiOperation(value = "发放排行榜奖励金")
    @RequestMapping(value = "/distribute/rank", method = RequestMethod.POST)
    public CommonResult<Boolean> distributeRankBonus(
            @RequestParam String rankType,
            @RequestParam Date rankDate) {
        boolean result = memberBonusService.distributeRankBonus(rankType, rankDate);
        return CommonResult.success(result);
    }

    /**
     * 发放推广升级奖励金
     */
    @ApiOperation(value = "发放推广升级奖励金")
    @RequestMapping(value = "/distribute/upgrade", method = RequestMethod.POST)
    public CommonResult<Boolean> distributeUpgradeBonus(
            @RequestParam Integer uid,
            @RequestParam Integer spreadUid) {
        boolean result = memberBonusService.distributeUpgradeBonus(uid, spreadUid);
        return CommonResult.success(result);
    }

    /**
     * 发放推广充值奖励金
     */
    @ApiOperation(value = "发放推广充值奖励金")
    @RequestMapping(value = "/distribute/recharge", method = RequestMethod.POST)
    public CommonResult<Boolean> distributeRechargeBonus(
            @RequestParam Integer uid,
            @RequestParam Integer spreadUid,
            @RequestParam BigDecimal rechargeAmount) {
        boolean result = memberBonusService.distributeRechargeBonus(uid, spreadUid, rechargeAmount);
        return CommonResult.success(result);
    }

    /**
     * 发放首单购买奖励金
     */
    @ApiOperation(value = "发放首单购买奖励金")
    @RequestMapping(value = "/distribute/firstOrder", method = RequestMethod.POST)
    public CommonResult<Boolean> distributeFirstOrderBonus(
            @RequestParam Integer uid,
            @RequestParam Integer spreadUid,
            @RequestParam String orderNo,
            @RequestParam BigDecimal orderAmount) {
        boolean result = memberBonusService.distributeFirstOrderBonus(uid, spreadUid, orderNo, orderAmount);
        return CommonResult.success(result);
    }
}