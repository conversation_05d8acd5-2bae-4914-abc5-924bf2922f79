package com.ylpz.admin.task.product;

import com.ylpz.core.common.utils.DateUtil;
import com.ylpz.core.service.StoreProductService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 商品自动上架定时任务
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2025-02-20 19:31
 */
@Component
@Configuration //读取配置
@EnableScheduling // 2.开启定时任务
public class ProductAutoOnShelfTask {
    //日志
    private static final Logger logger = LoggerFactory.getLogger(ProductAutoOnShelfTask.class);

    @Autowired
    private StoreProductService storeProductService;

    @Scheduled(fixedDelay = 1000 * 60L) //1分钟同步一次数据
    public void init() {
        logger.info("---ProductAutoOnShelfTask task------checking products to auto on-shelf: Execution Time - {}", DateUtil.nowDateTime());
        try {
            storeProductService.autoOnShelf();
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("ProductAutoOnShelfTask.task" + " | msg : " + e.getMessage());
        }
    }
} 