package com.ylpz.entity;

public interface ShippingTemplatesRegionDefine {
    String renewalPrice = "renewalPrice";
    String renewal = "renewal";
    String updateTime = "updateTime";
    String cityId = "cityId";
    String title = "title";
    String type = "type";
    String firstPrice = "firstPrice";
    String createTime = "createTime";
    String uniqid = "uniqid";
    String id = "id";
    String tempId = "tempId";
    String first = "first";
    String status = "status";
}
