package com.ylpz.admin.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ylpz.core.common.page.CommonPage;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.core.common.request.StoreActivityRequest;
import com.ylpz.core.common.request.StoreActivitySearchRequest;
import com.ylpz.core.common.response.CommonResult;
import com.ylpz.core.common.response.StoreActivityResponse;
import com.ylpz.core.service.StoreActivityService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * 活动管理表 前端控制器
 */
@Slf4j
@RestController
@RequestMapping("api/admin/marketing/activity")
@Api(tags = "营销 -- 活动")
public class StoreActivityController {

    @Autowired
    private StoreActivityService storeActivityService;

    /**
     * 分页显示活动管理表
     * 
     * @param request 搜索条件
     * @param pageParamRequest 分页参数
     */
    //@PreAuthorize("hasAuthority('admin:activity:list')")
    @ApiOperation(value = "分页列表")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public CommonResult<CommonPage<StoreActivityResponse>> getList(@Validated StoreActivitySearchRequest request,
        @Validated PageParamRequest pageParamRequest) {
        return CommonResult.success(CommonPage.restPage(storeActivityService.getList(request, pageParamRequest)));
    }

    /**
     * 新增活动管理表
     * 
     * @param request 新增参数
     */
    //@PreAuthorize("hasAuthority('admin:activity:save')")
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public CommonResult<String> save(@RequestBody @Validated StoreActivityRequest request) {
        if (storeActivityService.create(request)) {
            return CommonResult.success();
        } else {
            return CommonResult.failed();
        }
    }

    /**
     * 删除活动管理表
     * 
     * @param id Integer
     */
    //@PreAuthorize("hasAuthority('admin:activity:delete')")
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    public CommonResult<String> delete(@RequestParam(value = "id") Integer id) {
        if (storeActivityService.deleteById(id)) {
            return CommonResult.success();
        } else {
            return CommonResult.failed();
        }
    }

    /**
     * 修改活动管理表
     * 
     * @param id integer id
     * @param request 修改参数
     */
    //@PreAuthorize("hasAuthority('admin:activity:update')")
    @ApiOperation(value = "修改")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public CommonResult<String> update(@RequestParam Integer id, @RequestBody @Validated StoreActivityRequest request) {
        if (storeActivityService.updateActivity(id, request)) {
            return CommonResult.success();
        } else {
            return CommonResult.failed();
        }
    }

    /**
     * 查询活动管理表信息
     * 
     * @param id Integer
     */
    //@PreAuthorize("hasAuthority('admin:activity:info')")
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/info", method = RequestMethod.GET)
    public CommonResult<StoreActivityResponse> info(@RequestParam(value = "id") Integer id) {
        return CommonResult.success(storeActivityService.getDetail(id));
    }

    /**
     * 修改活动状态
     * 
     * @param id Integer
     * @param status Boolean
     */
    //@PreAuthorize("hasAuthority('admin:activity:update:status')")
    @ApiOperation(value = "修改状态")
    @RequestMapping(value = "/update/status", method = RequestMethod.POST)
    public CommonResult<String> updateStatus(@RequestParam Integer id, @RequestParam Boolean status) {
        if (storeActivityService.updateStatus(id, status)) {
            return CommonResult.success();
        } else {
            return CommonResult.failed();
        }
    }
}