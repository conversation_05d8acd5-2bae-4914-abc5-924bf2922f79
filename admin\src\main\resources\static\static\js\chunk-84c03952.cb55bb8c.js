(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-84c03952"],{"24ef":function(t,e,n){"use strict";n.r(e);var r=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",[r("div",{staticClass:"OrderCancellation"},[r("div",{staticClass:"header"}),t._v(" "),r("div",{staticClass:"whiteBg"},[r("div",{staticClass:"input"},[r("input",{directives:[{name:"model",rawName:"v-model",value:t.verify_code,expression:"verify_code"}],attrs:{type:"number",placeholder:"请输入核销码"},domProps:{value:t.verify_code},on:{input:function(e){e.target.composing||(t.verify_code=e.target.value)}}})]),t._v(" "),r("div",{staticClass:"bnt",on:{click:t.storeCancellation}},[t._v("立即核销")])]),t._v(" "),t.isWeixin?r("div",{staticClass:"scan"},[r("img",{attrs:{src:n("fa5e")},on:{click:t.openQRCode}})]):t._e()]),t._v(" "),t.orderInfo?r("WriteOff",{attrs:{iShidden:t.iShidden,orderInfo:t.orderInfo},on:{cancel:t.cancel,confirm:t.confirm}}):t._e()],1)},o=[],i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{directives:[{name:"show",rawName:"v-show",value:!1===t.iShidden,expression:"iShidden === false"}]},[n("div",{staticClass:"WriteOff"},[n("div",{staticClass:"pictrue"},[n("img",{attrs:{src:t.orderInfo.storeOrderInfoVos[0].info.image}})]),t._v(" "),n("div",{staticClass:"num acea-row row-center-wrapper"},[t._v("\n      "+t._s(t.orderInfo.orderId)+"\n      "),n("div",{staticClass:"views",on:{click:function(e){return t.toDetail(t.orderInfo)}}},[t._v("\n        查看"),n("span",{staticClass:"iconfont icon-jiantou views-jian"})])]),t._v(" "),n("div",{staticClass:"tip"},[t._v("确定要核销此订单吗？")]),t._v(" "),n("div",{staticClass:"sure",on:{click:t.confirm}},[t._v("确定核销")]),t._v(" "),n("div",{staticClass:"sure cancel",on:{click:t.cancel}},[t._v("取消")])]),t._v(" "),n("div",{staticClass:"maskModel",on:{touchmove:function(t){t.preventDefault()}}})])},a=[],c={name:"WriteOff",props:{iShidden:{type:Boolean,default:!0},orderInfo:{type:Object,default:null}},data:function(){return{}},created:function(){n.e("chunk-2d0d6f43").then(n.t.bind(null,"756e",7))},methods:{toDetail:function(t){this.$router.push({path:"/javaMobile/orderDetail/"+t.orderId+"/looks"})},cancel:function(){this.$emit("cancel",!0)},confirm:function(){this.$emit("confirm",!0)}}},d=c,s=(n("df1d"),n("2877")),u=Object(s["a"])(d,i,a,!1,null,"a9ba4704",null),f=u.exports,l=n("74f9"),m=n("f8b7"),h="OrderCancellation",v={name:h,components:{WriteOff:f},props:{},data:function(){return{isWeixin:this.$wechat.isWeixin(),iShidden:!0,orderInfo:null,verify_code:""}},created:function(){n.e("chunk-2d0d6f43").then(n.t.bind(null,"756e",7))},methods:{cancel:function(t){this.iShidden=t},confirm:function(){var t=this;Object(m["w"])(this.verify_code).then((function(e){t.iShidden=!0,t.verify_code="",t.$dialog.success(e.msg)})).catch((function(e){t.$dialog.error(e.msg)}))},storeCancellation:function(){var t=this,e=/[0-9]{10}/;return this.verify_code?e.test(this.verify_code)?(this.$dialog.loading.open("查询中"),void Object(m["v"])(this.verify_code).then((function(e){t.$dialog.loading.close(),t.orderInfo=e,t.iShidden=!1})).catch((function(e){return t.$dialog.loading.close(),t.verify_code="",t.$dialog.error(e.message)}))):this.$dialog.error("请输入正确的核销码"):this.$dialog.error("请输入核销码")},openQRCode:function(){var t=this;Object(l["wechatEvevt"])("scanQRCode",{needResult:1,scanType:["qrCode","barCode"]}).then((function(e){e.resultStr?(t.verify_code=e.resultStr,t.storeCancellation()):t.$dialog.error("没有扫描到什么！")})).catch((function(e){e.is_ready&&e.wx.scanQRCode({needResult:1,scanType:["qrCode","barCode"],success:function(e){t.verify_code=e.resultStr,t.storeCancellation()},fail:function(e){"scanQRCode:permission denied"==e.errMsg&&t.$dialog.error("没有权限")}})}))}}},p=v,g=(n("331a"),Object(s["a"])(p,r,o,!1,null,"4553470c",null));e["default"]=g.exports},"331a":function(t,e,n){"use strict";n("a5fc")},a5fc:function(t,e,n){},df1d:function(t,e,n){"use strict";n("f6dd")},f6dd:function(t,e,n){},f8b7:function(t,e,n){"use strict";n.d(e,"g",(function(){return o})),n.d(e,"p",(function(){return i})),n.d(e,"h",(function(){return a})),n.d(e,"e",(function(){return c})),n.d(e,"i",(function(){return d})),n.d(e,"f",(function(){return s})),n.d(e,"j",(function(){return u})),n.d(e,"n",(function(){return f})),n.d(e,"m",(function(){return l})),n.d(e,"l",(function(){return m})),n.d(e,"w",(function(){return h})),n.d(e,"v",(function(){return v})),n.d(e,"o",(function(){return p})),n.d(e,"s",(function(){return g})),n.d(e,"t",(function(){return b})),n.d(e,"q",(function(){return _})),n.d(e,"r",(function(){return O})),n.d(e,"d",(function(){return j})),n.d(e,"b",(function(){return C})),n.d(e,"u",(function(){return y})),n.d(e,"k",(function(){return w})),n.d(e,"a",(function(){return $}));var r=n("b775");function o(t){return Object(r["a"])({url:"/admin/store/order/list",method:"get",params:t})}function i(t){return Object(r["a"])({url:"/admin/store/order/status/num",method:"get",params:t})}function a(t){return Object(r["a"])({url:"/admin/store/order/list/data",method:"get",params:t})}function c(t){return Object(r["a"])({url:"/admin/store/order/delete",method:"get",params:t})}function d(t){return Object(r["a"])({url:"/admin/store/order/status/list",method:"get",params:t})}function s(t){return Object(r["a"])({url:"/admin/store/order/info",method:"get",params:t})}function u(t){return Object(r["a"])({url:"/admin/store/order/mark",method:"post",params:t})}function f(t){return Object(r["a"])({url:"/admin/store/order/send",method:"post",data:t})}function l(t){return Object(r["a"])({url:"/admin/store/order/refund/refuse",method:"get",params:t})}function m(t){return Object(r["a"])({url:"/admin/store/order/refund",method:"get",params:t})}function h(t){return Object(r["a"])({url:"/admin/store/order/writeUpdate/".concat(t),method:"get"})}function v(t){return Object(r["a"])({url:"/admin/store/order/writeConfirm/".concat(t),method:"get"})}function p(){return Object(r["a"])({url:"/admin/store/order/statistics",method:"get"})}function g(t){return Object(r["a"])({url:"/admin/store/order/statisticsData",method:"get",params:t})}function b(t){return Object(r["a"])({url:"admin/store/order/update/price",method:"post",data:t})}function _(t){return Object(r["a"])({url:"/admin/store/order/time",method:"get",params:t})}function O(){return Object(r["a"])({url:"/admin/store/order/sheet/info",method:"get"})}function j(t){return Object(r["a"])({url:"/admin/store/order/getLogisticsInfo",method:"get",params:t})}function C(){return Object(r["a"])({url:"/admin/pay/component/delivery/company/get/list",method:"get"})}function y(t){return Object(r["a"])({url:"/admin/store/order/video/send",method:"post",data:t})}function w(t){return Object(r["a"])({url:"/admin/yly/print/".concat(t),method:"get"})}function $(t){return Object(r["a"])({url:"/admin/store/order/auditAddressList",method:"get",params:t})}},fa5e:function(t,e,n){t.exports=n.p+"static/img/scan.2ca2147e.gif"}}]);